#ifndef _DATABASE_MANAGER_H_
#define _DATABASE_MANAGER_H_

#include "moduleBase.h"
#include "database.h"
#include "utils/inimanager/config_util.h"
// #include "utils/stringutil/stringutils.h"
#include <map>

volatile extern int gFactoryType ;

class CDatabaseManager : public CModuleBase
{
    public:
    CDatabaseManager(CMediator *pMediator);
    ~CDatabaseManager();

    void Init() override;
    void Start() override;
    void Stop() override;
    void Pause() override;

    bool MsgFilter(u32 msgType);
    void HandleMsg(u32 msgLevel,u32 deviceId,u32 msgType,std::shared_ptr<void> spData);

    protected:
    bool updateConfig();
    bool updateConnectArgs(std::shared_ptr<void> spData);
    bool updateData(std::shared_ptr<void> spData);
    bool dataCompressQueryReq(u32 msgLevel,u32 deviceId,std::shared_ptr<void> spData);
    bool dataQueryReq(u32 msgLevel,u32 deviceId,std::shared_ptr<void> spData);
    bool loadDatabaseList(std::string strPath);

    bool batchesDataQueryReq(u32 msgLevel,u32 deviceId,std::shared_ptr<void> spData);

    bool findNearestValue(const std::map<u64,std::string>& mapDatabase,u64 &llStartTime,
                         u64 &llEndTime,std::vector<std::string> &vecDatabaseName);

    bool updateRoadInfoList();
    void updataStorePath();


    private:
    fileutil::ConfigUtil m_config;
    std::vector<std::string> m_vecRoadId;
    std::map<std::string,CDatabase*> m_mapDatabaseHandle;
    std::map<std::string,std::map<u64,std::string>> m_mapDatabase;
    tagArgsList m_stArgLists;
    std::string m_strPath;
    std::string m_strFactoryPath;
    std::vector<std::thread> m_vecThread;

};



#endif