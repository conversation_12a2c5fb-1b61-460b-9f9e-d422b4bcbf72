#include "database_manager.h"
#include "commonFunction.h"
#include "logger.hpp"
#include <memory>
#include <dirent.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <cstring>
#include <thread>
#include "json/json.h"

CDatabaseManager::CDatabaseManager(CMediator *pMediator) : CModuleBase(pMediator)
{
    m_bIsRunning = false;
    m_strPath = gStrStorePath;
    m_strFactoryPath = "../conf/data_factory.ini";
}

CDatabaseManager::~CDatabaseManager()
{
}

void CDatabaseManager::Init()
{
    // 读取当前目录下的所有数据库文件
    loadDatabaseList(m_strPath);
}
void CDatabaseManager::Start()
{
    if (m_bIsRunning)
    {
        return;
    }

    for (auto iter : m_stArgLists.vecList)
    {
        if (!iter.bEnable)
        {
            continue;
        }
        CDatabase *pDatabase = new CDatabase();
        pDatabase->Init(iter.strRoadId, iter.dwFactory);
        m_mapDatabaseHandle[iter.strRoadId] = pDatabase;
    }

    auto iter = m_mapDatabaseHandle.begin();
    for (; iter != m_mapDatabaseHandle.end(); iter++)
    {
        iter->second->Start();
    }
    m_bIsRunning = true;
    updateRoadInfoList();
}
void CDatabaseManager::Stop()
{
    bool bIsAllDatabaseClosed = true;
    for (auto iter = m_mapDatabaseHandle.begin(); iter != m_mapDatabaseHandle.end();)
    {
        if(!iter->second->Stop())                // 停止处理
        {
            bIsAllDatabaseClosed = false;
        }
        delete iter->second;                    // 删除指针指向的对象
        iter = m_mapDatabaseHandle.erase(iter); // 删除元素并将迭代器指向下一个位置
    }
    m_bIsRunning = false;

    // 结束线程
    for(auto &it :m_vecThread)
    {
        if(it.joinable())
        {
            it.join();
        }
    }
    m_vecThread.clear();
    //通知出去状态
    std::shared_ptr<TSystemLog> spRes = std::make_shared<TSystemLog>();
    spRes->bStatus = bIsAllDatabaseClosed;  //传递是否全部关闭；
    Notify(MSG_LEVEL_ALL_DEVICE, NO_DEVICE, DATA_DATABSE_CLOSED, std::static_pointer_cast<void>(spRes));
}
void CDatabaseManager::Pause()
{
    auto iter = m_mapDatabaseHandle.begin();
    for (; iter != m_mapDatabaseHandle.end(); iter++)
    {
        iter->second->Pause();
    }
}

bool CDatabaseManager::MsgFilter(u32 msgType)
{
    bool ret = true;
    if (msgType == SYSTEM_INIT ||
        msgType == SYSTEM_START ||
        msgType == SYSTEM_STOP ||
        msgType == SYSTEM_PAUSE ||
        msgType == CAPTURE_DATA_UPDATE ||
        msgType == DATA_COMPRESS_QUERY_REQ ||
        msgType == DATA_QUERY_REQ ||
        msgType == CAPTURE_CONFIG_UPDATE ||
        msgType == SYSTEM_CLIENT_CONNECTED ||
        msgType == DATA_QUERY_BATCH_REQ ||
        msgType == SYSTEM_UPDATE_STORE_PATH)
    {
        ret = false;
    }
    return ret;
}

void CDatabaseManager::HandleMsg(u32 msgLevel, u32 deviceId, u32 msgType, std::shared_ptr<void> spData)
{

    switch (msgType)
    {
    case SYSTEM_INIT:
        Init();
        break;
    case SYSTEM_START:
        Start();
        break;
    case SYSTEM_STOP:
        Stop();
        break;
    case SYSTEM_PAUSE:
        Pause();
        break;
    case CAPTURE_DATA_UPDATE:
        updateData(spData);
        break;
    case DATA_COMPRESS_QUERY_REQ:
    {
        m_vecThread.emplace_back([this, msgLevel, deviceId, spData]{
            this->dataCompressQueryReq(msgLevel, deviceId, spData);
        });
        // std::thread handleThread(&CDatabaseManager::dataCompressQueryReq, this, msgLevel, deviceId, spData);
        // handleThread.detach();
        // dataCompressQueryReq(spData);
    }
    break;
    case DATA_QUERY_REQ:
    {
        m_vecThread.emplace_back([this, msgLevel, deviceId, spData]{
            this->dataQueryReq(msgLevel, deviceId, spData);
        });
        // std::thread handleThead(&CDatabaseManager::dataQueryReq, this, msgLevel, deviceId, spData);
        // handleThead.detach();
        // dataQueryReq(spData);
    }
    break;
    case DATA_QUERY_BATCH_REQ:
    {
        m_vecThread.emplace_back([this, msgLevel, deviceId, spData]{
           this->batchesDataQueryReq(msgLevel, deviceId, spData); 
        });
        // std::thread handleThread(&CDatabaseManager::batchesDataQueryReq, this, msgLevel, deviceId, spData);
        // handleThread.detach();
    }
    break;
    case CAPTURE_CONFIG_UPDATE:
        updateConnectArgs(spData);
        break;
    case SYSTEM_CLIENT_CONNECTED:
        updateRoadInfoList();
        break;
    case SYSTEM_UPDATE_STORE_PATH:
        updataStorePath();
        break;
    default:
        break;
    }
}
bool CDatabaseManager::updateConnectArgs(std::shared_ptr<void> spData)
{
    std::shared_ptr<TArgsList> spArgsList = std::static_pointer_cast<TArgsList>(spData);
    if (!spArgsList)
    {
        return false;
    }
    m_stArgLists.dwCnt = spArgsList->dwCnt;
    std::vector<TConnectArgs> emptyVec;
    m_stArgLists.vecList.swap(emptyVec);
    for (auto iter : spArgsList->vecList)
    {
        m_stArgLists.vecList.push_back(iter);
    }

    return true;
}

// bool CDatabaseManager::updateConfig()
// {
//     std::string strRoadIdList = "";
//     auto iter = m_mapDatabaseHandle.begin();
//     for (; iter != m_mapDatabaseHandle.end(); iter++)
//     {
//         strRoadIdList += iter->first + ",";
//     }
//     if (strRoadIdList.length() != 0)
//     {
//         strRoadIdList.pop_back();
//     }
//     if (m_config.Move2Section("roadInfo"))
//     {
//         m_config.WriteKey("roadIdList", strRoadIdList);
//     }

//     m_config.Save2File();
//     return true;
// }

bool CDatabaseManager::updateData(std::shared_ptr<void> spData)
{
    std::shared_ptr<TRawData> spCapData = std::static_pointer_cast<TRawData>(spData);

    for (auto iter : m_mapDatabaseHandle)
    {
        if (spCapData->strCrossroadId == iter.first)
        {
            if(iter.second!=nullptr)
            {
                iter.second->UpdateData(spCapData->llRecvTime, spCapData->strData);
            }
            break;
        }
    }

    return true;
}
bool CDatabaseManager::batchesDataQueryReq(u32 msgLevel, u32 deviceId, std::shared_ptr<void> spData)
{
    std::shared_ptr<TBatchesDataQueryRes> spRes = std::make_shared<TBatchesDataQueryRes>();
    bool ret = true;
    do
    {
        std::shared_ptr<TBatchesDataQueryReq> spReq = std::static_pointer_cast<TBatchesDataQueryReq>(spData);
        if (!spReq)
        {
            return false;
        }

        // 判断是否需要分包
        std::vector<std::string> vecDatabase;
        for (auto iter : m_mapDatabase)
        {
            if (iter.first == spReq->strCrossId) // 找到对应路口id的数据库map
            {
                // std::cout << "iter.first= " << iter.first << std::endl;
                // std::cout << "spReq->strCrossId = " << spReq->strCrossId << std::endl;
                // std::cout << "start  time = " << spReq->llStartTime << " end time = " <<spReq->llEndTime << std::endl;
                ret = findNearestValue(iter.second, spReq->llStartTime, spReq->llEndTime, vecDatabase);
            }
        }
        if (!ret)
        {
            spRes->bIsSucceed = false;
            spRes->strErr = "can not find database!";
            break;
        }

        // 计算总行数目和各个数据库的行数
        std::map<std::string, int> mapRow;
        // std::string strSqlRowNum = "select count(*) from data where recvTime between " +
        //                            std::to_string(spReq->llStartTime) + " and " +
        //                            std::to_string(spReq->llEndTime) + " ;";
        std::string strSqlRowNum = "select count(*) from data where recvTime between " +
                                   std::to_string(spReq->llStartTime) + " and " +
                                   std::to_string(spReq->llEndTime) + " ;";
       
        int totalNum = 0;
        // 获得行数并存储在map中
        for (auto iter : vecDatabase)
        {
            sqlite3 *db;
            char *errorMessage = 0;
            int rc;
            std::string db_path = m_strPath + iter;
             std::cout << "db_path = " << db_path << std::endl;
            rc = sqlite3_open_v2(db_path.c_str(), &db, SQLITE_OPEN_READONLY, nullptr);
            if (rc != SQLITE_OK)
            {
                std::cerr << "Can't open database : " << db_path << " errInfo : " << sqlite3_errmsg(db) << std::endl;
                spRes->bIsSucceed = false;
                spRes->strErr = "Can't open database: " + iter;
                ret = false;
                break;
            }

            sqlite3_stmt *stmt;
            rc = sqlite3_prepare_v2(db, strSqlRowNum.c_str(), -1, &stmt, 0);
            if (rc != SQLITE_OK)
            {
                ERROR("Failed to prepare statement: {}", sqlite3_errmsg(db));
                // std::cerr << "Failed to prepare statement: " << sqlite3_errmsg(db) << std::endl;
                spRes->bIsSucceed = false;
                spRes->strErr = "Failed to prepare statement: " + std::string(sqlite3_errmsg(db));
                ret = false;
                break;
            }
            while ((rc = sqlite3_step(stmt)) == SQLITE_ROW)
            {
                int dwNum = sqlite3_column_int(stmt, 0);
                mapRow[iter] = dwNum;
                totalNum += dwNum;
            }
            sqlite3_finalize(stmt);
            sqlite3_close(db);
        }
        // 计算总页数和总数据大小
        if (totalNum % 10 != 0)
        {
            spRes->dwTotalPages = totalNum / 10 + 1;
        }
        else
        {
            spRes->dwTotalPages = totalNum / 10;
        }
        spRes->dwTotalDatas = totalNum;

        // 根据当前页数确定数据库 分别读取数据
        int dwLastCnt = 0;
        int dwStartIndex = (spReq->dwNowPage - 1) * 10;
        for (auto iter : mapRow)
        {
            int dwDbSize = iter.second;
            if (dwDbSize + dwLastCnt < dwStartIndex)
            {
                dwLastCnt += dwDbSize;
                continue;
            }

            // 确定起始数据
            int dbStart = std::max(dwStartIndex - dwLastCnt, 0);
            int dbEnd = std::min(dwDbSize, dwStartIndex - dwLastCnt + 9);
            // 读取数据
            sqlite3 *db;
            char *errorMessage = 0;
            int rc = 0;
            std::string strDBPath = m_strPath + iter.first;
            rc = sqlite3_open_v2(strDBPath.c_str(), &db, SQLITE_OPEN_READONLY, nullptr);
            if (rc != SQLITE_OK)
            {
                ERROR("Can't open database: {1} path {2}", sqlite3_errmsg(db), strDBPath);
                // std::cerr << "Can't open database: " << sqlite3_errmsg(db) << std::endl;
                spRes->bIsSucceed = false;
                spRes->strErr = "Can't open database: " + iter.first;
                ret = false;
                break;
            }

            // std::string strSql = "select * from data where recvTime between " +
            //                      std::to_string(spReq->llStartTime) + " and " +
            //                      std::to_string(spReq->llEndTime) + " and id between " +
            //                      std::to_string(dbStart) + " and " + std::to_string(dbEnd)+";";

            int dwLimit = dbEnd - dbStart + 1;
            int dwOffset = dbStart;

            std::string strSql = "select * from data where recvTime between " +
                                 std::to_string(spReq->llStartTime) + " and " +
                                 std::to_string(spReq->llEndTime) + " limit  " +
                                 std::to_string(dwLimit) + " offset " + std::to_string(dwOffset) + ";";
            sqlite3_stmt *stmt;
            rc = sqlite3_prepare_v2(db, strSql.c_str(), -1, &stmt, 0);
            if (rc != SQLITE_OK)
            {
                ERROR("Failed to prepare statement: {}", sqlite3_errmsg(db));
                // std::cerr << "Failed to prepare statement: " << sqlite3_errmsg(db) << std::endl;
                spRes->bIsSucceed = false;
                spRes->strErr = "Failed to prepare statement: " + std::string(sqlite3_errmsg(db));
                ret = false;
                break;
            }

            while ((rc = sqlite3_step(stmt)) == SQLITE_ROW)
            {
                spRes->dwCnt++;
                int intValue = sqlite3_column_int(stmt, 0);
                TRawData stRawData;
                stRawData.strCrossroadId = spReq->strCrossId;
                stRawData.llRecvTime = sqlite3_column_int64(stmt, 1);
                stRawData.llDataTime = sqlite3_column_int64(stmt, 2);
                stRawData.wDataLength = sqlite3_column_int(stmt, 3);
                stRawData.strData.assign((char *)sqlite3_column_text(stmt, 4));
                spRes->vecList.push_back(stRawData);
            }
            sqlite3_finalize(stmt);
            sqlite3_close(db);

            if (dbEnd >= dwStartIndex - dwLastCnt + 9)
            {
                break;
            }
            else
            {
                dwLastCnt += dwDbSize;
                dwLastCnt += 1;
            }
        }

        if (!ret)
        {
            break;
        }

        ret = true;
        spRes->bIsSucceed = true;
    } while (0);

    Notify(msgLevel, deviceId, DATA_QUERY_BATCH_RES, std::static_pointer_cast<void>(spRes));

    return ret;
}
bool CDatabaseManager::dataQueryReq(u32 msgLevel, u32 deviceId, std::shared_ptr<void> spData)
{
    std::shared_ptr<TDataQueryRes> spDataQueryRes = std::make_shared<TDataQueryRes>();
    bool ret = false;
    do
    {
        std::shared_ptr<TDataQuery> spDataQuery = std::static_pointer_cast<TDataQuery>(spData);
        if (!spDataQuery)
        {
            return false;
        }

        // 判断是否需要分包
        std::vector<std::string> vecDatabase;
        for (auto iter : m_mapDatabase)
        {
            if (iter.first == spDataQuery->strCrossroadId) // 找到对应路口id的数据库map
            {
                ret = findNearestValue(iter.second, spDataQuery->llStartTime, spDataQuery->llEndTime, vecDatabase);
            }
        }

        if (!ret)
        {
            spDataQueryRes->bIsSucceed = false;
            spDataQueryRes->strErr = "can not find database!";
            break;
        }

        std::string strSql = "select * from data where recvTime between " +
                             std::to_string(spDataQuery->llStartTime) + " and " +
                             std::to_string(spDataQuery->llEndTime) + ";";
        std::cout << "strSql :" << strSql << std::endl;
        std::cout << "vecDatabase size = " << vecDatabase.size() << std::endl; 
        for (auto iter : vecDatabase)
        {
            // 查找数据
            sqlite3 *db;
            char *errorMessage = 0;
            int rc;
            std::string db_path = m_strPath + iter;
            rc = sqlite3_open_v2(db_path.c_str(), &db, SQLITE_OPEN_READONLY, nullptr);
            if (rc != SQLITE_OK)
            {
                ERROR("Can't open database: {1} path {2}", sqlite3_errmsg(db), db_path);
                // std::cerr << "Can't open database: " << sqlite3_errmsg(db) << std::endl;
                spDataQueryRes->bIsSucceed = false;
                spDataQueryRes->strErr = "Can't open database: " + iter;
                ret = false;
                break;
            }

            sqlite3_stmt *stmt;
            rc = sqlite3_prepare_v2(db, strSql.c_str(), -1, &stmt, 0);

            if (rc != SQLITE_OK)
            {
                ERROR("Failed to prepare statement: {}", sqlite3_errmsg(db));
                // std::cerr << "Failed to prepare statement: " << sqlite3_errmsg(db) << std::endl;
                spDataQueryRes->bIsSucceed = false;
                spDataQueryRes->strErr = "Failed to prepare statement: " + std::string(sqlite3_errmsg(db));
                ret = false;
                break;
            }

            while ((rc = sqlite3_step(stmt)) == SQLITE_ROW)
            {
                spDataQueryRes->dwCnt++;
                int intValue = sqlite3_column_int(stmt, 0);
                TRawData stRawData;
                stRawData.strCrossroadId = spDataQuery->strCrossroadId;
                stRawData.llRecvTime = sqlite3_column_int64(stmt, 1);
                stRawData.llDataTime = sqlite3_column_int64(stmt, 2);
                stRawData.wDataLength = sqlite3_column_int(stmt, 3);
                stRawData.strData.assign((char *)sqlite3_column_text(stmt, 4));
                spDataQueryRes->vecList.push_back(stRawData);
            }

            sqlite3_finalize(stmt);
            sqlite3_close(db);
        }
        ret = true;
        std::cout << "cnt = " <<spDataQueryRes->dwCnt << std::endl;
        spDataQueryRes->bIsSucceed = true;
    } while (0);
    
    Notify(msgLevel, deviceId, DATA_QUERY_RES, std::static_pointer_cast<void>(spDataQueryRes));

    return true;
}

bool CDatabaseManager::dataCompressQueryReq(u32 msgLevel, u32 deviceId, std::shared_ptr<void> spData)
{   
    bool ret = false;
    std::shared_ptr<TDataCompressResList> spDataCompressResList = std::make_shared<TDataCompressResList>();
    do
    {
        if(!spData)
        {
            std::cerr << "Error: spData is null!" << std::endl;
            return false;
        } 
        // 解析数据
        std::shared_ptr<TDataCompressReqList> spDataCompressReqList = std::static_pointer_cast<TDataCompressReqList>(spData);
        if (!spDataCompressReqList)
        {
            return false;
        }   
        spDataCompressResList->strSerial = spDataCompressReqList->strSerial;
        spDataCompressResList->strPackageName = spDataCompressReqList->strPackageName;
        spDataCompressResList->chCompressType = spDataCompressReqList->chCompressType;
        spDataCompressResList->strHttpUrl = spDataCompressReqList->strHttpUrl;
        for (auto iterReq : spDataCompressReqList->vecList)
        {
            // 判断是否需要分包
            std::vector<std::string> vecDatabase;
            for (auto iter : m_mapDatabase)
            {
                if (iter.first == iterReq.strCrossroadId) // 找到对应路口id的数据库map
                {
                    ret = findNearestValue(iter.second, iterReq.llStartTime, iterReq.llEndTime, vecDatabase);
                }
            }

            if (!ret)
            {
                ERROR("Can not find database");
                spDataCompressResList->bIsSucceed = false;
                spDataCompressResList->strErr = "can not find database!";
                break;
            }

            std::string strSql = "select * from data where recvTime between " +
                                 std::to_string(iterReq.llStartTime) + " and " +
                                 std::to_string(iterReq.llEndTime) + ";";
            for (auto iter : vecDatabase)
            {
                // 查找数据
                // test
                sqlite3 *db;
                char *errorMessage = 0;
                int rc;
                std::string db_path = m_strPath + iter;
                rc = sqlite3_open_v2(db_path.c_str(), &db, SQLITE_OPEN_READONLY, nullptr);
                if (rc != SQLITE_OK)
                {
                    ERROR("Can't open database: {1} path : {2}", sqlite3_errmsg(db), db_path);
                    // std::cerr << "Can't open database: " << sqlite3_errmsg(db) << std::endl;
                    spDataCompressResList->bIsSucceed = false;
                    spDataCompressResList->strErr = "Can't open database: " + iter;
                    ret = false;
                    break;
                }

                sqlite3_stmt *stmt;
                rc = sqlite3_prepare_v2(db, strSql.c_str(), -1, &stmt, 0);

                if (rc != SQLITE_OK)
                {
                    ERROR("Failed to prepare statement: {}", sqlite3_errmsg(db));
                    // std::cerr << "Failed to prepare statement: " << sqlite3_errmsg(db) << std::endl;
                    spDataCompressResList->bIsSucceed = false;
                    spDataCompressResList->strErr = "Failed to prepare statement: " + std::string(sqlite3_errmsg(db));
                    ret = false;
                    break;
                }
                // 压缩请求则直接存本地文件，避免内存占用过大
                // 打开文件
                std::string file_path = gStrStorePath + spDataCompressReqList->strSerial;
                commonFunc::createFile(file_path, "");
                // if(false == commonFunc::createFile(spDataCompressReqList->strSerial,""));
                // {
                //     std::cout << "create file failed !" << std::endl;
                //     break;
                // }
                //
                std::string file_name_type = "";
                switch (iterReq.dwDataType)
                {
                case TDataQuery::STABILITY_DATA:
                    file_name_type = "statability";
                    break;
                case TDataQuery::TSARI_DATA:
                    file_name_type= "standard";
                    break;
                default:
                    file_name_type= "rawdata";
                    break;
                }
                std::string fileName = file_path + "/" + iterReq.strCrossroadId + "_" + commonFunc::convertUtcTimestampToDateTimeString(iterReq.llStartTime) + "_" + commonFunc::convertUtcTimestampToDateTimeString(iterReq.llEndTime) +"_" +file_name_type+ ".txt";
                std::ofstream ofs(fileName, std::ios::app);
                if (!ofs)
                {
                    ERROR("Can not create tmp file");
                    spDataCompressResList->bIsSucceed = false;
                    spDataCompressResList->strErr = "can not create tmp file";
                    ret = false;
                    break;
                }

                // 写入数据头
                ofs<<"[" << std::endl;
                bool isFirstLine = true;
                // 判断数据类型 写在循环外 ，提高效率
                if(iterReq.dwDataType == TDataQuery::STABILITY_DATA)    // 写入recv_data但不写入object_data
                {
                    while (sqlite3_step(stmt) == SQLITE_ROW)
                    {
                        TRawData stRawData;
                        // stRawData.strCrossroadId = iterReq.strCrossroadId;
                        // stRawData.llRecvTime = sqlite3_column_int64(stmt, 1);
                        // stRawData.llDataTime = sqlite3_column_int64(stmt, 2);
                        // stRawData.wDataLength = sqlite3_column_int(stmt, 3);
                        stRawData.strData.assign((char *)sqlite3_column_text(stmt, 4));
                        Json::Value recvData;
                        Json::Reader reader;
                        bool isDecodedSucceed = reader.parse(stRawData.strData, recvData);
                        if(isDecodedSucceed)
                        {
                            Json::Value dataWriteFileValue;
                            dataWriteFileValue["timestamp"] =recvData["timestamp"];
                            dataWriteFileValue["timestamp_capture"] = recvData["timestamp_capture"];
                            dataWriteFileValue["timestamp_predicted_out"] = recvData["timestamp_predicted_out"];
                            dataWriteFileValue["timestamp_receive"] =recvData["timestamp_receive"];
                            Json::Value arrayObjects(Json::arrayValue);
                            dataWriteFileValue["data_object"] = arrayObjects;
                            Json::StreamWriterBuilder write_builder;
                            write_builder["indentation"] = "";
                            std::string strResult = Json::writeString(write_builder, dataWriteFileValue);
                            if(!isFirstLine)
                            {
                                ofs<<",";
                            }
                            isFirstLine=false;
                            ofs << strResult << std::endl;
                        }
                    }
                }else if(iterReq.dwDataType == TDataQuery::TSARI_DATA)  // 只写入recv_data
                {
                    while (sqlite3_step(stmt) == SQLITE_ROW)
                    {
                        TRawData stRawData;
                        // stRawData.strCrossroadId = iterReq.strCrossroadId;
                        // stRawData.llRecvTime = sqlite3_column_int64(stmt, 1);
                        // stRawData.llDataTime = sqlite3_column_int64(stmt, 2);
                        // stRawData.wDataLength = sqlite3_column_int(stmt, 3);
                        stRawData.strData.assign((char *)sqlite3_column_text(stmt, 4));
                        if(!isFirstLine)
                            {
                                ofs<<",";
                            }
                            isFirstLine=false;
                        ofs << stRawData.strData << std::endl;
                    }
                }
                else        // RAWDATA
                {
                    while (sqlite3_step(stmt) == SQLITE_ROW)
                    {
                        TRawData stRawData;
                        // stRawData.strCrossroadId = iterReq.strCrossroadId;
                        stRawData.llRecvTime = sqlite3_column_int64(stmt, 1);
                        // stRawData.llDataTime = sqlite3_column_int64(stmt, 2);
                        // stRawData.wDataLength = sqlite3_column_int(stmt, 3);
                        stRawData.strData.assign((char *)sqlite3_column_text(stmt, 4));
                        if(!isFirstLine)
                            {
                                ofs<<",";
                            }
                            isFirstLine=false;
                        ofs << "{\"recv_timestamp\":" << stRawData.llRecvTime << ",\"recv_data\":" << stRawData.strData << "}" << std::endl;
                    }
                }
                ofs << "]" << std::endl;
                ofs.close();
                sqlite3_finalize(stmt);
                sqlite3_close(db);
            }
        }
        ret = true;
        spDataCompressResList->bIsSucceed = true;
    } while (0);

    Notify(msgLevel, deviceId, DATA_COMPRESS_QUERY_RES, std::static_pointer_cast<void>(spDataCompressResList));

    return true;
}

bool CDatabaseManager::loadDatabaseList(std::string strPath)
{
    // 遍历文件夹
    DIR *pDir;
    struct dirent *ent;
    struct stat st;

    pDir = opendir(strPath.c_str());
    if (!pDir)
    {
        return false;
    }

    while ((ent = readdir(pDir)) != nullptr)
    {
        const std::string strFileName = ent->d_name;
        if (strFileName[0] == '.')
            continue;
        if (commonFunc::endsWith(strFileName.c_str(), ".db"))
        {
            std::string strRoadId = commonFunc::extractRoadId(strFileName);
            u64 llTimestamp = commonFunc::extractTimestamp(strFileName);
            auto iter = m_mapDatabase.find(strRoadId);
            if (iter != m_mapDatabase.end())
            {
                iter->second[llTimestamp] = strFileName;
            }
            else
            {
                std::map<u64, std::string> mapTimeAndFilename;
                mapTimeAndFilename[llTimestamp] = strFileName;
                m_mapDatabase[strRoadId] = mapTimeAndFilename;
            }
        }
    }

    closedir(pDir);
    return true;
}

bool CDatabaseManager::findNearestValue(const std::map<u64, std::string> &mapDatabase, u64 &llStartTime,
                                        u64 &llEndTime, std::vector<std::string> &vecDatabaseName)
{
    int ret = true;
    std::string strStartDataBase, strEndDatabase;
    u64 llstartDatabaseTime = 0, llendDatabaseTime = 0;
    do
    {
        auto it1 = mapDatabase.lower_bound(llStartTime);
        if (it1 == mapDatabase.begin())
        {
            // std::cout << "没有更小的值" << std::endl;
            // ret = false;
            // break;
            strStartDataBase = it1->second;
        }
        else if (it1 == mapDatabase.end())
        {

            strStartDataBase = mapDatabase.rbegin()->second;
        }
        else
        {
            strStartDataBase = (--it1)->second;
        }
        llstartDatabaseTime = commonFunc::extractTimestamp(strStartDataBase);
        vecDatabaseName.push_back(strStartDataBase);

        auto it2 = mapDatabase.lower_bound(llEndTime);
        if (it2 == mapDatabase.begin())
        {
            // std::cout << "没有更小的值" << std::endl;
            // ret = false;
            // break;
            strEndDatabase = it2->second;
        }
        else if (it2 == mapDatabase.end())
        {
            strEndDatabase = mapDatabase.rbegin()->second;
        }
        else
        {
            strEndDatabase = (--it2)->second;
        }
        llendDatabaseTime = commonFunc::extractTimestamp(strEndDatabase);
        for (auto iter : mapDatabase)
        {
            // std::cout << "llstartDatabaseTime = " << llstartDatabaseTime << " time = " << iter.first << " llendDatabaseTime= " << llendDatabaseTime << std::endl;
            if (iter.first > llstartDatabaseTime && iter.first < llendDatabaseTime)
            {
                vecDatabaseName.push_back(iter.second);
            }
        }
        if (strStartDataBase != strEndDatabase)
        {
            vecDatabaseName.push_back(strEndDatabase);
        }
    } while (0);

    return ret;
}

bool CDatabaseManager::updateRoadInfoList()
{
    loadDatabaseList(m_strPath);
    std::shared_ptr<TRoadInfoList> spData = std::make_shared<TRoadInfoList>();

    for (auto iter : m_mapDatabase)
    {
        TRoadInfo stRoadInfo;
        stRoadInfo.strID = iter.first;
        spData->vecList.push_back(stRoadInfo);
    }

    Notify(MSG_LEVEL_ALL_DEVICE, NO_DEVICE, DATA_ROADINFO_UPDATE, std::static_pointer_cast<void>(spData));

    return true;
}

void CDatabaseManager::updataStorePath()
{
    m_strPath = gStrStorePath;
    loadDatabaseList(m_strPath);
}
