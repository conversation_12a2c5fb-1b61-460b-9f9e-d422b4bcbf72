#ifndef _DATABASE_H_
#define _DATABASE_H_
#include "moduleBase.h"
#include "sqlite3.h"
#include <mutex>
#include <string>
#ifdef SUPPORT_ZXK
#include "zxkDecoder.h"
#endif
#include "zjjtDecoder.h"
#ifdef SUPPORT_WANJI
#include "wanjiDecoder.h"
#endif
#ifdef SUPPORT_SHFX
#include "shanghaiDecoder.h"
#include "shanghaiTrafficDecoder.h"
#endif
#include "shanghaiTcpDecoder.h"
#include "tsariDecoder.h"
#include "common_define.h"

volatile extern int gFactoryType;
extern std::string gStrStorePath;

class CDatabase 
{
public:
    CDatabase();
    ~CDatabase();

    void Init(std::string roadId,int facotryType); 
    void Start(); 
    bool Stop(); 
    void Pause(); 
    void UpdateData(u64 &llCapTime,std::string &strData);
    // void QueryData();
    void QueryData(TDataQuery stData);

protected:
    bool openDataBase();
    bool closeDataBase();
    bool switchDataBase();  //切换数据库
    std::string getTimeString(u64 llUTCtime);


private:
    sqlite3* m_pSqlte;
    u64 m_llLastTime;
    std::string m_strRoadId;
    u32 m_dwInsertCnt;
    u64 m_dwTotalCnt;
    bool m_bIsRunning;
    std::mutex m_mutexDatabase;
    CDecoderBase *m_pDecoder;
    int m_dwFactory;
    std::vector<std::thread>  m_vecThread;
    std::condition_variable m_cv;
    std::mutex m_cvMutex;
    bool m_bSwitchFlag;

};





#endif