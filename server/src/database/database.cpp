#include "database.h"
#include <iostream>
#include <thread>
#include <time.h>
#include "commonFunction.h"
#include "logger.hpp"
// #include "shanghaiDecoder.cpp"
// #include "shanghaiDecoder.h"
// #include "shanghaiTrafficDecoder.h"

CDatabase::CDatabase()
{
    m_llLastTime = 0;
    m_pSqlte = NULL;
    m_dwInsertCnt = 0;
    m_dwTotalCnt= 0;
    m_bIsRunning = false;
}

CDatabase::~CDatabase()
{
}

void CDatabase::Init(std::string roadId,int factoryType)
{
    m_strRoadId = roadId;

    // 初始化数据解析厂商
    switch (factoryType)
    {
    #ifdef SUPPORT_ZXK
    case FACTORY_ZXK:
        m_pDecoder = new CZxkDecoder();
        INFO("Current Factory is ZXK");
        break;
    #endif    
    case FACTORY_ZJJT:
        m_pDecoder = new CZjjtDecoder();
        break;
    #ifdef SUPPORT_WANJI
    case FACTORY_WANJI:
        m_pDecoder = new CWanjiDecoder();
        INFO("Current Factory is WanJi");
        break;
        #endif
    #ifdef SUPPORT_SHFX
    case FACTORY_SHANGHAI:
		m_pDecoder = new CShanghaiDecoder();
        INFO("Current Factory is Shanghai Fengxian");
		break;
	case FACTORY_SHANGHAI_TRAFFIC:
        m_pDecoder = new CShanghaiTrafficDecoder();
        INFO("Current Factory is Shanghai Fengxian Traffic");
		break;
    #endif
    case FACTORY_GROUP_STANDARDS:   //复用
    case FACTORY_SHANGHAI_TCP:
        m_pDecoder = new CShanghaiTcpDecoder();
        INFO("Current Factory is Shanghai TCP Server Decoder");
		break;
    case FACTORY_TSARI:
        m_pDecoder = new CTsariDecoder();
        INFO("Current Factory is TSARI Decoder");
        break;

	case FACTORY_NONE:
    default:
        m_pDecoder = new CDecoderBase();
        break;
    }
    m_pDecoder->Init(m_strRoadId);
}
void CDatabase::Start()
{
    //打开数据库
    m_mutexDatabase.lock();
    if(!openDataBase())
    {
        ERROR("open dataBase failed!\n");
    }
    m_mutexDatabase.unlock();
    m_bIsRunning = true;
    // 创建线程用于切换数据库
    {
        std::lock_guard<std::mutex> lock(m_cvMutex);
        m_bSwitchFlag = true;
    }
    std::thread threadSwitchDB(&CDatabase::switchDataBase,this);
    m_vecThread.push_back(std::move(threadSwitchDB));
}
bool CDatabase::Stop()
{
    bool ret = false;
    {
        std::lock_guard<std::mutex> lock(m_cvMutex);
        m_bSwitchFlag = false;
        m_cv.notify_all();
    }
    for (std::thread &it : m_vecThread)
    {
        it.join();
    }
    m_mutexDatabase.lock();
    ret = closeDataBase();
    m_mutexDatabase.unlock();
    return ret;
}
void CDatabase::Pause()
{
}

bool CDatabase::openDataBase()
{

    bool reslut = false;
    do
    {
        // 打开数据库
        if (m_pSqlte)
        {
            INFO("database is exist");
            break;
        }

        // 记录数据库时间并取数据库名
        m_llLastTime = commonFunc::GetMsTime();
        struct tm dbtm;
        std::string strTime = getTimeString(m_llLastTime);
        std::string strDbName = gStrStorePath + m_strRoadId + "_" + std::to_string(m_llLastTime) + ".db";
        int ret = sqlite3_open_v2(strDbName.c_str(), &m_pSqlte, SQLITE_OPEN_READWRITE | SQLITE_OPEN_CREATE | SQLITE_OPEN_SHAREDCACHE, nullptr);
        if (ret != SQLITE_OK)
        {
            ERROR("oepn database failed!{1}",sqlite3_errmsg(m_pSqlte));
            sqlite3_close(m_pSqlte);
            break;
        }

        // 启用 WAL 模式
        const char *walModeSql = "PRAGMA journal_mode = WAL";
        char *errMsg;
        ret = sqlite3_exec(m_pSqlte, walModeSql, nullptr, nullptr, &errMsg);
        if (ret != SQLITE_OK)
        {
            ERROR("Failed to enable WAL mode: ",errMsg);
            if(errMsg)
            {
                // sqlite3_free(errMsg);
            }
            sqlite3_close(m_pSqlte);
            break;
        }

        // 创建表
        const char *createTableSql = "CREATE TABLE data (\
                                  id INTEGER PRIMARY KEY,\
                                  recvtime INTEGER,\
                                  datatime INTEGER,\
                                  datalen INTEGER,\
                                  data TEXT\
                                  );";
        ret = sqlite3_exec(m_pSqlte, createTableSql, nullptr, nullptr, nullptr);
        if (ret != SQLITE_OK)
        {
            ERROR("create table failed!\n");
            break;
        }

        reslut = true;
    } while (0);

    return reslut;
}

bool CDatabase::closeDataBase()
{
    
    bool ret = false;
    do
    {
        if (m_pSqlte == nullptr)
        {
            ret = true;
            break;
        }
        try
        {
            sqlite3_exec(m_pSqlte, "commit;", 0, 0, 0);
            sqlite3_wal_checkpoint_v2(m_pSqlte, NULL, SQLITE_CHECKPOINT_FULL, NULL, NULL);
            int sqlRet = sqlite3_close(m_pSqlte);
            if(sqlRet !=SQLITE_OK)
            {
                m_pSqlte = NULL;
                break;
            }
            m_pSqlte = NULL;
            
        }
        catch (const std::system_error &e)
        {
            ERROR("sysem error",e.what());
            std::cerr << "System error: " << e.what() << std::endl;
        }
        catch (const std::exception &e)
        {
            ERROR("standard exception",e.what());
            std::cerr << "Standard exception: " << e.what() << std::endl;
        }
        catch (...)
        {
            ERROR("Unknown exception caught");
            std::cerr << "Unknown exception caught" << std::endl;
        }

        ret = true;
    } while (0);


    return ret;
}

void CDatabase::UpdateData(u64 &llCapTime, std::string &strData)
{
    if(!m_bIsRunning)
    {
        return;
    }

    m_mutexDatabase.lock();
    do
    {
        if(!m_pSqlte)
        {
            ERROR("m_pSqlte is nullptr");
            break;
        }
        //解析数据
        std::string resultStr;
        bool ret1 = m_pDecoder->DecodeData(llCapTime,strData,&resultStr);
        if (!ret1)
        {
            ERROR("decode failed!\n");
            break;
        }

        if(resultStr == "")
        {
            break;
        }
        char *errMsg = nullptr;
        // 开启事务
        if (m_dwInsertCnt++ == 0)
        {
            int ret = sqlite3_exec(m_pSqlte, "begin;", 0, 0, &errMsg);
            if(ret != SQLITE_OK)
            {
                ERROR("sqlite3_exec failed!\n");
                if(errMsg != nullptr)
                {
                    ERROR(errMsg);
                    // sqlite3_free(errMsg);
                }
                break;
            }
            
        }

        
        const char *sql = "INSERT INTO data VALUES (?, ?, 0, ?, ?)";
        sqlite3_stmt *stmt;
        int rc = sqlite3_prepare_v2(m_pSqlte, sql, -1, &stmt, nullptr);
        if (rc != SQLITE_OK)
        { 
            ERROR("failed to prepare statement");
            std::cerr << "Failed to prepare statement: " << sqlite3_errmsg(m_pSqlte) << std::endl;
            break;
        }
        sqlite3_bind_int(stmt, 1, m_dwTotalCnt++);                        // 绑定第一个参数
        sqlite3_bind_int64(stmt, 2, llCapTime);                           // 绑定第二个参数
        sqlite3_bind_int(stmt, 3, resultStr.length());                    // 绑定第三个参数
        sqlite3_bind_text(stmt, 4, resultStr.c_str(), -1, SQLITE_STATIC); // 绑定第四个参数

        rc = sqlite3_step(stmt);
        if (rc != SQLITE_DONE)
        {
            std::cerr << "Execution failed: " << sqlite3_errmsg(m_pSqlte) << std::endl;
            break;
        }
        // 清理
        sqlite3_finalize(stmt);

        // 关闭事务
        if (m_dwInsertCnt == 10000)
        {
            sqlite3_exec(m_pSqlte, "commit;", 0, 0, 0);
            sqlite3_wal_checkpoint_v2(m_pSqlte, NULL, SQLITE_CHECKPOINT_FULL, NULL, NULL);
            m_dwInsertCnt = 0;
        }
    } while (0);

    m_mutexDatabase.unlock();
}


//切换数据库
bool CDatabase::switchDataBase()
{
    while (m_bIsRunning)
    {

        // 获取当前时间
        std::time_t t = std::time(nullptr);
        std::tm *currentTime = std::localtime(&t);
        // std::cout << "nowTime = " << currentTime->tm_hour << ":" << currentTime->tm_min<<":" << currentTime->tm_sec << std::endl;

        std::time_t curtime = std::mktime(currentTime);
         
        // 计算下一次0点的时间（00:00:00）
        currentTime->tm_hour = 0;
        currentTime->tm_min = 0;
        currentTime->tm_sec = 0;

        std::time_t midnight = std::mktime(currentTime) + 24 * 60 * 60; // 移动到第二天0点
        // std::time_t midnight = std::mktime(currentTime) + 60*60; // 1小时一次分包

        {
            std::unique_lock<std::mutex> lock(m_cvMutex);
            if (m_cv.wait_until(lock, std::chrono::system_clock::from_time_t(midnight), [this]()
                                { return !m_bSwitchFlag; }))
            {
                break; // 如果因 m_bIsRunning 变为 false 醒来，则退出循环
            }
        }

        m_mutexDatabase.lock();
        INFO("start to switch database ...");
        // switch
        closeDataBase();
        openDataBase();

        INFO("switch database end!");
        m_mutexDatabase.unlock();
        
    }
    INFO("switch database thread exit");
    return true;
}

std::string CDatabase::getTimeString(u64 llUTCtime)
{
    time_t time = static_cast<time_t>(llUTCtime/1000);
    struct tm* timeinfo = std::gmtime(&time);

    int year = timeinfo->tm_year + 1900;
    int month = timeinfo->tm_mon + 1;
    int day = timeinfo->tm_mday;
    int hour = timeinfo->tm_hour;
    int minute = timeinfo->tm_min;
    int second = timeinfo->tm_sec;

    std::string strTime = std::to_string(year)+"-"+std::to_string(month)+"-"+std::to_string(day)+"_"+
                          std::to_string(hour)+":"+std::to_string(minute)+":"+std::to_string(second);

    return strTime;
}

void CDatabase::QueryData(TDataQuery stData)
{
    //判断是否需要从多个数据库中查找数据
}