#ifndef _TCP_SERVER_H_
#define _TCP_SERVER_H_

#include <iostream>
#include <cstring>
#include <semaphore.h>
#include <unistd.h>
#include <sys/socket.h>
#include <netinet/in.h>
#include <thread>
#include <mutex>
#include <condition_variable>
#include <queue>
#include <sys/types.h>
#include <arpa/inet.h>
#include <functional>
#include <map>
#include "tcpClientInstance.h"
#include "message.h"

// #include "log.h"

const int MAX_BUFFER_SIZE = 1024;

class CTcpServer{
public:
    CTcpServer();
    ~CTcpServer();

    void Init(int dwPort);
    bool Start();
    bool Stop();
    void SetCallBack(const std::function<void(int dwDeviceID,std::string strMsg)>& pCb);
    void SetConnected(const std::function<void(int)>& pCb);
    void SendMessageToClient(int dwMsgLevel,int dwDeviceID,const std::string& message);
    void GetClientInfo(int &deviceId,std::string *strIp,int *pPort);
private:
    void acceptConnect();
    void msgArrived(int dwDeviceID,std::string msg);
    void clientStatusUpdate(int dwDeviceID,bool bStatus);
    void monitorClient();
    
    
private:
    int m_dwPort;
    bool m_bIsRunnning;
    bool m_bIsConnected;
    int m_Sokcet;
    int m_clientSocket;
    int m_dwPackageMaxLength;

    std::vector<std::thread> m_vecClientThreads;
    std::mutex m_mutex;
    std::condition_variable m_condition;

    std::function<void(int dwDeviceID,std::string strMsg)> m_pCb;
    std::function<void(int)> m_pConnectCb;

    std::map<int,CTcpClientInstance*> m_mapClientHandle;
    int m_dwDeviceCnt;
    sem_t m_sem;
    std::queue<int> m_queueDisconnect;
    std::map<int,std::pair<std::string,int>> m_mapClientInfo;
    std::thread m_monitorThread;
    std::thread m_recvThread;

};

#endif