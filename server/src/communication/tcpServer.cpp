#include "tcpServer.h"
#include "commonFunction.h"
#include "struct.h"
#include "tcpClientInstance.h"
#include <semaphore.h>
#include <utility>
#include "logger.hpp"

CTcpServer::CTcpServer()
{
	m_dwDeviceCnt = 1;
	m_bIsConnected = false;
	m_bIsRunnning = false;
	m_dwPort = -1;
	m_clientSocket = -1;
	m_dwPackageMaxLength = 5 * 10 * 1024;
	sem_init(&m_sem, 0, 0);
}

CTcpServer::~CTcpServer() {}

void CTcpServer::Init(int dwPort) { m_dwPort = dwPort; }

bool CTcpServer::Start()
{
	bool ret = false;
	do
	{
		// 创建服务器套接字
		m_Sokcet = socket(AF_INET, SOCK_STREAM, 0);
		if (-1 == m_Sokcet)
		{
			ERROR("Failed to create server socket.");
			break;
		}

		// 设置端口复用
		int optval = 1;
		if (setsockopt(m_Sokcet, SOL_SOCKET, SO_REUSEADDR, &optval,
					   sizeof(optval)) < 0)
		{
			ERROR("Error setting SO_REUSEADDR option");
			return 1;
		}

		// 设置接收超时
		struct timeval stTime;
		stTime.tv_sec = 5;
		stTime.tv_usec = 0;
		setsockopt(m_Sokcet, SOL_SOCKET, SO_RCVTIMEO, (const char *)&stTime,
				   sizeof(stTime));

		// 设置服务器地址和端口
		sockaddr_in serverAddress{};
		serverAddress.sin_family = AF_INET;
		serverAddress.sin_addr.s_addr = INADDR_ANY;
		serverAddress.sin_port = htons(m_dwPort);

		// 绑定套接字到地址和端口
		if (bind(m_Sokcet, (struct sockaddr *)&serverAddress,
				 sizeof(serverAddress)) < 0)
		{
			ERROR("Failed to bind server socket");
			close(m_Sokcet);
			break;
		}

		// 监听连接
		if (listen(m_Sokcet, 5) < 0)
		{
			ERROR("Failed to listen on server socket");
			close(m_Sokcet);
			break;
		}
		INFO("start socket {} ", m_Sokcet);

		m_bIsRunnning = true;

		// 启动接收线程
		m_recvThread = std::thread(&CTcpServer::acceptConnect, this);

		sockaddr_in localAddress;
		socklen_t addressLength = sizeof(localAddress);
		if (getsockname(m_Sokcet, (struct sockaddr *)&localAddress,
						&addressLength) == -1)
		{
			ERROR("get local address failed!");
		}
		else
		{
			// 将二进制ip转换为字符串
			char ipString[INET_ADDRSTRLEN];
			inet_ntop(AF_INET, &(localAddress.sin_addr), ipString, INET_ADDRSTRLEN);
			INFO("Server started on {} : {} ", ipString, ntohs(localAddress.sin_port));
			// std::cout << "Server started on " << ipString << ":"
			//           << ntohs(localAddress.sin_port) << std::endl;
		}

		// 启动连接关闭监控线程
		m_monitorThread = std::thread(&CTcpServer::monitorClient, this);
		ret = true;
	} while (0);

	return ret;
}

bool CTcpServer::Stop()
{
	m_bIsRunnning = false;
	if (m_recvThread.joinable())
	{
		m_recvThread.join();
	}
	
	if (m_monitorThread.joinable())
	{
		m_monitorThread.join();
	}
	
	// 等待所有客户端线程退出
	for (std::thread &clientThread : m_vecClientThreads)
	{
		if (clientThread.joinable())
		{
			clientThread.join();
		}
	}

	// 关闭套接字
	if (-1 != m_Sokcet)
	{
		close(m_Sokcet);

		m_Sokcet = -1;
	}
	INFO("TCP Server stopped!");

	return true;
}

void CTcpServer::SetCallBack(
	const std::function<void(int dwDeviceID, std::string strMsg)> &pCb)
{
	if (pCb != NULL)
	{
		m_pCb = pCb;
	}
}

void CTcpServer::SetConnected(const std::function<void(int)> &pCb)
{
	if (pCb != NULL)
	{
		m_pConnectCb = pCb;
	}
}

void CTcpServer::acceptConnect()
{
	while (m_bIsRunnning)
	{
		// 接收连接
		sockaddr_in clientAddress{};
		socklen_t clientAddressLength = sizeof(clientAddress);
		int clientSocket = accept(m_Sokcet, (struct sockaddr *)&clientAddress,
								  &clientAddressLength);
		if (clientSocket == -1)
		{
			continue;
		}

		// 将 IP 地址转换为字符串并输出
		char ipStr[INET_ADDRSTRLEN];
		inet_ntop(AF_INET, &(clientAddress.sin_addr), ipStr, INET_ADDRSTRLEN);
		INFO("Client connected. IP : {}", ipStr);
		// std::cout << "Connected device IP address: " << ipStr << std::endl;
		// 记录ip信息
		std::pair<std::string, int> clientInfo = std::make_pair(ipStr, 0);
		m_mapClientInfo[m_dwDeviceCnt] = clientInfo;
		// 创建实例
		CTcpClientInstance *handle =
			new CTcpClientInstance(std::string(ipStr), m_dwDeviceCnt);
		handle->SetSocketFd(clientSocket);
		// 设置回调函数
		std::function<void(int dwDeviceID, std::string strMsg)> MsgCallBack =
			[this](int dwDeviceID, std::string strMsg)
		{
			this->msgArrived(dwDeviceID, strMsg);
		};
		handle->SetMsgCallBack(MsgCallBack);

		std::function<void(int dwDeviceID, bool bStatus)> StatusCallBack =
			[this](int dwDeviceID, bool bStatus)
		{
			this->clientStatusUpdate(dwDeviceID, bStatus);
		};
		handle->SetStatusCallBack(StatusCallBack);

		handle->Start();
		m_pConnectCb(m_dwDeviceCnt);
		m_mapClientHandle[m_dwDeviceCnt] = handle;
		if (m_mapClientHandle[m_dwDeviceCnt] == nullptr)
		{
			// printf("handle is empty\n");
		}
		m_dwDeviceCnt++;
	}
}

void CTcpServer::msgArrived(int dwDeviceID, std::string msg)
{
	if (m_pCb != NULL)
	{
		m_pCb(dwDeviceID, msg);
	}
}

void CTcpServer::clientStatusUpdate(int dwDeviceID, bool bStatus)
{
	if (!bStatus)
	{
		m_queueDisconnect.push(dwDeviceID);
		sem_post(&m_sem);
	}
}

void CTcpServer::SendMessageToClient(int dwMsgLevel, int dwDeviceID,
									 const std::string &message)
{
	if (dwMsgLevel == MSG_LEVEL_ALL_DEVICE)
	{
		for (auto iter : m_mapClientHandle)
		{
			iter.second->SendMsg(message);
		}
	}
	else if (dwMsgLevel == MSG_LEVEL_ONE_DEVICE)
	{
		if (m_mapClientHandle[dwDeviceID] != NULL)
		{
			m_mapClientHandle[dwDeviceID]->SendMsg(message);
		}
	}
}

void CTcpServer::monitorClient()
{
	struct timespec ts;
	clock_gettime(CLOCK_REALTIME ,&ts);
	ts.tv_sec+=5;
	while (m_bIsRunnning)
	{
		if (sem_timedwait(&m_sem, &ts) == -1)
		{
			if (errno == ETIMEDOUT)
			{
				continue;
			}else{
				ERROR("sem_timedwait failed!");
				break;
			}
		}

		if (!m_bIsRunnning)
		{
			break;
		}
		if (m_queueDisconnect.size() == 0)
		{
			break;
		}
		int dwDeviceID = m_queueDisconnect.front();
		m_queueDisconnect.pop();
		if (m_mapClientHandle[dwDeviceID] != nullptr)
		{
			m_mapClientHandle[dwDeviceID]->Stop();
			int ret = close(m_mapClientHandle[dwDeviceID]->GetSocketFd());
			CTcpClientInstance *tmp = m_mapClientHandle[dwDeviceID];
			tmp->~CTcpClientInstance();
			m_mapClientHandle[dwDeviceID] = nullptr;
			m_mapClientHandle.erase(dwDeviceID);
			std::string strIp = "";
			int dwPort = 0;
			GetClientInfo(dwDeviceID, &strIp, &dwPort);
			INFO("{}:{} disconnected!", strIp, dwPort);
		}
	}
}

void CTcpServer::GetClientInfo(int &deviceId, std::string *strIp, int *pPort)
{
	for (auto it = m_mapClientInfo.begin(); it != m_mapClientInfo.end(); it++)
	{
		if (it->first == deviceId)
		{
			*strIp = it->second.first;
			*pPort = it->second.second;
		}
	}
}