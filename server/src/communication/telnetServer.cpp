#include "telnetServer.h"
#include "struct.h"
#include "commonFunction.h"

CTelnetServer::CTelnetServer()
{
    m_bIsConnected = false;
    m_bIsRunnning = false;
    m_dwPort = -1;
    m_clientSocket = -1;
    m_dwPackageMaxLength = 5 * 10 * 1024;
}

CTelnetServer::~CTelnetServer()
{
}

void CTelnetServer::Init(int dwPort)
{
    m_dwPort = dwPort;
}

bool CTelnetServer::Start()
{
    bool ret = false;
    do
    {
        // 创建服务器套接字
        m_Sokcet = socket(AF_INET, SOCK_STREAM, 0);
        if (-1 == m_Sokcet)
        {
            std::cout << "Failed to create server socket." << std::endl;
            break;
        }

        // 设置端口复用
        int optval = 1;
        if (setsockopt(m_Sokcet, SOL_SOCKET, SO_REUSEADDR, &optval, sizeof(optval)) < 0)
        {
            std::cerr << "Error setting SO_REUSEADDR option" << std::endl;
            return 1;
        }

        // 设置接收超时
        struct timeval stTime;
        stTime.tv_sec = 5;
        stTime.tv_usec = 0;
        setsockopt(m_Sokcet, SOL_SOCKET, SO_RCVTIMEO, (const char *)&stTime, sizeof(stTime));

        // 设置服务器地址和端口
        sockaddr_in serverAddress{};
        serverAddress.sin_family = AF_INET;
        serverAddress.sin_addr.s_addr = INADDR_ANY;
        serverAddress.sin_port = htons(m_dwPort);

        // 绑定套接字到地址和端口
        if (bind(m_Sokcet, (struct sockaddr *)&serverAddress, sizeof(serverAddress)) < 0)
        {
            std::cout << "Failed to bind server socket " << std::endl;
            close(m_Sokcet);
            break;
        }

        // 监听连接
        if (listen(m_Sokcet, 5) < 0)
        {
            std::cout << "Failed to listen on server socket " << std::endl;
            close(m_Sokcet);
            break;
        }

        std::cout << "start socket = " << m_Sokcet << std::endl;

        m_bIsRunnning = true;

        // 启动接收线程
        m_acceptThread = std::thread(&CTelnetServer::acceptConnect, this);

        sockaddr_in localAddress;
        socklen_t addressLength = sizeof(localAddress);
        if (getsockname(m_Sokcet, (struct sockaddr *)&localAddress, &addressLength) == -1)
        {
            std::cout << "get local address failed! " << std::endl;
        }
        else
        {
            // 将二进制ip转换为字符串
            char ipString[INET_ADDRSTRLEN];
            inet_ntop(AF_INET, &(localAddress.sin_addr), ipString, INET_ADDRSTRLEN);
            std::cout << "Server started on " << ipString << ":" << ntohs(localAddress.sin_port) << std::endl;
        }

        ret = true;
    } while (0);

    return ret;
}

bool CTelnetServer::Stop()
{
    m_bIsRunnning = false;
    if(m_acceptThread.joinable())
    {
        m_acceptThread.join();
    }
    // 等待所有客户端线程退出
    for (std::thread &clientThread : m_vecClientThreads)
    {
        if (clientThread.joinable())
        {
            clientThread.join();
        }
    }

    // 关闭套接字
    if (-1 != m_Sokcet)
    {
        close(m_Sokcet);

        m_Sokcet = -1;
    }

    std::cout << "Server stopped." << std::endl;

    return true;
}

void CTelnetServer::SetCallBack(const std::function<void(std::string strMsg)> &pCb)
{
    if (pCb != NULL)
    {
        m_pCb = pCb;
    }
}

void CTelnetServer::SetConnected(const std::function<void(int)> &pCb)
{
    if (pCb != NULL)
    {
        m_pConnectCb = pCb;
    }
}

void CTelnetServer::acceptConnect()
{
    while (m_bIsRunnning)
    {
        // 接收连接
        sockaddr_in clientAddress{};
        socklen_t clientAddressLength = sizeof(clientAddress);
        int clientSocket = accept(m_Sokcet, (struct sockaddr *)&clientAddress, &clientAddressLength);
        if (clientSocket == -1)
        {
            continue;
        }
        if (!m_bIsConnected)
        {
            m_clientSocket = clientSocket;
            m_bIsConnected = true;
            std::thread clientThread(&CTelnetServer::handleClient, this, clientSocket);
            m_vecClientThreads.push_back(std::move(clientThread));
        }
        else
        {
            std::string replayMessage = "server is busy";
            send(clientSocket, replayMessage.c_str(), replayMessage.size(), 0);
            sleep(1);
            close(clientSocket);
        }
    }
}

void CTelnetServer::handleClient(int clientSocket)
{
    std::cout << "Client connected" << std::endl;
    // 通知上层已经连接上
    // m_pConnectCb();
    std::string strRecvData; // 用于保存收到的数据
    while (m_bIsRunnning)
    {
        char buffer[m_dwPackageMaxLength];
        ssize_t bytesRead = recv(clientSocket, buffer, m_dwPackageMaxLength, 0);
        if (bytesRead > 0)
        {
            
            strRecvData.append(buffer, bytesRead);
            strRecvData.resize(bytesRead-2);
            if(strRecvData=="bye")
            {break;}
            m_pCb(strRecvData);
            strRecvData.clear();
        }
        else if (bytesRead == 0)
        {
            break;
        }
        else
        {
            if (errno == EWOULDBLOCK || errno == EAGAIN)
            {
            }
            else
            {
                break;
            }
        }
    }

    close(clientSocket);
    m_bIsConnected = false;
    std::cout << "Client disconnected." << std::endl;
}

void CTelnetServer::SendMessageToClient(const std::string &message)
{
    send(m_clientSocket, message.c_str(), message.size(), 0);
}
