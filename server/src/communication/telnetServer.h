#ifndef _TELNET_SERVER_H_
#define _TELNET_SERVER_H_

#include <iostream>
#include <cstring>
#include <unistd.h>
#include <sys/socket.h>
#include <netinet/in.h>
#include <thread>
#include <mutex>
#include <condition_variable>
#include <queue>
#include <sys/types.h>
#include <arpa/inet.h>
#include <functional>

// #include "log.h"


class CTelnetServer{
public:
    CTelnetServer();
    ~CTelnetServer();

    void Init(int dwPort);
    bool Start();
    bool Stop();
    void SetCallBack(const std::function<void(std::string strMsg)>& pCb);
    void SetConnected(const std::function<void(int)>& pCb);
    void SendMessageToClient(const std::string& message);
private:
    void acceptConnect();
    void handleClient(int clientSocket);
    
    
private:
    int m_dwPort;
    bool m_bIsRunnning;
    bool m_bIsConnected;
    int m_Sokcet;
    int m_clientSocket;
    int m_dwPackageMaxLength;

    std::vector<std::thread> m_vecClientThreads;
    std::mutex m_mutex;
    std::condition_variable m_condition;

    std::function<void(std::string strMsg)> m_pCb;
    std::function<void(int)> m_pConnectCb;
    std::thread m_acceptThread;
};

#endif