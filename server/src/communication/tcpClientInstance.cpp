#include "tcpClientInstance.h"
#include "commonFunction.h"
#include "struct.h"
#include <cstdio>
#include <fcntl.h>
#include <semaphore.h>
#include <stdlib.h>
#include <string.h>
#include <sys/socket.h>
#include <thread>
#include <unistd.h>

CTcpClientInstance::CTcpClientInstance(std::string strIp, int deviceId)
{

  m_strIp = strIp;
  dwPort = 0;
  m_dwDeviceID = deviceId;
  m_dwPackageMaxLength = 5 * 10 * 1024;
  m_bIsRunning = false;
  sem_init(&m_semMsg, 0, 0);
}

CTcpClientInstance::~CTcpClientInstance()
{
  for (std::thread &it : m_vecThread)
  {
    it.join();
  }
  sem_destroy(&m_semMsg);
}

bool CTcpClientInstance::Start()
{
  m_bIsRunning = true;
  std::thread threadSend(&CTcpClientInstance::sendThread, this);
  m_vecThread.push_back(std::move(threadSend));
  std::thread threadRecv(&CTcpClientInstance::recvThread, this);
  m_vecThread.push_back(std::move(threadRecv));

  return true;
}

bool CTcpClientInstance::Stop()
{
  m_bIsRunning = false;
  sem_post(&m_semMsg);
  return true;
}

void CTcpClientInstance::SetStatusCallBack(
    std::function<void(int dwDeviceId, bool bStatus)> &cb)
{
  if (cb != NULL)
  {
    m_pStatusCallback = cb;
  }
}

void CTcpClientInstance::SetMsgCallBack(
    std::function<void(int dwDeviceID, std::string strMsg)> &cb)
{
  if (cb != NULL)
  {
    m_pDataRecvCallback = cb;
  }
}

void CTcpClientInstance::SetSocketFd(int dwFd)
{
  m_dwFd = dwFd;
}

int CTcpClientInstance::GetSocketFd() { return m_dwFd; }

void CTcpClientInstance::SendMsg(const std::string &message)
{
  m_queueMsg.push(message);
  sem_post(&m_semMsg);
}

void CTcpClientInstance::sendThread()
{
  while (m_bIsRunning)
  {
    sem_wait(&m_semMsg);
    if (!m_bIsRunning)
    {
      // printf("exit sendThread!\n");
      break;
    }
    std::string message = m_queueMsg.front();
    m_queueMsg.pop();
    // 组数据头 分包处理
    std::string strSerial = std::to_string(commonFunc::GetUsTime()) +
                            commonFunc::generateUniqueRandomString(7);
    if (message.length() <= m_dwPackageMaxLength)
    {
      TMsgHead stMsgHead{};
      stMsgHead.bIsCompleted = true;
      stMsgHead.bIsEnd = true;
      stMsgHead.chVersion = 0XF1;
      stMsgHead.wDataLenghth = message.length();
      stMsgHead.wPackageLength = message.length();
      memcpy(stMsgHead.szSerial, strSerial.c_str(), sizeof(stMsgHead.szSerial));
      send(m_dwFd, &stMsgHead, sizeof(stMsgHead), 0);
      send(m_dwFd, message.c_str(), message.size(), 0);
      // std::cout << "send data length = " << message.size() << std::endl;
    }
    else
    {
      int dwDataLength = message.length();
      int dwPackageCnt =
          (dwDataLength + m_dwPackageMaxLength - 1) / m_dwPackageMaxLength;

      TMsgHead stMsgHead{};
      stMsgHead.bIsCompleted = false;
      stMsgHead.bIsEnd = false;
      stMsgHead.chVersion = 0XF1;
      stMsgHead.wDataLenghth = message.length();
      memcpy(stMsgHead.szSerial, strSerial.c_str(), sizeof(stMsgHead.szSerial));
      for (int i = 1; i <= dwPackageCnt; i++)
      {
        stMsgHead.bIsEnd = (i == dwPackageCnt);
        int packageSize = (i == dwPackageCnt)
                              ? (dwDataLength - (i - 1) * m_dwPackageMaxLength)
                              : m_dwPackageMaxLength;
        stMsgHead.wPackageLength = packageSize;
        ssize_t leng = send(m_dwFd, &stMsgHead, sizeof(stMsgHead), 0);
        // std::cout << "send head length = " << leng << std::endl;
        // usleep(100);
        leng = send(m_dwFd, &message.data()[(i - 1) * m_dwPackageMaxLength],
                    packageSize, 0);
        // std::cout << "send data length = " << leng << std::endl;
      }
    }
  }
}

void CTcpClientInstance::recvThread()
{
  // 通知上层已经连接上
  std::string strRecvData; // 用于保存收到的数据
  while (m_bIsRunning)
  {
    TMsgHead stMsgHead{};
    ssize_t bytesRead = recv(m_dwFd, &stMsgHead, sizeof(stMsgHead), 0);
    if (bytesRead > 0)
    {
      if (stMsgHead.chVersion != 0xF1)
      {
        continue;
      }
      char buffer[m_dwPackageMaxLength];
      bytesRead = recv(m_dwFd, buffer, stMsgHead.wPackageLength, 0);
      strRecvData.append(buffer, bytesRead);
      if ((stMsgHead.bIsCompleted || stMsgHead.bIsEnd) &&
          strRecvData.length() == stMsgHead.wDataLenghth)
      {
        m_pDataRecvCallback(m_dwDeviceID, strRecvData);
        strRecvData.clear();
      }
    }
    else if (bytesRead == 0)
    {
      break;
    }
    else
    {
      if (errno == EWOULDBLOCK || errno == EAGAIN)
      {
      }
      else
      {
        break;
      }
    }
  }

  m_bIsRunning = false;
  m_pStatusCallback(m_dwDeviceID, false);
}
