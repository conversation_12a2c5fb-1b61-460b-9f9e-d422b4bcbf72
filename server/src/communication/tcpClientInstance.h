#ifndef _TCP_CLIENT_INSTANCE_H_
#define _TCP_CLIENT_INSTANCE_H_

#include <functional>
#include <queue>
#include <semaphore.h>
#include <string>
#include <thread>
#include <mutex>

class CTcpClientInstance {

public:
  CTcpClientInstance(std::string strIp,int deviceId);
  ~CTcpClientInstance();

  bool Start();
  bool Stop();
  void SetStatusCallBack(std::function<void(int dwDeviceId, bool bStatus)>
                             &); // bStatus true:connected,false:disconnected;
  void SetMsgCallBack(std::function<void(int dwDeviceID, std::string strMsg)> &);
  void SetSocketFd(int dwFd);
  int GetSocketFd();
  void SendMsg(const std::string &strMsg);

private:
  void sendThread();
  void recvThread();

private:
  int m_dwDeviceID;
  int m_dwFd;
  std::queue<std::string> m_queueMsg;
  int m_dwPackageMaxLength;
  bool m_bIsRunning;
  sem_t m_semMsg;
  std::string m_strIp;
  int dwPort;

  std::function<void(int dwDeviceID, std::string strMsg)> m_pDataRecvCallback;
  std::function<void(int dwDeviceId, bool bStatus)> m_pStatusCallback;

  std::vector<std::thread> m_vecThread;
};

#endif