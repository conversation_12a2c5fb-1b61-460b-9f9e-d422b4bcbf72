cmake_minimum_required(VERSION 3.0.2)

aux_source_directory(. DIR_SRC)
add_library(communication SHARED ${DIR_SRC} )
target_link_libraries(communication datacapProto)



add_custom_command(TARGET communication POST_BUILD
 COMMAND
 mv libcommunication.so libcommunication.so.${PROJECT_VERSION}
 COMMAND
 ln -s libcommunication.so.${PROJECT_VERSION} libcommunication.so
 WORKING_DIRECTORY ${LIBRARY_OUTPUT_PATH})
