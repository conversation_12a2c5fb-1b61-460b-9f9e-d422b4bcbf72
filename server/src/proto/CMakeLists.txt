cmake_minimum_required(VERSION 3.0.2)

aux_source_directory(. DIR_SRC)

add_library(datacapProto SHARED ${DIR_SRC})


find_package(Protobuf REQUIRED)
include_directories(${PROTOBUF_INCLUDE_DIRS})
MESSAGE(${PROTOBUF_LIBRARIES})
target_link_libraries(datacapProto ${PROTOBUF_LIBRARIES})

add_custom_command(TARGET datacapProto POST_BUILD
 COMMAND
 mv libdatacapProto.so libdatacapProto.so.${PROJECT_VERSION}
 COMMAND
 ln -s libdatacapProto.so.${PROJECT_VERSION} libdatacapProto.so
 WORKING_DIRECTORY ${LIBRARY_OUTPUT_PATH})
