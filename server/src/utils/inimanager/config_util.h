#pragma once

#include <iostream>
#include <fstream>
#include <map>
#include <string>
namespace fileutil
{
    class ConfigUtil
    {
    public:
        /*
        打开配置文件
        in: 配置文件绝对路径
        out:成功返回true,失败返回false
        */
        bool OpenConfig(std::string strFile);
        /*
        移动至指定区域
        in: 指定配置段名
        out:成功返回true，失败返回false
        */
        bool Move2Section(std::string strSection);
        /*
        根据配置的键获取值
        in:strKey 想要获取的键值 strDefault 当获取失败时返回默认值
        out: 成功返回获取的值，失败返回strDefault
        */
        std::string GetValueByKey(std::string strKey, std::string strDefault);
        /*
        写入键值
        in: strKey 写入的键值 strValue 写入的值
        out:
        */
        void WriteKey(std::string strKey, std::string strValue);

        void WriteKey(std::string strKey, int value);
        /*
        保存文件
        */
        void Save2File();

    private:
        std::string m_str_file_path;
        std::string m_cur_section;
        std::map<std::string, std::string> m_value_map;
        std::map<std::string, std::map<std::string, std::string>> m_section_map;
    };
}
