#include "config_util.h"
#include <stdio.h>
#include <istream>
#include <vector>
#include "common/logger/logger.hpp"

using namespace fileutil;

void trim(std::string &s)
{
    if (s.empty())
    {
        return;
    }
    std::vector<std::string::const_iterator> blank_vec;
    std::string::const_iterator iter;
    int nCount = 0;
    for (iter = s.begin(); iter != s.end(); iter++)
    {
        if (s[nCount] == 32)
        {
            blank_vec.push_back(iter);
        }
        nCount++;
    }
    for (int i = 0; i < blank_vec.size(); i++)
    {
        s.erase(blank_vec[i] - i);
    }
}

bool ConfigUtil::OpenConfig(std::string strPath)
{
    if (strPath.empty())
    {
        ERROR("config file path is empty!");
        return false;
    }
    m_str_file_path = strPath;
    std::ifstream m_file;
    m_file.open(strPath.c_str(), std::ios::in);
    if (!m_file.is_open())
    {
        ERROR("can't open %s file!", strPath.c_str());
        return false;
    }
    std::string str_buffer;
    std::string last_section;
    last_section.clear();
    std::map<std::string, std::string> value_map;
    while (getline(m_file, str_buffer))
    {

        trim(str_buffer);
        if (str_buffer[0] == '#')
        {
            continue;
        }
        if (str_buffer[0] == '[' && str_buffer[str_buffer.size() - 1] == ']')
        {
            str_buffer.erase(str_buffer.begin());
            str_buffer.erase(str_buffer.end() - 1);
            if (last_section != str_buffer)
            {
                if (!last_section.empty())
                {
                    m_section_map[last_section] = value_map;
                    value_map.clear();
                }
                last_section = str_buffer;
            }
        }
        else if (str_buffer.find("=") != -1)
        {
            if (str_buffer.find("#") != -1)
            {
                int nPos = str_buffer.find("#");
                str_buffer = str_buffer.substr(0, nPos);
            }
            int nPos = str_buffer.find("=");
            std::string str_key = str_buffer.substr(0, nPos);
            std::string str_value = str_buffer.substr(nPos + 1, str_buffer.size());
            value_map[str_key] = str_value;
        }
    }
    m_section_map[last_section] = value_map;
    m_file.close();
    return true;
}

bool ConfigUtil::Move2Section(std::string strSection)
{
    if (m_section_map.find(strSection) != m_section_map.end())
    {
        m_value_map = m_section_map[strSection];
        m_cur_section = strSection;
        return true;
    }
    return false;
}

std::string ConfigUtil::GetValueByKey(std::string strKey, std::string strDefault)
{
    if (m_value_map.find(strKey) != m_value_map.end())
    {
        return m_value_map[strKey];
    }
    return strDefault;
}

void ConfigUtil::WriteKey(std::string strKey, std::string strValue)
{
    if (m_section_map[m_cur_section].find(strKey) != m_section_map[m_cur_section].end())
    {
        m_section_map[m_cur_section][strKey] = strValue;
    }
}

void ConfigUtil::WriteKey(std::string str_key, int value)
{
    m_section_map[m_cur_section][str_key] = std::to_string(value);
}

void ConfigUtil::Save2File()
{
    std::ofstream file(m_str_file_path, std::ios::out);
    if (file.is_open())
    {
        file.clear();
        for (auto section : m_section_map)
        {
            std::string str_section;
            str_section = "[" + section.first + "]";
            file << str_section << std::endl;
            for (auto data : section.second)
            {
                std::string strline;
                strline = data.first + " = " + data.second;
                file << strline << std::endl;
            }
        }
        file.close();
    }
}
