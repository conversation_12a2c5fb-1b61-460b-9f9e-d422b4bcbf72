#ifndef STRING_UTILS_H
#define STRING_UTILS_H

#include <vector>
#include <string>

namespace utils
{
    class StringUtils
    {
    public:
        StringUtils(){};
        ~StringUtils(){};

        /// @brief 根据正则表达式分割字符串strIn
        /// @param strIn 要分割的字符串
        /// @param delim 分割符的正则表达式
        /// @return 分割后的字符串数组
        static std::vector<std::string> split(const std::string &strIn, const std::string &delim);
        static std::vector<std::string> split(const char *strIn, const char *delim);

        /// @brief string vector加入分割符转换为字符串
        /// @param delim 分割字符串
        /// @param vecStrings 需要转换的字符串vec
        /// @return 加入分割符的字符串
        static std::string join(const std::string &delim, std::vector<std::string>& vecStrings);
    };
}

#endif