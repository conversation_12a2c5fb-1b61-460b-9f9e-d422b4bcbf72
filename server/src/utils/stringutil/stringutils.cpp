#include "stringutils.h"

#include <iterator>
#include <regex>

using namespace utils;

std::vector<std::string> StringUtils::split(const std::string &strIn, const std::string &delim)
{
    std::regex re{delim};
    return std::vector<std::string> {
        std::sregex_token_iterator(strIn.begin(), strIn.end(), re, -1),
        std::sregex_token_iterator()
    };
}

std::vector<std::string> StringUtils::split(const char *strIn, const char *delim)
{
    std::regex re{delim};
    return std::vector<std::string> {
        std::cregex_token_iterator(strIn, strIn + strlen(strIn),re, -1),
        std::cregex_token_iterator()
    };
}

std::string StringUtils::join(const std::string &delim, std::vector<std::string>& vecStrings)
{
    if (0 == vecStrings.size())
    {
        return NULL;
    }

    auto it = vecStrings.begin();
    std::string res = *it;

    for (it++; it != vecStrings.end(); it++)
    {
        res += delim;
        res += *it;
    }

    return res;
}