#include "communication.h"
#include "data_capture_manager.h"
#include "database_manager.h"
#include "fileManager.h"
#include "interaction.h"
#include "logger.hpp"
#include "systemStatus.h"
#include "utils/inimanager/config_util.h"
// #include "utils/stringutil/stringutils.h"
#include "commonFunction.h"
#include "common_define.h"


#include <iostream>
#include <unistd.h>
#include <stdlib.h>
#include <signal.h>

fileutil::ConfigUtil config;
fileutil::ConfigUtil commongConfig;
volatile bool gIsExit = false;

volatile int gFactoryType = FACTORY_NONE;
volatile int gConnectType = CAPTURE_GUI;
volatile int gPort = 9999;
volatile int gIsStoreRaw = 0;
std::string gStrStorePath = "./";

void handle_signal(int sig)
{
    printf("recv sigterm!\n");
    gIsExit = true;
}


int main(int argc,char *argv[])
{
   signal(SIGINT, handle_signal);
   signal(SIGTERM, handle_signal);

   // 加载配置
   int dwLogFileMaxSize = 0,dwLogMaxCnt = 0;
   std::string strLogName,strLogFileName;
   if(config.OpenConfig("../conf/log_config.ini"))
   {
      if (config.Move2Section("log"))
      {
         strLogName = config.GetValueByKey("loggername", "baseapp");
         strLogFileName = config.GetValueByKey("filename", "baseapp");
         dwLogFileMaxSize = atoi(config.GetValueByKey("filemaxsize", "0").c_str());
         dwLogMaxCnt = atoi(config.GetValueByKey("filemaxcnt", "0").c_str());
      }
   }

   if(commongConfig.OpenConfig("../conf/config.ini"))
   {
      if (commongConfig.Move2Section("data_factory"))
      {
         gFactoryType = atoi(commongConfig.GetValueByKey("factory","0").c_str());
      }

      if (commongConfig.Move2Section("client_connect"))
      {
         gConnectType = atoi(commongConfig.GetValueByKey("type","0").c_str());
         gPort = atoi(commongConfig.GetValueByKey("port","9999").c_str());
      }
      if (commongConfig.Move2Section("data_store"))
      {
         gStrStorePath = commongConfig.GetValueByKey("path", "./");
         if(gStrStorePath.back() != '/')
         {
            gStrStorePath += "/";
         }
         gIsStoreRaw = atoi(commongConfig.GetValueByKey("isStoreRaw","0").c_str());
      }
   }

   // 初始化日志
   Logger::SetLogLevel(argc, argv);
   Logger::Instance().InitLogger(strLogName, strLogFileName, dwLogFileMaxSize, dwLogMaxCnt);

   INFO("____________________SERVER START____________________");


   //初始化中介者
   CInteraction *pInteraction = new CInteraction();

   //实例化各模块
   CFileManager *pFileManager = new CFileManager(pInteraction);
   CCommunication *pCommunication = new CCommunication(pInteraction);
   CDataCaptureManager *pDataCaptureManager = new CDataCaptureManager(pInteraction);
   CDatabaseManager *pDatabaseManager = new CDatabaseManager(pInteraction);
   CSystemStatus *pSystemStatus = new CSystemStatus(pInteraction);

   //注册各模块
   pInteraction->AddModule(pFileManager);
   pInteraction->AddModule(pCommunication);
   pInteraction->AddModule(pDataCaptureManager);
   pInteraction->AddModule(pDatabaseManager);
   pInteraction->AddModule(pSystemStatus);

   //初始化
   pInteraction->Notify(NULL,MSG_LEVEL_ALL_DEVICE,NO_DEVICE,SYSTEM_INIT, nullptr);

   sleep(1);

   pCommunication->Start();

   while(!gIsExit)
   {
      pInteraction->Notify(NULL,MSG_LEVEL_ALL_DEVICE,NO_DEVICE,SYSTEM_INFO_REQ,nullptr);
      std::this_thread::sleep_for(std::chrono::seconds(1)); // 使用现代的休眠函数
   }

   sleep(1);
   pInteraction->Notify(NULL, MSG_LEVEL_ALL_DEVICE,NO_DEVICE,SYSTEM_STOP, nullptr);

   pCommunication->Stop();
   sleep(1);

   if(commongConfig.OpenConfig("../conf/config.ini"))
   {
      if (commongConfig.Move2Section("data_store"))
      {
         commongConfig.WriteKey("path",gStrStorePath);
      }
   }
   commongConfig.Save2File();

   Logger::Instance().CleanLogger();

   return 0;
}