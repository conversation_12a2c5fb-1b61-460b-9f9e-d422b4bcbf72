#include "moduleBase.h"
#include <thread>
#include <iostream>
#include <unistd.h>


CModuleBase::CModuleBase(CMediator *pMediator):
    m_pMediator(pMediator)
{   
    m_isExit = false;
    m_bIsRunning = false;
    sem_init(&m_semMsg,0,0);
    m_handleThread = std::thread(&CModuleBase::HandleMsgRoutine,this);
}

void CModuleBase::Init()
{   
    
}

void CModuleBase::Start()
{
    m_bIsRunning = true;
}
void CModuleBase::Stop()
{
    m_bIsRunning = false;
    if(m_handleThread.joinable())
    {
        m_handleThread.join();  
    }
    
}
void CModuleBase::Pause(){}

void CModuleBase::Notify(u32 msgLevel,u32 deviceId,u32 msgType,std::shared_ptr<void> spData)
{
    if(m_pMediator)
    {
        m_pMediator->Notify(this,msgLevel,deviceId, msgType, spData);
    }
}


bool CModuleBase::MsgFilter(u32 msgType)
{
    bool ret = true;
    if (msgType == SYSTEM_INIT ||
        msgType == SYSTEM_START ||
        msgType == SYSTEM_STOP ||
        msgType == SYSTEM_PAUSE)
    {
        ret = false;
    }
    return ret;
}

// void CModuleBase::RecvMsg(u32 msgLevel,u32 deviceId,u32 msgType,std::shared_ptr<void> spData)
// {
//     if(MsgFilter(msgType))
//     {
//         return;
//     }
//     m_queueMsg.push(msgType);
//     m_queueData.push(spData);
//     std::pair<u32,u32> tmpInfo;
//     tmpInfo.first = msgLevel;
//     tmpInfo.second = deviceId;
//     m_queueMsgInfo.push(tmpInfo);
//     sem_post(&m_semMsg);
// }

// void CModuleBase::HandleMsgRoutine()
// {
//     while(1)
//     {
//         sem_wait(&m_semMsg);
//         u32 dwMsgType = m_queueMsg.front();
//         std::shared_ptr<void> pData = m_queueData.front();
//         std::pair<u32,u32>msgInfo = m_queueMsgInfo.front();
//         HandleMsg(msgInfo.first,msgInfo.second,dwMsgType,pData);
//         m_queueMsg.pop();
//         m_queueData.pop();
//         m_queueMsgInfo.pop();
//     }
// }

void CModuleBase::RecvMsg(u32 msgLevel, u32 deviceId, u32 msgType, std::shared_ptr<void> spData)
{
    if (MsgFilter(msgType))
    {
        return;
    }

    // 使用互斥锁保护队列操作
    std::lock_guard<std::mutex> lock(m_mutex);

    m_queueMsg.push(msgType);
    m_queueData.push(spData);
    std::pair<u32, u32> tmpInfo;
    tmpInfo.first = msgLevel;
    tmpInfo.second = deviceId;
    m_queueMsgInfo.push(tmpInfo);

    // 发送通知
    m_cvMsg.notify_one();
}

void CModuleBase::HandleMsgRoutine()
{
    while (1)
    {
        std::unique_lock<std::mutex> lock(m_mutex);

        // 等待消息可处理
        m_cvMsg.wait(lock, [&] {
            return !m_queueMsg.empty() && !m_queueData.empty() && !m_queueMsgInfo.empty();
        });

        u32 dwMsgType = m_queueMsg.front();
        std::shared_ptr<void> pData = std::move(m_queueData.front());
        std::pair<u32, u32> msgInfo = m_queueMsgInfo.front();

        // 释放互斥锁
        lock.unlock();

        HandleMsg(msgInfo.first, msgInfo.second, dwMsgType, pData);

        // 重新获取互斥锁
        lock.lock();

        m_queueMsg.pop();
        m_queueData.pop();
        m_queueMsgInfo.pop();
    }
}

void CModuleBase::HandleMsg(u32 msgLevel,u32 deviceId,u32 msgType,std::shared_ptr<void> spData)
{
    switch (msgType)
    {
    case SYSTEM_INIT:
        Init();
        break;
    case SYSTEM_START:
        Start();
        break;
    case SYSTEM_STOP:
        Stop();
        break;
    case SYSTEM_PAUSE:
        Pause();
        break;
    default:
        break;
    }
}
