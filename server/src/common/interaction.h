#ifndef _INTERACTION_H_
#define _INTERACTION_H_
#include <vector>

#include "mediator.h"
#include <mutex>
class CInteraction : public CMediator
{
public:
    CInteraction();
    ~CInteraction();

    void AddModule(CModuleBase *pModule);
    void Notify(CModuleBase *pModule,u32 msgLevel,u32 deviceId,u32 msgType,std::shared_ptr<void> spData);

private:
    std::vector<CModuleBase*> m_vecModule;
    std::mutex m_mutex; // 互斥锁用于保护队列操作
};

#endif