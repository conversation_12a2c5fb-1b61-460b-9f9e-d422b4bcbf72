#ifndef _COMMON_FUNCTIONG_H_
#define _COMMON_FUNCTIONG_H_
#include <string>
#include <unordered_set>
#include "struct.h"
#include "common_define.h"
#include <fstream>
#include <iostream>
#include <sys/statvfs.h>
#include <sys/sysinfo.h>
#include <unistd.h>

namespace commonFunc
{
    u64 GetUsTime();

    u64 GetMsTime();

    u32 GetBJTimeHour(u64 timestamp);

    //查找str结尾是否为suffix true:是 false:否
    bool endsWith(const char *str,const char *suffix);

    //获取路口id 格式要求 ID_XXXX.db
    std::string extractRoadId(const std::string &input);
    //获取事件戳 
    u64 extractTimestamp(const std::string &input);

    // 获取随机字符串
    std::string generateUniqueRandomString(int length);
    
    //创建文件夹
    bool createFile(const std::string& directory, const std::string& filename);

    //将13位UTC时间戳转换为 年-月-日_时:分:秒
    std::string convertUtcTimestampToDateTimeString(u64 timestamp);

    //分割字符串
    std::vector<std::string> splitString(const std::string& s, char delim);

    // 获取 CPU 核心数
    int getCPUCores();

    // 获取 CPU 使用率
    float getCPUUsage();

    // 获取 CPU 温度（需要根据实际情况修改）
    float getTemperature();

    // 获取内存总容量
    long getMemoryTotal();

    // 获取内存使用率
    float getMemoryUsage();

    // 获取磁盘总容量
    long getDiskTotal(const char *path);

    // 获取磁盘使用率
    float getDiskUsage(const char *path);

} // namespace commonFunc

#endif