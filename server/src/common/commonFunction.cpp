#include "commonFunction.h"
#include "struct.h"
#include <string.h>
#include <ctime>
#include <chrono>
#include <random>
#include <fstream>
#include <sys/stat.h>
#include <iostream>
#include "logger.hpp"
#include <sstream>
#include <vector>

namespace commonFunc
{
    u64 GetUsTime()
    {
        auto duration_since_epoch = std::chrono::system_clock::now().time_since_epoch();                                     // 从1970-01-01 00:00:00到当前时间点的时长
        auto microseconds_since_epoch = std::chrono::duration_cast<std::chrono::microseconds>(duration_since_epoch).count(); // 将时长转换为微秒数
        time_t seconds_since_epoch = static_cast<time_t>(microseconds_since_epoch / 1000000);                                // 将时长转换为秒数
        std::tm current_time;
        localtime_r(&seconds_since_epoch, &current_time);          // 获取当前时间（精确到秒）
        auto tm_microsec = microseconds_since_epoch % 1000;        // 当前时间的微妙数
        auto tm_millisec = microseconds_since_epoch / 1000 % 1000; // 当前时间的毫秒数
        unsigned long long lltime = (unsigned long long)seconds_since_epoch * 1000 * 1000 + tm_millisec * 1000 + tm_microsec;
        return lltime;
    }

    u64 GetMsTime()
    {
        auto now = std::chrono::system_clock::now();
        auto duration = now.time_since_epoch();

        // 获取时间戳，精确到毫秒
        long long timestamp_ms = std::chrono::duration_cast<std::chrono::milliseconds>(duration).count();
        // return timestamp_ms + 28800 * 1000;
        return timestamp_ms;
    }

    u32 GetBJTimeHour(u64 llMsTimestamp)
    {

        // return  ((llMsTimestamp/(1000*60*60)) %24)+8;
        return ((llMsTimestamp / (1000 * 60 * 60)));
    }

    // 查找str结尾是否为suffix true:是 false:否
    bool endsWith(const char *str, const char *suffix)
    {
        size_t str_len = strlen(str);
        size_t suffix_len = strlen(suffix);
        return (str_len >= suffix_len) && (strcmp(str + str_len - suffix_len, suffix) == 0);
    }

    // 获取路口id 格式要求 ID_XXXX
    std::string extractRoadId(const std::string &input)
    {
        size_t lastUnderscorePos = input.find_last_of('_');
        if (lastUnderscorePos != std::string::npos)
        {
            // 提取路口id
            std::string roadId = input.substr(0, lastUnderscorePos);
            return roadId;
        }
        else
        {
            ERROR("extractRoadId error");
        }
        return "";
    }

    u64 extractTimestamp(const std::string &input)
    {
        do
        {
            size_t lastUnderscorePos = input.find_last_of('_');
            if (lastUnderscorePos == std::string::npos)
            {
                break;
            }
            size_t dotPos = input.find_last_of('.');
            if (dotPos == std::string::npos)
            {
                break;
            }
            std::string timestamp = input.substr(lastUnderscorePos + 1, dotPos - lastUnderscorePos - 1); // 提取从最后一个下划线后面到倒数第二个点的部分
            return std::stoull(timestamp);

        } while (0);
        ERROR("extractTimestamp error");
        return 0;
    }

    static std::unordered_set<std::string> generatedStrings; // 存储已经生成过的字符串
    std::string generateUniqueRandomString(int length)
    {
        std::string characters = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
        std::string randomString;

        std::random_device rd;
        std::mt19937 generator(rd());
        std::uniform_int_distribution<int> distribution(0, characters.length() - 1);

        do
        {
            randomString.clear(); // 清空之前的字符串
            for (int i = 0; i < length; ++i)
            {
                randomString += characters[distribution(generator)];
            }
        } while (generatedStrings.find(randomString) != generatedStrings.end());

        // generatedStrings.insert(randomString); // 将新生成的字符串加入集合

        return randomString;
    }

    bool createFile(const std::string &directory, const std::string &filename)
    {
        struct stat info;
        if (stat(directory.c_str(), &info) == 0)
        {
            // 文件存在
            return false;
        }

        int status = mkdir(directory.c_str(), S_IRWXU | S_IRWXG | S_IROTH | S_IXOTH);
        if (status == 0)
        {
            return true;
        }
        else
        {
            perror("Failed to create directory");
            return false;
        }
        // std::ofstream ofs(directory + "/" + filename);
        // if (!ofs)
        // {
        //     return false;
        // }
        // ofs.close();
        // return true;
    };

    std::string convertUtcTimestampToDateTimeString(u64 timestamp)
    {
        timestamp += 60 * 60 * 8 * 1000;                   // 调整时差
        time_t ts = static_cast<time_t>(timestamp / 1000); // 将毫秒级的时间戳转换为秒级
        struct tm *timeinfo;
        char buffer[80];

        timeinfo = gmtime(&ts);                              // 使用gmtime将时间戳转换为UTC时间
        strftime(buffer, 80, "%Y-%m-%d_%H:%M:%S", timeinfo); // 格式化时间字符串
        return buffer;
    }

    std::vector<std::string> splitString(const std::string& s, char delim) 
    {
    std::vector<std::string> tokens;
    std::string token;
    std::istringstream tokenStream(s);
    while (std::getline(tokenStream, token, delim)) {
        tokens.push_back(token);
    }
    return tokens;
}

// 获取 CPU 核心数
int getCPUCores() { return sysconf(_SC_NPROCESSORS_ONLN); }

// 获取 CPU 使用率
float getCPUUsage() {
  std::ifstream file("/proc/stat");
  std::string line;
  std::getline(file, line); // 读取第一行
  // 提取每个核心的使用时间
  long user, nice, system, idle, iowait, irq, softirq;
  sscanf(line.c_str(), "cpu %ld %ld %ld %ld %ld %ld %ld", &user, &nice, &system,
         &idle, &iowait, &irq, &softirq);
  long totalIdle = idle + iowait;
  long totalNonIdle = user + nice + system + irq + softirq;
  long total = totalIdle + totalNonIdle;
  return 100.0 * totalNonIdle / total;
}

// 获取 CPU 温度（需要根据实际情况修改）
float getTemperature() {
  // 这里假设你有一个可以读取 CPU 温度的函数或文件
  // 你需要根据你的系统和 CPU 来确定如何获取温度
  // 这里只是一个示例，实际情况可能会有所不同
  return 0.0; // 假设温度为 0
}

// 获取内存总容量
long getMemoryTotal() {
  struct sysinfo memInfo;
  sysinfo(&memInfo);
  return memInfo.totalram / (1024 * 1024); // 转换为 MB
}

// 获取内存使用率
float getMemoryUsage() {
  struct sysinfo memInfo;
  sysinfo(&memInfo);
  long totalMemory = memInfo.totalram;
  long freeMemory = memInfo.freeram;
  long usedMemory = totalMemory - freeMemory;
  return (float)usedMemory / totalMemory * 100.0;
}

// 获取磁盘总容量
long getDiskTotal(const char *path) {
  struct statvfs stat;
  if (statvfs(path, &stat) != 0) {
    return -1; // 获取失败
  }
  return (stat.f_frsize * stat.f_blocks) / (1024 * 1024); // 转换为 MB
}

// 获取磁盘使用率
float getDiskUsage(const char *path) {
  struct statvfs stat;
  if (statvfs(path, &stat) != 0) {
    return -1.0; // 获取失败
  }
  long totalSpace = stat.f_frsize * stat.f_blocks;
  long freeSpace = stat.f_frsize * stat.f_bfree;
  long usedSpace = totalSpace - freeSpace;
  return (float)usedSpace / totalSpace * 100.0;
}
}
