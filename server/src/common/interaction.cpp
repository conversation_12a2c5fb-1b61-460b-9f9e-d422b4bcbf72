#include "interaction.h"

CInteraction::CInteraction()
{

}

CInteraction::~CInteraction()
{

}

void CInteraction::Notify(CModuleBase *pModule,u32 msgLevel,u32 deviceId,u32 msgType,std::shared_ptr<void> spData)
{
    std::lock_guard<std::mutex> lock(m_mutex);
    for(auto iter:m_vecModule)
    {
        if(iter != pModule)
        {
            iter->RecvMsg(msgLevel,deviceId,msgType,spData);
        }
    }
}



void CInteraction::AddModule(CModuleBase *pModule)
{
    if(pModule)
    {
        m_vecModule.push_back(pModule);
    }
}
