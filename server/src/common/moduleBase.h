#ifndef _MODULE_BASE_H_
#define _MODULE_BASE_H_
#include <string>

#include "mediator.h"
#include "struct.h"
#include <queue>
#include <pthread.h>
#include <semaphore.h>
#include "logger.hpp"
#include "message.h"
#include <condition_variable>
#include <mutex>

class CMediator;

class CBase
{
    virtual void Init() = 0;
    virtual void Start() = 0;
    virtual void Stop() = 0;
    virtual void Pause() = 0;

    virtual void Notify(u32 msgLevel,u32 deviceId,u32 msgType,std::shared_ptr<void> spData) = 0;
    virtual void RecvMsg(u32 msgLevel,u32 deviceId,u32 msgType,std::shared_ptr<void> spData) = 0;

};

class CModuleBase :public CBase
{
public:
    CModuleBase(CMediator *pMediator);
    ~CModuleBase() {}

    virtual void Init();
    virtual void Start();
    virtual void Stop();
    virtual void Pause();

    virtual void Notify(u32 msgLevel,u32 deviceId,u32 msgType,std::shared_ptr<void> spData);
    virtual bool MsgFilter(u32 msgType);
    virtual void RecvMsg(u32 msgLevel,u32 deviceId,u32 msgType,std::shared_ptr<void> spData);
    virtual void HandleMsgRoutine();
    virtual void HandleMsg(u32 msgLevel,u32 deviceId,u32 msgType,std::shared_ptr<void> spData);


protected:
    CMediator *m_pMediator;
    bool m_bIsRunning;
    std::queue<std::shared_ptr<void>> m_queueData;
    std::queue<u32> m_queueMsg;
    std::queue<std::pair<u32,u32>> m_queueMsgInfo;
    sem_t m_semMsg;
    bool m_isExit;
    std::mutex m_mutex; // 互斥锁用于保护队列操作
    std::condition_variable m_cvMsg; // 条件变量用于通知消息可处理
    std::thread m_handleThread;
};

#endif