// automatically generated by the <PERSON><PERSON>uffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_MONTH_MECDATA_H_
#define FLATBUFFERS_GENERATED_MONTH_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

namespace MECData {

enum DE_Month : uint8_t {
  DE_Month_UNKNOWN = 0,
  DE_Month_JAN = 1,
  DE_Month_FEB = 2,
  DE_Month_MAR = 3,
  DE_Month_APR = 4,
  DE_Month_MAY = 5,
  DE_Month_JUN = 6,
  DE_Month_JUL = 7,
  DE_Month_AUG = 8,
  DE_Month_SEP = 9,
  DE_Month_OCT = 10,
  DE_Month_NOV = 11,
  DE_Month_DEC = 12,
  DE_Month_MIN = DE_Month_UNKNOWN,
  DE_Month_MAX = DE_Month_DEC
};

inline const DE_Month (&EnumValuesDE_Month())[13] {
  static const DE_Month values[] = {
    DE_Month_UNKNOWN,
    DE_Month_JAN,
    DE_Month_FEB,
    DE_Month_MAR,
    DE_Month_APR,
    DE_Month_MAY,
    DE_Month_JUN,
    DE_Month_JUL,
    DE_Month_AUG,
    DE_Month_SEP,
    DE_Month_OCT,
    DE_Month_NOV,
    DE_Month_DEC
  };
  return values;
}

inline const char * const *EnumNamesDE_Month() {
  static const char * const names[14] = {
    "UNKNOWN",
    "JAN",
    "FEB",
    "MAR",
    "APR",
    "MAY",
    "JUN",
    "JUL",
    "AUG",
    "SEP",
    "OCT",
    "NOV",
    "DEC",
    nullptr
  };
  return names;
}

inline const char *EnumNameDE_Month(DE_Month e) {
  if (::flatbuffers::IsOutRange(e, DE_Month_UNKNOWN, DE_Month_DEC)) return "";
  const size_t index = static_cast<size_t>(e);
  return EnumNamesDE_Month()[index];
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_MONTH_MECDATA_H_
