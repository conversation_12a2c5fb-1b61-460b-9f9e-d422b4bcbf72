// automatically generated by the <PERSON>Buffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_ACCELERATIONSET4WAY_MECDATA_H_
#define FLATBUFFERS_GENERATED_ACCELERATIONSET4WAY_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

namespace MECData {

struct DF_AccelerationSet4Way;
struct DF_AccelerationSet4WayBuilder;

struct DF_AccelerationSet4Way FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_AccelerationSet4WayBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_LON = 4,
    VT_LAT = 6,
    VT_VERT = 8,
    VT_YAW = 10
  };
  int16_t lon() const {
    return GetField<int16_t>(VT_LON, 0);
  }
  int16_t lat() const {
    return GetField<int16_t>(VT_LAT, 0);
  }
  int8_t vert() const {
    return GetField<int8_t>(VT_VERT, 0);
  }
  int16_t yaw() const {
    return GetField<int16_t>(VT_YAW, 0);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<int16_t>(verifier, VT_LON, 2) &&
           VerifyField<int16_t>(verifier, VT_LAT, 2) &&
           VerifyField<int8_t>(verifier, VT_VERT, 1) &&
           VerifyField<int16_t>(verifier, VT_YAW, 2) &&
           verifier.EndTable();
  }
};

struct DF_AccelerationSet4WayBuilder {
  typedef DF_AccelerationSet4Way Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_lon(int16_t lon) {
    fbb_.AddElement<int16_t>(DF_AccelerationSet4Way::VT_LON, lon, 0);
  }
  void add_lat(int16_t lat) {
    fbb_.AddElement<int16_t>(DF_AccelerationSet4Way::VT_LAT, lat, 0);
  }
  void add_vert(int8_t vert) {
    fbb_.AddElement<int8_t>(DF_AccelerationSet4Way::VT_VERT, vert, 0);
  }
  void add_yaw(int16_t yaw) {
    fbb_.AddElement<int16_t>(DF_AccelerationSet4Way::VT_YAW, yaw, 0);
  }
  explicit DF_AccelerationSet4WayBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_AccelerationSet4Way> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_AccelerationSet4Way>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_AccelerationSet4Way> CreateDF_AccelerationSet4Way(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    int16_t lon = 0,
    int16_t lat = 0,
    int8_t vert = 0,
    int16_t yaw = 0) {
  DF_AccelerationSet4WayBuilder builder_(_fbb);
  builder_.add_yaw(yaw);
  builder_.add_lat(lat);
  builder_.add_lon(lon);
  builder_.add_vert(vert);
  return builder_.Finish();
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_ACCELERATIONSET4WAY_MECDATA_H_
