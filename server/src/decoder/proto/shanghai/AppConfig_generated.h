// automatically generated by the FlatBuffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_APPCONFIG_MECDATA_H_
#define FLATBUFFERS_GENERATED_APPCONFIG_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

#include "DeploySpecificInfo_generated.h"
#include "DeviceInfo_generated.h"
#include "LogConfig_generated.h"
#include "ModuleMiscellaneous_generated.h"
#include "OperationTags_generated.h"
#include "ZStub_generated.h"

namespace MECData {

struct DF_AppInstanceConfig;
struct DF_AppInstanceConfigBuilder;

struct DF_AppPackageConfig;
struct DF_AppPackageConfigBuilder;

struct DF_AppInstanceConfig FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_AppInstanceConfigBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_ID = 4,
    VT_NAME = 6,
    VT_DESCRIPTION = 8,
    VT_VERSION = 10,
    VT_IO_REQUIREMENTS = 12,
    VT_DEPLOY_SPECIFIC_TYPE = 14,
    VT_DEPLOY_SPECIFIC = 16,
    VT_ZSTUB = 18,
    VT_CRASH = 20,
    VT_LOG = 22,
    VT_IO_RECORD = 24,
    VT_UI = 26,
    VT_SSL_NAMES = 28,
    VT_FREE_CONFIG = 30,
    VT_TAGS = 32,
    VT_DEVICE_INFO = 34
  };
  uint16_t id() const {
    return GetField<uint16_t>(VT_ID, 0);
  }
  const ::flatbuffers::String *name() const {
    return GetPointer<const ::flatbuffers::String *>(VT_NAME);
  }
  const ::flatbuffers::String *description() const {
    return GetPointer<const ::flatbuffers::String *>(VT_DESCRIPTION);
  }
  const ::flatbuffers::String *version() const {
    return GetPointer<const ::flatbuffers::String *>(VT_VERSION);
  }
  const MECData::DF_ModuleIOConfig *io_requirements() const {
    return GetPointer<const MECData::DF_ModuleIOConfig *>(VT_IO_REQUIREMENTS);
  }
  MECData::DF_DeploySpecificInfo deploy_specific_type() const {
    return static_cast<MECData::DF_DeploySpecificInfo>(GetField<uint8_t>(VT_DEPLOY_SPECIFIC_TYPE, 0));
  }
  const void *deploy_specific() const {
    return GetPointer<const void *>(VT_DEPLOY_SPECIFIC);
  }
  template<typename T> const T *deploy_specific_as() const;
  const MECData::DF_NativeClassModuleInfo *deploy_specific_as_DF_NativeClassModuleInfo() const {
    return deploy_specific_type() == MECData::DF_DeploySpecificInfo_DF_NativeClassModuleInfo ? static_cast<const MECData::DF_NativeClassModuleInfo *>(deploy_specific()) : nullptr;
  }
  const MECData::DF_SharedObjectModuleInfo *deploy_specific_as_DF_SharedObjectModuleInfo() const {
    return deploy_specific_type() == MECData::DF_DeploySpecificInfo_DF_SharedObjectModuleInfo ? static_cast<const MECData::DF_SharedObjectModuleInfo *>(deploy_specific()) : nullptr;
  }
  const MECData::DF_DockerModuleInfo *deploy_specific_as_DF_DockerModuleInfo() const {
    return deploy_specific_type() == MECData::DF_DeploySpecificInfo_DF_DockerModuleInfo ? static_cast<const MECData::DF_DockerModuleInfo *>(deploy_specific()) : nullptr;
  }
  const MECData::DF_StandaloneProgramModuleInfo *deploy_specific_as_DF_StandaloneProgramModuleInfo() const {
    return deploy_specific_type() == MECData::DF_DeploySpecificInfo_DF_StandaloneProgramModuleInfo ? static_cast<const MECData::DF_StandaloneProgramModuleInfo *>(deploy_specific()) : nullptr;
  }
  const MECData::DF_ZStubConfig *zstub() const {
    return GetPointer<const MECData::DF_ZStubConfig *>(VT_ZSTUB);
  }
  const MECData::DF_CrashRestartConfig *crash() const {
    return GetPointer<const MECData::DF_CrashRestartConfig *>(VT_CRASH);
  }
  const MECData::DF_LogConfig *log() const {
    return GetPointer<const MECData::DF_LogConfig *>(VT_LOG);
  }
  const MECData::DF_IORecorder *io_record() const {
    return GetPointer<const MECData::DF_IORecorder *>(VT_IO_RECORD);
  }
  const MECData::DF_ModuleWebUI *ui() const {
    return GetPointer<const MECData::DF_ModuleWebUI *>(VT_UI);
  }
  const ::flatbuffers::Vector<::flatbuffers::Offset<::flatbuffers::String>> *ssl_names() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<::flatbuffers::String>> *>(VT_SSL_NAMES);
  }
  const ::flatbuffers::Vector<uint8_t> *free_config() const {
    return GetPointer<const ::flatbuffers::Vector<uint8_t> *>(VT_FREE_CONFIG);
  }
  const MECData::DE_OperationTags *tags() const {
    return GetPointer<const MECData::DE_OperationTags *>(VT_TAGS);
  }
  const MECData::DF_DeviceInfo *device_info() const {
    return GetPointer<const MECData::DF_DeviceInfo *>(VT_DEVICE_INFO);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<uint16_t>(verifier, VT_ID, 2) &&
           VerifyOffset(verifier, VT_NAME) &&
           verifier.VerifyString(name()) &&
           VerifyOffset(verifier, VT_DESCRIPTION) &&
           verifier.VerifyString(description()) &&
           VerifyOffset(verifier, VT_VERSION) &&
           verifier.VerifyString(version()) &&
           VerifyOffset(verifier, VT_IO_REQUIREMENTS) &&
           verifier.VerifyTable(io_requirements()) &&
           VerifyField<uint8_t>(verifier, VT_DEPLOY_SPECIFIC_TYPE, 1) &&
           VerifyOffset(verifier, VT_DEPLOY_SPECIFIC) &&
           VerifyDF_DeploySpecificInfo(verifier, deploy_specific(), deploy_specific_type()) &&
           VerifyOffset(verifier, VT_ZSTUB) &&
           verifier.VerifyTable(zstub()) &&
           VerifyOffset(verifier, VT_CRASH) &&
           verifier.VerifyTable(crash()) &&
           VerifyOffset(verifier, VT_LOG) &&
           verifier.VerifyTable(log()) &&
           VerifyOffset(verifier, VT_IO_RECORD) &&
           verifier.VerifyTable(io_record()) &&
           VerifyOffset(verifier, VT_UI) &&
           verifier.VerifyTable(ui()) &&
           VerifyOffset(verifier, VT_SSL_NAMES) &&
           verifier.VerifyVector(ssl_names()) &&
           verifier.VerifyVectorOfStrings(ssl_names()) &&
           VerifyOffset(verifier, VT_FREE_CONFIG) &&
           verifier.VerifyVector(free_config()) &&
           VerifyOffset(verifier, VT_TAGS) &&
           verifier.VerifyTable(tags()) &&
           VerifyOffset(verifier, VT_DEVICE_INFO) &&
           verifier.VerifyTable(device_info()) &&
           verifier.EndTable();
  }
};

template<> inline const MECData::DF_NativeClassModuleInfo *DF_AppInstanceConfig::deploy_specific_as<MECData::DF_NativeClassModuleInfo>() const {
  return deploy_specific_as_DF_NativeClassModuleInfo();
}

template<> inline const MECData::DF_SharedObjectModuleInfo *DF_AppInstanceConfig::deploy_specific_as<MECData::DF_SharedObjectModuleInfo>() const {
  return deploy_specific_as_DF_SharedObjectModuleInfo();
}

template<> inline const MECData::DF_DockerModuleInfo *DF_AppInstanceConfig::deploy_specific_as<MECData::DF_DockerModuleInfo>() const {
  return deploy_specific_as_DF_DockerModuleInfo();
}

template<> inline const MECData::DF_StandaloneProgramModuleInfo *DF_AppInstanceConfig::deploy_specific_as<MECData::DF_StandaloneProgramModuleInfo>() const {
  return deploy_specific_as_DF_StandaloneProgramModuleInfo();
}

struct DF_AppInstanceConfigBuilder {
  typedef DF_AppInstanceConfig Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_id(uint16_t id) {
    fbb_.AddElement<uint16_t>(DF_AppInstanceConfig::VT_ID, id, 0);
  }
  void add_name(::flatbuffers::Offset<::flatbuffers::String> name) {
    fbb_.AddOffset(DF_AppInstanceConfig::VT_NAME, name);
  }
  void add_description(::flatbuffers::Offset<::flatbuffers::String> description) {
    fbb_.AddOffset(DF_AppInstanceConfig::VT_DESCRIPTION, description);
  }
  void add_version(::flatbuffers::Offset<::flatbuffers::String> version) {
    fbb_.AddOffset(DF_AppInstanceConfig::VT_VERSION, version);
  }
  void add_io_requirements(::flatbuffers::Offset<MECData::DF_ModuleIOConfig> io_requirements) {
    fbb_.AddOffset(DF_AppInstanceConfig::VT_IO_REQUIREMENTS, io_requirements);
  }
  void add_deploy_specific_type(MECData::DF_DeploySpecificInfo deploy_specific_type) {
    fbb_.AddElement<uint8_t>(DF_AppInstanceConfig::VT_DEPLOY_SPECIFIC_TYPE, static_cast<uint8_t>(deploy_specific_type), 0);
  }
  void add_deploy_specific(::flatbuffers::Offset<void> deploy_specific) {
    fbb_.AddOffset(DF_AppInstanceConfig::VT_DEPLOY_SPECIFIC, deploy_specific);
  }
  void add_zstub(::flatbuffers::Offset<MECData::DF_ZStubConfig> zstub) {
    fbb_.AddOffset(DF_AppInstanceConfig::VT_ZSTUB, zstub);
  }
  void add_crash(::flatbuffers::Offset<MECData::DF_CrashRestartConfig> crash) {
    fbb_.AddOffset(DF_AppInstanceConfig::VT_CRASH, crash);
  }
  void add_log(::flatbuffers::Offset<MECData::DF_LogConfig> log) {
    fbb_.AddOffset(DF_AppInstanceConfig::VT_LOG, log);
  }
  void add_io_record(::flatbuffers::Offset<MECData::DF_IORecorder> io_record) {
    fbb_.AddOffset(DF_AppInstanceConfig::VT_IO_RECORD, io_record);
  }
  void add_ui(::flatbuffers::Offset<MECData::DF_ModuleWebUI> ui) {
    fbb_.AddOffset(DF_AppInstanceConfig::VT_UI, ui);
  }
  void add_ssl_names(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<::flatbuffers::String>>> ssl_names) {
    fbb_.AddOffset(DF_AppInstanceConfig::VT_SSL_NAMES, ssl_names);
  }
  void add_free_config(::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> free_config) {
    fbb_.AddOffset(DF_AppInstanceConfig::VT_FREE_CONFIG, free_config);
  }
  void add_tags(::flatbuffers::Offset<MECData::DE_OperationTags> tags) {
    fbb_.AddOffset(DF_AppInstanceConfig::VT_TAGS, tags);
  }
  void add_device_info(::flatbuffers::Offset<MECData::DF_DeviceInfo> device_info) {
    fbb_.AddOffset(DF_AppInstanceConfig::VT_DEVICE_INFO, device_info);
  }
  explicit DF_AppInstanceConfigBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_AppInstanceConfig> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_AppInstanceConfig>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_AppInstanceConfig> CreateDF_AppInstanceConfig(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    uint16_t id = 0,
    ::flatbuffers::Offset<::flatbuffers::String> name = 0,
    ::flatbuffers::Offset<::flatbuffers::String> description = 0,
    ::flatbuffers::Offset<::flatbuffers::String> version = 0,
    ::flatbuffers::Offset<MECData::DF_ModuleIOConfig> io_requirements = 0,
    MECData::DF_DeploySpecificInfo deploy_specific_type = MECData::DF_DeploySpecificInfo_NONE,
    ::flatbuffers::Offset<void> deploy_specific = 0,
    ::flatbuffers::Offset<MECData::DF_ZStubConfig> zstub = 0,
    ::flatbuffers::Offset<MECData::DF_CrashRestartConfig> crash = 0,
    ::flatbuffers::Offset<MECData::DF_LogConfig> log = 0,
    ::flatbuffers::Offset<MECData::DF_IORecorder> io_record = 0,
    ::flatbuffers::Offset<MECData::DF_ModuleWebUI> ui = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<::flatbuffers::String>>> ssl_names = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> free_config = 0,
    ::flatbuffers::Offset<MECData::DE_OperationTags> tags = 0,
    ::flatbuffers::Offset<MECData::DF_DeviceInfo> device_info = 0) {
  DF_AppInstanceConfigBuilder builder_(_fbb);
  builder_.add_device_info(device_info);
  builder_.add_tags(tags);
  builder_.add_free_config(free_config);
  builder_.add_ssl_names(ssl_names);
  builder_.add_ui(ui);
  builder_.add_io_record(io_record);
  builder_.add_log(log);
  builder_.add_crash(crash);
  builder_.add_zstub(zstub);
  builder_.add_deploy_specific(deploy_specific);
  builder_.add_io_requirements(io_requirements);
  builder_.add_version(version);
  builder_.add_description(description);
  builder_.add_name(name);
  builder_.add_id(id);
  builder_.add_deploy_specific_type(deploy_specific_type);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DF_AppInstanceConfig> CreateDF_AppInstanceConfigDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    uint16_t id = 0,
    const char *name = nullptr,
    const char *description = nullptr,
    const char *version = nullptr,
    ::flatbuffers::Offset<MECData::DF_ModuleIOConfig> io_requirements = 0,
    MECData::DF_DeploySpecificInfo deploy_specific_type = MECData::DF_DeploySpecificInfo_NONE,
    ::flatbuffers::Offset<void> deploy_specific = 0,
    ::flatbuffers::Offset<MECData::DF_ZStubConfig> zstub = 0,
    ::flatbuffers::Offset<MECData::DF_CrashRestartConfig> crash = 0,
    ::flatbuffers::Offset<MECData::DF_LogConfig> log = 0,
    ::flatbuffers::Offset<MECData::DF_IORecorder> io_record = 0,
    ::flatbuffers::Offset<MECData::DF_ModuleWebUI> ui = 0,
    const std::vector<::flatbuffers::Offset<::flatbuffers::String>> *ssl_names = nullptr,
    const std::vector<uint8_t> *free_config = nullptr,
    ::flatbuffers::Offset<MECData::DE_OperationTags> tags = 0,
    ::flatbuffers::Offset<MECData::DF_DeviceInfo> device_info = 0) {
  auto name__ = name ? _fbb.CreateString(name) : 0;
  auto description__ = description ? _fbb.CreateString(description) : 0;
  auto version__ = version ? _fbb.CreateString(version) : 0;
  auto ssl_names__ = ssl_names ? _fbb.CreateVector<::flatbuffers::Offset<::flatbuffers::String>>(*ssl_names) : 0;
  auto free_config__ = free_config ? _fbb.CreateVector<uint8_t>(*free_config) : 0;
  return MECData::CreateDF_AppInstanceConfig(
      _fbb,
      id,
      name__,
      description__,
      version__,
      io_requirements,
      deploy_specific_type,
      deploy_specific,
      zstub,
      crash,
      log,
      io_record,
      ui,
      ssl_names__,
      free_config__,
      tags,
      device_info);
}

struct DF_AppPackageConfig FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_AppPackageConfigBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_DESCRIPTION = 4,
    VT_DEPLOYMENT_TYPE = 6,
    VT_FREE_CONFIG = 8,
    VT_TAGS = 10,
    VT_DEPLOY_SPECIFIC_TYPE = 12,
    VT_DEPLOY_SPECIFIC = 14
  };
  const ::flatbuffers::String *description() const {
    return GetPointer<const ::flatbuffers::String *>(VT_DESCRIPTION);
  }
  MECData::DE_DeploymentType deployment_type() const {
    return static_cast<MECData::DE_DeploymentType>(GetField<uint8_t>(VT_DEPLOYMENT_TYPE, 0));
  }
  const ::flatbuffers::Vector<uint8_t> *free_config() const {
    return GetPointer<const ::flatbuffers::Vector<uint8_t> *>(VT_FREE_CONFIG);
  }
  const MECData::DE_OperationTags *tags() const {
    return GetPointer<const MECData::DE_OperationTags *>(VT_TAGS);
  }
  MECData::DF_DeploySpecificInfo deploy_specific_type() const {
    return static_cast<MECData::DF_DeploySpecificInfo>(GetField<uint8_t>(VT_DEPLOY_SPECIFIC_TYPE, 0));
  }
  const void *deploy_specific() const {
    return GetPointer<const void *>(VT_DEPLOY_SPECIFIC);
  }
  template<typename T> const T *deploy_specific_as() const;
  const MECData::DF_NativeClassModuleInfo *deploy_specific_as_DF_NativeClassModuleInfo() const {
    return deploy_specific_type() == MECData::DF_DeploySpecificInfo_DF_NativeClassModuleInfo ? static_cast<const MECData::DF_NativeClassModuleInfo *>(deploy_specific()) : nullptr;
  }
  const MECData::DF_SharedObjectModuleInfo *deploy_specific_as_DF_SharedObjectModuleInfo() const {
    return deploy_specific_type() == MECData::DF_DeploySpecificInfo_DF_SharedObjectModuleInfo ? static_cast<const MECData::DF_SharedObjectModuleInfo *>(deploy_specific()) : nullptr;
  }
  const MECData::DF_DockerModuleInfo *deploy_specific_as_DF_DockerModuleInfo() const {
    return deploy_specific_type() == MECData::DF_DeploySpecificInfo_DF_DockerModuleInfo ? static_cast<const MECData::DF_DockerModuleInfo *>(deploy_specific()) : nullptr;
  }
  const MECData::DF_StandaloneProgramModuleInfo *deploy_specific_as_DF_StandaloneProgramModuleInfo() const {
    return deploy_specific_type() == MECData::DF_DeploySpecificInfo_DF_StandaloneProgramModuleInfo ? static_cast<const MECData::DF_StandaloneProgramModuleInfo *>(deploy_specific()) : nullptr;
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_DESCRIPTION) &&
           verifier.VerifyString(description()) &&
           VerifyField<uint8_t>(verifier, VT_DEPLOYMENT_TYPE, 1) &&
           VerifyOffset(verifier, VT_FREE_CONFIG) &&
           verifier.VerifyVector(free_config()) &&
           VerifyOffset(verifier, VT_TAGS) &&
           verifier.VerifyTable(tags()) &&
           VerifyField<uint8_t>(verifier, VT_DEPLOY_SPECIFIC_TYPE, 1) &&
           VerifyOffset(verifier, VT_DEPLOY_SPECIFIC) &&
           VerifyDF_DeploySpecificInfo(verifier, deploy_specific(), deploy_specific_type()) &&
           verifier.EndTable();
  }
};

template<> inline const MECData::DF_NativeClassModuleInfo *DF_AppPackageConfig::deploy_specific_as<MECData::DF_NativeClassModuleInfo>() const {
  return deploy_specific_as_DF_NativeClassModuleInfo();
}

template<> inline const MECData::DF_SharedObjectModuleInfo *DF_AppPackageConfig::deploy_specific_as<MECData::DF_SharedObjectModuleInfo>() const {
  return deploy_specific_as_DF_SharedObjectModuleInfo();
}

template<> inline const MECData::DF_DockerModuleInfo *DF_AppPackageConfig::deploy_specific_as<MECData::DF_DockerModuleInfo>() const {
  return deploy_specific_as_DF_DockerModuleInfo();
}

template<> inline const MECData::DF_StandaloneProgramModuleInfo *DF_AppPackageConfig::deploy_specific_as<MECData::DF_StandaloneProgramModuleInfo>() const {
  return deploy_specific_as_DF_StandaloneProgramModuleInfo();
}

struct DF_AppPackageConfigBuilder {
  typedef DF_AppPackageConfig Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_description(::flatbuffers::Offset<::flatbuffers::String> description) {
    fbb_.AddOffset(DF_AppPackageConfig::VT_DESCRIPTION, description);
  }
  void add_deployment_type(MECData::DE_DeploymentType deployment_type) {
    fbb_.AddElement<uint8_t>(DF_AppPackageConfig::VT_DEPLOYMENT_TYPE, static_cast<uint8_t>(deployment_type), 0);
  }
  void add_free_config(::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> free_config) {
    fbb_.AddOffset(DF_AppPackageConfig::VT_FREE_CONFIG, free_config);
  }
  void add_tags(::flatbuffers::Offset<MECData::DE_OperationTags> tags) {
    fbb_.AddOffset(DF_AppPackageConfig::VT_TAGS, tags);
  }
  void add_deploy_specific_type(MECData::DF_DeploySpecificInfo deploy_specific_type) {
    fbb_.AddElement<uint8_t>(DF_AppPackageConfig::VT_DEPLOY_SPECIFIC_TYPE, static_cast<uint8_t>(deploy_specific_type), 0);
  }
  void add_deploy_specific(::flatbuffers::Offset<void> deploy_specific) {
    fbb_.AddOffset(DF_AppPackageConfig::VT_DEPLOY_SPECIFIC, deploy_specific);
  }
  explicit DF_AppPackageConfigBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_AppPackageConfig> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_AppPackageConfig>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_AppPackageConfig> CreateDF_AppPackageConfig(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::String> description = 0,
    MECData::DE_DeploymentType deployment_type = MECData::DE_DeploymentType_UNKNOWN,
    ::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> free_config = 0,
    ::flatbuffers::Offset<MECData::DE_OperationTags> tags = 0,
    MECData::DF_DeploySpecificInfo deploy_specific_type = MECData::DF_DeploySpecificInfo_NONE,
    ::flatbuffers::Offset<void> deploy_specific = 0) {
  DF_AppPackageConfigBuilder builder_(_fbb);
  builder_.add_deploy_specific(deploy_specific);
  builder_.add_tags(tags);
  builder_.add_free_config(free_config);
  builder_.add_description(description);
  builder_.add_deploy_specific_type(deploy_specific_type);
  builder_.add_deployment_type(deployment_type);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DF_AppPackageConfig> CreateDF_AppPackageConfigDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const char *description = nullptr,
    MECData::DE_DeploymentType deployment_type = MECData::DE_DeploymentType_UNKNOWN,
    const std::vector<uint8_t> *free_config = nullptr,
    ::flatbuffers::Offset<MECData::DE_OperationTags> tags = 0,
    MECData::DF_DeploySpecificInfo deploy_specific_type = MECData::DF_DeploySpecificInfo_NONE,
    ::flatbuffers::Offset<void> deploy_specific = 0) {
  auto description__ = description ? _fbb.CreateString(description) : 0;
  auto free_config__ = free_config ? _fbb.CreateVector<uint8_t>(*free_config) : 0;
  return MECData::CreateDF_AppPackageConfig(
      _fbb,
      description__,
      deployment_type,
      free_config__,
      tags,
      deploy_specific_type,
      deploy_specific);
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_APPCONFIG_MECDATA_H_
