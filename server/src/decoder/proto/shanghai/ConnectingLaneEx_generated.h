// automatically generated by the <PERSON><PERSON>uffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_CONNECTINGLANEEX_MECDATA_H_
#define FLATBUFFERS_GENERATED_CONNECTINGLANEEX_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

#include "RoadPoint_generated.h"

namespace MECData {

struct DF_ConnectingLaneEx;
struct DF_ConnectingLaneExBuilder;

struct DF_ConnectingLaneEx FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_ConnectingLaneExBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_TARGET_SECTION = 4,
    VT_TARGET_LANE = 6,
    VT_CONNECTINGLANEWIDTH = 8,
    VT_CONNECTINGLANEPOINTS = 10,
    VT_ISOLATEDCONNECTINGLANE = 12,
    VT_EXT_ID = 14,
    VT_TARGET_SECTION_EXT_ID = 16,
    VT_TARGET_LANE_EXT_ID = 18
  };
  uint8_t target_section() const {
    return GetField<uint8_t>(VT_TARGET_SECTION, 0);
  }
  int8_t target_lane() const {
    return GetField<int8_t>(VT_TARGET_LANE, 0);
  }
  uint16_t connectingLaneWidth() const {
    return GetField<uint16_t>(VT_CONNECTINGLANEWIDTH, 0);
  }
  const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_RoadPoint>> *connectingLanePoints() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_RoadPoint>> *>(VT_CONNECTINGLANEPOINTS);
  }
  bool isolatedConnectingLane() const {
    return GetField<uint8_t>(VT_ISOLATEDCONNECTINGLANE, 0) != 0;
  }
  const ::flatbuffers::String *ext_id() const {
    return GetPointer<const ::flatbuffers::String *>(VT_EXT_ID);
  }
  const ::flatbuffers::String *target_section_ext_id() const {
    return GetPointer<const ::flatbuffers::String *>(VT_TARGET_SECTION_EXT_ID);
  }
  const ::flatbuffers::String *target_lane_ext_id() const {
    return GetPointer<const ::flatbuffers::String *>(VT_TARGET_LANE_EXT_ID);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<uint8_t>(verifier, VT_TARGET_SECTION, 1) &&
           VerifyField<int8_t>(verifier, VT_TARGET_LANE, 1) &&
           VerifyField<uint16_t>(verifier, VT_CONNECTINGLANEWIDTH, 2) &&
           VerifyOffset(verifier, VT_CONNECTINGLANEPOINTS) &&
           verifier.VerifyVector(connectingLanePoints()) &&
           verifier.VerifyVectorOfTables(connectingLanePoints()) &&
           VerifyField<uint8_t>(verifier, VT_ISOLATEDCONNECTINGLANE, 1) &&
           VerifyOffset(verifier, VT_EXT_ID) &&
           verifier.VerifyString(ext_id()) &&
           VerifyOffset(verifier, VT_TARGET_SECTION_EXT_ID) &&
           verifier.VerifyString(target_section_ext_id()) &&
           VerifyOffset(verifier, VT_TARGET_LANE_EXT_ID) &&
           verifier.VerifyString(target_lane_ext_id()) &&
           verifier.EndTable();
  }
};

struct DF_ConnectingLaneExBuilder {
  typedef DF_ConnectingLaneEx Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_target_section(uint8_t target_section) {
    fbb_.AddElement<uint8_t>(DF_ConnectingLaneEx::VT_TARGET_SECTION, target_section, 0);
  }
  void add_target_lane(int8_t target_lane) {
    fbb_.AddElement<int8_t>(DF_ConnectingLaneEx::VT_TARGET_LANE, target_lane, 0);
  }
  void add_connectingLaneWidth(uint16_t connectingLaneWidth) {
    fbb_.AddElement<uint16_t>(DF_ConnectingLaneEx::VT_CONNECTINGLANEWIDTH, connectingLaneWidth, 0);
  }
  void add_connectingLanePoints(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_RoadPoint>>> connectingLanePoints) {
    fbb_.AddOffset(DF_ConnectingLaneEx::VT_CONNECTINGLANEPOINTS, connectingLanePoints);
  }
  void add_isolatedConnectingLane(bool isolatedConnectingLane) {
    fbb_.AddElement<uint8_t>(DF_ConnectingLaneEx::VT_ISOLATEDCONNECTINGLANE, static_cast<uint8_t>(isolatedConnectingLane), 0);
  }
  void add_ext_id(::flatbuffers::Offset<::flatbuffers::String> ext_id) {
    fbb_.AddOffset(DF_ConnectingLaneEx::VT_EXT_ID, ext_id);
  }
  void add_target_section_ext_id(::flatbuffers::Offset<::flatbuffers::String> target_section_ext_id) {
    fbb_.AddOffset(DF_ConnectingLaneEx::VT_TARGET_SECTION_EXT_ID, target_section_ext_id);
  }
  void add_target_lane_ext_id(::flatbuffers::Offset<::flatbuffers::String> target_lane_ext_id) {
    fbb_.AddOffset(DF_ConnectingLaneEx::VT_TARGET_LANE_EXT_ID, target_lane_ext_id);
  }
  explicit DF_ConnectingLaneExBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_ConnectingLaneEx> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_ConnectingLaneEx>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_ConnectingLaneEx> CreateDF_ConnectingLaneEx(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    uint8_t target_section = 0,
    int8_t target_lane = 0,
    uint16_t connectingLaneWidth = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_RoadPoint>>> connectingLanePoints = 0,
    bool isolatedConnectingLane = false,
    ::flatbuffers::Offset<::flatbuffers::String> ext_id = 0,
    ::flatbuffers::Offset<::flatbuffers::String> target_section_ext_id = 0,
    ::flatbuffers::Offset<::flatbuffers::String> target_lane_ext_id = 0) {
  DF_ConnectingLaneExBuilder builder_(_fbb);
  builder_.add_target_lane_ext_id(target_lane_ext_id);
  builder_.add_target_section_ext_id(target_section_ext_id);
  builder_.add_ext_id(ext_id);
  builder_.add_connectingLanePoints(connectingLanePoints);
  builder_.add_connectingLaneWidth(connectingLaneWidth);
  builder_.add_isolatedConnectingLane(isolatedConnectingLane);
  builder_.add_target_lane(target_lane);
  builder_.add_target_section(target_section);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DF_ConnectingLaneEx> CreateDF_ConnectingLaneExDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    uint8_t target_section = 0,
    int8_t target_lane = 0,
    uint16_t connectingLaneWidth = 0,
    const std::vector<::flatbuffers::Offset<MECData::DF_RoadPoint>> *connectingLanePoints = nullptr,
    bool isolatedConnectingLane = false,
    const char *ext_id = nullptr,
    const char *target_section_ext_id = nullptr,
    const char *target_lane_ext_id = nullptr) {
  auto connectingLanePoints__ = connectingLanePoints ? _fbb.CreateVector<::flatbuffers::Offset<MECData::DF_RoadPoint>>(*connectingLanePoints) : 0;
  auto ext_id__ = ext_id ? _fbb.CreateString(ext_id) : 0;
  auto target_section_ext_id__ = target_section_ext_id ? _fbb.CreateString(target_section_ext_id) : 0;
  auto target_lane_ext_id__ = target_lane_ext_id ? _fbb.CreateString(target_lane_ext_id) : 0;
  return MECData::CreateDF_ConnectingLaneEx(
      _fbb,
      target_section,
      target_lane,
      connectingLaneWidth,
      connectingLanePoints__,
      isolatedConnectingLane,
      ext_id__,
      target_section_ext_id__,
      target_lane_ext_id__);
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_CONNECTINGLANEEX_MECDATA_H_
