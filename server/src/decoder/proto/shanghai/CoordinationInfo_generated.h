// automatically generated by the <PERSON>Buffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_COORDINATIONINFO_MECDATA_H_
#define FLATBUFFERS_GENERATED_COORDINATIONINFO_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

namespace MECData {

struct DE_CoordinationInfo;
struct DE_CoordinationInfoBuilder;

struct DE_CoordinationInfo FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DE_CoordinationInfoBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_INFO = 4
  };
  int8_t info() const {
    return GetField<int8_t>(VT_INFO, 0);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<int8_t>(verifier, VT_INFO, 1) &&
           verifier.EndTable();
  }
};

struct DE_CoordinationInfoBuilder {
  typedef DE_CoordinationInfo Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_info(int8_t info) {
    fbb_.AddElement<int8_t>(DE_CoordinationInfo::VT_INFO, info, 0);
  }
  explicit DE_CoordinationInfoBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DE_CoordinationInfo> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DE_CoordinationInfo>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DE_CoordinationInfo> CreateDE_CoordinationInfo(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    int8_t info = 0) {
  DE_CoordinationInfoBuilder builder_(_fbb);
  builder_.add_info(info);
  return builder_.Finish();
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_COORDINATIONINFO_MECDATA_H_
