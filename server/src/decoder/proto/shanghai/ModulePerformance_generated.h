// automatically generated by the <PERSON>Buffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_MODULEPERFORMANCE_MECDATA_H_
#define FLATBUFFERS_GENERATED_MODULEPERFORMANCE_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

namespace MECData {

struct DF_ModulePerformance;
struct DF_ModulePerformanceBuilder;

struct DF_ModulePerformance FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_ModulePerformanceBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_NAME = 4,
    VT_VALUE = 6,
    VT_GEN_TIME = 8
  };
  const ::flatbuffers::String *name() const {
    return GetPointer<const ::flatbuffers::String *>(VT_NAME);
  }
  int16_t value() const {
    return GetField<int16_t>(VT_VALUE, 0);
  }
  int64_t gen_time() const {
    return GetField<int64_t>(VT_GEN_TIME, 0);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffsetRequired(verifier, VT_NAME) &&
           verifier.VerifyString(name()) &&
           VerifyField<int16_t>(verifier, VT_VALUE, 2) &&
           VerifyField<int64_t>(verifier, VT_GEN_TIME, 8) &&
           verifier.EndTable();
  }
};

struct DF_ModulePerformanceBuilder {
  typedef DF_ModulePerformance Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_name(::flatbuffers::Offset<::flatbuffers::String> name) {
    fbb_.AddOffset(DF_ModulePerformance::VT_NAME, name);
  }
  void add_value(int16_t value) {
    fbb_.AddElement<int16_t>(DF_ModulePerformance::VT_VALUE, value, 0);
  }
  void add_gen_time(int64_t gen_time) {
    fbb_.AddElement<int64_t>(DF_ModulePerformance::VT_GEN_TIME, gen_time, 0);
  }
  explicit DF_ModulePerformanceBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_ModulePerformance> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_ModulePerformance>(end);
    fbb_.Required(o, DF_ModulePerformance::VT_NAME);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_ModulePerformance> CreateDF_ModulePerformance(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::String> name = 0,
    int16_t value = 0,
    int64_t gen_time = 0) {
  DF_ModulePerformanceBuilder builder_(_fbb);
  builder_.add_gen_time(gen_time);
  builder_.add_name(name);
  builder_.add_value(value);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DF_ModulePerformance> CreateDF_ModulePerformanceDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const char *name = nullptr,
    int16_t value = 0,
    int64_t gen_time = 0) {
  auto name__ = name ? _fbb.CreateString(name) : 0;
  return MECData::CreateDF_ModulePerformance(
      _fbb,
      name__,
      value,
      gen_time);
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_MODULEPERFORMANCE_MECDATA_H_
