// automatically generated by the FlatBuffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_ACCSET4WAYCONFIDENCE_MECDATA_H_
#define FLATBUFFERS_GENERATED_ACCSET4WAYCONFIDENCE_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

#include "AccConfidence_generated.h"
#include "AngularVelocityConfidence_generated.h"

namespace MECData {

struct DF_AccSet4WayConfidence;
struct DF_AccSet4WayConfidenceBuilder;

struct DF_AccSet4WayConfidence FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_AccSet4WayConfidenceBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_LONACCCONFIDENCE = 4,
    VT_LATACCCONFIDENCE = 6,
    VT_VERTACCCONFIDENCE = 8,
    VT_YAWRATECON = 10
  };
  MECData::DE_AccConfidence lonAccConfidence() const {
    return static_cast<MECData::DE_AccConfidence>(GetField<int8_t>(VT_LONACCCONFIDENCE, 0));
  }
  MECData::DE_AccConfidence latAccConfidence() const {
    return static_cast<MECData::DE_AccConfidence>(GetField<int8_t>(VT_LATACCCONFIDENCE, 0));
  }
  MECData::DE_AccConfidence vertAccConfidence() const {
    return static_cast<MECData::DE_AccConfidence>(GetField<int8_t>(VT_VERTACCCONFIDENCE, 0));
  }
  MECData::DE_AngularVConfidence yawRateCon() const {
    return static_cast<MECData::DE_AngularVConfidence>(GetField<int8_t>(VT_YAWRATECON, 0));
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<int8_t>(verifier, VT_LONACCCONFIDENCE, 1) &&
           VerifyField<int8_t>(verifier, VT_LATACCCONFIDENCE, 1) &&
           VerifyField<int8_t>(verifier, VT_VERTACCCONFIDENCE, 1) &&
           VerifyField<int8_t>(verifier, VT_YAWRATECON, 1) &&
           verifier.EndTable();
  }
};

struct DF_AccSet4WayConfidenceBuilder {
  typedef DF_AccSet4WayConfidence Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_lonAccConfidence(MECData::DE_AccConfidence lonAccConfidence) {
    fbb_.AddElement<int8_t>(DF_AccSet4WayConfidence::VT_LONACCCONFIDENCE, static_cast<int8_t>(lonAccConfidence), 0);
  }
  void add_latAccConfidence(MECData::DE_AccConfidence latAccConfidence) {
    fbb_.AddElement<int8_t>(DF_AccSet4WayConfidence::VT_LATACCCONFIDENCE, static_cast<int8_t>(latAccConfidence), 0);
  }
  void add_vertAccConfidence(MECData::DE_AccConfidence vertAccConfidence) {
    fbb_.AddElement<int8_t>(DF_AccSet4WayConfidence::VT_VERTACCCONFIDENCE, static_cast<int8_t>(vertAccConfidence), 0);
  }
  void add_yawRateCon(MECData::DE_AngularVConfidence yawRateCon) {
    fbb_.AddElement<int8_t>(DF_AccSet4WayConfidence::VT_YAWRATECON, static_cast<int8_t>(yawRateCon), 0);
  }
  explicit DF_AccSet4WayConfidenceBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_AccSet4WayConfidence> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_AccSet4WayConfidence>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_AccSet4WayConfidence> CreateDF_AccSet4WayConfidence(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    MECData::DE_AccConfidence lonAccConfidence = MECData::DE_AccConfidence_unavailable,
    MECData::DE_AccConfidence latAccConfidence = MECData::DE_AccConfidence_unavailable,
    MECData::DE_AccConfidence vertAccConfidence = MECData::DE_AccConfidence_unavailable,
    MECData::DE_AngularVConfidence yawRateCon = MECData::DE_AngularVConfidence_unavailable) {
  DF_AccSet4WayConfidenceBuilder builder_(_fbb);
  builder_.add_yawRateCon(yawRateCon);
  builder_.add_vertAccConfidence(vertAccConfidence);
  builder_.add_latAccConfidence(latAccConfidence);
  builder_.add_lonAccConfidence(lonAccConfidence);
  return builder_.Finish();
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_ACCSET4WAYCONFIDENCE_MECDATA_H_
