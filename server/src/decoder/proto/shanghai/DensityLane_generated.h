// automatically generated by the <PERSON><PERSON>uffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_DENSITYLANE_MECDATA_H_
#define FLATBUFFERS_GENERATED_DENSITYLANE_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

namespace MECData {

struct MSG_DensityLane;
struct MSG_DensityLaneBuilder;

struct MSG_DensityLane FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef MSG_DensityLaneBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_DEVICEID = 4,
    VT_STARTDETECTAERAID = 6,
    VT_ENDDETECTAERAID = 8,
    VT_MOY = 10,
    VT_SECMARK = 12,
    VT_CYCLE = 14,
    VT_DENSITY = 16
  };
  uint16_t deviceId() const {
    return GetField<uint16_t>(VT_DEVICEID, 0);
  }
  int32_t startDetectAeraId() const {
    return GetField<int32_t>(VT_STARTDETECTAERAID, 0);
  }
  int32_t endDetectAeraId() const {
    return GetField<int32_t>(VT_ENDDETECTAERAID, 0);
  }
  uint32_t moy() const {
    return GetField<uint32_t>(VT_MOY, 4294967295);
  }
  uint16_t secMark() const {
    return GetField<uint16_t>(VT_SECMARK, 65535);
  }
  uint16_t cycle() const {
    return GetField<uint16_t>(VT_CYCLE, 65535);
  }
  uint16_t density() const {
    return GetField<uint16_t>(VT_DENSITY, 65535);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<uint16_t>(verifier, VT_DEVICEID, 2) &&
           VerifyField<int32_t>(verifier, VT_STARTDETECTAERAID, 4) &&
           VerifyField<int32_t>(verifier, VT_ENDDETECTAERAID, 4) &&
           VerifyField<uint32_t>(verifier, VT_MOY, 4) &&
           VerifyField<uint16_t>(verifier, VT_SECMARK, 2) &&
           VerifyField<uint16_t>(verifier, VT_CYCLE, 2) &&
           VerifyField<uint16_t>(verifier, VT_DENSITY, 2) &&
           verifier.EndTable();
  }
};

struct MSG_DensityLaneBuilder {
  typedef MSG_DensityLane Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_deviceId(uint16_t deviceId) {
    fbb_.AddElement<uint16_t>(MSG_DensityLane::VT_DEVICEID, deviceId, 0);
  }
  void add_startDetectAeraId(int32_t startDetectAeraId) {
    fbb_.AddElement<int32_t>(MSG_DensityLane::VT_STARTDETECTAERAID, startDetectAeraId, 0);
  }
  void add_endDetectAeraId(int32_t endDetectAeraId) {
    fbb_.AddElement<int32_t>(MSG_DensityLane::VT_ENDDETECTAERAID, endDetectAeraId, 0);
  }
  void add_moy(uint32_t moy) {
    fbb_.AddElement<uint32_t>(MSG_DensityLane::VT_MOY, moy, 4294967295);
  }
  void add_secMark(uint16_t secMark) {
    fbb_.AddElement<uint16_t>(MSG_DensityLane::VT_SECMARK, secMark, 65535);
  }
  void add_cycle(uint16_t cycle) {
    fbb_.AddElement<uint16_t>(MSG_DensityLane::VT_CYCLE, cycle, 65535);
  }
  void add_density(uint16_t density) {
    fbb_.AddElement<uint16_t>(MSG_DensityLane::VT_DENSITY, density, 65535);
  }
  explicit MSG_DensityLaneBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<MSG_DensityLane> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<MSG_DensityLane>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<MSG_DensityLane> CreateMSG_DensityLane(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    uint16_t deviceId = 0,
    int32_t startDetectAeraId = 0,
    int32_t endDetectAeraId = 0,
    uint32_t moy = 4294967295,
    uint16_t secMark = 65535,
    uint16_t cycle = 65535,
    uint16_t density = 65535) {
  MSG_DensityLaneBuilder builder_(_fbb);
  builder_.add_moy(moy);
  builder_.add_endDetectAeraId(endDetectAeraId);
  builder_.add_startDetectAeraId(startDetectAeraId);
  builder_.add_density(density);
  builder_.add_cycle(cycle);
  builder_.add_secMark(secMark);
  builder_.add_deviceId(deviceId);
  return builder_.Finish();
}

inline const MECData::MSG_DensityLane *GetMSG_DensityLane(const void *buf) {
  return ::flatbuffers::GetRoot<MECData::MSG_DensityLane>(buf);
}

inline const MECData::MSG_DensityLane *GetSizePrefixedMSG_DensityLane(const void *buf) {
  return ::flatbuffers::GetSizePrefixedRoot<MECData::MSG_DensityLane>(buf);
}

inline bool VerifyMSG_DensityLaneBuffer(
    ::flatbuffers::Verifier &verifier) {
  return verifier.VerifyBuffer<MECData::MSG_DensityLane>(nullptr);
}

inline bool VerifySizePrefixedMSG_DensityLaneBuffer(
    ::flatbuffers::Verifier &verifier) {
  return verifier.VerifySizePrefixedBuffer<MECData::MSG_DensityLane>(nullptr);
}

inline void FinishMSG_DensityLaneBuffer(
    ::flatbuffers::FlatBufferBuilder &fbb,
    ::flatbuffers::Offset<MECData::MSG_DensityLane> root) {
  fbb.Finish(root);
}

inline void FinishSizePrefixedMSG_DensityLaneBuffer(
    ::flatbuffers::FlatBufferBuilder &fbb,
    ::flatbuffers::Offset<MECData::MSG_DensityLane> root) {
  fbb.FinishSizePrefixed(root);
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_DENSITYLANE_MECDATA_H_
