// automatically generated by the <PERSON><PERSON>uffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_PEDCROSSING_MECDATA_H_
#define FLATBUFFERS_GENERATED_PEDCROSSING_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

#include "Polygon_generated.h"

namespace MECData {

struct DF_PedCrossing;
struct DF_PedCrossingBuilder;

struct DF_PedCrossing FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_PedCrossingBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_EXT_ID = 4,
    VT_NAME = 6,
    VT_POLYGON = 8,
    VT_LINK_EXT_ID = 10,
    VT_SECTION_EXT_ID = 12,
    VT_PHASE_ID = 14
  };
  const ::flatbuffers::String *ext_id() const {
    return GetPointer<const ::flatbuffers::String *>(VT_EXT_ID);
  }
  const ::flatbuffers::String *name() const {
    return GetPointer<const ::flatbuffers::String *>(VT_NAME);
  }
  const MECData::DF_Polygon *polygon() const {
    return GetPointer<const MECData::DF_Polygon *>(VT_POLYGON);
  }
  const ::flatbuffers::String *link_ext_id() const {
    return GetPointer<const ::flatbuffers::String *>(VT_LINK_EXT_ID);
  }
  const ::flatbuffers::String *section_ext_id() const {
    return GetPointer<const ::flatbuffers::String *>(VT_SECTION_EXT_ID);
  }
  uint8_t phase_id() const {
    return GetField<uint8_t>(VT_PHASE_ID, 0);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_EXT_ID) &&
           verifier.VerifyString(ext_id()) &&
           VerifyOffset(verifier, VT_NAME) &&
           verifier.VerifyString(name()) &&
           VerifyOffset(verifier, VT_POLYGON) &&
           verifier.VerifyTable(polygon()) &&
           VerifyOffset(verifier, VT_LINK_EXT_ID) &&
           verifier.VerifyString(link_ext_id()) &&
           VerifyOffset(verifier, VT_SECTION_EXT_ID) &&
           verifier.VerifyString(section_ext_id()) &&
           VerifyField<uint8_t>(verifier, VT_PHASE_ID, 1) &&
           verifier.EndTable();
  }
};

struct DF_PedCrossingBuilder {
  typedef DF_PedCrossing Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_ext_id(::flatbuffers::Offset<::flatbuffers::String> ext_id) {
    fbb_.AddOffset(DF_PedCrossing::VT_EXT_ID, ext_id);
  }
  void add_name(::flatbuffers::Offset<::flatbuffers::String> name) {
    fbb_.AddOffset(DF_PedCrossing::VT_NAME, name);
  }
  void add_polygon(::flatbuffers::Offset<MECData::DF_Polygon> polygon) {
    fbb_.AddOffset(DF_PedCrossing::VT_POLYGON, polygon);
  }
  void add_link_ext_id(::flatbuffers::Offset<::flatbuffers::String> link_ext_id) {
    fbb_.AddOffset(DF_PedCrossing::VT_LINK_EXT_ID, link_ext_id);
  }
  void add_section_ext_id(::flatbuffers::Offset<::flatbuffers::String> section_ext_id) {
    fbb_.AddOffset(DF_PedCrossing::VT_SECTION_EXT_ID, section_ext_id);
  }
  void add_phase_id(uint8_t phase_id) {
    fbb_.AddElement<uint8_t>(DF_PedCrossing::VT_PHASE_ID, phase_id, 0);
  }
  explicit DF_PedCrossingBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_PedCrossing> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_PedCrossing>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_PedCrossing> CreateDF_PedCrossing(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::String> ext_id = 0,
    ::flatbuffers::Offset<::flatbuffers::String> name = 0,
    ::flatbuffers::Offset<MECData::DF_Polygon> polygon = 0,
    ::flatbuffers::Offset<::flatbuffers::String> link_ext_id = 0,
    ::flatbuffers::Offset<::flatbuffers::String> section_ext_id = 0,
    uint8_t phase_id = 0) {
  DF_PedCrossingBuilder builder_(_fbb);
  builder_.add_section_ext_id(section_ext_id);
  builder_.add_link_ext_id(link_ext_id);
  builder_.add_polygon(polygon);
  builder_.add_name(name);
  builder_.add_ext_id(ext_id);
  builder_.add_phase_id(phase_id);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DF_PedCrossing> CreateDF_PedCrossingDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const char *ext_id = nullptr,
    const char *name = nullptr,
    ::flatbuffers::Offset<MECData::DF_Polygon> polygon = 0,
    const char *link_ext_id = nullptr,
    const char *section_ext_id = nullptr,
    uint8_t phase_id = 0) {
  auto ext_id__ = ext_id ? _fbb.CreateString(ext_id) : 0;
  auto name__ = name ? _fbb.CreateString(name) : 0;
  auto link_ext_id__ = link_ext_id ? _fbb.CreateString(link_ext_id) : 0;
  auto section_ext_id__ = section_ext_id ? _fbb.CreateString(section_ext_id) : 0;
  return MECData::CreateDF_PedCrossing(
      _fbb,
      ext_id__,
      name__,
      polygon,
      link_ext_id__,
      section_ext_id__,
      phase_id);
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_PEDCROSSING_MECDATA_H_
