// automatically generated by the <PERSON>Buffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_LINKEX_MECDATA_H_
#define FLATBUFFERS_GENERATED_LINKEX_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

#include "Direction8_generated.h"
#include "MovementEx_generated.h"
#include "NodeReferenceID_generated.h"
#include "RegulatorySpeedLimit_generated.h"
#include "RoadClass_generated.h"
#include "RoadPoint_generated.h"
#include "Section_generated.h"

namespace MECData {

struct DF_LinkEx;
struct DF_LinkExBuilder;

struct DF_LinkEx FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_LinkExBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_NAME = 4,
    VT_UPSTREAMNODEID = 6,
    VT_SPEEDLIMITS = 8,
    VT_REFLINE = 10,
    VT_MOVEMENTS_EX = 12,
    VT_SECTIONS = 14,
    VT_EXT_ID = 16,
    VT_DIRECTION = 18,
    VT_ROAD_CLASS = 20,
    VT_LENGTH = 22
  };
  const ::flatbuffers::String *name() const {
    return GetPointer<const ::flatbuffers::String *>(VT_NAME);
  }
  const MECData::DF_NodeReferenceID *upstreamNodeId() const {
    return GetPointer<const MECData::DF_NodeReferenceID *>(VT_UPSTREAMNODEID);
  }
  const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_RegulatorySpeedLimit>> *speedLimits() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_RegulatorySpeedLimit>> *>(VT_SPEEDLIMITS);
  }
  const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_RoadPoint>> *refLine() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_RoadPoint>> *>(VT_REFLINE);
  }
  const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_MovementEx>> *movements_ex() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_MovementEx>> *>(VT_MOVEMENTS_EX);
  }
  const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_Section>> *sections() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_Section>> *>(VT_SECTIONS);
  }
  const ::flatbuffers::String *ext_id() const {
    return GetPointer<const ::flatbuffers::String *>(VT_EXT_ID);
  }
  MECData::DE_Direction8 direction() const {
    return static_cast<MECData::DE_Direction8>(GetField<uint8_t>(VT_DIRECTION, 0));
  }
  MECData::DE_RoadClass road_class() const {
    return static_cast<MECData::DE_RoadClass>(GetField<uint8_t>(VT_ROAD_CLASS, 0));
  }
  uint32_t length() const {
    return GetField<uint32_t>(VT_LENGTH, 0);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_NAME) &&
           verifier.VerifyString(name()) &&
           VerifyOffsetRequired(verifier, VT_UPSTREAMNODEID) &&
           verifier.VerifyTable(upstreamNodeId()) &&
           VerifyOffset(verifier, VT_SPEEDLIMITS) &&
           verifier.VerifyVector(speedLimits()) &&
           verifier.VerifyVectorOfTables(speedLimits()) &&
           VerifyOffset(verifier, VT_REFLINE) &&
           verifier.VerifyVector(refLine()) &&
           verifier.VerifyVectorOfTables(refLine()) &&
           VerifyOffset(verifier, VT_MOVEMENTS_EX) &&
           verifier.VerifyVector(movements_ex()) &&
           verifier.VerifyVectorOfTables(movements_ex()) &&
           VerifyOffset(verifier, VT_SECTIONS) &&
           verifier.VerifyVector(sections()) &&
           verifier.VerifyVectorOfTables(sections()) &&
           VerifyOffset(verifier, VT_EXT_ID) &&
           verifier.VerifyString(ext_id()) &&
           VerifyField<uint8_t>(verifier, VT_DIRECTION, 1) &&
           VerifyField<uint8_t>(verifier, VT_ROAD_CLASS, 1) &&
           VerifyField<uint32_t>(verifier, VT_LENGTH, 4) &&
           verifier.EndTable();
  }
};

struct DF_LinkExBuilder {
  typedef DF_LinkEx Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_name(::flatbuffers::Offset<::flatbuffers::String> name) {
    fbb_.AddOffset(DF_LinkEx::VT_NAME, name);
  }
  void add_upstreamNodeId(::flatbuffers::Offset<MECData::DF_NodeReferenceID> upstreamNodeId) {
    fbb_.AddOffset(DF_LinkEx::VT_UPSTREAMNODEID, upstreamNodeId);
  }
  void add_speedLimits(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_RegulatorySpeedLimit>>> speedLimits) {
    fbb_.AddOffset(DF_LinkEx::VT_SPEEDLIMITS, speedLimits);
  }
  void add_refLine(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_RoadPoint>>> refLine) {
    fbb_.AddOffset(DF_LinkEx::VT_REFLINE, refLine);
  }
  void add_movements_ex(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_MovementEx>>> movements_ex) {
    fbb_.AddOffset(DF_LinkEx::VT_MOVEMENTS_EX, movements_ex);
  }
  void add_sections(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_Section>>> sections) {
    fbb_.AddOffset(DF_LinkEx::VT_SECTIONS, sections);
  }
  void add_ext_id(::flatbuffers::Offset<::flatbuffers::String> ext_id) {
    fbb_.AddOffset(DF_LinkEx::VT_EXT_ID, ext_id);
  }
  void add_direction(MECData::DE_Direction8 direction) {
    fbb_.AddElement<uint8_t>(DF_LinkEx::VT_DIRECTION, static_cast<uint8_t>(direction), 0);
  }
  void add_road_class(MECData::DE_RoadClass road_class) {
    fbb_.AddElement<uint8_t>(DF_LinkEx::VT_ROAD_CLASS, static_cast<uint8_t>(road_class), 0);
  }
  void add_length(uint32_t length) {
    fbb_.AddElement<uint32_t>(DF_LinkEx::VT_LENGTH, length, 0);
  }
  explicit DF_LinkExBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_LinkEx> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_LinkEx>(end);
    fbb_.Required(o, DF_LinkEx::VT_UPSTREAMNODEID);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_LinkEx> CreateDF_LinkEx(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::String> name = 0,
    ::flatbuffers::Offset<MECData::DF_NodeReferenceID> upstreamNodeId = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_RegulatorySpeedLimit>>> speedLimits = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_RoadPoint>>> refLine = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_MovementEx>>> movements_ex = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_Section>>> sections = 0,
    ::flatbuffers::Offset<::flatbuffers::String> ext_id = 0,
    MECData::DE_Direction8 direction = MECData::DE_Direction8_N,
    MECData::DE_RoadClass road_class = MECData::DE_RoadClass_UNCLASSIFIED,
    uint32_t length = 0) {
  DF_LinkExBuilder builder_(_fbb);
  builder_.add_length(length);
  builder_.add_ext_id(ext_id);
  builder_.add_sections(sections);
  builder_.add_movements_ex(movements_ex);
  builder_.add_refLine(refLine);
  builder_.add_speedLimits(speedLimits);
  builder_.add_upstreamNodeId(upstreamNodeId);
  builder_.add_name(name);
  builder_.add_road_class(road_class);
  builder_.add_direction(direction);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DF_LinkEx> CreateDF_LinkExDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const char *name = nullptr,
    ::flatbuffers::Offset<MECData::DF_NodeReferenceID> upstreamNodeId = 0,
    const std::vector<::flatbuffers::Offset<MECData::DF_RegulatorySpeedLimit>> *speedLimits = nullptr,
    const std::vector<::flatbuffers::Offset<MECData::DF_RoadPoint>> *refLine = nullptr,
    const std::vector<::flatbuffers::Offset<MECData::DF_MovementEx>> *movements_ex = nullptr,
    const std::vector<::flatbuffers::Offset<MECData::DF_Section>> *sections = nullptr,
    const char *ext_id = nullptr,
    MECData::DE_Direction8 direction = MECData::DE_Direction8_N,
    MECData::DE_RoadClass road_class = MECData::DE_RoadClass_UNCLASSIFIED,
    uint32_t length = 0) {
  auto name__ = name ? _fbb.CreateString(name) : 0;
  auto speedLimits__ = speedLimits ? _fbb.CreateVector<::flatbuffers::Offset<MECData::DF_RegulatorySpeedLimit>>(*speedLimits) : 0;
  auto refLine__ = refLine ? _fbb.CreateVector<::flatbuffers::Offset<MECData::DF_RoadPoint>>(*refLine) : 0;
  auto movements_ex__ = movements_ex ? _fbb.CreateVector<::flatbuffers::Offset<MECData::DF_MovementEx>>(*movements_ex) : 0;
  auto sections__ = sections ? _fbb.CreateVector<::flatbuffers::Offset<MECData::DF_Section>>(*sections) : 0;
  auto ext_id__ = ext_id ? _fbb.CreateString(ext_id) : 0;
  return MECData::CreateDF_LinkEx(
      _fbb,
      name__,
      upstreamNodeId,
      speedLimits__,
      refLine__,
      movements_ex__,
      sections__,
      ext_id__,
      direction,
      road_class,
      length);
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_LINKEX_MECDATA_H_
