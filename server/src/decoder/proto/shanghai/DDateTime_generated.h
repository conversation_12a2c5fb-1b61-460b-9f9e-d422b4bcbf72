// automatically generated by the <PERSON><PERSON>uffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_DDATETIME_MECDATA_H_
#define FLATBUFFERS_GENERATED_DDATETIME_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

namespace MECData {

struct DF_DDateTime;
struct DF_DDateTimeBuilder;

struct DF_DDateTime FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_DDateTimeBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_YEAR = 4,
    VT_MONTH = 6,
    VT_DAY = 8,
    VT_HOUR = 10,
    VT_MINUTE = 12,
    VT_SECOND = 14,
    VT_OFFSET = 16
  };
  uint16_t year() const {
    return GetField<uint16_t>(VT_YEAR, 0);
  }
  uint8_t month() const {
    return GetField<uint8_t>(VT_MONTH, 0);
  }
  uint8_t day() const {
    return GetField<uint8_t>(VT_DAY, 0);
  }
  uint8_t hour() const {
    return GetField<uint8_t>(VT_HOUR, 0);
  }
  uint8_t minute() const {
    return GetField<uint8_t>(VT_MINUTE, 0);
  }
  uint16_t second() const {
    return GetField<uint16_t>(VT_SECOND, 0);
  }
  int16_t offset() const {
    return GetField<int16_t>(VT_OFFSET, 0);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<uint16_t>(verifier, VT_YEAR, 2) &&
           VerifyField<uint8_t>(verifier, VT_MONTH, 1) &&
           VerifyField<uint8_t>(verifier, VT_DAY, 1) &&
           VerifyField<uint8_t>(verifier, VT_HOUR, 1) &&
           VerifyField<uint8_t>(verifier, VT_MINUTE, 1) &&
           VerifyField<uint16_t>(verifier, VT_SECOND, 2) &&
           VerifyField<int16_t>(verifier, VT_OFFSET, 2) &&
           verifier.EndTable();
  }
};

struct DF_DDateTimeBuilder {
  typedef DF_DDateTime Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_year(uint16_t year) {
    fbb_.AddElement<uint16_t>(DF_DDateTime::VT_YEAR, year, 0);
  }
  void add_month(uint8_t month) {
    fbb_.AddElement<uint8_t>(DF_DDateTime::VT_MONTH, month, 0);
  }
  void add_day(uint8_t day) {
    fbb_.AddElement<uint8_t>(DF_DDateTime::VT_DAY, day, 0);
  }
  void add_hour(uint8_t hour) {
    fbb_.AddElement<uint8_t>(DF_DDateTime::VT_HOUR, hour, 0);
  }
  void add_minute(uint8_t minute) {
    fbb_.AddElement<uint8_t>(DF_DDateTime::VT_MINUTE, minute, 0);
  }
  void add_second(uint16_t second) {
    fbb_.AddElement<uint16_t>(DF_DDateTime::VT_SECOND, second, 0);
  }
  void add_offset(int16_t offset) {
    fbb_.AddElement<int16_t>(DF_DDateTime::VT_OFFSET, offset, 0);
  }
  explicit DF_DDateTimeBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_DDateTime> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_DDateTime>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_DDateTime> CreateDF_DDateTime(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    uint16_t year = 0,
    uint8_t month = 0,
    uint8_t day = 0,
    uint8_t hour = 0,
    uint8_t minute = 0,
    uint16_t second = 0,
    int16_t offset = 0) {
  DF_DDateTimeBuilder builder_(_fbb);
  builder_.add_offset(offset);
  builder_.add_second(second);
  builder_.add_year(year);
  builder_.add_minute(minute);
  builder_.add_hour(hour);
  builder_.add_day(day);
  builder_.add_month(month);
  return builder_.Finish();
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_DDATETIME_MECDATA_H_
