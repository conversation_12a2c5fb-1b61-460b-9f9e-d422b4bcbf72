// automatically generated by the FlatBuffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_ANGULARVELOCITYCONFIDENCE_MECDATA_H_
#define FLATBUFFERS_GENERATED_ANGULARVELOCITYCONFIDENCE_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

#include "AngularVConfidence_generated.h"

namespace MECData {

struct DF_AngularVelocityConfidence;
struct DF_AngularVelocityConfidenceBuilder;

struct DF_AngularVelocityConfidence FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_AngularVelocityConfidenceBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_PITCHRATE = 4,
    VT_ROLLRATE = 6,
    VT_YAWRATE = 8
  };
  MECData::DE_AngularVConfidence pitchRate() const {
    return static_cast<MECData::DE_AngularVConfidence>(GetField<int8_t>(VT_PITCHRATE, 0));
  }
  MECData::DE_AngularVConfidence rollRate() const {
    return static_cast<MECData::DE_AngularVConfidence>(GetField<int8_t>(VT_ROLLRATE, 0));
  }
  MECData::DE_AngularVConfidence yawRate() const {
    return static_cast<MECData::DE_AngularVConfidence>(GetField<int8_t>(VT_YAWRATE, 0));
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<int8_t>(verifier, VT_PITCHRATE, 1) &&
           VerifyField<int8_t>(verifier, VT_ROLLRATE, 1) &&
           VerifyField<int8_t>(verifier, VT_YAWRATE, 1) &&
           verifier.EndTable();
  }
};

struct DF_AngularVelocityConfidenceBuilder {
  typedef DF_AngularVelocityConfidence Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_pitchRate(MECData::DE_AngularVConfidence pitchRate) {
    fbb_.AddElement<int8_t>(DF_AngularVelocityConfidence::VT_PITCHRATE, static_cast<int8_t>(pitchRate), 0);
  }
  void add_rollRate(MECData::DE_AngularVConfidence rollRate) {
    fbb_.AddElement<int8_t>(DF_AngularVelocityConfidence::VT_ROLLRATE, static_cast<int8_t>(rollRate), 0);
  }
  void add_yawRate(MECData::DE_AngularVConfidence yawRate) {
    fbb_.AddElement<int8_t>(DF_AngularVelocityConfidence::VT_YAWRATE, static_cast<int8_t>(yawRate), 0);
  }
  explicit DF_AngularVelocityConfidenceBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_AngularVelocityConfidence> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_AngularVelocityConfidence>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_AngularVelocityConfidence> CreateDF_AngularVelocityConfidence(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    MECData::DE_AngularVConfidence pitchRate = MECData::DE_AngularVConfidence_unavailable,
    MECData::DE_AngularVConfidence rollRate = MECData::DE_AngularVConfidence_unavailable,
    MECData::DE_AngularVConfidence yawRate = MECData::DE_AngularVConfidence_unavailable) {
  DF_AngularVelocityConfidenceBuilder builder_(_fbb);
  builder_.add_yawRate(yawRate);
  builder_.add_rollRate(rollRate);
  builder_.add_pitchRate(pitchRate);
  return builder_.Finish();
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_ANGULARVELOCITYCONFIDENCE_MECDATA_H_
