// automatically generated by the FlatBuffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_CARBONEMISSION_MECDATA_H_
#define FLATBUFFERS_GENERATED_CARBONEMISSION_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

#include "VehicleClassification_generated.h"

namespace MECData {

struct DF_CFSingleParticipant;
struct DF_CFSingleParticipantBuilder;

struct MSG_CarbonEmission;
struct MSG_CarbonEmissionBuilder;

struct DF_CFSingleParticipant FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_CFSingleParticipantBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_TOKEN = 4,
    VT_CE_TOKEN = 6,
    VT_CARBON_EMISSION = 8
  };
  const ::flatbuffers::String *token() const {
    return GetPointer<const ::flatbuffers::String *>(VT_TOKEN);
  }
  const ::flatbuffers::String *ce_token() const {
    return GetPointer<const ::flatbuffers::String *>(VT_CE_TOKEN);
  }
  uint64_t carbon_emission() const {
    return GetField<uint64_t>(VT_CARBON_EMISSION, 0);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffsetRequired(verifier, VT_TOKEN) &&
           verifier.VerifyString(token()) &&
           VerifyOffset(verifier, VT_CE_TOKEN) &&
           verifier.VerifyString(ce_token()) &&
           VerifyField<uint64_t>(verifier, VT_CARBON_EMISSION, 8) &&
           verifier.EndTable();
  }
};

struct DF_CFSingleParticipantBuilder {
  typedef DF_CFSingleParticipant Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_token(::flatbuffers::Offset<::flatbuffers::String> token) {
    fbb_.AddOffset(DF_CFSingleParticipant::VT_TOKEN, token);
  }
  void add_ce_token(::flatbuffers::Offset<::flatbuffers::String> ce_token) {
    fbb_.AddOffset(DF_CFSingleParticipant::VT_CE_TOKEN, ce_token);
  }
  void add_carbon_emission(uint64_t carbon_emission) {
    fbb_.AddElement<uint64_t>(DF_CFSingleParticipant::VT_CARBON_EMISSION, carbon_emission, 0);
  }
  explicit DF_CFSingleParticipantBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_CFSingleParticipant> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_CFSingleParticipant>(end);
    fbb_.Required(o, DF_CFSingleParticipant::VT_TOKEN);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_CFSingleParticipant> CreateDF_CFSingleParticipant(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::String> token = 0,
    ::flatbuffers::Offset<::flatbuffers::String> ce_token = 0,
    uint64_t carbon_emission = 0) {
  DF_CFSingleParticipantBuilder builder_(_fbb);
  builder_.add_carbon_emission(carbon_emission);
  builder_.add_ce_token(ce_token);
  builder_.add_token(token);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DF_CFSingleParticipant> CreateDF_CFSingleParticipantDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const char *token = nullptr,
    const char *ce_token = nullptr,
    uint64_t carbon_emission = 0) {
  auto token__ = token ? _fbb.CreateString(token) : 0;
  auto ce_token__ = ce_token ? _fbb.CreateString(ce_token) : 0;
  return MECData::CreateDF_CFSingleParticipant(
      _fbb,
      token__,
      ce_token__,
      carbon_emission);
}

struct MSG_CarbonEmission FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef MSG_CarbonEmissionBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_MEC_ID = 4,
    VT_PARTICIPANTS = 6,
    VT_MSG_ID = 8
  };
  const ::flatbuffers::String *mec_id() const {
    return GetPointer<const ::flatbuffers::String *>(VT_MEC_ID);
  }
  const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_CFSingleParticipant>> *participants() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_CFSingleParticipant>> *>(VT_PARTICIPANTS);
  }
  int64_t msg_id() const {
    return GetField<int64_t>(VT_MSG_ID, 0);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_MEC_ID) &&
           verifier.VerifyString(mec_id()) &&
           VerifyOffset(verifier, VT_PARTICIPANTS) &&
           verifier.VerifyVector(participants()) &&
           verifier.VerifyVectorOfTables(participants()) &&
           VerifyField<int64_t>(verifier, VT_MSG_ID, 8) &&
           verifier.EndTable();
  }
};

struct MSG_CarbonEmissionBuilder {
  typedef MSG_CarbonEmission Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_mec_id(::flatbuffers::Offset<::flatbuffers::String> mec_id) {
    fbb_.AddOffset(MSG_CarbonEmission::VT_MEC_ID, mec_id);
  }
  void add_participants(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_CFSingleParticipant>>> participants) {
    fbb_.AddOffset(MSG_CarbonEmission::VT_PARTICIPANTS, participants);
  }
  void add_msg_id(int64_t msg_id) {
    fbb_.AddElement<int64_t>(MSG_CarbonEmission::VT_MSG_ID, msg_id, 0);
  }
  explicit MSG_CarbonEmissionBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<MSG_CarbonEmission> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<MSG_CarbonEmission>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<MSG_CarbonEmission> CreateMSG_CarbonEmission(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::String> mec_id = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_CFSingleParticipant>>> participants = 0,
    int64_t msg_id = 0) {
  MSG_CarbonEmissionBuilder builder_(_fbb);
  builder_.add_msg_id(msg_id);
  builder_.add_participants(participants);
  builder_.add_mec_id(mec_id);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<MSG_CarbonEmission> CreateMSG_CarbonEmissionDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const char *mec_id = nullptr,
    const std::vector<::flatbuffers::Offset<MECData::DF_CFSingleParticipant>> *participants = nullptr,
    int64_t msg_id = 0) {
  auto mec_id__ = mec_id ? _fbb.CreateString(mec_id) : 0;
  auto participants__ = participants ? _fbb.CreateVector<::flatbuffers::Offset<MECData::DF_CFSingleParticipant>>(*participants) : 0;
  return MECData::CreateMSG_CarbonEmission(
      _fbb,
      mec_id__,
      participants__,
      msg_id);
}

inline const MECData::MSG_CarbonEmission *GetMSG_CarbonEmission(const void *buf) {
  return ::flatbuffers::GetRoot<MECData::MSG_CarbonEmission>(buf);
}

inline const MECData::MSG_CarbonEmission *GetSizePrefixedMSG_CarbonEmission(const void *buf) {
  return ::flatbuffers::GetSizePrefixedRoot<MECData::MSG_CarbonEmission>(buf);
}

inline bool VerifyMSG_CarbonEmissionBuffer(
    ::flatbuffers::Verifier &verifier) {
  return verifier.VerifyBuffer<MECData::MSG_CarbonEmission>(nullptr);
}

inline bool VerifySizePrefixedMSG_CarbonEmissionBuffer(
    ::flatbuffers::Verifier &verifier) {
  return verifier.VerifySizePrefixedBuffer<MECData::MSG_CarbonEmission>(nullptr);
}

inline void FinishMSG_CarbonEmissionBuffer(
    ::flatbuffers::FlatBufferBuilder &fbb,
    ::flatbuffers::Offset<MECData::MSG_CarbonEmission> root) {
  fbb.Finish(root);
}

inline void FinishSizePrefixedMSG_CarbonEmissionBuffer(
    ::flatbuffers::FlatBufferBuilder &fbb,
    ::flatbuffers::Offset<MECData::MSG_CarbonEmission> root) {
  fbb.FinishSizePrefixed(root);
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_CARBONEMISSION_MECDATA_H_
