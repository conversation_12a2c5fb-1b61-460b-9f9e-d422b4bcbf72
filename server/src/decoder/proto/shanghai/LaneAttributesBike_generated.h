// automatically generated by the <PERSON>Buffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_LANEATTRIBUTESBIKE_MECDATA_H_
#define FLATBUFFERS_GENERATED_LANEATTRIBUTESBIKE_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

namespace MECData {

struct DF_LaneAttributesBike;
struct DF_LaneAttributesBikeBuilder;

struct DF_LaneAttributesBike FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_LaneAttributesBikeBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_BIKE = 4
  };
  int8_t bike() const {
    return GetField<int8_t>(VT_BIKE, 0);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<int8_t>(verifier, VT_BIKE, 1) &&
           verifier.EndTable();
  }
};

struct DF_LaneAttributesBikeBuilder {
  typedef DF_LaneAttributesBike Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_bike(int8_t bike) {
    fbb_.AddElement<int8_t>(DF_LaneAttributesBike::VT_BIKE, bike, 0);
  }
  explicit DF_LaneAttributesBikeBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_LaneAttributesBike> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_LaneAttributesBike>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_LaneAttributesBike> CreateDF_LaneAttributesBike(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    int8_t bike = 0) {
  DF_LaneAttributesBikeBuilder builder_(_fbb);
  builder_.add_bike(bike);
  return builder_.Finish();
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_LANEATTRIBUTESBIKE_MECDATA_H_
