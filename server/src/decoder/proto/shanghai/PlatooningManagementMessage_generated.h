// automatically generated by the <PERSON>Buffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_PLATOONINGMANAGEMENTMESSAGE_MECDATA_H_
#define FLATBUFFERS_GENERATED_PLATOONINGMANAGEMENTMESSAGE_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

#include "DebugTimeRecords_generated.h"

namespace MECData {

struct DF_MemberNode;
struct DF_MemberNodeBuilder;

struct DF_MemberManagement;
struct DF_MemberManagementBuilder;

struct MSG_PlatooningManagementMessage;
struct MSG_PlatooningManagementMessageBuilder;

enum DE_RoleInPlatooning : uint8_t {
  DE_RoleInPlatooning_LEADER = 0,
  DE_RoleInPlatooning_FOLLOWER = 1,
  DE_RoleInPlatooning_TAIL = 2,
  DE_RoleInPlatooning_FREE_VEHICLE = 3,
  DE_RoleInPlatooning_MIN = DE_RoleInPlatooning_LEADER,
  DE_RoleInPlatooning_MAX = DE_RoleInPlatooning_FREE_VEHICLE
};

inline const DE_RoleInPlatooning (&EnumValuesDE_RoleInPlatooning())[4] {
  static const DE_RoleInPlatooning values[] = {
    DE_RoleInPlatooning_LEADER,
    DE_RoleInPlatooning_FOLLOWER,
    DE_RoleInPlatooning_TAIL,
    DE_RoleInPlatooning_FREE_VEHICLE
  };
  return values;
}

inline const char * const *EnumNamesDE_RoleInPlatooning() {
  static const char * const names[5] = {
    "LEADER",
    "FOLLOWER",
    "TAIL",
    "FREE_VEHICLE",
    nullptr
  };
  return names;
}

inline const char *EnumNameDE_RoleInPlatooning(DE_RoleInPlatooning e) {
  if (::flatbuffers::IsOutRange(e, DE_RoleInPlatooning_LEADER, DE_RoleInPlatooning_FREE_VEHICLE)) return "";
  const size_t index = static_cast<size_t>(e);
  return EnumNamesDE_RoleInPlatooning()[index];
}

enum DE_StatusInPlatooning : uint8_t {
  DE_StatusInPlatooning_NAVIGATING = 0,
  DE_StatusInPlatooning_BEGIN_TO_DISMISS = 1,
  DE_StatusInPlatooning_ASK_FOR_JOINING = 2,
  DE_StatusInPlatooning_JOINING = 3,
  DE_StatusInPlatooning_FOLLOWING = 4,
  DE_StatusInPlatooning_ASK_FOR_LEAVING = 5,
  DE_StatusInPlatooning_LEAVING = 6,
  DE_StatusInPlatooning_MIN = DE_StatusInPlatooning_NAVIGATING,
  DE_StatusInPlatooning_MAX = DE_StatusInPlatooning_LEAVING
};

inline const DE_StatusInPlatooning (&EnumValuesDE_StatusInPlatooning())[7] {
  static const DE_StatusInPlatooning values[] = {
    DE_StatusInPlatooning_NAVIGATING,
    DE_StatusInPlatooning_BEGIN_TO_DISMISS,
    DE_StatusInPlatooning_ASK_FOR_JOINING,
    DE_StatusInPlatooning_JOINING,
    DE_StatusInPlatooning_FOLLOWING,
    DE_StatusInPlatooning_ASK_FOR_LEAVING,
    DE_StatusInPlatooning_LEAVING
  };
  return values;
}

inline const char * const *EnumNamesDE_StatusInPlatooning() {
  static const char * const names[8] = {
    "NAVIGATING",
    "BEGIN_TO_DISMISS",
    "ASK_FOR_JOINING",
    "JOINING",
    "FOLLOWING",
    "ASK_FOR_LEAVING",
    "LEAVING",
    nullptr
  };
  return names;
}

inline const char *EnumNameDE_StatusInPlatooning(DE_StatusInPlatooning e) {
  if (::flatbuffers::IsOutRange(e, DE_StatusInPlatooning_NAVIGATING, DE_StatusInPlatooning_LEAVING)) return "";
  const size_t index = static_cast<size_t>(e);
  return EnumNamesDE_StatusInPlatooning()[index];
}

struct DF_MemberNode FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_MemberNodeBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_VID = 4
  };
  const ::flatbuffers::Vector<uint8_t> *vid() const {
    return GetPointer<const ::flatbuffers::Vector<uint8_t> *>(VT_VID);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffsetRequired(verifier, VT_VID) &&
           verifier.VerifyVector(vid()) &&
           verifier.EndTable();
  }
};

struct DF_MemberNodeBuilder {
  typedef DF_MemberNode Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_vid(::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> vid) {
    fbb_.AddOffset(DF_MemberNode::VT_VID, vid);
  }
  explicit DF_MemberNodeBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_MemberNode> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_MemberNode>(end);
    fbb_.Required(o, DF_MemberNode::VT_VID);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_MemberNode> CreateDF_MemberNode(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> vid = 0) {
  DF_MemberNodeBuilder builder_(_fbb);
  builder_.add_vid(vid);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DF_MemberNode> CreateDF_MemberNodeDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const std::vector<uint8_t> *vid = nullptr) {
  auto vid__ = vid ? _fbb.CreateVector<uint8_t>(*vid) : 0;
  return MECData::CreateDF_MemberNode(
      _fbb,
      vid__);
}

struct DF_MemberManagement FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_MemberManagementBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_MEMBER_LIST = 4,
    VT_JOINING_LIST = 6,
    VT_LEAVING_LIST = 8,
    VT_CAPACITY = 10,
    VT_OPEN_TO_JOIN = 12
  };
  const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_MemberNode>> *member_list() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_MemberNode>> *>(VT_MEMBER_LIST);
  }
  const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_MemberNode>> *joining_list() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_MemberNode>> *>(VT_JOINING_LIST);
  }
  const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_MemberNode>> *leaving_list() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_MemberNode>> *>(VT_LEAVING_LIST);
  }
  uint8_t capacity() const {
    return GetField<uint8_t>(VT_CAPACITY, 0);
  }
  bool open_to_join() const {
    return GetField<uint8_t>(VT_OPEN_TO_JOIN, 0) != 0;
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_MEMBER_LIST) &&
           verifier.VerifyVector(member_list()) &&
           verifier.VerifyVectorOfTables(member_list()) &&
           VerifyOffset(verifier, VT_JOINING_LIST) &&
           verifier.VerifyVector(joining_list()) &&
           verifier.VerifyVectorOfTables(joining_list()) &&
           VerifyOffset(verifier, VT_LEAVING_LIST) &&
           verifier.VerifyVector(leaving_list()) &&
           verifier.VerifyVectorOfTables(leaving_list()) &&
           VerifyField<uint8_t>(verifier, VT_CAPACITY, 1) &&
           VerifyField<uint8_t>(verifier, VT_OPEN_TO_JOIN, 1) &&
           verifier.EndTable();
  }
};

struct DF_MemberManagementBuilder {
  typedef DF_MemberManagement Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_member_list(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_MemberNode>>> member_list) {
    fbb_.AddOffset(DF_MemberManagement::VT_MEMBER_LIST, member_list);
  }
  void add_joining_list(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_MemberNode>>> joining_list) {
    fbb_.AddOffset(DF_MemberManagement::VT_JOINING_LIST, joining_list);
  }
  void add_leaving_list(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_MemberNode>>> leaving_list) {
    fbb_.AddOffset(DF_MemberManagement::VT_LEAVING_LIST, leaving_list);
  }
  void add_capacity(uint8_t capacity) {
    fbb_.AddElement<uint8_t>(DF_MemberManagement::VT_CAPACITY, capacity, 0);
  }
  void add_open_to_join(bool open_to_join) {
    fbb_.AddElement<uint8_t>(DF_MemberManagement::VT_OPEN_TO_JOIN, static_cast<uint8_t>(open_to_join), 0);
  }
  explicit DF_MemberManagementBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_MemberManagement> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_MemberManagement>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_MemberManagement> CreateDF_MemberManagement(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_MemberNode>>> member_list = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_MemberNode>>> joining_list = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_MemberNode>>> leaving_list = 0,
    uint8_t capacity = 0,
    bool open_to_join = false) {
  DF_MemberManagementBuilder builder_(_fbb);
  builder_.add_leaving_list(leaving_list);
  builder_.add_joining_list(joining_list);
  builder_.add_member_list(member_list);
  builder_.add_open_to_join(open_to_join);
  builder_.add_capacity(capacity);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DF_MemberManagement> CreateDF_MemberManagementDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const std::vector<::flatbuffers::Offset<MECData::DF_MemberNode>> *member_list = nullptr,
    const std::vector<::flatbuffers::Offset<MECData::DF_MemberNode>> *joining_list = nullptr,
    const std::vector<::flatbuffers::Offset<MECData::DF_MemberNode>> *leaving_list = nullptr,
    uint8_t capacity = 0,
    bool open_to_join = false) {
  auto member_list__ = member_list ? _fbb.CreateVector<::flatbuffers::Offset<MECData::DF_MemberNode>>(*member_list) : 0;
  auto joining_list__ = joining_list ? _fbb.CreateVector<::flatbuffers::Offset<MECData::DF_MemberNode>>(*joining_list) : 0;
  auto leaving_list__ = leaving_list ? _fbb.CreateVector<::flatbuffers::Offset<MECData::DF_MemberNode>>(*leaving_list) : 0;
  return MECData::CreateDF_MemberManagement(
      _fbb,
      member_list__,
      joining_list__,
      leaving_list__,
      capacity,
      open_to_join);
}

struct MSG_PlatooningManagementMessage FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef MSG_PlatooningManagementMessageBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_ID = 4,
    VT_MOY = 6,
    VT_SECMARK = 8,
    VT_PID = 10,
    VT_ROLE = 12,
    VT_STATUS = 14,
    VT_LEADING_EXT = 16,
    VT_TIME_RECORDS = 18,
    VT_MSG_ID = 20
  };
  const ::flatbuffers::Vector<uint8_t> *id() const {
    return GetPointer<const ::flatbuffers::Vector<uint8_t> *>(VT_ID);
  }
  uint32_t moy() const {
    return GetField<uint32_t>(VT_MOY, 0);
  }
  uint16_t secmark() const {
    return GetField<uint16_t>(VT_SECMARK, 0);
  }
  const ::flatbuffers::Vector<uint8_t> *pid() const {
    return GetPointer<const ::flatbuffers::Vector<uint8_t> *>(VT_PID);
  }
  MECData::DE_RoleInPlatooning role() const {
    return static_cast<MECData::DE_RoleInPlatooning>(GetField<uint8_t>(VT_ROLE, 0));
  }
  MECData::DE_StatusInPlatooning status() const {
    return static_cast<MECData::DE_StatusInPlatooning>(GetField<uint8_t>(VT_STATUS, 0));
  }
  const MECData::DF_MemberManagement *leading_ext() const {
    return GetPointer<const MECData::DF_MemberManagement *>(VT_LEADING_EXT);
  }
  const MECData::DF_DebugTimeRecords *time_records() const {
    return GetPointer<const MECData::DF_DebugTimeRecords *>(VT_TIME_RECORDS);
  }
  int64_t msg_id() const {
    return GetField<int64_t>(VT_MSG_ID, 0);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffsetRequired(verifier, VT_ID) &&
           verifier.VerifyVector(id()) &&
           VerifyField<uint32_t>(verifier, VT_MOY, 4) &&
           VerifyField<uint16_t>(verifier, VT_SECMARK, 2) &&
           VerifyOffsetRequired(verifier, VT_PID) &&
           verifier.VerifyVector(pid()) &&
           VerifyField<uint8_t>(verifier, VT_ROLE, 1) &&
           VerifyField<uint8_t>(verifier, VT_STATUS, 1) &&
           VerifyOffset(verifier, VT_LEADING_EXT) &&
           verifier.VerifyTable(leading_ext()) &&
           VerifyOffset(verifier, VT_TIME_RECORDS) &&
           verifier.VerifyTable(time_records()) &&
           VerifyField<int64_t>(verifier, VT_MSG_ID, 8) &&
           verifier.EndTable();
  }
};

struct MSG_PlatooningManagementMessageBuilder {
  typedef MSG_PlatooningManagementMessage Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_id(::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> id) {
    fbb_.AddOffset(MSG_PlatooningManagementMessage::VT_ID, id);
  }
  void add_moy(uint32_t moy) {
    fbb_.AddElement<uint32_t>(MSG_PlatooningManagementMessage::VT_MOY, moy, 0);
  }
  void add_secmark(uint16_t secmark) {
    fbb_.AddElement<uint16_t>(MSG_PlatooningManagementMessage::VT_SECMARK, secmark, 0);
  }
  void add_pid(::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> pid) {
    fbb_.AddOffset(MSG_PlatooningManagementMessage::VT_PID, pid);
  }
  void add_role(MECData::DE_RoleInPlatooning role) {
    fbb_.AddElement<uint8_t>(MSG_PlatooningManagementMessage::VT_ROLE, static_cast<uint8_t>(role), 0);
  }
  void add_status(MECData::DE_StatusInPlatooning status) {
    fbb_.AddElement<uint8_t>(MSG_PlatooningManagementMessage::VT_STATUS, static_cast<uint8_t>(status), 0);
  }
  void add_leading_ext(::flatbuffers::Offset<MECData::DF_MemberManagement> leading_ext) {
    fbb_.AddOffset(MSG_PlatooningManagementMessage::VT_LEADING_EXT, leading_ext);
  }
  void add_time_records(::flatbuffers::Offset<MECData::DF_DebugTimeRecords> time_records) {
    fbb_.AddOffset(MSG_PlatooningManagementMessage::VT_TIME_RECORDS, time_records);
  }
  void add_msg_id(int64_t msg_id) {
    fbb_.AddElement<int64_t>(MSG_PlatooningManagementMessage::VT_MSG_ID, msg_id, 0);
  }
  explicit MSG_PlatooningManagementMessageBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<MSG_PlatooningManagementMessage> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<MSG_PlatooningManagementMessage>(end);
    fbb_.Required(o, MSG_PlatooningManagementMessage::VT_ID);
    fbb_.Required(o, MSG_PlatooningManagementMessage::VT_PID);
    return o;
  }
};

inline ::flatbuffers::Offset<MSG_PlatooningManagementMessage> CreateMSG_PlatooningManagementMessage(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> id = 0,
    uint32_t moy = 0,
    uint16_t secmark = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> pid = 0,
    MECData::DE_RoleInPlatooning role = MECData::DE_RoleInPlatooning_LEADER,
    MECData::DE_StatusInPlatooning status = MECData::DE_StatusInPlatooning_NAVIGATING,
    ::flatbuffers::Offset<MECData::DF_MemberManagement> leading_ext = 0,
    ::flatbuffers::Offset<MECData::DF_DebugTimeRecords> time_records = 0,
    int64_t msg_id = 0) {
  MSG_PlatooningManagementMessageBuilder builder_(_fbb);
  builder_.add_msg_id(msg_id);
  builder_.add_time_records(time_records);
  builder_.add_leading_ext(leading_ext);
  builder_.add_pid(pid);
  builder_.add_moy(moy);
  builder_.add_id(id);
  builder_.add_secmark(secmark);
  builder_.add_status(status);
  builder_.add_role(role);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<MSG_PlatooningManagementMessage> CreateMSG_PlatooningManagementMessageDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const std::vector<uint8_t> *id = nullptr,
    uint32_t moy = 0,
    uint16_t secmark = 0,
    const std::vector<uint8_t> *pid = nullptr,
    MECData::DE_RoleInPlatooning role = MECData::DE_RoleInPlatooning_LEADER,
    MECData::DE_StatusInPlatooning status = MECData::DE_StatusInPlatooning_NAVIGATING,
    ::flatbuffers::Offset<MECData::DF_MemberManagement> leading_ext = 0,
    ::flatbuffers::Offset<MECData::DF_DebugTimeRecords> time_records = 0,
    int64_t msg_id = 0) {
  auto id__ = id ? _fbb.CreateVector<uint8_t>(*id) : 0;
  auto pid__ = pid ? _fbb.CreateVector<uint8_t>(*pid) : 0;
  return MECData::CreateMSG_PlatooningManagementMessage(
      _fbb,
      id__,
      moy,
      secmark,
      pid__,
      role,
      status,
      leading_ext,
      time_records,
      msg_id);
}

inline const MECData::MSG_PlatooningManagementMessage *GetMSG_PlatooningManagementMessage(const void *buf) {
  return ::flatbuffers::GetRoot<MECData::MSG_PlatooningManagementMessage>(buf);
}

inline const MECData::MSG_PlatooningManagementMessage *GetSizePrefixedMSG_PlatooningManagementMessage(const void *buf) {
  return ::flatbuffers::GetSizePrefixedRoot<MECData::MSG_PlatooningManagementMessage>(buf);
}

inline bool VerifyMSG_PlatooningManagementMessageBuffer(
    ::flatbuffers::Verifier &verifier) {
  return verifier.VerifyBuffer<MECData::MSG_PlatooningManagementMessage>(nullptr);
}

inline bool VerifySizePrefixedMSG_PlatooningManagementMessageBuffer(
    ::flatbuffers::Verifier &verifier) {
  return verifier.VerifySizePrefixedBuffer<MECData::MSG_PlatooningManagementMessage>(nullptr);
}

inline void FinishMSG_PlatooningManagementMessageBuffer(
    ::flatbuffers::FlatBufferBuilder &fbb,
    ::flatbuffers::Offset<MECData::MSG_PlatooningManagementMessage> root) {
  fbb.Finish(root);
}

inline void FinishSizePrefixedMSG_PlatooningManagementMessageBuffer(
    ::flatbuffers::FlatBufferBuilder &fbb,
    ::flatbuffers::Offset<MECData::MSG_PlatooningManagementMessage> root) {
  fbb.FinishSizePrefixed(root);
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_PLATOONINGMANAGEMENTMESSAGE_MECDATA_H_
