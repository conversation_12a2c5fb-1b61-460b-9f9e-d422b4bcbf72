// automatically generated by the <PERSON>Buffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_LANETYPEATTRIBUTES_MECDATA_H_
#define FLATBUFFERS_GENERATED_LANETYPEATTRIBUTES_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

#include "LaneAttributesBarrier_generated.h"
#include "LaneAttributesBike_generated.h"
#include "LaneAttributesCrosswalk_generated.h"
#include "LaneAttributesParking_generated.h"
#include "LaneAttributesSidewalk_generated.h"
#include "LaneAttributesStriping_generated.h"
#include "LaneAttributesTrackedVehicle_generated.h"
#include "LaneAttributesVehicle_generated.h"

namespace MECData {

enum DF_LaneTypeAttributes : uint8_t {
  DF_LaneTypeAttributes_NONE = 0,
  DF_LaneTypeAttributes_DF_LaneAttributesVehicle = 1,
  DF_LaneTypeAttributes_DF_LaneAttributesCrosswalk = 2,
  DF_LaneTypeAttributes_DF_LaneAttributesBike = 3,
  DF_LaneTypeAttributes_DF_LaneAttributesSidewalk = 4,
  DF_LaneTypeAttributes_DF_LaneAttributesBarrier = 5,
  DF_LaneTypeAttributes_DF_LaneAttributesStriping = 6,
  DF_LaneTypeAttributes_DF_LaneAttributesTrackedVehicle = 7,
  DF_LaneTypeAttributes_DF_LaneAttributesParking = 8,
  DF_LaneTypeAttributes_MIN = DF_LaneTypeAttributes_NONE,
  DF_LaneTypeAttributes_MAX = DF_LaneTypeAttributes_DF_LaneAttributesParking
};

inline const DF_LaneTypeAttributes (&EnumValuesDF_LaneTypeAttributes())[9] {
  static const DF_LaneTypeAttributes values[] = {
    DF_LaneTypeAttributes_NONE,
    DF_LaneTypeAttributes_DF_LaneAttributesVehicle,
    DF_LaneTypeAttributes_DF_LaneAttributesCrosswalk,
    DF_LaneTypeAttributes_DF_LaneAttributesBike,
    DF_LaneTypeAttributes_DF_LaneAttributesSidewalk,
    DF_LaneTypeAttributes_DF_LaneAttributesBarrier,
    DF_LaneTypeAttributes_DF_LaneAttributesStriping,
    DF_LaneTypeAttributes_DF_LaneAttributesTrackedVehicle,
    DF_LaneTypeAttributes_DF_LaneAttributesParking
  };
  return values;
}

inline const char * const *EnumNamesDF_LaneTypeAttributes() {
  static const char * const names[10] = {
    "NONE",
    "DF_LaneAttributesVehicle",
    "DF_LaneAttributesCrosswalk",
    "DF_LaneAttributesBike",
    "DF_LaneAttributesSidewalk",
    "DF_LaneAttributesBarrier",
    "DF_LaneAttributesStriping",
    "DF_LaneAttributesTrackedVehicle",
    "DF_LaneAttributesParking",
    nullptr
  };
  return names;
}

inline const char *EnumNameDF_LaneTypeAttributes(DF_LaneTypeAttributes e) {
  if (::flatbuffers::IsOutRange(e, DF_LaneTypeAttributes_NONE, DF_LaneTypeAttributes_DF_LaneAttributesParking)) return "";
  const size_t index = static_cast<size_t>(e);
  return EnumNamesDF_LaneTypeAttributes()[index];
}

template<typename T> struct DF_LaneTypeAttributesTraits {
  static const DF_LaneTypeAttributes enum_value = DF_LaneTypeAttributes_NONE;
};

template<> struct DF_LaneTypeAttributesTraits<MECData::DF_LaneAttributesVehicle> {
  static const DF_LaneTypeAttributes enum_value = DF_LaneTypeAttributes_DF_LaneAttributesVehicle;
};

template<> struct DF_LaneTypeAttributesTraits<MECData::DF_LaneAttributesCrosswalk> {
  static const DF_LaneTypeAttributes enum_value = DF_LaneTypeAttributes_DF_LaneAttributesCrosswalk;
};

template<> struct DF_LaneTypeAttributesTraits<MECData::DF_LaneAttributesBike> {
  static const DF_LaneTypeAttributes enum_value = DF_LaneTypeAttributes_DF_LaneAttributesBike;
};

template<> struct DF_LaneTypeAttributesTraits<MECData::DF_LaneAttributesSidewalk> {
  static const DF_LaneTypeAttributes enum_value = DF_LaneTypeAttributes_DF_LaneAttributesSidewalk;
};

template<> struct DF_LaneTypeAttributesTraits<MECData::DF_LaneAttributesBarrier> {
  static const DF_LaneTypeAttributes enum_value = DF_LaneTypeAttributes_DF_LaneAttributesBarrier;
};

template<> struct DF_LaneTypeAttributesTraits<MECData::DF_LaneAttributesStriping> {
  static const DF_LaneTypeAttributes enum_value = DF_LaneTypeAttributes_DF_LaneAttributesStriping;
};

template<> struct DF_LaneTypeAttributesTraits<MECData::DF_LaneAttributesTrackedVehicle> {
  static const DF_LaneTypeAttributes enum_value = DF_LaneTypeAttributes_DF_LaneAttributesTrackedVehicle;
};

template<> struct DF_LaneTypeAttributesTraits<MECData::DF_LaneAttributesParking> {
  static const DF_LaneTypeAttributes enum_value = DF_LaneTypeAttributes_DF_LaneAttributesParking;
};

bool VerifyDF_LaneTypeAttributes(::flatbuffers::Verifier &verifier, const void *obj, DF_LaneTypeAttributes type);
bool VerifyDF_LaneTypeAttributesVector(::flatbuffers::Verifier &verifier, const ::flatbuffers::Vector<::flatbuffers::Offset<void>> *values, const ::flatbuffers::Vector<uint8_t> *types);

inline bool VerifyDF_LaneTypeAttributes(::flatbuffers::Verifier &verifier, const void *obj, DF_LaneTypeAttributes type) {
  switch (type) {
    case DF_LaneTypeAttributes_NONE: {
      return true;
    }
    case DF_LaneTypeAttributes_DF_LaneAttributesVehicle: {
      auto ptr = reinterpret_cast<const MECData::DF_LaneAttributesVehicle *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case DF_LaneTypeAttributes_DF_LaneAttributesCrosswalk: {
      auto ptr = reinterpret_cast<const MECData::DF_LaneAttributesCrosswalk *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case DF_LaneTypeAttributes_DF_LaneAttributesBike: {
      auto ptr = reinterpret_cast<const MECData::DF_LaneAttributesBike *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case DF_LaneTypeAttributes_DF_LaneAttributesSidewalk: {
      auto ptr = reinterpret_cast<const MECData::DF_LaneAttributesSidewalk *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case DF_LaneTypeAttributes_DF_LaneAttributesBarrier: {
      auto ptr = reinterpret_cast<const MECData::DF_LaneAttributesBarrier *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case DF_LaneTypeAttributes_DF_LaneAttributesStriping: {
      auto ptr = reinterpret_cast<const MECData::DF_LaneAttributesStriping *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case DF_LaneTypeAttributes_DF_LaneAttributesTrackedVehicle: {
      auto ptr = reinterpret_cast<const MECData::DF_LaneAttributesTrackedVehicle *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case DF_LaneTypeAttributes_DF_LaneAttributesParking: {
      auto ptr = reinterpret_cast<const MECData::DF_LaneAttributesParking *>(obj);
      return verifier.VerifyTable(ptr);
    }
    default: return true;
  }
}

inline bool VerifyDF_LaneTypeAttributesVector(::flatbuffers::Verifier &verifier, const ::flatbuffers::Vector<::flatbuffers::Offset<void>> *values, const ::flatbuffers::Vector<uint8_t> *types) {
  if (!values || !types) return !values && !types;
  if (values->size() != types->size()) return false;
  for (::flatbuffers::uoffset_t i = 0; i < values->size(); ++i) {
    if (!VerifyDF_LaneTypeAttributes(
        verifier,  values->Get(i), types->GetEnum<DF_LaneTypeAttributes>(i))) {
      return false;
    }
  }
  return true;
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_LANETYPEATTRIBUTES_MECDATA_H_
