// automatically generated by the <PERSON>Buffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_EXTERIORLIGHTS_MECDATA_H_
#define FLATBUFFERS_GENERATED_EXTERIORLIGHTS_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

namespace MECData {

struct DE_ExteriorLights;
struct DE_ExteriorLightsBuilder;

struct DE_ExteriorLights FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DE_ExteriorLightsBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_LIGHTS = 4
  };
  int16_t lights() const {
    return GetField<int16_t>(VT_LIGHTS, 0);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<int16_t>(verifier, VT_LIGHTS, 2) &&
           verifier.EndTable();
  }
};

struct DE_ExteriorLightsBuilder {
  typedef DE_ExteriorLights Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_lights(int16_t lights) {
    fbb_.AddElement<int16_t>(DE_ExteriorLights::VT_LIGHTS, lights, 0);
  }
  explicit DE_ExteriorLightsBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DE_ExteriorLights> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DE_ExteriorLights>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DE_ExteriorLights> CreateDE_ExteriorLights(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    int16_t lights = 0) {
  DE_ExteriorLightsBuilder builder_(_fbb);
  builder_.add_lights(lights);
  return builder_.Finish();
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_EXTERIORLIGHTS_MECDATA_H_
