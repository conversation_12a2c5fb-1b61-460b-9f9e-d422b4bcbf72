// automatically generated by the <PERSON>Buffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_DEVICEINFO_MECDATA_H_
#define FLATBUFFERS_GENERATED_DEVICEINFO_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

#include "DeviceAbnormal_generated.h"
#include "DeviceSpecificInfo_generated.h"
#include "Mileage_generated.h"
#include "ModulePerformance_generated.h"
#include "NodeReferenceID_generated.h"
#include "PacketFlowRecord_generated.h"
#include "Position3D_generated.h"

namespace MECData {

struct DF_DeviceDescription;
struct DF_DeviceDescriptionBuilder;

struct DF_SubDeviceInfo;
struct DF_SubDeviceInfoBuilder;

struct DF_DeviceInfo;
struct DF_DeviceInfoBuilder;

struct DF_DeviceDescription FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_DeviceDescriptionBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_MODEL = 4,
    VT_MANUFACTURER = 6,
    VT_RAW_SERIAL_NUMBER = 8
  };
  const ::flatbuffers::String *model() const {
    return GetPointer<const ::flatbuffers::String *>(VT_MODEL);
  }
  const ::flatbuffers::String *manufacturer() const {
    return GetPointer<const ::flatbuffers::String *>(VT_MANUFACTURER);
  }
  const ::flatbuffers::String *raw_serial_number() const {
    return GetPointer<const ::flatbuffers::String *>(VT_RAW_SERIAL_NUMBER);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_MODEL) &&
           verifier.VerifyString(model()) &&
           VerifyOffset(verifier, VT_MANUFACTURER) &&
           verifier.VerifyString(manufacturer()) &&
           VerifyOffset(verifier, VT_RAW_SERIAL_NUMBER) &&
           verifier.VerifyString(raw_serial_number()) &&
           verifier.EndTable();
  }
};

struct DF_DeviceDescriptionBuilder {
  typedef DF_DeviceDescription Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_model(::flatbuffers::Offset<::flatbuffers::String> model) {
    fbb_.AddOffset(DF_DeviceDescription::VT_MODEL, model);
  }
  void add_manufacturer(::flatbuffers::Offset<::flatbuffers::String> manufacturer) {
    fbb_.AddOffset(DF_DeviceDescription::VT_MANUFACTURER, manufacturer);
  }
  void add_raw_serial_number(::flatbuffers::Offset<::flatbuffers::String> raw_serial_number) {
    fbb_.AddOffset(DF_DeviceDescription::VT_RAW_SERIAL_NUMBER, raw_serial_number);
  }
  explicit DF_DeviceDescriptionBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_DeviceDescription> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_DeviceDescription>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_DeviceDescription> CreateDF_DeviceDescription(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::String> model = 0,
    ::flatbuffers::Offset<::flatbuffers::String> manufacturer = 0,
    ::flatbuffers::Offset<::flatbuffers::String> raw_serial_number = 0) {
  DF_DeviceDescriptionBuilder builder_(_fbb);
  builder_.add_raw_serial_number(raw_serial_number);
  builder_.add_manufacturer(manufacturer);
  builder_.add_model(model);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DF_DeviceDescription> CreateDF_DeviceDescriptionDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const char *model = nullptr,
    const char *manufacturer = nullptr,
    const char *raw_serial_number = nullptr) {
  auto model__ = model ? _fbb.CreateString(model) : 0;
  auto manufacturer__ = manufacturer ? _fbb.CreateString(manufacturer) : 0;
  auto raw_serial_number__ = raw_serial_number ? _fbb.CreateString(raw_serial_number) : 0;
  return MECData::CreateDF_DeviceDescription(
      _fbb,
      model__,
      manufacturer__,
      raw_serial_number__);
}

struct DF_SubDeviceInfo FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_SubDeviceInfoBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_ID = 4,
    VT_POS = 6,
    VT_MILEAGE = 8,
    VT_DESCRIPTION = 10,
    VT_SPECIFICS_TYPE = 12,
    VT_SPECIFICS = 14,
    VT_STATUS = 16,
    VT_PACKET_STATS = 18,
    VT_PERFORMANCE = 20
  };
  uint8_t id() const {
    return GetField<uint8_t>(VT_ID, 0);
  }
  const MECData::DF_Position3D *pos() const {
    return GetPointer<const MECData::DF_Position3D *>(VT_POS);
  }
  const MECData::DF_Mileage *mileage() const {
    return GetPointer<const MECData::DF_Mileage *>(VT_MILEAGE);
  }
  const MECData::DF_DeviceDescription *description() const {
    return GetPointer<const MECData::DF_DeviceDescription *>(VT_DESCRIPTION);
  }
  MECData::DF_DeviceSpecificInfo specifics_type() const {
    return static_cast<MECData::DF_DeviceSpecificInfo>(GetField<uint8_t>(VT_SPECIFICS_TYPE, 0));
  }
  const void *specifics() const {
    return GetPointer<const void *>(VT_SPECIFICS);
  }
  template<typename T> const T *specifics_as() const;
  const MECData::DF_CameraModuleInfo *specifics_as_DF_CameraModuleInfo() const {
    return specifics_type() == MECData::DF_DeviceSpecificInfo_DF_CameraModuleInfo ? static_cast<const MECData::DF_CameraModuleInfo *>(specifics()) : nullptr;
  }
  const MECData::DF_MMWRadarModuleInfo *specifics_as_DF_MMWRadarModuleInfo() const {
    return specifics_type() == MECData::DF_DeviceSpecificInfo_DF_MMWRadarModuleInfo ? static_cast<const MECData::DF_MMWRadarModuleInfo *>(specifics()) : nullptr;
  }
  const MECData::DF_TrafficSignalModuleInfo *specifics_as_DF_TrafficSignalModuleInfo() const {
    return specifics_type() == MECData::DF_DeviceSpecificInfo_DF_TrafficSignalModuleInfo ? static_cast<const MECData::DF_TrafficSignalModuleInfo *>(specifics()) : nullptr;
  }
  const MECData::DF_RSUModuleInfo *specifics_as_DF_RSUModuleInfo() const {
    return specifics_type() == MECData::DF_DeviceSpecificInfo_DF_RSUModuleInfo ? static_cast<const MECData::DF_RSUModuleInfo *>(specifics()) : nullptr;
  }
  const MECData::DF_CloudInfo *specifics_as_DF_CloudInfo() const {
    return specifics_type() == MECData::DF_DeviceSpecificInfo_DF_CloudInfo ? static_cast<const MECData::DF_CloudInfo *>(specifics()) : nullptr;
  }
  const MECData::DF_LidarModuleInfo *specifics_as_DF_LidarModuleInfo() const {
    return specifics_type() == MECData::DF_DeviceSpecificInfo_DF_LidarModuleInfo ? static_cast<const MECData::DF_LidarModuleInfo *>(specifics()) : nullptr;
  }
  const MECData::DF_DeviceAbnormal *status() const {
    return GetPointer<const MECData::DF_DeviceAbnormal *>(VT_STATUS);
  }
  const MECData::DF_PacketFlowRecord *packet_stats() const {
    return GetPointer<const MECData::DF_PacketFlowRecord *>(VT_PACKET_STATS);
  }
  const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_ModulePerformance>> *performance() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_ModulePerformance>> *>(VT_PERFORMANCE);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<uint8_t>(verifier, VT_ID, 1) &&
           VerifyOffset(verifier, VT_POS) &&
           verifier.VerifyTable(pos()) &&
           VerifyOffset(verifier, VT_MILEAGE) &&
           verifier.VerifyTable(mileage()) &&
           VerifyOffset(verifier, VT_DESCRIPTION) &&
           verifier.VerifyTable(description()) &&
           VerifyField<uint8_t>(verifier, VT_SPECIFICS_TYPE, 1) &&
           VerifyOffset(verifier, VT_SPECIFICS) &&
           VerifyDF_DeviceSpecificInfo(verifier, specifics(), specifics_type()) &&
           VerifyOffset(verifier, VT_STATUS) &&
           verifier.VerifyTable(status()) &&
           VerifyOffset(verifier, VT_PACKET_STATS) &&
           verifier.VerifyTable(packet_stats()) &&
           VerifyOffset(verifier, VT_PERFORMANCE) &&
           verifier.VerifyVector(performance()) &&
           verifier.VerifyVectorOfTables(performance()) &&
           verifier.EndTable();
  }
};

template<> inline const MECData::DF_CameraModuleInfo *DF_SubDeviceInfo::specifics_as<MECData::DF_CameraModuleInfo>() const {
  return specifics_as_DF_CameraModuleInfo();
}

template<> inline const MECData::DF_MMWRadarModuleInfo *DF_SubDeviceInfo::specifics_as<MECData::DF_MMWRadarModuleInfo>() const {
  return specifics_as_DF_MMWRadarModuleInfo();
}

template<> inline const MECData::DF_TrafficSignalModuleInfo *DF_SubDeviceInfo::specifics_as<MECData::DF_TrafficSignalModuleInfo>() const {
  return specifics_as_DF_TrafficSignalModuleInfo();
}

template<> inline const MECData::DF_RSUModuleInfo *DF_SubDeviceInfo::specifics_as<MECData::DF_RSUModuleInfo>() const {
  return specifics_as_DF_RSUModuleInfo();
}

template<> inline const MECData::DF_CloudInfo *DF_SubDeviceInfo::specifics_as<MECData::DF_CloudInfo>() const {
  return specifics_as_DF_CloudInfo();
}

template<> inline const MECData::DF_LidarModuleInfo *DF_SubDeviceInfo::specifics_as<MECData::DF_LidarModuleInfo>() const {
  return specifics_as_DF_LidarModuleInfo();
}

struct DF_SubDeviceInfoBuilder {
  typedef DF_SubDeviceInfo Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_id(uint8_t id) {
    fbb_.AddElement<uint8_t>(DF_SubDeviceInfo::VT_ID, id, 0);
  }
  void add_pos(::flatbuffers::Offset<MECData::DF_Position3D> pos) {
    fbb_.AddOffset(DF_SubDeviceInfo::VT_POS, pos);
  }
  void add_mileage(::flatbuffers::Offset<MECData::DF_Mileage> mileage) {
    fbb_.AddOffset(DF_SubDeviceInfo::VT_MILEAGE, mileage);
  }
  void add_description(::flatbuffers::Offset<MECData::DF_DeviceDescription> description) {
    fbb_.AddOffset(DF_SubDeviceInfo::VT_DESCRIPTION, description);
  }
  void add_specifics_type(MECData::DF_DeviceSpecificInfo specifics_type) {
    fbb_.AddElement<uint8_t>(DF_SubDeviceInfo::VT_SPECIFICS_TYPE, static_cast<uint8_t>(specifics_type), 0);
  }
  void add_specifics(::flatbuffers::Offset<void> specifics) {
    fbb_.AddOffset(DF_SubDeviceInfo::VT_SPECIFICS, specifics);
  }
  void add_status(::flatbuffers::Offset<MECData::DF_DeviceAbnormal> status) {
    fbb_.AddOffset(DF_SubDeviceInfo::VT_STATUS, status);
  }
  void add_packet_stats(::flatbuffers::Offset<MECData::DF_PacketFlowRecord> packet_stats) {
    fbb_.AddOffset(DF_SubDeviceInfo::VT_PACKET_STATS, packet_stats);
  }
  void add_performance(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_ModulePerformance>>> performance) {
    fbb_.AddOffset(DF_SubDeviceInfo::VT_PERFORMANCE, performance);
  }
  explicit DF_SubDeviceInfoBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_SubDeviceInfo> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_SubDeviceInfo>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_SubDeviceInfo> CreateDF_SubDeviceInfo(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    uint8_t id = 0,
    ::flatbuffers::Offset<MECData::DF_Position3D> pos = 0,
    ::flatbuffers::Offset<MECData::DF_Mileage> mileage = 0,
    ::flatbuffers::Offset<MECData::DF_DeviceDescription> description = 0,
    MECData::DF_DeviceSpecificInfo specifics_type = MECData::DF_DeviceSpecificInfo_NONE,
    ::flatbuffers::Offset<void> specifics = 0,
    ::flatbuffers::Offset<MECData::DF_DeviceAbnormal> status = 0,
    ::flatbuffers::Offset<MECData::DF_PacketFlowRecord> packet_stats = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_ModulePerformance>>> performance = 0) {
  DF_SubDeviceInfoBuilder builder_(_fbb);
  builder_.add_performance(performance);
  builder_.add_packet_stats(packet_stats);
  builder_.add_status(status);
  builder_.add_specifics(specifics);
  builder_.add_description(description);
  builder_.add_mileage(mileage);
  builder_.add_pos(pos);
  builder_.add_specifics_type(specifics_type);
  builder_.add_id(id);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DF_SubDeviceInfo> CreateDF_SubDeviceInfoDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    uint8_t id = 0,
    ::flatbuffers::Offset<MECData::DF_Position3D> pos = 0,
    ::flatbuffers::Offset<MECData::DF_Mileage> mileage = 0,
    ::flatbuffers::Offset<MECData::DF_DeviceDescription> description = 0,
    MECData::DF_DeviceSpecificInfo specifics_type = MECData::DF_DeviceSpecificInfo_NONE,
    ::flatbuffers::Offset<void> specifics = 0,
    ::flatbuffers::Offset<MECData::DF_DeviceAbnormal> status = 0,
    ::flatbuffers::Offset<MECData::DF_PacketFlowRecord> packet_stats = 0,
    const std::vector<::flatbuffers::Offset<MECData::DF_ModulePerformance>> *performance = nullptr) {
  auto performance__ = performance ? _fbb.CreateVector<::flatbuffers::Offset<MECData::DF_ModulePerformance>>(*performance) : 0;
  return MECData::CreateDF_SubDeviceInfo(
      _fbb,
      id,
      pos,
      mileage,
      description,
      specifics_type,
      specifics,
      status,
      packet_stats,
      performance__);
}

struct DF_DeviceInfo FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_DeviceInfoBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_SUB_DEVICES = 4
  };
  const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_SubDeviceInfo>> *sub_devices() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_SubDeviceInfo>> *>(VT_SUB_DEVICES);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_SUB_DEVICES) &&
           verifier.VerifyVector(sub_devices()) &&
           verifier.VerifyVectorOfTables(sub_devices()) &&
           verifier.EndTable();
  }
};

struct DF_DeviceInfoBuilder {
  typedef DF_DeviceInfo Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_sub_devices(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_SubDeviceInfo>>> sub_devices) {
    fbb_.AddOffset(DF_DeviceInfo::VT_SUB_DEVICES, sub_devices);
  }
  explicit DF_DeviceInfoBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_DeviceInfo> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_DeviceInfo>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_DeviceInfo> CreateDF_DeviceInfo(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_SubDeviceInfo>>> sub_devices = 0) {
  DF_DeviceInfoBuilder builder_(_fbb);
  builder_.add_sub_devices(sub_devices);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DF_DeviceInfo> CreateDF_DeviceInfoDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const std::vector<::flatbuffers::Offset<MECData::DF_SubDeviceInfo>> *sub_devices = nullptr) {
  auto sub_devices__ = sub_devices ? _fbb.CreateVector<::flatbuffers::Offset<MECData::DF_SubDeviceInfo>>(*sub_devices) : 0;
  return MECData::CreateDF_DeviceInfo(
      _fbb,
      sub_devices__);
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_DEVICEINFO_MECDATA_H_
