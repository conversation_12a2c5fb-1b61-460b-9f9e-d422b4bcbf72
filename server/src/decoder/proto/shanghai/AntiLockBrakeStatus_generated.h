// automatically generated by the <PERSON>Buffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_ANTILOCKBRAKESTATUS_MECDATA_H_
#define FLATBUFFERS_GENERATED_ANTILOCKBRAKESTATUS_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

namespace MECData {

enum DE_AntiLockBrakeStatus : int8_t {
  DE_AntiLockBrakeStatus_unavailable = 0,
  DE_AntiLockBrakeStatus_off = 1,
  DE_AntiLockBrakeStatus_on = 2,
  DE_AntiLockBrakeStatus_engaged = 3,
  DE_AntiLockBrakeStatus_MIN = DE_AntiLockBrakeStatus_unavailable,
  DE_AntiLockBrakeStatus_MAX = DE_AntiLockBrakeStatus_engaged
};

inline const DE_AntiLockBrakeStatus (&EnumValuesDE_AntiLockBrakeStatus())[4] {
  static const DE_AntiLockBrakeStatus values[] = {
    DE_AntiLockBrakeStatus_unavailable,
    DE_AntiLockBrakeStatus_off,
    DE_AntiLockBrakeStatus_on,
    DE_AntiLockBrakeStatus_engaged
  };
  return values;
}

inline const char * const *EnumNamesDE_AntiLockBrakeStatus() {
  static const char * const names[5] = {
    "unavailable",
    "off",
    "on",
    "engaged",
    nullptr
  };
  return names;
}

inline const char *EnumNameDE_AntiLockBrakeStatus(DE_AntiLockBrakeStatus e) {
  if (::flatbuffers::IsOutRange(e, DE_AntiLockBrakeStatus_unavailable, DE_AntiLockBrakeStatus_engaged)) return "";
  const size_t index = static_cast<size_t>(e);
  return EnumNamesDE_AntiLockBrakeStatus()[index];
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_ANTILOCKBRAKESTATUS_MECDATA_H_
