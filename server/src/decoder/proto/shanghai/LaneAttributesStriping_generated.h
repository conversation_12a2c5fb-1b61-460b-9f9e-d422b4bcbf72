// automatically generated by the <PERSON>Buffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_LANEATTRIBUTESSTRIPING_MECDATA_H_
#define FLATBUFFERS_GENERATED_LANEATTRIBUTESSTRIPING_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

namespace MECData {

struct DF_LaneAttributesStriping;
struct DF_LaneAttributesStripingBuilder;

struct DF_LaneAttributesStriping FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_LaneAttributesStripingBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_STRIPING = 4
  };
  int8_t striping() const {
    return GetField<int8_t>(VT_STRIPING, 0);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<int8_t>(verifier, VT_STRIPING, 1) &&
           verifier.EndTable();
  }
};

struct DF_LaneAttributesStripingBuilder {
  typedef DF_LaneAttributesStriping Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_striping(int8_t striping) {
    fbb_.AddElement<int8_t>(DF_LaneAttributesStriping::VT_STRIPING, striping, 0);
  }
  explicit DF_LaneAttributesStripingBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_LaneAttributesStriping> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_LaneAttributesStriping>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_LaneAttributesStriping> CreateDF_LaneAttributesStriping(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    int8_t striping = 0) {
  DF_LaneAttributesStripingBuilder builder_(_fbb);
  builder_.add_striping(striping);
  return builder_.Finish();
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_LANEATTRIBUTESSTRIPING_MECDATA_H_
