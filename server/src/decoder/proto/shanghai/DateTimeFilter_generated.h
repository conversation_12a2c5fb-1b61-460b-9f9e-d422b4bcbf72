// automatically generated by the FlatBuffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_DATETIMEFILTER_MECDATA_H_
#define FLATBUFFERS_GENERATED_DATETIMEFILTER_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

#include "LocalTimePoint_generated.h"
#include "Month_generated.h"
#include "Weekday_generated.h"

namespace MECData {

struct DF_DateTimeFilter;
struct DF_DateTimeFilterBuilder;

struct DF_DateTimeFilter FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_DateTimeFilterBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_MONTH_FILTER = 4,
    VT_DAY_FILTER = 6,
    VT_WEEKDAY_FILTER = 8,
    VT_FROM_TIME_POINT = 10,
    VT_TO_TIME_POINT = 12
  };
  const ::flatbuffers::Vector<uint8_t> *month_filter() const {
    return GetPointer<const ::flatbuffers::Vector<uint8_t> *>(VT_MONTH_FILTER);
  }
  const ::flatbuffers::Vector<uint8_t> *day_filter() const {
    return GetPointer<const ::flatbuffers::Vector<uint8_t> *>(VT_DAY_FILTER);
  }
  const ::flatbuffers::Vector<uint8_t> *weekday_filter() const {
    return GetPointer<const ::flatbuffers::Vector<uint8_t> *>(VT_WEEKDAY_FILTER);
  }
  const MECData::DF_LocalTimePoint *from_time_point() const {
    return GetPointer<const MECData::DF_LocalTimePoint *>(VT_FROM_TIME_POINT);
  }
  const MECData::DF_LocalTimePoint *to_time_point() const {
    return GetPointer<const MECData::DF_LocalTimePoint *>(VT_TO_TIME_POINT);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_MONTH_FILTER) &&
           verifier.VerifyVector(month_filter()) &&
           VerifyOffset(verifier, VT_DAY_FILTER) &&
           verifier.VerifyVector(day_filter()) &&
           VerifyOffset(verifier, VT_WEEKDAY_FILTER) &&
           verifier.VerifyVector(weekday_filter()) &&
           VerifyOffset(verifier, VT_FROM_TIME_POINT) &&
           verifier.VerifyTable(from_time_point()) &&
           VerifyOffset(verifier, VT_TO_TIME_POINT) &&
           verifier.VerifyTable(to_time_point()) &&
           verifier.EndTable();
  }
};

struct DF_DateTimeFilterBuilder {
  typedef DF_DateTimeFilter Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_month_filter(::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> month_filter) {
    fbb_.AddOffset(DF_DateTimeFilter::VT_MONTH_FILTER, month_filter);
  }
  void add_day_filter(::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> day_filter) {
    fbb_.AddOffset(DF_DateTimeFilter::VT_DAY_FILTER, day_filter);
  }
  void add_weekday_filter(::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> weekday_filter) {
    fbb_.AddOffset(DF_DateTimeFilter::VT_WEEKDAY_FILTER, weekday_filter);
  }
  void add_from_time_point(::flatbuffers::Offset<MECData::DF_LocalTimePoint> from_time_point) {
    fbb_.AddOffset(DF_DateTimeFilter::VT_FROM_TIME_POINT, from_time_point);
  }
  void add_to_time_point(::flatbuffers::Offset<MECData::DF_LocalTimePoint> to_time_point) {
    fbb_.AddOffset(DF_DateTimeFilter::VT_TO_TIME_POINT, to_time_point);
  }
  explicit DF_DateTimeFilterBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_DateTimeFilter> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_DateTimeFilter>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_DateTimeFilter> CreateDF_DateTimeFilter(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> month_filter = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> day_filter = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> weekday_filter = 0,
    ::flatbuffers::Offset<MECData::DF_LocalTimePoint> from_time_point = 0,
    ::flatbuffers::Offset<MECData::DF_LocalTimePoint> to_time_point = 0) {
  DF_DateTimeFilterBuilder builder_(_fbb);
  builder_.add_to_time_point(to_time_point);
  builder_.add_from_time_point(from_time_point);
  builder_.add_weekday_filter(weekday_filter);
  builder_.add_day_filter(day_filter);
  builder_.add_month_filter(month_filter);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DF_DateTimeFilter> CreateDF_DateTimeFilterDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const std::vector<uint8_t> *month_filter = nullptr,
    const std::vector<uint8_t> *day_filter = nullptr,
    const std::vector<uint8_t> *weekday_filter = nullptr,
    ::flatbuffers::Offset<MECData::DF_LocalTimePoint> from_time_point = 0,
    ::flatbuffers::Offset<MECData::DF_LocalTimePoint> to_time_point = 0) {
  auto month_filter__ = month_filter ? _fbb.CreateVector<uint8_t>(*month_filter) : 0;
  auto day_filter__ = day_filter ? _fbb.CreateVector<uint8_t>(*day_filter) : 0;
  auto weekday_filter__ = weekday_filter ? _fbb.CreateVector<uint8_t>(*weekday_filter) : 0;
  return MECData::CreateDF_DateTimeFilter(
      _fbb,
      month_filter__,
      day_filter__,
      weekday_filter__,
      from_time_point,
      to_time_point);
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_DATETIMEFILTER_MECDATA_H_
