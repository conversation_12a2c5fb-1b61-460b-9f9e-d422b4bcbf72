// automatically generated by the FlatBuffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_AUXILIARYBRAKESTATUS_MECDATA_H_
#define FLATBUFFERS_GENERATED_AUXILIARYBRAKESTATUS_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

namespace MECData {

enum DE_AuxiliaryBrakeStatus : int8_t {
  DE_AuxiliaryBrakeStatus_unavailable = 0,
  DE_AuxiliaryBrakeStatus_off = 1,
  DE_AuxiliaryBrakeStatus_on = 2,
  DE_AuxiliaryBrakeStatus_reserved = 3,
  DE_AuxiliaryBrakeStatus_MIN = DE_AuxiliaryBrakeStatus_unavailable,
  DE_AuxiliaryBrakeStatus_MAX = DE_AuxiliaryBrakeStatus_reserved
};

inline const DE_AuxiliaryBrakeStatus (&EnumValuesDE_AuxiliaryBrakeStatus())[4] {
  static const DE_AuxiliaryBrakeStatus values[] = {
    DE_AuxiliaryBrakeStatus_unavailable,
    DE_AuxiliaryBrakeStatus_off,
    DE_AuxiliaryBrakeStatus_on,
    DE_AuxiliaryBrakeStatus_reserved
  };
  return values;
}

inline const char * const *EnumNamesDE_AuxiliaryBrakeStatus() {
  static const char * const names[5] = {
    "unavailable",
    "off",
    "on",
    "reserved",
    nullptr
  };
  return names;
}

inline const char *EnumNameDE_AuxiliaryBrakeStatus(DE_AuxiliaryBrakeStatus e) {
  if (::flatbuffers::IsOutRange(e, DE_AuxiliaryBrakeStatus_unavailable, DE_AuxiliaryBrakeStatus_reserved)) return "";
  const size_t index = static_cast<size_t>(e);
  return EnumNamesDE_AuxiliaryBrakeStatus()[index];
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_AUXILIARYBRAKESTATUS_MECDATA_H_
