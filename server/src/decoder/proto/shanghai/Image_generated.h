// automatically generated by the <PERSON>Buffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_IMAGE_MECDATA_H_
#define FLATBUFFERS_GENERATED_IMAGE_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

namespace MECData {

struct DF_ImageURL;
struct DF_ImageURLBuilder;

struct DF_VideoClipURL;
struct DF_VideoClipURLBuilder;

struct DF_ImageBytes;
struct DF_ImageBytesBuilder;

struct DF_Image;
struct DF_ImageBuilder;

enum DE_ImageFormat : uint8_t {
  DE_ImageFormat_JPG = 0,
  DE_ImageFormat_PNG = 1,
  DE_ImageFormat_TIFF = 2,
  DE_ImageFormat_BMP = 3,
  DE_ImageFormat_GIF = 4,
  DE_ImageFormat_MIN = DE_ImageFormat_JPG,
  DE_ImageFormat_MAX = DE_ImageFormat_GIF
};

inline const DE_ImageFormat (&EnumValuesDE_ImageFormat())[5] {
  static const DE_ImageFormat values[] = {
    DE_ImageFormat_JPG,
    DE_ImageFormat_PNG,
    DE_ImageFormat_TIFF,
    DE_ImageFormat_BMP,
    DE_ImageFormat_GIF
  };
  return values;
}

inline const char * const *EnumNamesDE_ImageFormat() {
  static const char * const names[6] = {
    "JPG",
    "PNG",
    "TIFF",
    "BMP",
    "GIF",
    nullptr
  };
  return names;
}

inline const char *EnumNameDE_ImageFormat(DE_ImageFormat e) {
  if (::flatbuffers::IsOutRange(e, DE_ImageFormat_JPG, DE_ImageFormat_GIF)) return "";
  const size_t index = static_cast<size_t>(e);
  return EnumNamesDE_ImageFormat()[index];
}

enum DF_ImageContent : uint8_t {
  DF_ImageContent_NONE = 0,
  DF_ImageContent_DF_ImageURL = 1,
  DF_ImageContent_DF_VideoClipURL = 2,
  DF_ImageContent_DF_ImageBytes = 3,
  DF_ImageContent_MIN = DF_ImageContent_NONE,
  DF_ImageContent_MAX = DF_ImageContent_DF_ImageBytes
};

inline const DF_ImageContent (&EnumValuesDF_ImageContent())[4] {
  static const DF_ImageContent values[] = {
    DF_ImageContent_NONE,
    DF_ImageContent_DF_ImageURL,
    DF_ImageContent_DF_VideoClipURL,
    DF_ImageContent_DF_ImageBytes
  };
  return values;
}

inline const char * const *EnumNamesDF_ImageContent() {
  static const char * const names[5] = {
    "NONE",
    "DF_ImageURL",
    "DF_VideoClipURL",
    "DF_ImageBytes",
    nullptr
  };
  return names;
}

inline const char *EnumNameDF_ImageContent(DF_ImageContent e) {
  if (::flatbuffers::IsOutRange(e, DF_ImageContent_NONE, DF_ImageContent_DF_ImageBytes)) return "";
  const size_t index = static_cast<size_t>(e);
  return EnumNamesDF_ImageContent()[index];
}

template<typename T> struct DF_ImageContentTraits {
  static const DF_ImageContent enum_value = DF_ImageContent_NONE;
};

template<> struct DF_ImageContentTraits<MECData::DF_ImageURL> {
  static const DF_ImageContent enum_value = DF_ImageContent_DF_ImageURL;
};

template<> struct DF_ImageContentTraits<MECData::DF_VideoClipURL> {
  static const DF_ImageContent enum_value = DF_ImageContent_DF_VideoClipURL;
};

template<> struct DF_ImageContentTraits<MECData::DF_ImageBytes> {
  static const DF_ImageContent enum_value = DF_ImageContent_DF_ImageBytes;
};

bool VerifyDF_ImageContent(::flatbuffers::Verifier &verifier, const void *obj, DF_ImageContent type);
bool VerifyDF_ImageContentVector(::flatbuffers::Verifier &verifier, const ::flatbuffers::Vector<::flatbuffers::Offset<void>> *values, const ::flatbuffers::Vector<uint8_t> *types);

struct DF_ImageURL FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_ImageURLBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_URL = 4
  };
  const ::flatbuffers::String *url() const {
    return GetPointer<const ::flatbuffers::String *>(VT_URL);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_URL) &&
           verifier.VerifyString(url()) &&
           verifier.EndTable();
  }
};

struct DF_ImageURLBuilder {
  typedef DF_ImageURL Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_url(::flatbuffers::Offset<::flatbuffers::String> url) {
    fbb_.AddOffset(DF_ImageURL::VT_URL, url);
  }
  explicit DF_ImageURLBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_ImageURL> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_ImageURL>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_ImageURL> CreateDF_ImageURL(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::String> url = 0) {
  DF_ImageURLBuilder builder_(_fbb);
  builder_.add_url(url);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DF_ImageURL> CreateDF_ImageURLDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const char *url = nullptr) {
  auto url__ = url ? _fbb.CreateString(url) : 0;
  return MECData::CreateDF_ImageURL(
      _fbb,
      url__);
}

struct DF_VideoClipURL FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_VideoClipURLBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_URL = 4
  };
  const ::flatbuffers::String *url() const {
    return GetPointer<const ::flatbuffers::String *>(VT_URL);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_URL) &&
           verifier.VerifyString(url()) &&
           verifier.EndTable();
  }
};

struct DF_VideoClipURLBuilder {
  typedef DF_VideoClipURL Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_url(::flatbuffers::Offset<::flatbuffers::String> url) {
    fbb_.AddOffset(DF_VideoClipURL::VT_URL, url);
  }
  explicit DF_VideoClipURLBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_VideoClipURL> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_VideoClipURL>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_VideoClipURL> CreateDF_VideoClipURL(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::String> url = 0) {
  DF_VideoClipURLBuilder builder_(_fbb);
  builder_.add_url(url);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DF_VideoClipURL> CreateDF_VideoClipURLDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const char *url = nullptr) {
  auto url__ = url ? _fbb.CreateString(url) : 0;
  return MECData::CreateDF_VideoClipURL(
      _fbb,
      url__);
}

struct DF_ImageBytes FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_ImageBytesBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_FORMAT = 4,
    VT_BYTES = 6
  };
  MECData::DE_ImageFormat format() const {
    return static_cast<MECData::DE_ImageFormat>(GetField<uint8_t>(VT_FORMAT, 0));
  }
  const ::flatbuffers::Vector<uint8_t> *bytes() const {
    return GetPointer<const ::flatbuffers::Vector<uint8_t> *>(VT_BYTES);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<uint8_t>(verifier, VT_FORMAT, 1) &&
           VerifyOffset(verifier, VT_BYTES) &&
           verifier.VerifyVector(bytes()) &&
           verifier.EndTable();
  }
};

struct DF_ImageBytesBuilder {
  typedef DF_ImageBytes Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_format(MECData::DE_ImageFormat format) {
    fbb_.AddElement<uint8_t>(DF_ImageBytes::VT_FORMAT, static_cast<uint8_t>(format), 0);
  }
  void add_bytes(::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> bytes) {
    fbb_.AddOffset(DF_ImageBytes::VT_BYTES, bytes);
  }
  explicit DF_ImageBytesBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_ImageBytes> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_ImageBytes>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_ImageBytes> CreateDF_ImageBytes(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    MECData::DE_ImageFormat format = MECData::DE_ImageFormat_JPG,
    ::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> bytes = 0) {
  DF_ImageBytesBuilder builder_(_fbb);
  builder_.add_bytes(bytes);
  builder_.add_format(format);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DF_ImageBytes> CreateDF_ImageBytesDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    MECData::DE_ImageFormat format = MECData::DE_ImageFormat_JPG,
    const std::vector<uint8_t> *bytes = nullptr) {
  auto bytes__ = bytes ? _fbb.CreateVector<uint8_t>(*bytes) : 0;
  return MECData::CreateDF_ImageBytes(
      _fbb,
      format,
      bytes__);
}

struct DF_Image FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_ImageBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_ID = 4,
    VT_TIMESTAMP = 6,
    VT_DESCRIPTION = 8,
    VT_CONTENT_TYPE = 10,
    VT_CONTENT = 12
  };
  uint16_t id() const {
    return GetField<uint16_t>(VT_ID, 0);
  }
  uint64_t timestamp() const {
    return GetField<uint64_t>(VT_TIMESTAMP, 0);
  }
  const ::flatbuffers::String *description() const {
    return GetPointer<const ::flatbuffers::String *>(VT_DESCRIPTION);
  }
  MECData::DF_ImageContent content_type() const {
    return static_cast<MECData::DF_ImageContent>(GetField<uint8_t>(VT_CONTENT_TYPE, 0));
  }
  const void *content() const {
    return GetPointer<const void *>(VT_CONTENT);
  }
  template<typename T> const T *content_as() const;
  const MECData::DF_ImageURL *content_as_DF_ImageURL() const {
    return content_type() == MECData::DF_ImageContent_DF_ImageURL ? static_cast<const MECData::DF_ImageURL *>(content()) : nullptr;
  }
  const MECData::DF_VideoClipURL *content_as_DF_VideoClipURL() const {
    return content_type() == MECData::DF_ImageContent_DF_VideoClipURL ? static_cast<const MECData::DF_VideoClipURL *>(content()) : nullptr;
  }
  const MECData::DF_ImageBytes *content_as_DF_ImageBytes() const {
    return content_type() == MECData::DF_ImageContent_DF_ImageBytes ? static_cast<const MECData::DF_ImageBytes *>(content()) : nullptr;
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<uint16_t>(verifier, VT_ID, 2) &&
           VerifyField<uint64_t>(verifier, VT_TIMESTAMP, 8) &&
           VerifyOffset(verifier, VT_DESCRIPTION) &&
           verifier.VerifyString(description()) &&
           VerifyField<uint8_t>(verifier, VT_CONTENT_TYPE, 1) &&
           VerifyOffset(verifier, VT_CONTENT) &&
           VerifyDF_ImageContent(verifier, content(), content_type()) &&
           verifier.EndTable();
  }
};

template<> inline const MECData::DF_ImageURL *DF_Image::content_as<MECData::DF_ImageURL>() const {
  return content_as_DF_ImageURL();
}

template<> inline const MECData::DF_VideoClipURL *DF_Image::content_as<MECData::DF_VideoClipURL>() const {
  return content_as_DF_VideoClipURL();
}

template<> inline const MECData::DF_ImageBytes *DF_Image::content_as<MECData::DF_ImageBytes>() const {
  return content_as_DF_ImageBytes();
}

struct DF_ImageBuilder {
  typedef DF_Image Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_id(uint16_t id) {
    fbb_.AddElement<uint16_t>(DF_Image::VT_ID, id, 0);
  }
  void add_timestamp(uint64_t timestamp) {
    fbb_.AddElement<uint64_t>(DF_Image::VT_TIMESTAMP, timestamp, 0);
  }
  void add_description(::flatbuffers::Offset<::flatbuffers::String> description) {
    fbb_.AddOffset(DF_Image::VT_DESCRIPTION, description);
  }
  void add_content_type(MECData::DF_ImageContent content_type) {
    fbb_.AddElement<uint8_t>(DF_Image::VT_CONTENT_TYPE, static_cast<uint8_t>(content_type), 0);
  }
  void add_content(::flatbuffers::Offset<void> content) {
    fbb_.AddOffset(DF_Image::VT_CONTENT, content);
  }
  explicit DF_ImageBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_Image> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_Image>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_Image> CreateDF_Image(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    uint16_t id = 0,
    uint64_t timestamp = 0,
    ::flatbuffers::Offset<::flatbuffers::String> description = 0,
    MECData::DF_ImageContent content_type = MECData::DF_ImageContent_NONE,
    ::flatbuffers::Offset<void> content = 0) {
  DF_ImageBuilder builder_(_fbb);
  builder_.add_timestamp(timestamp);
  builder_.add_content(content);
  builder_.add_description(description);
  builder_.add_id(id);
  builder_.add_content_type(content_type);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DF_Image> CreateDF_ImageDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    uint16_t id = 0,
    uint64_t timestamp = 0,
    const char *description = nullptr,
    MECData::DF_ImageContent content_type = MECData::DF_ImageContent_NONE,
    ::flatbuffers::Offset<void> content = 0) {
  auto description__ = description ? _fbb.CreateString(description) : 0;
  return MECData::CreateDF_Image(
      _fbb,
      id,
      timestamp,
      description__,
      content_type,
      content);
}

inline bool VerifyDF_ImageContent(::flatbuffers::Verifier &verifier, const void *obj, DF_ImageContent type) {
  switch (type) {
    case DF_ImageContent_NONE: {
      return true;
    }
    case DF_ImageContent_DF_ImageURL: {
      auto ptr = reinterpret_cast<const MECData::DF_ImageURL *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case DF_ImageContent_DF_VideoClipURL: {
      auto ptr = reinterpret_cast<const MECData::DF_VideoClipURL *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case DF_ImageContent_DF_ImageBytes: {
      auto ptr = reinterpret_cast<const MECData::DF_ImageBytes *>(obj);
      return verifier.VerifyTable(ptr);
    }
    default: return true;
  }
}

inline bool VerifyDF_ImageContentVector(::flatbuffers::Verifier &verifier, const ::flatbuffers::Vector<::flatbuffers::Offset<void>> *values, const ::flatbuffers::Vector<uint8_t> *types) {
  if (!values || !types) return !values && !types;
  if (values->size() != types->size()) return false;
  for (::flatbuffers::uoffset_t i = 0; i < values->size(); ++i) {
    if (!VerifyDF_ImageContent(
        verifier,  values->Get(i), types->GetEnum<DF_ImageContent>(i))) {
      return false;
    }
  }
  return true;
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_IMAGE_MECDATA_H_
