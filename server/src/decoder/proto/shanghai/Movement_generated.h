// automatically generated by the <PERSON>Buffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_MOVEMENT_MECDATA_H_
#define FLATBUFFERS_GENERATED_MOVEMENT_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

#include "NodeReferenceID_generated.h"

namespace MECData {

struct DF_Movement;
struct DF_MovementBuilder;

struct DF_Movement FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_MovementBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_REMOTEINTERSECTION = 4,
    VT_PHASEID = 6
  };
  const MECData::DF_NodeReferenceID *remoteIntersection() const {
    return GetPointer<const MECData::DF_NodeReferenceID *>(VT_REMOTEINTERSECTION);
  }
  uint8_t phaseId() const {
    return GetField<uint8_t>(VT_PHASEID, 0);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffsetRequired(verifier, VT_REMOTEINTERSECTION) &&
           verifier.VerifyTable(remoteIntersection()) &&
           VerifyField<uint8_t>(verifier, VT_PHASEID, 1) &&
           verifier.EndTable();
  }
};

struct DF_MovementBuilder {
  typedef DF_Movement Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_remoteIntersection(::flatbuffers::Offset<MECData::DF_NodeReferenceID> remoteIntersection) {
    fbb_.AddOffset(DF_Movement::VT_REMOTEINTERSECTION, remoteIntersection);
  }
  void add_phaseId(uint8_t phaseId) {
    fbb_.AddElement<uint8_t>(DF_Movement::VT_PHASEID, phaseId, 0);
  }
  explicit DF_MovementBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_Movement> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_Movement>(end);
    fbb_.Required(o, DF_Movement::VT_REMOTEINTERSECTION);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_Movement> CreateDF_Movement(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<MECData::DF_NodeReferenceID> remoteIntersection = 0,
    uint8_t phaseId = 0) {
  DF_MovementBuilder builder_(_fbb);
  builder_.add_remoteIntersection(remoteIntersection);
  builder_.add_phaseId(phaseId);
  return builder_.Finish();
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_MOVEMENT_MECDATA_H_
