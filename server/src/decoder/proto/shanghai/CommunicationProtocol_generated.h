// automatically generated by the FlatBuffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_COMMUNICATIONPROTOCOL_MECDATA_H_
#define FLATBUFFERS_GENERATED_COMMUNICATIONPROTOCOL_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

namespace MECData {

enum DE_CommunicationProtocol : int8_t {
  DE_CommunicationProtocol_MQTT = 0,
  DE_CommunicationProtocol_UDP = 1,
  DE_CommunicationProtocol_USOCK = 2,
  DE_CommunicationProtocol_TCP = 3,
  DE_CommunicationProtocol_MIN = DE_CommunicationProtocol_MQTT,
  DE_CommunicationProtocol_MAX = DE_CommunicationProtocol_TCP
};

inline const DE_CommunicationProtocol (&EnumValuesDE_CommunicationProtocol())[4] {
  static const DE_CommunicationProtocol values[] = {
    DE_CommunicationProtocol_MQTT,
    DE_CommunicationProtocol_UDP,
    DE_CommunicationProtocol_USOCK,
    DE_CommunicationProtocol_TCP
  };
  return values;
}

inline const char * const *EnumNamesDE_CommunicationProtocol() {
  static const char * const names[5] = {
    "MQTT",
    "UDP",
    "USOCK",
    "TCP",
    nullptr
  };
  return names;
}

inline const char *EnumNameDE_CommunicationProtocol(DE_CommunicationProtocol e) {
  if (::flatbuffers::IsOutRange(e, DE_CommunicationProtocol_MQTT, DE_CommunicationProtocol_TCP)) return "";
  const size_t index = static_cast<size_t>(e);
  return EnumNamesDE_CommunicationProtocol()[index];
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_COMMUNICATIONPROTOCOL_MECDATA_H_
