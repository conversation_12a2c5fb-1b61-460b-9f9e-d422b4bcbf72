// automatically generated by the <PERSON>Buffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_LANEATTRIBUTESCROSSWALK_MECDATA_H_
#define FLATBUFFERS_GENERATED_LANEATTRIBUTESCROSSWALK_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

namespace MECData {

struct DF_LaneAttributesCrosswalk;
struct DF_LaneAttributesCrosswalkBuilder;

struct DF_LaneAttributesCrosswalk FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_LaneAttributesCrosswalkBuilder Builder;
  enum <PERSON>BuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_CROSSWALK = 4
  };
  int16_t crosswalk() const {
    return GetField<int16_t>(VT_CROSSWALK, 0);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<int16_t>(verifier, VT_CROSSWALK, 2) &&
           verifier.EndTable();
  }
};

struct DF_LaneAttributesCrosswalkBuilder {
  typedef DF_LaneAttributesCrosswalk Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_crosswalk(int16_t crosswalk) {
    fbb_.AddElement<int16_t>(DF_LaneAttributesCrosswalk::VT_CROSSWALK, crosswalk, 0);
  }
  explicit DF_LaneAttributesCrosswalkBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_LaneAttributesCrosswalk> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_LaneAttributesCrosswalk>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_LaneAttributesCrosswalk> CreateDF_LaneAttributesCrosswalk(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    int16_t crosswalk = 0) {
  DF_LaneAttributesCrosswalkBuilder builder_(_fbb);
  builder_.add_crosswalk(crosswalk);
  return builder_.Finish();
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_LANEATTRIBUTESCROSSWALK_MECDATA_H_
