// automatically generated by the FlatBuffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_NODETHRESHACTIVE_MECDATA_H_
#define FLATBUFFERS_GENERATED_NODETHRESHACTIVE_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

#include "NodeReferenceID_generated.h"
#include "ThreshActive_generated.h"

namespace MECData {

struct MSG_NodeThreshActive;
struct MSG_NodeThreshActiveBuilder;

struct MSG_NodeThreshActive FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef MSG_NodeThreshActiveBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_TIMESTAMP = 4,
    VT_NODE_ID = 6,
    VT_THRESH_ACTIVE_RESULTS = 8
  };
  int64_t timestamp() const {
    return GetField<int64_t>(VT_TIMESTAMP, 0);
  }
  const MECData::DF_NodeReferenceID *node_id() const {
    return GetPointer<const MECData::DF_NodeReferenceID *>(VT_NODE_ID);
  }
  const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_ThreshActive>> *thresh_active_results() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_ThreshActive>> *>(VT_THRESH_ACTIVE_RESULTS);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<int64_t>(verifier, VT_TIMESTAMP, 8) &&
           VerifyOffset(verifier, VT_NODE_ID) &&
           verifier.VerifyTable(node_id()) &&
           VerifyOffset(verifier, VT_THRESH_ACTIVE_RESULTS) &&
           verifier.VerifyVector(thresh_active_results()) &&
           verifier.VerifyVectorOfTables(thresh_active_results()) &&
           verifier.EndTable();
  }
};

struct MSG_NodeThreshActiveBuilder {
  typedef MSG_NodeThreshActive Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_timestamp(int64_t timestamp) {
    fbb_.AddElement<int64_t>(MSG_NodeThreshActive::VT_TIMESTAMP, timestamp, 0);
  }
  void add_node_id(::flatbuffers::Offset<MECData::DF_NodeReferenceID> node_id) {
    fbb_.AddOffset(MSG_NodeThreshActive::VT_NODE_ID, node_id);
  }
  void add_thresh_active_results(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_ThreshActive>>> thresh_active_results) {
    fbb_.AddOffset(MSG_NodeThreshActive::VT_THRESH_ACTIVE_RESULTS, thresh_active_results);
  }
  explicit MSG_NodeThreshActiveBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<MSG_NodeThreshActive> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<MSG_NodeThreshActive>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<MSG_NodeThreshActive> CreateMSG_NodeThreshActive(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    int64_t timestamp = 0,
    ::flatbuffers::Offset<MECData::DF_NodeReferenceID> node_id = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_ThreshActive>>> thresh_active_results = 0) {
  MSG_NodeThreshActiveBuilder builder_(_fbb);
  builder_.add_timestamp(timestamp);
  builder_.add_thresh_active_results(thresh_active_results);
  builder_.add_node_id(node_id);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<MSG_NodeThreshActive> CreateMSG_NodeThreshActiveDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    int64_t timestamp = 0,
    ::flatbuffers::Offset<MECData::DF_NodeReferenceID> node_id = 0,
    const std::vector<::flatbuffers::Offset<MECData::DF_ThreshActive>> *thresh_active_results = nullptr) {
  auto thresh_active_results__ = thresh_active_results ? _fbb.CreateVector<::flatbuffers::Offset<MECData::DF_ThreshActive>>(*thresh_active_results) : 0;
  return MECData::CreateMSG_NodeThreshActive(
      _fbb,
      timestamp,
      node_id,
      thresh_active_results__);
}

inline const MECData::MSG_NodeThreshActive *GetMSG_NodeThreshActive(const void *buf) {
  return ::flatbuffers::GetRoot<MECData::MSG_NodeThreshActive>(buf);
}

inline const MECData::MSG_NodeThreshActive *GetSizePrefixedMSG_NodeThreshActive(const void *buf) {
  return ::flatbuffers::GetSizePrefixedRoot<MECData::MSG_NodeThreshActive>(buf);
}

inline bool VerifyMSG_NodeThreshActiveBuffer(
    ::flatbuffers::Verifier &verifier) {
  return verifier.VerifyBuffer<MECData::MSG_NodeThreshActive>(nullptr);
}

inline bool VerifySizePrefixedMSG_NodeThreshActiveBuffer(
    ::flatbuffers::Verifier &verifier) {
  return verifier.VerifySizePrefixedBuffer<MECData::MSG_NodeThreshActive>(nullptr);
}

inline void FinishMSG_NodeThreshActiveBuffer(
    ::flatbuffers::FlatBufferBuilder &fbb,
    ::flatbuffers::Offset<MECData::MSG_NodeThreshActive> root) {
  fbb.Finish(root);
}

inline void FinishSizePrefixedMSG_NodeThreshActiveBuffer(
    ::flatbuffers::FlatBufferBuilder &fbb,
    ::flatbuffers::Offset<MECData::MSG_NodeThreshActive> root) {
  fbb.FinishSizePrefixed(root);
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_NODETHRESHACTIVE_MECDATA_H_
