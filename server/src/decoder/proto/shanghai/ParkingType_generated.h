// automatically generated by the <PERSON>Buffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_PARKINGTYPE_MECDATA_H_
#define FLATBUFFERS_GENERATED_PARKINGTYPE_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

namespace MECData {

struct DE_ParkingType;
struct DE_ParkingTypeBuilder;

struct DE_ParkingType FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DE_ParkingTypeBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_PARKINGTYPE = 4
  };
  int16_t parkingType() const {
    return GetField<int16_t>(VT_PARKINGTYPE, 0);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<int16_t>(verifier, VT_PARKINGTYPE, 2) &&
           verifier.EndTable();
  }
};

struct DE_ParkingTypeBuilder {
  typedef DE_ParkingType Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_parkingType(int16_t parkingType) {
    fbb_.AddElement<int16_t>(DE_ParkingType::VT_PARKINGTYPE, parkingType, 0);
  }
  explicit DE_ParkingTypeBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DE_ParkingType> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DE_ParkingType>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DE_ParkingType> CreateDE_ParkingType(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    int16_t parkingType = 0) {
  DE_ParkingTypeBuilder builder_(_fbb);
  builder_.add_parkingType(parkingType);
  return builder_.Finish();
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_PARKINGTYPE_MECDATA_H_
