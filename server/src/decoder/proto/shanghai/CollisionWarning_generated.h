// automatically generated by the <PERSON>Buffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_COLLISIONWARNING_MECDATA_H_
#define FLATBUFFERS_GENERATED_COLLISIONWARNING_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

#include "BasicVehicleClass_generated.h"
#include "NodeReferenceID_generated.h"
#include "Position3D_generated.h"
#include "PositionOffsetLLV_generated.h"

namespace MECData {

struct DE_CriticalityMetricTimeHeadway;
struct DE_CriticalityMetricTimeHeadwayBuilder;

struct DE_CriticalityMetricTimeToCollision;
struct DE_CriticalityMetricTimeToCollisionBuilder;

struct DE_CriticalityMetricPostEncroachmentTime;
struct DE_CriticalityMetricPostEncroachmentTimeBuilder;

struct DF_CollisionWarningParticipant;
struct DF_CollisionWarningParticipantBuilder;

struct MSG_CollisionWarning;
struct MSG_CollisionWarningBuilder;

enum DE_CriticalityMetric : uint8_t {
  DE_CriticalityMetric_NONE = 0,
  DE_CriticalityMetric_DE_CriticalityMetricTimeHeadway = 1,
  DE_CriticalityMetric_DE_CriticalityMetricTimeToCollision = 2,
  DE_CriticalityMetric_DE_CriticalityMetricPostEncroachmentTime = 3,
  DE_CriticalityMetric_MIN = DE_CriticalityMetric_NONE,
  DE_CriticalityMetric_MAX = DE_CriticalityMetric_DE_CriticalityMetricPostEncroachmentTime
};

inline const DE_CriticalityMetric (&EnumValuesDE_CriticalityMetric())[4] {
  static const DE_CriticalityMetric values[] = {
    DE_CriticalityMetric_NONE,
    DE_CriticalityMetric_DE_CriticalityMetricTimeHeadway,
    DE_CriticalityMetric_DE_CriticalityMetricTimeToCollision,
    DE_CriticalityMetric_DE_CriticalityMetricPostEncroachmentTime
  };
  return values;
}

inline const char * const *EnumNamesDE_CriticalityMetric() {
  static const char * const names[5] = {
    "NONE",
    "DE_CriticalityMetricTimeHeadway",
    "DE_CriticalityMetricTimeToCollision",
    "DE_CriticalityMetricPostEncroachmentTime",
    nullptr
  };
  return names;
}

inline const char *EnumNameDE_CriticalityMetric(DE_CriticalityMetric e) {
  if (::flatbuffers::IsOutRange(e, DE_CriticalityMetric_NONE, DE_CriticalityMetric_DE_CriticalityMetricPostEncroachmentTime)) return "";
  const size_t index = static_cast<size_t>(e);
  return EnumNamesDE_CriticalityMetric()[index];
}

template<typename T> struct DE_CriticalityMetricTraits {
  static const DE_CriticalityMetric enum_value = DE_CriticalityMetric_NONE;
};

template<> struct DE_CriticalityMetricTraits<MECData::DE_CriticalityMetricTimeHeadway> {
  static const DE_CriticalityMetric enum_value = DE_CriticalityMetric_DE_CriticalityMetricTimeHeadway;
};

template<> struct DE_CriticalityMetricTraits<MECData::DE_CriticalityMetricTimeToCollision> {
  static const DE_CriticalityMetric enum_value = DE_CriticalityMetric_DE_CriticalityMetricTimeToCollision;
};

template<> struct DE_CriticalityMetricTraits<MECData::DE_CriticalityMetricPostEncroachmentTime> {
  static const DE_CriticalityMetric enum_value = DE_CriticalityMetric_DE_CriticalityMetricPostEncroachmentTime;
};

bool VerifyDE_CriticalityMetric(::flatbuffers::Verifier &verifier, const void *obj, DE_CriticalityMetric type);
bool VerifyDE_CriticalityMetricVector(::flatbuffers::Verifier &verifier, const ::flatbuffers::Vector<::flatbuffers::Offset<void>> *values, const ::flatbuffers::Vector<uint8_t> *types);

struct DE_CriticalityMetricTimeHeadway FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DE_CriticalityMetricTimeHeadwayBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_H = 4
  };
  int32_t h() const {
    return GetField<int32_t>(VT_H, 0);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<int32_t>(verifier, VT_H, 4) &&
           verifier.EndTable();
  }
};

struct DE_CriticalityMetricTimeHeadwayBuilder {
  typedef DE_CriticalityMetricTimeHeadway Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_h(int32_t h) {
    fbb_.AddElement<int32_t>(DE_CriticalityMetricTimeHeadway::VT_H, h, 0);
  }
  explicit DE_CriticalityMetricTimeHeadwayBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DE_CriticalityMetricTimeHeadway> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DE_CriticalityMetricTimeHeadway>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DE_CriticalityMetricTimeHeadway> CreateDE_CriticalityMetricTimeHeadway(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    int32_t h = 0) {
  DE_CriticalityMetricTimeHeadwayBuilder builder_(_fbb);
  builder_.add_h(h);
  return builder_.Finish();
}

struct DE_CriticalityMetricTimeToCollision FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DE_CriticalityMetricTimeToCollisionBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_TTC = 4
  };
  int32_t ttc() const {
    return GetField<int32_t>(VT_TTC, 0);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<int32_t>(verifier, VT_TTC, 4) &&
           verifier.EndTable();
  }
};

struct DE_CriticalityMetricTimeToCollisionBuilder {
  typedef DE_CriticalityMetricTimeToCollision Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_ttc(int32_t ttc) {
    fbb_.AddElement<int32_t>(DE_CriticalityMetricTimeToCollision::VT_TTC, ttc, 0);
  }
  explicit DE_CriticalityMetricTimeToCollisionBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DE_CriticalityMetricTimeToCollision> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DE_CriticalityMetricTimeToCollision>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DE_CriticalityMetricTimeToCollision> CreateDE_CriticalityMetricTimeToCollision(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    int32_t ttc = 0) {
  DE_CriticalityMetricTimeToCollisionBuilder builder_(_fbb);
  builder_.add_ttc(ttc);
  return builder_.Finish();
}

struct DE_CriticalityMetricPostEncroachmentTime FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DE_CriticalityMetricPostEncroachmentTimeBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_PET = 4
  };
  int32_t pet() const {
    return GetField<int32_t>(VT_PET, 0);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<int32_t>(verifier, VT_PET, 4) &&
           verifier.EndTable();
  }
};

struct DE_CriticalityMetricPostEncroachmentTimeBuilder {
  typedef DE_CriticalityMetricPostEncroachmentTime Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_pet(int32_t pet) {
    fbb_.AddElement<int32_t>(DE_CriticalityMetricPostEncroachmentTime::VT_PET, pet, 0);
  }
  explicit DE_CriticalityMetricPostEncroachmentTimeBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DE_CriticalityMetricPostEncroachmentTime> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DE_CriticalityMetricPostEncroachmentTime>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DE_CriticalityMetricPostEncroachmentTime> CreateDE_CriticalityMetricPostEncroachmentTime(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    int32_t pet = 0) {
  DE_CriticalityMetricPostEncroachmentTimeBuilder builder_(_fbb);
  builder_.add_pet(pet);
  return builder_.Finish();
}

struct DF_CollisionWarningParticipant FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_CollisionWarningParticipantBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_REFER_POS = 4,
    VT_SPEED = 6,
    VT_PTC_ID = 8,
    VT_PTC_TYPE = 10,
    VT_VEH_CLASS = 12,
    VT_OBU_ID = 14,
    VT_CRITICALITY_TYPE = 16,
    VT_CRITICALITY = 18
  };
  const MECData::DF_PositionOffsetLLV *refer_pos() const {
    return GetPointer<const MECData::DF_PositionOffsetLLV *>(VT_REFER_POS);
  }
  uint16_t speed() const {
    return GetField<uint16_t>(VT_SPEED, 0);
  }
  uint16_t ptc_id() const {
    return GetField<uint16_t>(VT_PTC_ID, 0);
  }
  uint8_t ptc_type() const {
    return GetField<uint8_t>(VT_PTC_TYPE, 0);
  }
  MECData::DE_BasicVehicleClass veh_class() const {
    return static_cast<MECData::DE_BasicVehicleClass>(GetField<uint8_t>(VT_VEH_CLASS, 0));
  }
  const ::flatbuffers::Vector<uint8_t> *obu_id() const {
    return GetPointer<const ::flatbuffers::Vector<uint8_t> *>(VT_OBU_ID);
  }
  const ::flatbuffers::Vector<uint8_t> *criticality_type() const {
    return GetPointer<const ::flatbuffers::Vector<uint8_t> *>(VT_CRITICALITY_TYPE);
  }
  const ::flatbuffers::Vector<::flatbuffers::Offset<void>> *criticality() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<void>> *>(VT_CRITICALITY);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_REFER_POS) &&
           verifier.VerifyTable(refer_pos()) &&
           VerifyField<uint16_t>(verifier, VT_SPEED, 2) &&
           VerifyField<uint16_t>(verifier, VT_PTC_ID, 2) &&
           VerifyField<uint8_t>(verifier, VT_PTC_TYPE, 1) &&
           VerifyField<uint8_t>(verifier, VT_VEH_CLASS, 1) &&
           VerifyOffset(verifier, VT_OBU_ID) &&
           verifier.VerifyVector(obu_id()) &&
           VerifyOffset(verifier, VT_CRITICALITY_TYPE) &&
           verifier.VerifyVector(criticality_type()) &&
           VerifyOffset(verifier, VT_CRITICALITY) &&
           verifier.VerifyVector(criticality()) &&
           VerifyDE_CriticalityMetricVector(verifier, criticality(), criticality_type()) &&
           verifier.EndTable();
  }
};

struct DF_CollisionWarningParticipantBuilder {
  typedef DF_CollisionWarningParticipant Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_refer_pos(::flatbuffers::Offset<MECData::DF_PositionOffsetLLV> refer_pos) {
    fbb_.AddOffset(DF_CollisionWarningParticipant::VT_REFER_POS, refer_pos);
  }
  void add_speed(uint16_t speed) {
    fbb_.AddElement<uint16_t>(DF_CollisionWarningParticipant::VT_SPEED, speed, 0);
  }
  void add_ptc_id(uint16_t ptc_id) {
    fbb_.AddElement<uint16_t>(DF_CollisionWarningParticipant::VT_PTC_ID, ptc_id, 0);
  }
  void add_ptc_type(uint8_t ptc_type) {
    fbb_.AddElement<uint8_t>(DF_CollisionWarningParticipant::VT_PTC_TYPE, ptc_type, 0);
  }
  void add_veh_class(MECData::DE_BasicVehicleClass veh_class) {
    fbb_.AddElement<uint8_t>(DF_CollisionWarningParticipant::VT_VEH_CLASS, static_cast<uint8_t>(veh_class), 0);
  }
  void add_obu_id(::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> obu_id) {
    fbb_.AddOffset(DF_CollisionWarningParticipant::VT_OBU_ID, obu_id);
  }
  void add_criticality_type(::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> criticality_type) {
    fbb_.AddOffset(DF_CollisionWarningParticipant::VT_CRITICALITY_TYPE, criticality_type);
  }
  void add_criticality(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<void>>> criticality) {
    fbb_.AddOffset(DF_CollisionWarningParticipant::VT_CRITICALITY, criticality);
  }
  explicit DF_CollisionWarningParticipantBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_CollisionWarningParticipant> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_CollisionWarningParticipant>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_CollisionWarningParticipant> CreateDF_CollisionWarningParticipant(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<MECData::DF_PositionOffsetLLV> refer_pos = 0,
    uint16_t speed = 0,
    uint16_t ptc_id = 0,
    uint8_t ptc_type = 0,
    MECData::DE_BasicVehicleClass veh_class = MECData::DE_BasicVehicleClass_unknownVehicleClass,
    ::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> obu_id = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> criticality_type = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<void>>> criticality = 0) {
  DF_CollisionWarningParticipantBuilder builder_(_fbb);
  builder_.add_criticality(criticality);
  builder_.add_criticality_type(criticality_type);
  builder_.add_obu_id(obu_id);
  builder_.add_refer_pos(refer_pos);
  builder_.add_ptc_id(ptc_id);
  builder_.add_speed(speed);
  builder_.add_veh_class(veh_class);
  builder_.add_ptc_type(ptc_type);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DF_CollisionWarningParticipant> CreateDF_CollisionWarningParticipantDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<MECData::DF_PositionOffsetLLV> refer_pos = 0,
    uint16_t speed = 0,
    uint16_t ptc_id = 0,
    uint8_t ptc_type = 0,
    MECData::DE_BasicVehicleClass veh_class = MECData::DE_BasicVehicleClass_unknownVehicleClass,
    const std::vector<uint8_t> *obu_id = nullptr,
    const std::vector<uint8_t> *criticality_type = nullptr,
    const std::vector<::flatbuffers::Offset<void>> *criticality = nullptr) {
  auto obu_id__ = obu_id ? _fbb.CreateVector<uint8_t>(*obu_id) : 0;
  auto criticality_type__ = criticality_type ? _fbb.CreateVector<uint8_t>(*criticality_type) : 0;
  auto criticality__ = criticality ? _fbb.CreateVector<::flatbuffers::Offset<void>>(*criticality) : 0;
  return MECData::CreateDF_CollisionWarningParticipant(
      _fbb,
      refer_pos,
      speed,
      ptc_id,
      ptc_type,
      veh_class,
      obu_id__,
      criticality_type__,
      criticality__);
}

struct MSG_CollisionWarning FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef MSG_CollisionWarningBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_MEC_ID = 4,
    VT_TIME = 6,
    VT_NODE = 8,
    VT_POS = 10,
    VT_SPEED = 12,
    VT_PTC_ID = 14,
    VT_PTC_TYPE = 16,
    VT_VEH_CLASS = 18,
    VT_OBU_ID = 20,
    VT_PARTS = 22,
    VT_MSG_ID = 24
  };
  const ::flatbuffers::String *mec_id() const {
    return GetPointer<const ::flatbuffers::String *>(VT_MEC_ID);
  }
  uint64_t time() const {
    return GetField<uint64_t>(VT_TIME, 0);
  }
  const MECData::DF_NodeReferenceID *node() const {
    return GetPointer<const MECData::DF_NodeReferenceID *>(VT_NODE);
  }
  const MECData::DF_Position3D *pos() const {
    return GetPointer<const MECData::DF_Position3D *>(VT_POS);
  }
  uint16_t speed() const {
    return GetField<uint16_t>(VT_SPEED, 0);
  }
  uint16_t ptc_id() const {
    return GetField<uint16_t>(VT_PTC_ID, 0);
  }
  uint8_t ptc_type() const {
    return GetField<uint8_t>(VT_PTC_TYPE, 0);
  }
  MECData::DE_BasicVehicleClass veh_class() const {
    return static_cast<MECData::DE_BasicVehicleClass>(GetField<uint8_t>(VT_VEH_CLASS, 0));
  }
  const ::flatbuffers::Vector<uint8_t> *obu_id() const {
    return GetPointer<const ::flatbuffers::Vector<uint8_t> *>(VT_OBU_ID);
  }
  const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_CollisionWarningParticipant>> *parts() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_CollisionWarningParticipant>> *>(VT_PARTS);
  }
  int64_t msg_id() const {
    return GetField<int64_t>(VT_MSG_ID, 0);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_MEC_ID) &&
           verifier.VerifyString(mec_id()) &&
           VerifyField<uint64_t>(verifier, VT_TIME, 8) &&
           VerifyOffset(verifier, VT_NODE) &&
           verifier.VerifyTable(node()) &&
           VerifyOffsetRequired(verifier, VT_POS) &&
           verifier.VerifyTable(pos()) &&
           VerifyField<uint16_t>(verifier, VT_SPEED, 2) &&
           VerifyField<uint16_t>(verifier, VT_PTC_ID, 2) &&
           VerifyField<uint8_t>(verifier, VT_PTC_TYPE, 1) &&
           VerifyField<uint8_t>(verifier, VT_VEH_CLASS, 1) &&
           VerifyOffset(verifier, VT_OBU_ID) &&
           verifier.VerifyVector(obu_id()) &&
           VerifyOffset(verifier, VT_PARTS) &&
           verifier.VerifyVector(parts()) &&
           verifier.VerifyVectorOfTables(parts()) &&
           VerifyField<int64_t>(verifier, VT_MSG_ID, 8) &&
           verifier.EndTable();
  }
};

struct MSG_CollisionWarningBuilder {
  typedef MSG_CollisionWarning Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_mec_id(::flatbuffers::Offset<::flatbuffers::String> mec_id) {
    fbb_.AddOffset(MSG_CollisionWarning::VT_MEC_ID, mec_id);
  }
  void add_time(uint64_t time) {
    fbb_.AddElement<uint64_t>(MSG_CollisionWarning::VT_TIME, time, 0);
  }
  void add_node(::flatbuffers::Offset<MECData::DF_NodeReferenceID> node) {
    fbb_.AddOffset(MSG_CollisionWarning::VT_NODE, node);
  }
  void add_pos(::flatbuffers::Offset<MECData::DF_Position3D> pos) {
    fbb_.AddOffset(MSG_CollisionWarning::VT_POS, pos);
  }
  void add_speed(uint16_t speed) {
    fbb_.AddElement<uint16_t>(MSG_CollisionWarning::VT_SPEED, speed, 0);
  }
  void add_ptc_id(uint16_t ptc_id) {
    fbb_.AddElement<uint16_t>(MSG_CollisionWarning::VT_PTC_ID, ptc_id, 0);
  }
  void add_ptc_type(uint8_t ptc_type) {
    fbb_.AddElement<uint8_t>(MSG_CollisionWarning::VT_PTC_TYPE, ptc_type, 0);
  }
  void add_veh_class(MECData::DE_BasicVehicleClass veh_class) {
    fbb_.AddElement<uint8_t>(MSG_CollisionWarning::VT_VEH_CLASS, static_cast<uint8_t>(veh_class), 0);
  }
  void add_obu_id(::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> obu_id) {
    fbb_.AddOffset(MSG_CollisionWarning::VT_OBU_ID, obu_id);
  }
  void add_parts(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_CollisionWarningParticipant>>> parts) {
    fbb_.AddOffset(MSG_CollisionWarning::VT_PARTS, parts);
  }
  void add_msg_id(int64_t msg_id) {
    fbb_.AddElement<int64_t>(MSG_CollisionWarning::VT_MSG_ID, msg_id, 0);
  }
  explicit MSG_CollisionWarningBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<MSG_CollisionWarning> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<MSG_CollisionWarning>(end);
    fbb_.Required(o, MSG_CollisionWarning::VT_POS);
    return o;
  }
};

inline ::flatbuffers::Offset<MSG_CollisionWarning> CreateMSG_CollisionWarning(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::String> mec_id = 0,
    uint64_t time = 0,
    ::flatbuffers::Offset<MECData::DF_NodeReferenceID> node = 0,
    ::flatbuffers::Offset<MECData::DF_Position3D> pos = 0,
    uint16_t speed = 0,
    uint16_t ptc_id = 0,
    uint8_t ptc_type = 0,
    MECData::DE_BasicVehicleClass veh_class = MECData::DE_BasicVehicleClass_unknownVehicleClass,
    ::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> obu_id = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_CollisionWarningParticipant>>> parts = 0,
    int64_t msg_id = 0) {
  MSG_CollisionWarningBuilder builder_(_fbb);
  builder_.add_msg_id(msg_id);
  builder_.add_time(time);
  builder_.add_parts(parts);
  builder_.add_obu_id(obu_id);
  builder_.add_pos(pos);
  builder_.add_node(node);
  builder_.add_mec_id(mec_id);
  builder_.add_ptc_id(ptc_id);
  builder_.add_speed(speed);
  builder_.add_veh_class(veh_class);
  builder_.add_ptc_type(ptc_type);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<MSG_CollisionWarning> CreateMSG_CollisionWarningDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const char *mec_id = nullptr,
    uint64_t time = 0,
    ::flatbuffers::Offset<MECData::DF_NodeReferenceID> node = 0,
    ::flatbuffers::Offset<MECData::DF_Position3D> pos = 0,
    uint16_t speed = 0,
    uint16_t ptc_id = 0,
    uint8_t ptc_type = 0,
    MECData::DE_BasicVehicleClass veh_class = MECData::DE_BasicVehicleClass_unknownVehicleClass,
    const std::vector<uint8_t> *obu_id = nullptr,
    const std::vector<::flatbuffers::Offset<MECData::DF_CollisionWarningParticipant>> *parts = nullptr,
    int64_t msg_id = 0) {
  auto mec_id__ = mec_id ? _fbb.CreateString(mec_id) : 0;
  auto obu_id__ = obu_id ? _fbb.CreateVector<uint8_t>(*obu_id) : 0;
  auto parts__ = parts ? _fbb.CreateVector<::flatbuffers::Offset<MECData::DF_CollisionWarningParticipant>>(*parts) : 0;
  return MECData::CreateMSG_CollisionWarning(
      _fbb,
      mec_id__,
      time,
      node,
      pos,
      speed,
      ptc_id,
      ptc_type,
      veh_class,
      obu_id__,
      parts__,
      msg_id);
}

inline bool VerifyDE_CriticalityMetric(::flatbuffers::Verifier &verifier, const void *obj, DE_CriticalityMetric type) {
  switch (type) {
    case DE_CriticalityMetric_NONE: {
      return true;
    }
    case DE_CriticalityMetric_DE_CriticalityMetricTimeHeadway: {
      auto ptr = reinterpret_cast<const MECData::DE_CriticalityMetricTimeHeadway *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case DE_CriticalityMetric_DE_CriticalityMetricTimeToCollision: {
      auto ptr = reinterpret_cast<const MECData::DE_CriticalityMetricTimeToCollision *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case DE_CriticalityMetric_DE_CriticalityMetricPostEncroachmentTime: {
      auto ptr = reinterpret_cast<const MECData::DE_CriticalityMetricPostEncroachmentTime *>(obj);
      return verifier.VerifyTable(ptr);
    }
    default: return true;
  }
}

inline bool VerifyDE_CriticalityMetricVector(::flatbuffers::Verifier &verifier, const ::flatbuffers::Vector<::flatbuffers::Offset<void>> *values, const ::flatbuffers::Vector<uint8_t> *types) {
  if (!values || !types) return !values && !types;
  if (values->size() != types->size()) return false;
  for (::flatbuffers::uoffset_t i = 0; i < values->size(); ++i) {
    if (!VerifyDE_CriticalityMetric(
        verifier,  values->Get(i), types->GetEnum<DE_CriticalityMetric>(i))) {
      return false;
    }
  }
  return true;
}

inline const MECData::MSG_CollisionWarning *GetMSG_CollisionWarning(const void *buf) {
  return ::flatbuffers::GetRoot<MECData::MSG_CollisionWarning>(buf);
}

inline const MECData::MSG_CollisionWarning *GetSizePrefixedMSG_CollisionWarning(const void *buf) {
  return ::flatbuffers::GetSizePrefixedRoot<MECData::MSG_CollisionWarning>(buf);
}

inline bool VerifyMSG_CollisionWarningBuffer(
    ::flatbuffers::Verifier &verifier) {
  return verifier.VerifyBuffer<MECData::MSG_CollisionWarning>(nullptr);
}

inline bool VerifySizePrefixedMSG_CollisionWarningBuffer(
    ::flatbuffers::Verifier &verifier) {
  return verifier.VerifySizePrefixedBuffer<MECData::MSG_CollisionWarning>(nullptr);
}

inline void FinishMSG_CollisionWarningBuffer(
    ::flatbuffers::FlatBufferBuilder &fbb,
    ::flatbuffers::Offset<MECData::MSG_CollisionWarning> root) {
  fbb.Finish(root);
}

inline void FinishSizePrefixedMSG_CollisionWarningBuffer(
    ::flatbuffers::FlatBufferBuilder &fbb,
    ::flatbuffers::Offset<MECData::MSG_CollisionWarning> root) {
  fbb.FinishSizePrefixed(root);
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_COLLISIONWARNING_MECDATA_H_
