// automatically generated by the FlatBuffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_CONNECTIONEX_MECDATA_H_
#define FLATBUFFERS_GENERATED_CONNECTIONEX_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

#include "ConnectingLaneEx_generated.h"
#include "Maneuver_generated.h"
#include "NodeReferenceID_generated.h"
#include "SignalWaitingLane_generated.h"

namespace MECData {

struct DF_ConnectionEx;
struct DF_ConnectionExBuilder;

struct DF_ConnectionEx FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_ConnectionExBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_REMOTEINTERSECTION = 4,
    VT_SWL = 6,
    VT_CONNECTINGLANE = 8,
    VT_PHASEID = 10,
    VT_TURNDIRECTION = 12,
    VT_EXT_ID = 14
  };
  const MECData::DF_NodeReferenceID *remoteIntersection() const {
    return GetPointer<const MECData::DF_NodeReferenceID *>(VT_REMOTEINTERSECTION);
  }
  const MECData::DF_SingleWaitingLane *swl() const {
    return GetPointer<const MECData::DF_SingleWaitingLane *>(VT_SWL);
  }
  const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_ConnectingLaneEx>> *connectingLane() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_ConnectingLaneEx>> *>(VT_CONNECTINGLANE);
  }
  uint8_t phaseID() const {
    return GetField<uint8_t>(VT_PHASEID, 0);
  }
  MECData::DE_Maneuver turnDirection() const {
    return static_cast<MECData::DE_Maneuver>(GetField<uint8_t>(VT_TURNDIRECTION, 0));
  }
  const ::flatbuffers::String *ext_id() const {
    return GetPointer<const ::flatbuffers::String *>(VT_EXT_ID);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_REMOTEINTERSECTION) &&
           verifier.VerifyTable(remoteIntersection()) &&
           VerifyOffset(verifier, VT_SWL) &&
           verifier.VerifyTable(swl()) &&
           VerifyOffset(verifier, VT_CONNECTINGLANE) &&
           verifier.VerifyVector(connectingLane()) &&
           verifier.VerifyVectorOfTables(connectingLane()) &&
           VerifyField<uint8_t>(verifier, VT_PHASEID, 1) &&
           VerifyField<uint8_t>(verifier, VT_TURNDIRECTION, 1) &&
           VerifyOffset(verifier, VT_EXT_ID) &&
           verifier.VerifyString(ext_id()) &&
           verifier.EndTable();
  }
};

struct DF_ConnectionExBuilder {
  typedef DF_ConnectionEx Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_remoteIntersection(::flatbuffers::Offset<MECData::DF_NodeReferenceID> remoteIntersection) {
    fbb_.AddOffset(DF_ConnectionEx::VT_REMOTEINTERSECTION, remoteIntersection);
  }
  void add_swl(::flatbuffers::Offset<MECData::DF_SingleWaitingLane> swl) {
    fbb_.AddOffset(DF_ConnectionEx::VT_SWL, swl);
  }
  void add_connectingLane(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_ConnectingLaneEx>>> connectingLane) {
    fbb_.AddOffset(DF_ConnectionEx::VT_CONNECTINGLANE, connectingLane);
  }
  void add_phaseID(uint8_t phaseID) {
    fbb_.AddElement<uint8_t>(DF_ConnectionEx::VT_PHASEID, phaseID, 0);
  }
  void add_turnDirection(MECData::DE_Maneuver turnDirection) {
    fbb_.AddElement<uint8_t>(DF_ConnectionEx::VT_TURNDIRECTION, static_cast<uint8_t>(turnDirection), 0);
  }
  void add_ext_id(::flatbuffers::Offset<::flatbuffers::String> ext_id) {
    fbb_.AddOffset(DF_ConnectionEx::VT_EXT_ID, ext_id);
  }
  explicit DF_ConnectionExBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_ConnectionEx> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_ConnectionEx>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_ConnectionEx> CreateDF_ConnectionEx(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<MECData::DF_NodeReferenceID> remoteIntersection = 0,
    ::flatbuffers::Offset<MECData::DF_SingleWaitingLane> swl = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_ConnectingLaneEx>>> connectingLane = 0,
    uint8_t phaseID = 0,
    MECData::DE_Maneuver turnDirection = MECData::DE_Maneuver_maneuverStraight,
    ::flatbuffers::Offset<::flatbuffers::String> ext_id = 0) {
  DF_ConnectionExBuilder builder_(_fbb);
  builder_.add_ext_id(ext_id);
  builder_.add_connectingLane(connectingLane);
  builder_.add_swl(swl);
  builder_.add_remoteIntersection(remoteIntersection);
  builder_.add_turnDirection(turnDirection);
  builder_.add_phaseID(phaseID);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DF_ConnectionEx> CreateDF_ConnectionExDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<MECData::DF_NodeReferenceID> remoteIntersection = 0,
    ::flatbuffers::Offset<MECData::DF_SingleWaitingLane> swl = 0,
    const std::vector<::flatbuffers::Offset<MECData::DF_ConnectingLaneEx>> *connectingLane = nullptr,
    uint8_t phaseID = 0,
    MECData::DE_Maneuver turnDirection = MECData::DE_Maneuver_maneuverStraight,
    const char *ext_id = nullptr) {
  auto connectingLane__ = connectingLane ? _fbb.CreateVector<::flatbuffers::Offset<MECData::DF_ConnectingLaneEx>>(*connectingLane) : 0;
  auto ext_id__ = ext_id ? _fbb.CreateString(ext_id) : 0;
  return MECData::CreateDF_ConnectionEx(
      _fbb,
      remoteIntersection,
      swl,
      connectingLane__,
      phaseID,
      turnDirection,
      ext_id__);
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_CONNECTIONEX_MECDATA_H_
