// automatically generated by the <PERSON>Buffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_OBJECTSIZE_MECDATA_H_
#define FLATBUFFERS_GENERATED_OBJECTSIZE_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

namespace MECData {

struct DF_ObjectSize;
struct DF_ObjectSizeBuilder;

struct DF_ObjectSize FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_ObjectSizeBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_WIDTH = 4,
    VT_LENGTH = 6,
    VT_HEIGHT = 8
  };
  uint16_t width() const {
    return GetField<uint16_t>(VT_WIDTH, 0);
  }
  uint16_t length() const {
    return GetField<uint16_t>(VT_LENGTH, 0);
  }
  uint16_t height() const {
    return GetField<uint16_t>(VT_HEIGHT, 0);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<uint16_t>(verifier, VT_WIDTH, 2) &&
           VerifyField<uint16_t>(verifier, VT_LENGTH, 2) &&
           VerifyField<uint16_t>(verifier, VT_HEIGHT, 2) &&
           verifier.EndTable();
  }
};

struct DF_ObjectSizeBuilder {
  typedef DF_ObjectSize Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_width(uint16_t width) {
    fbb_.AddElement<uint16_t>(DF_ObjectSize::VT_WIDTH, width, 0);
  }
  void add_length(uint16_t length) {
    fbb_.AddElement<uint16_t>(DF_ObjectSize::VT_LENGTH, length, 0);
  }
  void add_height(uint16_t height) {
    fbb_.AddElement<uint16_t>(DF_ObjectSize::VT_HEIGHT, height, 0);
  }
  explicit DF_ObjectSizeBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_ObjectSize> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_ObjectSize>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_ObjectSize> CreateDF_ObjectSize(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    uint16_t width = 0,
    uint16_t length = 0,
    uint16_t height = 0) {
  DF_ObjectSizeBuilder builder_(_fbb);
  builder_.add_height(height);
  builder_.add_length(length);
  builder_.add_width(width);
  return builder_.Finish();
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_OBJECTSIZE_MECDATA_H_
