// automatically generated by the <PERSON>Buffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_INTERCEPTEDPACKET_MECDATA_H_
#define FLATBUFFERS_GENERATED_INTERCEPTEDPACKET_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

#include "AppInfo_generated.h"
#include "AppOTA_generated.h"
#include "CarbonEmission_generated.h"
#include "CollisionWarning_generated.h"
#include "DedicatedLaneControl_generated.h"
#include "DensityLane_generated.h"
#include "ErrorCollection_generated.h"
#include "ErrorImmediateReport_generated.h"
#include "ForecastRain_generated.h"
#include "ForecastWeather_generated.h"
#include "MECSysRes_generated.h"
#include "Map_generated.h"
#include "NowWeather_generated.h"
#include "PlatooningManagementMessage_generated.h"
#include "QueueLength_generated.h"
#include "RampMetering_generated.h"
#include "RoadSideInformation_generated.h"
#include "RoadsideCoordination_generated.h"
#include "RoadsideSafetyMessage_generated.h"
#include "RootOTA_generated.h"
#include "RoutePlan_generated.h"
#include "SafetyMessage_generated.h"
#include "SignalExecution_generated.h"
#include "SignalPhaseAndTiming_generated.h"
#include "SignalRequest_generated.h"
#include "SignalScheme_generated.h"
#include "SpeedGuide_generated.h"
#include "SpeedLaneArea_generated.h"
#include "SpeedLanePoint_generated.h"
#include "Ticket_generated.h"
#include "TrafficEvent_generated.h"
#include "TrafficFlow_generated.h"
#include "TrafficSign_generated.h"
#include "TrajectoryPoints_generated.h"
#include "VariableMessageSign_generated.h"
#include "VariableSpeedLimit_generated.h"
#include "VehIntentionAndRequest_generated.h"
#include "VolumeLane_generated.h"
#include "WildCard_generated.h"
#include "ZBRSFeatures_generated.h"

namespace MECData {

struct DF_AppCustomizedPacket;
struct DF_AppCustomizedPacketBuilder;

struct DF_AppInfoPacket;
struct DF_AppInfoPacketBuilder;

struct DF_ErrorImmediateReportPacket;
struct DF_ErrorImmediateReportPacketBuilder;

struct DF_ErrorCollectionPacket;
struct DF_ErrorCollectionPacketBuilder;

struct DF_RootOTAPacket;
struct DF_RootOTAPacketBuilder;

struct DF_AppOTAPacket;
struct DF_AppOTAPacketBuilder;

struct DF_VolumeLanePacket;
struct DF_VolumeLanePacketBuilder;

struct DF_DensityLanePacket;
struct DF_DensityLanePacketBuilder;

struct DF_SpeedLaneAreaPacket;
struct DF_SpeedLaneAreaPacketBuilder;

struct DF_SpeedLanePointPacket;
struct DF_SpeedLanePointPacketBuilder;

struct DF_TrafficSignPacket;
struct DF_TrafficSignPacketBuilder;

struct DF_TrafficEventPacket;
struct DF_TrafficEventPacketBuilder;

struct DF_SafetyMessagePacket;
struct DF_SafetyMessagePacketBuilder;

struct DF_SignalPhaseAndTimingPacket;
struct DF_SignalPhaseAndTimingPacketBuilder;

struct DF_MapPacket;
struct DF_MapPacketBuilder;

struct DF_RoadSideCoordinationPacket;
struct DF_RoadSideCoordinationPacketBuilder;

struct DF_RoadSideInformationPacket;
struct DF_RoadSideInformationPacketBuilder;

struct DF_RoadsideSafetyMessagePacket;
struct DF_RoadsideSafetyMessagePacketBuilder;

struct DF_VehIntentionAndRequestPacket;
struct DF_VehIntentionAndRequestPacketBuilder;

struct DF_DedicatedLaneControlPacket;
struct DF_DedicatedLaneControlPacketBuilder;

struct DF_VariableSpeedLimitPacket;
struct DF_VariableSpeedLimitPacketBuilder;

struct DF_RampMeteringPacket;
struct DF_RampMeteringPacketBuilder;

struct DF_NowWeatherPacket;
struct DF_NowWeatherPacketBuilder;

struct DF_ForecastWeatherPacket;
struct DF_ForecastWeatherPacketBuilder;

struct DF_ForecastRainPacket;
struct DF_ForecastRainPacketBuilder;

struct DF_SignalSchemePacket;
struct DF_SignalSchemePacketBuilder;

struct DF_TrafficFlowPacket;
struct DF_TrafficFlowPacketBuilder;

struct DF_TrajectoryPointsPacket;
struct DF_TrajectoryPointsPacketBuilder;

struct DF_QueueLengthPacket;
struct DF_QueueLengthPacketBuilder;

struct DF_VariableMessageSignPacket;
struct DF_VariableMessageSignPacketBuilder;

struct DF_CarbonEmissionPacket;
struct DF_CarbonEmissionPacketBuilder;

struct DF_SignalExecutionPacket;
struct DF_SignalExecutionPacketBuilder;

struct DF_SignalRequestPacket;
struct DF_SignalRequestPacketBuilder;

struct DF_CollisionWarningPacket;
struct DF_CollisionWarningPacketBuilder;

struct DF_SpeedGuidePacket;
struct DF_SpeedGuidePacketBuilder;

struct DF_RoutePlanPacket;
struct DF_RoutePlanPacketBuilder;

struct DF_PlatooningManagementMessagenPacket;
struct DF_PlatooningManagementMessagenPacketBuilder;

struct DF_TicketPacket;
struct DF_TicketPacketBuilder;

struct DF_WildCardPacket;
struct DF_WildCardPacketBuilder;

struct DF_ZBRSFeaturesPacket;
struct DF_ZBRSFeaturesPacketBuilder;

struct DF_MECSysResPacket;
struct DF_MECSysResPacketBuilder;

struct MSG_InterceptedPacket;
struct MSG_InterceptedPacketBuilder;

enum DF_PacketContent : uint8_t {
  DF_PacketContent_NONE = 0,
  DF_PacketContent_DF_AppInfoPacket = 1,
  DF_PacketContent_DF_ErrorImmediateReportPacket = 2,
  DF_PacketContent_DF_RootOTAPacket = 3,
  DF_PacketContent_DF_AppOTAPacket = 4,
  DF_PacketContent_DF_VolumeLanePacket = 5,
  DF_PacketContent_DF_DensityLanePacket = 6,
  DF_PacketContent_DF_SpeedLaneAreaPacket = 7,
  DF_PacketContent_DF_SpeedLanePointPacket = 8,
  DF_PacketContent_DF_TrafficSignPacket = 9,
  DF_PacketContent_DF_TrafficEventPacket = 10,
  DF_PacketContent_DF_SafetyMessagePacket = 11,
  DF_PacketContent_DF_SignalPhaseAndTimingPacket = 12,
  DF_PacketContent_DF_MapPacket = 13,
  DF_PacketContent_DF_RoadSideCoordinationPacket = 14,
  DF_PacketContent_DF_RoadSideInformationPacket = 15,
  DF_PacketContent_DF_RoadsideSafetyMessagePacket = 16,
  DF_PacketContent_DF_VehIntentionAndRequestPacket = 17,
  DF_PacketContent_DF_DedicatedLaneControlPacket = 18,
  DF_PacketContent_DF_VariableSpeedLimitPacket = 19,
  DF_PacketContent_DF_RampMeteringPacket = 20,
  DF_PacketContent_DF_NowWeatherPacket = 21,
  DF_PacketContent_DF_ForecastWeatherPacket = 22,
  DF_PacketContent_DF_ForecastRainPacket = 23,
  DF_PacketContent_DF_SignalSchemePacket = 24,
  DF_PacketContent_DF_TrafficFlowPacket = 25,
  DF_PacketContent_DF_TrajectoryPointsPacket = 26,
  DF_PacketContent_DF_QueueLengthPacket = 27,
  DF_PacketContent_DF_VariableMessageSignPacket = 28,
  DF_PacketContent_DF_CarbonEmissionPacket = 29,
  DF_PacketContent_DF_SignalExecutionPacket = 30,
  DF_PacketContent_DF_SignalRequestPacket = 31,
  DF_PacketContent_DF_CollisionWarningPacket = 32,
  DF_PacketContent_DF_SpeedGuidePacket = 33,
  DF_PacketContent_DF_RoutePlanPacket = 34,
  DF_PacketContent_DF_PlatooningManagementMessagenPacket = 35,
  DF_PacketContent_DF_TicketPacket = 36,
  DF_PacketContent_DF_WildCardPacket = 37,
  DF_PacketContent_DF_ZBRSFeaturesPacket = 38,
  DF_PacketContent_DF_MECSysResPacket = 39,
  DF_PacketContent_MIN = DF_PacketContent_NONE,
  DF_PacketContent_MAX = DF_PacketContent_DF_MECSysResPacket
};

inline const DF_PacketContent (&EnumValuesDF_PacketContent())[40] {
  static const DF_PacketContent values[] = {
    DF_PacketContent_NONE,
    DF_PacketContent_DF_AppInfoPacket,
    DF_PacketContent_DF_ErrorImmediateReportPacket,
    DF_PacketContent_DF_RootOTAPacket,
    DF_PacketContent_DF_AppOTAPacket,
    DF_PacketContent_DF_VolumeLanePacket,
    DF_PacketContent_DF_DensityLanePacket,
    DF_PacketContent_DF_SpeedLaneAreaPacket,
    DF_PacketContent_DF_SpeedLanePointPacket,
    DF_PacketContent_DF_TrafficSignPacket,
    DF_PacketContent_DF_TrafficEventPacket,
    DF_PacketContent_DF_SafetyMessagePacket,
    DF_PacketContent_DF_SignalPhaseAndTimingPacket,
    DF_PacketContent_DF_MapPacket,
    DF_PacketContent_DF_RoadSideCoordinationPacket,
    DF_PacketContent_DF_RoadSideInformationPacket,
    DF_PacketContent_DF_RoadsideSafetyMessagePacket,
    DF_PacketContent_DF_VehIntentionAndRequestPacket,
    DF_PacketContent_DF_DedicatedLaneControlPacket,
    DF_PacketContent_DF_VariableSpeedLimitPacket,
    DF_PacketContent_DF_RampMeteringPacket,
    DF_PacketContent_DF_NowWeatherPacket,
    DF_PacketContent_DF_ForecastWeatherPacket,
    DF_PacketContent_DF_ForecastRainPacket,
    DF_PacketContent_DF_SignalSchemePacket,
    DF_PacketContent_DF_TrafficFlowPacket,
    DF_PacketContent_DF_TrajectoryPointsPacket,
    DF_PacketContent_DF_QueueLengthPacket,
    DF_PacketContent_DF_VariableMessageSignPacket,
    DF_PacketContent_DF_CarbonEmissionPacket,
    DF_PacketContent_DF_SignalExecutionPacket,
    DF_PacketContent_DF_SignalRequestPacket,
    DF_PacketContent_DF_CollisionWarningPacket,
    DF_PacketContent_DF_SpeedGuidePacket,
    DF_PacketContent_DF_RoutePlanPacket,
    DF_PacketContent_DF_PlatooningManagementMessagenPacket,
    DF_PacketContent_DF_TicketPacket,
    DF_PacketContent_DF_WildCardPacket,
    DF_PacketContent_DF_ZBRSFeaturesPacket,
    DF_PacketContent_DF_MECSysResPacket
  };
  return values;
}

inline const char * const *EnumNamesDF_PacketContent() {
  static const char * const names[41] = {
    "NONE",
    "DF_AppInfoPacket",
    "DF_ErrorImmediateReportPacket",
    "DF_RootOTAPacket",
    "DF_AppOTAPacket",
    "DF_VolumeLanePacket",
    "DF_DensityLanePacket",
    "DF_SpeedLaneAreaPacket",
    "DF_SpeedLanePointPacket",
    "DF_TrafficSignPacket",
    "DF_TrafficEventPacket",
    "DF_SafetyMessagePacket",
    "DF_SignalPhaseAndTimingPacket",
    "DF_MapPacket",
    "DF_RoadSideCoordinationPacket",
    "DF_RoadSideInformationPacket",
    "DF_RoadsideSafetyMessagePacket",
    "DF_VehIntentionAndRequestPacket",
    "DF_DedicatedLaneControlPacket",
    "DF_VariableSpeedLimitPacket",
    "DF_RampMeteringPacket",
    "DF_NowWeatherPacket",
    "DF_ForecastWeatherPacket",
    "DF_ForecastRainPacket",
    "DF_SignalSchemePacket",
    "DF_TrafficFlowPacket",
    "DF_TrajectoryPointsPacket",
    "DF_QueueLengthPacket",
    "DF_VariableMessageSignPacket",
    "DF_CarbonEmissionPacket",
    "DF_SignalExecutionPacket",
    "DF_SignalRequestPacket",
    "DF_CollisionWarningPacket",
    "DF_SpeedGuidePacket",
    "DF_RoutePlanPacket",
    "DF_PlatooningManagementMessagenPacket",
    "DF_TicketPacket",
    "DF_WildCardPacket",
    "DF_ZBRSFeaturesPacket",
    "DF_MECSysResPacket",
    nullptr
  };
  return names;
}

inline const char *EnumNameDF_PacketContent(DF_PacketContent e) {
  if (::flatbuffers::IsOutRange(e, DF_PacketContent_NONE, DF_PacketContent_DF_MECSysResPacket)) return "";
  const size_t index = static_cast<size_t>(e);
  return EnumNamesDF_PacketContent()[index];
}

template<typename T> struct DF_PacketContentTraits {
  static const DF_PacketContent enum_value = DF_PacketContent_NONE;
};

template<> struct DF_PacketContentTraits<MECData::DF_AppInfoPacket> {
  static const DF_PacketContent enum_value = DF_PacketContent_DF_AppInfoPacket;
};

template<> struct DF_PacketContentTraits<MECData::DF_ErrorImmediateReportPacket> {
  static const DF_PacketContent enum_value = DF_PacketContent_DF_ErrorImmediateReportPacket;
};

template<> struct DF_PacketContentTraits<MECData::DF_RootOTAPacket> {
  static const DF_PacketContent enum_value = DF_PacketContent_DF_RootOTAPacket;
};

template<> struct DF_PacketContentTraits<MECData::DF_AppOTAPacket> {
  static const DF_PacketContent enum_value = DF_PacketContent_DF_AppOTAPacket;
};

template<> struct DF_PacketContentTraits<MECData::DF_VolumeLanePacket> {
  static const DF_PacketContent enum_value = DF_PacketContent_DF_VolumeLanePacket;
};

template<> struct DF_PacketContentTraits<MECData::DF_DensityLanePacket> {
  static const DF_PacketContent enum_value = DF_PacketContent_DF_DensityLanePacket;
};

template<> struct DF_PacketContentTraits<MECData::DF_SpeedLaneAreaPacket> {
  static const DF_PacketContent enum_value = DF_PacketContent_DF_SpeedLaneAreaPacket;
};

template<> struct DF_PacketContentTraits<MECData::DF_SpeedLanePointPacket> {
  static const DF_PacketContent enum_value = DF_PacketContent_DF_SpeedLanePointPacket;
};

template<> struct DF_PacketContentTraits<MECData::DF_TrafficSignPacket> {
  static const DF_PacketContent enum_value = DF_PacketContent_DF_TrafficSignPacket;
};

template<> struct DF_PacketContentTraits<MECData::DF_TrafficEventPacket> {
  static const DF_PacketContent enum_value = DF_PacketContent_DF_TrafficEventPacket;
};

template<> struct DF_PacketContentTraits<MECData::DF_SafetyMessagePacket> {
  static const DF_PacketContent enum_value = DF_PacketContent_DF_SafetyMessagePacket;
};

template<> struct DF_PacketContentTraits<MECData::DF_SignalPhaseAndTimingPacket> {
  static const DF_PacketContent enum_value = DF_PacketContent_DF_SignalPhaseAndTimingPacket;
};

template<> struct DF_PacketContentTraits<MECData::DF_MapPacket> {
  static const DF_PacketContent enum_value = DF_PacketContent_DF_MapPacket;
};

template<> struct DF_PacketContentTraits<MECData::DF_RoadSideCoordinationPacket> {
  static const DF_PacketContent enum_value = DF_PacketContent_DF_RoadSideCoordinationPacket;
};

template<> struct DF_PacketContentTraits<MECData::DF_RoadSideInformationPacket> {
  static const DF_PacketContent enum_value = DF_PacketContent_DF_RoadSideInformationPacket;
};

template<> struct DF_PacketContentTraits<MECData::DF_RoadsideSafetyMessagePacket> {
  static const DF_PacketContent enum_value = DF_PacketContent_DF_RoadsideSafetyMessagePacket;
};

template<> struct DF_PacketContentTraits<MECData::DF_VehIntentionAndRequestPacket> {
  static const DF_PacketContent enum_value = DF_PacketContent_DF_VehIntentionAndRequestPacket;
};

template<> struct DF_PacketContentTraits<MECData::DF_DedicatedLaneControlPacket> {
  static const DF_PacketContent enum_value = DF_PacketContent_DF_DedicatedLaneControlPacket;
};

template<> struct DF_PacketContentTraits<MECData::DF_VariableSpeedLimitPacket> {
  static const DF_PacketContent enum_value = DF_PacketContent_DF_VariableSpeedLimitPacket;
};

template<> struct DF_PacketContentTraits<MECData::DF_RampMeteringPacket> {
  static const DF_PacketContent enum_value = DF_PacketContent_DF_RampMeteringPacket;
};

template<> struct DF_PacketContentTraits<MECData::DF_NowWeatherPacket> {
  static const DF_PacketContent enum_value = DF_PacketContent_DF_NowWeatherPacket;
};

template<> struct DF_PacketContentTraits<MECData::DF_ForecastWeatherPacket> {
  static const DF_PacketContent enum_value = DF_PacketContent_DF_ForecastWeatherPacket;
};

template<> struct DF_PacketContentTraits<MECData::DF_ForecastRainPacket> {
  static const DF_PacketContent enum_value = DF_PacketContent_DF_ForecastRainPacket;
};

template<> struct DF_PacketContentTraits<MECData::DF_SignalSchemePacket> {
  static const DF_PacketContent enum_value = DF_PacketContent_DF_SignalSchemePacket;
};

template<> struct DF_PacketContentTraits<MECData::DF_TrafficFlowPacket> {
  static const DF_PacketContent enum_value = DF_PacketContent_DF_TrafficFlowPacket;
};

template<> struct DF_PacketContentTraits<MECData::DF_TrajectoryPointsPacket> {
  static const DF_PacketContent enum_value = DF_PacketContent_DF_TrajectoryPointsPacket;
};

template<> struct DF_PacketContentTraits<MECData::DF_QueueLengthPacket> {
  static const DF_PacketContent enum_value = DF_PacketContent_DF_QueueLengthPacket;
};

template<> struct DF_PacketContentTraits<MECData::DF_VariableMessageSignPacket> {
  static const DF_PacketContent enum_value = DF_PacketContent_DF_VariableMessageSignPacket;
};

template<> struct DF_PacketContentTraits<MECData::DF_CarbonEmissionPacket> {
  static const DF_PacketContent enum_value = DF_PacketContent_DF_CarbonEmissionPacket;
};

template<> struct DF_PacketContentTraits<MECData::DF_SignalExecutionPacket> {
  static const DF_PacketContent enum_value = DF_PacketContent_DF_SignalExecutionPacket;
};

template<> struct DF_PacketContentTraits<MECData::DF_SignalRequestPacket> {
  static const DF_PacketContent enum_value = DF_PacketContent_DF_SignalRequestPacket;
};

template<> struct DF_PacketContentTraits<MECData::DF_CollisionWarningPacket> {
  static const DF_PacketContent enum_value = DF_PacketContent_DF_CollisionWarningPacket;
};

template<> struct DF_PacketContentTraits<MECData::DF_SpeedGuidePacket> {
  static const DF_PacketContent enum_value = DF_PacketContent_DF_SpeedGuidePacket;
};

template<> struct DF_PacketContentTraits<MECData::DF_RoutePlanPacket> {
  static const DF_PacketContent enum_value = DF_PacketContent_DF_RoutePlanPacket;
};

template<> struct DF_PacketContentTraits<MECData::DF_PlatooningManagementMessagenPacket> {
  static const DF_PacketContent enum_value = DF_PacketContent_DF_PlatooningManagementMessagenPacket;
};

template<> struct DF_PacketContentTraits<MECData::DF_TicketPacket> {
  static const DF_PacketContent enum_value = DF_PacketContent_DF_TicketPacket;
};

template<> struct DF_PacketContentTraits<MECData::DF_WildCardPacket> {
  static const DF_PacketContent enum_value = DF_PacketContent_DF_WildCardPacket;
};

template<> struct DF_PacketContentTraits<MECData::DF_ZBRSFeaturesPacket> {
  static const DF_PacketContent enum_value = DF_PacketContent_DF_ZBRSFeaturesPacket;
};

template<> struct DF_PacketContentTraits<MECData::DF_MECSysResPacket> {
  static const DF_PacketContent enum_value = DF_PacketContent_DF_MECSysResPacket;
};

bool VerifyDF_PacketContent(::flatbuffers::Verifier &verifier, const void *obj, DF_PacketContent type);
bool VerifyDF_PacketContentVector(::flatbuffers::Verifier &verifier, const ::flatbuffers::Vector<::flatbuffers::Offset<void>> *values, const ::flatbuffers::Vector<uint8_t> *types);

struct DF_AppCustomizedPacket FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_AppCustomizedPacketBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_TYPE_CODE = 4,
    VT_DATA = 6
  };
  uint16_t type_code() const {
    return GetField<uint16_t>(VT_TYPE_CODE, 0);
  }
  const ::flatbuffers::Vector<uint8_t> *data() const {
    return GetPointer<const ::flatbuffers::Vector<uint8_t> *>(VT_DATA);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<uint16_t>(verifier, VT_TYPE_CODE, 2) &&
           VerifyOffset(verifier, VT_DATA) &&
           verifier.VerifyVector(data()) &&
           verifier.EndTable();
  }
};

struct DF_AppCustomizedPacketBuilder {
  typedef DF_AppCustomizedPacket Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_type_code(uint16_t type_code) {
    fbb_.AddElement<uint16_t>(DF_AppCustomizedPacket::VT_TYPE_CODE, type_code, 0);
  }
  void add_data(::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> data) {
    fbb_.AddOffset(DF_AppCustomizedPacket::VT_DATA, data);
  }
  explicit DF_AppCustomizedPacketBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_AppCustomizedPacket> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_AppCustomizedPacket>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_AppCustomizedPacket> CreateDF_AppCustomizedPacket(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    uint16_t type_code = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> data = 0) {
  DF_AppCustomizedPacketBuilder builder_(_fbb);
  builder_.add_data(data);
  builder_.add_type_code(type_code);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DF_AppCustomizedPacket> CreateDF_AppCustomizedPacketDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    uint16_t type_code = 0,
    const std::vector<uint8_t> *data = nullptr) {
  auto data__ = data ? _fbb.CreateVector<uint8_t>(*data) : 0;
  return MECData::CreateDF_AppCustomizedPacket(
      _fbb,
      type_code,
      data__);
}

struct DF_AppInfoPacket FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_AppInfoPacketBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_PACKET = 4
  };
  const ::flatbuffers::Vector<uint8_t> *packet() const {
    return GetPointer<const ::flatbuffers::Vector<uint8_t> *>(VT_PACKET);
  }
  const MECData::MSG_AppInfo *packet_nested_root() const {
    const auto _f = packet();
    return _f ? ::flatbuffers::GetRoot<MECData::MSG_AppInfo>(_f->Data())
              : nullptr;
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_PACKET) &&
           verifier.VerifyVector(packet()) &&
           verifier.VerifyNestedFlatBuffer<MECData::MSG_AppInfo>(packet(), nullptr) &&
           verifier.EndTable();
  }
};

struct DF_AppInfoPacketBuilder {
  typedef DF_AppInfoPacket Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_packet(::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> packet) {
    fbb_.AddOffset(DF_AppInfoPacket::VT_PACKET, packet);
  }
  explicit DF_AppInfoPacketBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_AppInfoPacket> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_AppInfoPacket>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_AppInfoPacket> CreateDF_AppInfoPacket(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> packet = 0) {
  DF_AppInfoPacketBuilder builder_(_fbb);
  builder_.add_packet(packet);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DF_AppInfoPacket> CreateDF_AppInfoPacketDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const std::vector<uint8_t> *packet = nullptr) {
  auto packet__ = packet ? _fbb.CreateVector<uint8_t>(*packet) : 0;
  return MECData::CreateDF_AppInfoPacket(
      _fbb,
      packet__);
}

struct DF_ErrorImmediateReportPacket FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_ErrorImmediateReportPacketBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_PACKET = 4
  };
  const ::flatbuffers::Vector<uint8_t> *packet() const {
    return GetPointer<const ::flatbuffers::Vector<uint8_t> *>(VT_PACKET);
  }
  const MECData::MSG_ErrorImmediateReport *packet_nested_root() const {
    const auto _f = packet();
    return _f ? ::flatbuffers::GetRoot<MECData::MSG_ErrorImmediateReport>(_f->Data())
              : nullptr;
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_PACKET) &&
           verifier.VerifyVector(packet()) &&
           verifier.VerifyNestedFlatBuffer<MECData::MSG_ErrorImmediateReport>(packet(), nullptr) &&
           verifier.EndTable();
  }
};

struct DF_ErrorImmediateReportPacketBuilder {
  typedef DF_ErrorImmediateReportPacket Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_packet(::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> packet) {
    fbb_.AddOffset(DF_ErrorImmediateReportPacket::VT_PACKET, packet);
  }
  explicit DF_ErrorImmediateReportPacketBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_ErrorImmediateReportPacket> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_ErrorImmediateReportPacket>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_ErrorImmediateReportPacket> CreateDF_ErrorImmediateReportPacket(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> packet = 0) {
  DF_ErrorImmediateReportPacketBuilder builder_(_fbb);
  builder_.add_packet(packet);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DF_ErrorImmediateReportPacket> CreateDF_ErrorImmediateReportPacketDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const std::vector<uint8_t> *packet = nullptr) {
  auto packet__ = packet ? _fbb.CreateVector<uint8_t>(*packet) : 0;
  return MECData::CreateDF_ErrorImmediateReportPacket(
      _fbb,
      packet__);
}

struct DF_ErrorCollectionPacket FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_ErrorCollectionPacketBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_PACKET = 4
  };
  const ::flatbuffers::Vector<uint8_t> *packet() const {
    return GetPointer<const ::flatbuffers::Vector<uint8_t> *>(VT_PACKET);
  }
  const MECData::MSG_ErrorCollection *packet_nested_root() const {
    const auto _f = packet();
    return _f ? ::flatbuffers::GetRoot<MECData::MSG_ErrorCollection>(_f->Data())
              : nullptr;
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_PACKET) &&
           verifier.VerifyVector(packet()) &&
           verifier.VerifyNestedFlatBuffer<MECData::MSG_ErrorCollection>(packet(), nullptr) &&
           verifier.EndTable();
  }
};

struct DF_ErrorCollectionPacketBuilder {
  typedef DF_ErrorCollectionPacket Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_packet(::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> packet) {
    fbb_.AddOffset(DF_ErrorCollectionPacket::VT_PACKET, packet);
  }
  explicit DF_ErrorCollectionPacketBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_ErrorCollectionPacket> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_ErrorCollectionPacket>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_ErrorCollectionPacket> CreateDF_ErrorCollectionPacket(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> packet = 0) {
  DF_ErrorCollectionPacketBuilder builder_(_fbb);
  builder_.add_packet(packet);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DF_ErrorCollectionPacket> CreateDF_ErrorCollectionPacketDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const std::vector<uint8_t> *packet = nullptr) {
  auto packet__ = packet ? _fbb.CreateVector<uint8_t>(*packet) : 0;
  return MECData::CreateDF_ErrorCollectionPacket(
      _fbb,
      packet__);
}

struct DF_RootOTAPacket FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_RootOTAPacketBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_PACKET = 4
  };
  const ::flatbuffers::Vector<uint8_t> *packet() const {
    return GetPointer<const ::flatbuffers::Vector<uint8_t> *>(VT_PACKET);
  }
  const MECData::MSG_RootOTA *packet_nested_root() const {
    const auto _f = packet();
    return _f ? ::flatbuffers::GetRoot<MECData::MSG_RootOTA>(_f->Data())
              : nullptr;
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_PACKET) &&
           verifier.VerifyVector(packet()) &&
           verifier.VerifyNestedFlatBuffer<MECData::MSG_RootOTA>(packet(), nullptr) &&
           verifier.EndTable();
  }
};

struct DF_RootOTAPacketBuilder {
  typedef DF_RootOTAPacket Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_packet(::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> packet) {
    fbb_.AddOffset(DF_RootOTAPacket::VT_PACKET, packet);
  }
  explicit DF_RootOTAPacketBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_RootOTAPacket> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_RootOTAPacket>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_RootOTAPacket> CreateDF_RootOTAPacket(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> packet = 0) {
  DF_RootOTAPacketBuilder builder_(_fbb);
  builder_.add_packet(packet);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DF_RootOTAPacket> CreateDF_RootOTAPacketDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const std::vector<uint8_t> *packet = nullptr) {
  auto packet__ = packet ? _fbb.CreateVector<uint8_t>(*packet) : 0;
  return MECData::CreateDF_RootOTAPacket(
      _fbb,
      packet__);
}

struct DF_AppOTAPacket FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_AppOTAPacketBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_PACKET = 4
  };
  const ::flatbuffers::Vector<uint8_t> *packet() const {
    return GetPointer<const ::flatbuffers::Vector<uint8_t> *>(VT_PACKET);
  }
  const MECData::MSG_AppOTA *packet_nested_root() const {
    const auto _f = packet();
    return _f ? ::flatbuffers::GetRoot<MECData::MSG_AppOTA>(_f->Data())
              : nullptr;
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_PACKET) &&
           verifier.VerifyVector(packet()) &&
           verifier.VerifyNestedFlatBuffer<MECData::MSG_AppOTA>(packet(), nullptr) &&
           verifier.EndTable();
  }
};

struct DF_AppOTAPacketBuilder {
  typedef DF_AppOTAPacket Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_packet(::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> packet) {
    fbb_.AddOffset(DF_AppOTAPacket::VT_PACKET, packet);
  }
  explicit DF_AppOTAPacketBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_AppOTAPacket> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_AppOTAPacket>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_AppOTAPacket> CreateDF_AppOTAPacket(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> packet = 0) {
  DF_AppOTAPacketBuilder builder_(_fbb);
  builder_.add_packet(packet);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DF_AppOTAPacket> CreateDF_AppOTAPacketDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const std::vector<uint8_t> *packet = nullptr) {
  auto packet__ = packet ? _fbb.CreateVector<uint8_t>(*packet) : 0;
  return MECData::CreateDF_AppOTAPacket(
      _fbb,
      packet__);
}

struct DF_VolumeLanePacket FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_VolumeLanePacketBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_PACKET = 4
  };
  const ::flatbuffers::Vector<uint8_t> *packet() const {
    return GetPointer<const ::flatbuffers::Vector<uint8_t> *>(VT_PACKET);
  }
  const MECData::MSG_VolumeLane *packet_nested_root() const {
    const auto _f = packet();
    return _f ? ::flatbuffers::GetRoot<MECData::MSG_VolumeLane>(_f->Data())
              : nullptr;
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_PACKET) &&
           verifier.VerifyVector(packet()) &&
           verifier.VerifyNestedFlatBuffer<MECData::MSG_VolumeLane>(packet(), nullptr) &&
           verifier.EndTable();
  }
};

struct DF_VolumeLanePacketBuilder {
  typedef DF_VolumeLanePacket Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_packet(::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> packet) {
    fbb_.AddOffset(DF_VolumeLanePacket::VT_PACKET, packet);
  }
  explicit DF_VolumeLanePacketBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_VolumeLanePacket> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_VolumeLanePacket>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_VolumeLanePacket> CreateDF_VolumeLanePacket(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> packet = 0) {
  DF_VolumeLanePacketBuilder builder_(_fbb);
  builder_.add_packet(packet);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DF_VolumeLanePacket> CreateDF_VolumeLanePacketDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const std::vector<uint8_t> *packet = nullptr) {
  auto packet__ = packet ? _fbb.CreateVector<uint8_t>(*packet) : 0;
  return MECData::CreateDF_VolumeLanePacket(
      _fbb,
      packet__);
}

struct DF_DensityLanePacket FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_DensityLanePacketBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_PACKET = 4
  };
  const ::flatbuffers::Vector<uint8_t> *packet() const {
    return GetPointer<const ::flatbuffers::Vector<uint8_t> *>(VT_PACKET);
  }
  const MECData::MSG_DensityLane *packet_nested_root() const {
    const auto _f = packet();
    return _f ? ::flatbuffers::GetRoot<MECData::MSG_DensityLane>(_f->Data())
              : nullptr;
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_PACKET) &&
           verifier.VerifyVector(packet()) &&
           verifier.VerifyNestedFlatBuffer<MECData::MSG_DensityLane>(packet(), nullptr) &&
           verifier.EndTable();
  }
};

struct DF_DensityLanePacketBuilder {
  typedef DF_DensityLanePacket Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_packet(::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> packet) {
    fbb_.AddOffset(DF_DensityLanePacket::VT_PACKET, packet);
  }
  explicit DF_DensityLanePacketBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_DensityLanePacket> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_DensityLanePacket>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_DensityLanePacket> CreateDF_DensityLanePacket(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> packet = 0) {
  DF_DensityLanePacketBuilder builder_(_fbb);
  builder_.add_packet(packet);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DF_DensityLanePacket> CreateDF_DensityLanePacketDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const std::vector<uint8_t> *packet = nullptr) {
  auto packet__ = packet ? _fbb.CreateVector<uint8_t>(*packet) : 0;
  return MECData::CreateDF_DensityLanePacket(
      _fbb,
      packet__);
}

struct DF_SpeedLaneAreaPacket FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_SpeedLaneAreaPacketBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_PACKET = 4
  };
  const ::flatbuffers::Vector<uint8_t> *packet() const {
    return GetPointer<const ::flatbuffers::Vector<uint8_t> *>(VT_PACKET);
  }
  const MECData::MSG_SpeedLaneArea *packet_nested_root() const {
    const auto _f = packet();
    return _f ? ::flatbuffers::GetRoot<MECData::MSG_SpeedLaneArea>(_f->Data())
              : nullptr;
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_PACKET) &&
           verifier.VerifyVector(packet()) &&
           verifier.VerifyNestedFlatBuffer<MECData::MSG_SpeedLaneArea>(packet(), nullptr) &&
           verifier.EndTable();
  }
};

struct DF_SpeedLaneAreaPacketBuilder {
  typedef DF_SpeedLaneAreaPacket Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_packet(::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> packet) {
    fbb_.AddOffset(DF_SpeedLaneAreaPacket::VT_PACKET, packet);
  }
  explicit DF_SpeedLaneAreaPacketBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_SpeedLaneAreaPacket> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_SpeedLaneAreaPacket>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_SpeedLaneAreaPacket> CreateDF_SpeedLaneAreaPacket(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> packet = 0) {
  DF_SpeedLaneAreaPacketBuilder builder_(_fbb);
  builder_.add_packet(packet);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DF_SpeedLaneAreaPacket> CreateDF_SpeedLaneAreaPacketDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const std::vector<uint8_t> *packet = nullptr) {
  auto packet__ = packet ? _fbb.CreateVector<uint8_t>(*packet) : 0;
  return MECData::CreateDF_SpeedLaneAreaPacket(
      _fbb,
      packet__);
}

struct DF_SpeedLanePointPacket FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_SpeedLanePointPacketBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_PACKET = 4
  };
  const ::flatbuffers::Vector<uint8_t> *packet() const {
    return GetPointer<const ::flatbuffers::Vector<uint8_t> *>(VT_PACKET);
  }
  const MECData::MSG_SpeedLanePoint *packet_nested_root() const {
    const auto _f = packet();
    return _f ? ::flatbuffers::GetRoot<MECData::MSG_SpeedLanePoint>(_f->Data())
              : nullptr;
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_PACKET) &&
           verifier.VerifyVector(packet()) &&
           verifier.VerifyNestedFlatBuffer<MECData::MSG_SpeedLanePoint>(packet(), nullptr) &&
           verifier.EndTable();
  }
};

struct DF_SpeedLanePointPacketBuilder {
  typedef DF_SpeedLanePointPacket Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_packet(::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> packet) {
    fbb_.AddOffset(DF_SpeedLanePointPacket::VT_PACKET, packet);
  }
  explicit DF_SpeedLanePointPacketBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_SpeedLanePointPacket> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_SpeedLanePointPacket>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_SpeedLanePointPacket> CreateDF_SpeedLanePointPacket(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> packet = 0) {
  DF_SpeedLanePointPacketBuilder builder_(_fbb);
  builder_.add_packet(packet);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DF_SpeedLanePointPacket> CreateDF_SpeedLanePointPacketDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const std::vector<uint8_t> *packet = nullptr) {
  auto packet__ = packet ? _fbb.CreateVector<uint8_t>(*packet) : 0;
  return MECData::CreateDF_SpeedLanePointPacket(
      _fbb,
      packet__);
}

struct DF_TrafficSignPacket FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_TrafficSignPacketBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_PACKET = 4
  };
  const ::flatbuffers::Vector<uint8_t> *packet() const {
    return GetPointer<const ::flatbuffers::Vector<uint8_t> *>(VT_PACKET);
  }
  const MECData::MSG_TrafficSign *packet_nested_root() const {
    const auto _f = packet();
    return _f ? ::flatbuffers::GetRoot<MECData::MSG_TrafficSign>(_f->Data())
              : nullptr;
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_PACKET) &&
           verifier.VerifyVector(packet()) &&
           verifier.VerifyNestedFlatBuffer<MECData::MSG_TrafficSign>(packet(), nullptr) &&
           verifier.EndTable();
  }
};

struct DF_TrafficSignPacketBuilder {
  typedef DF_TrafficSignPacket Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_packet(::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> packet) {
    fbb_.AddOffset(DF_TrafficSignPacket::VT_PACKET, packet);
  }
  explicit DF_TrafficSignPacketBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_TrafficSignPacket> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_TrafficSignPacket>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_TrafficSignPacket> CreateDF_TrafficSignPacket(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> packet = 0) {
  DF_TrafficSignPacketBuilder builder_(_fbb);
  builder_.add_packet(packet);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DF_TrafficSignPacket> CreateDF_TrafficSignPacketDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const std::vector<uint8_t> *packet = nullptr) {
  auto packet__ = packet ? _fbb.CreateVector<uint8_t>(*packet) : 0;
  return MECData::CreateDF_TrafficSignPacket(
      _fbb,
      packet__);
}

struct DF_TrafficEventPacket FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_TrafficEventPacketBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_PACKET = 4
  };
  const ::flatbuffers::Vector<uint8_t> *packet() const {
    return GetPointer<const ::flatbuffers::Vector<uint8_t> *>(VT_PACKET);
  }
  const MECData::MSG_TrafficEvent *packet_nested_root() const {
    const auto _f = packet();
    return _f ? ::flatbuffers::GetRoot<MECData::MSG_TrafficEvent>(_f->Data())
              : nullptr;
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_PACKET) &&
           verifier.VerifyVector(packet()) &&
           verifier.VerifyNestedFlatBuffer<MECData::MSG_TrafficEvent>(packet(), nullptr) &&
           verifier.EndTable();
  }
};

struct DF_TrafficEventPacketBuilder {
  typedef DF_TrafficEventPacket Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_packet(::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> packet) {
    fbb_.AddOffset(DF_TrafficEventPacket::VT_PACKET, packet);
  }
  explicit DF_TrafficEventPacketBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_TrafficEventPacket> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_TrafficEventPacket>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_TrafficEventPacket> CreateDF_TrafficEventPacket(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> packet = 0) {
  DF_TrafficEventPacketBuilder builder_(_fbb);
  builder_.add_packet(packet);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DF_TrafficEventPacket> CreateDF_TrafficEventPacketDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const std::vector<uint8_t> *packet = nullptr) {
  auto packet__ = packet ? _fbb.CreateVector<uint8_t>(*packet) : 0;
  return MECData::CreateDF_TrafficEventPacket(
      _fbb,
      packet__);
}

struct DF_SafetyMessagePacket FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_SafetyMessagePacketBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_PACKET = 4
  };
  const ::flatbuffers::Vector<uint8_t> *packet() const {
    return GetPointer<const ::flatbuffers::Vector<uint8_t> *>(VT_PACKET);
  }
  const MECData::MSG_SafetyMessage *packet_nested_root() const {
    const auto _f = packet();
    return _f ? ::flatbuffers::GetRoot<MECData::MSG_SafetyMessage>(_f->Data())
              : nullptr;
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_PACKET) &&
           verifier.VerifyVector(packet()) &&
           verifier.VerifyNestedFlatBuffer<MECData::MSG_SafetyMessage>(packet(), nullptr) &&
           verifier.EndTable();
  }
};

struct DF_SafetyMessagePacketBuilder {
  typedef DF_SafetyMessagePacket Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_packet(::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> packet) {
    fbb_.AddOffset(DF_SafetyMessagePacket::VT_PACKET, packet);
  }
  explicit DF_SafetyMessagePacketBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_SafetyMessagePacket> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_SafetyMessagePacket>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_SafetyMessagePacket> CreateDF_SafetyMessagePacket(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> packet = 0) {
  DF_SafetyMessagePacketBuilder builder_(_fbb);
  builder_.add_packet(packet);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DF_SafetyMessagePacket> CreateDF_SafetyMessagePacketDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const std::vector<uint8_t> *packet = nullptr) {
  auto packet__ = packet ? _fbb.CreateVector<uint8_t>(*packet) : 0;
  return MECData::CreateDF_SafetyMessagePacket(
      _fbb,
      packet__);
}

struct DF_SignalPhaseAndTimingPacket FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_SignalPhaseAndTimingPacketBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_PACKET = 4
  };
  const ::flatbuffers::Vector<uint8_t> *packet() const {
    return GetPointer<const ::flatbuffers::Vector<uint8_t> *>(VT_PACKET);
  }
  const MECData::MSG_SignalPhaseAndTiming *packet_nested_root() const {
    const auto _f = packet();
    return _f ? ::flatbuffers::GetRoot<MECData::MSG_SignalPhaseAndTiming>(_f->Data())
              : nullptr;
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_PACKET) &&
           verifier.VerifyVector(packet()) &&
           verifier.VerifyNestedFlatBuffer<MECData::MSG_SignalPhaseAndTiming>(packet(), nullptr) &&
           verifier.EndTable();
  }
};

struct DF_SignalPhaseAndTimingPacketBuilder {
  typedef DF_SignalPhaseAndTimingPacket Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_packet(::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> packet) {
    fbb_.AddOffset(DF_SignalPhaseAndTimingPacket::VT_PACKET, packet);
  }
  explicit DF_SignalPhaseAndTimingPacketBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_SignalPhaseAndTimingPacket> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_SignalPhaseAndTimingPacket>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_SignalPhaseAndTimingPacket> CreateDF_SignalPhaseAndTimingPacket(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> packet = 0) {
  DF_SignalPhaseAndTimingPacketBuilder builder_(_fbb);
  builder_.add_packet(packet);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DF_SignalPhaseAndTimingPacket> CreateDF_SignalPhaseAndTimingPacketDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const std::vector<uint8_t> *packet = nullptr) {
  auto packet__ = packet ? _fbb.CreateVector<uint8_t>(*packet) : 0;
  return MECData::CreateDF_SignalPhaseAndTimingPacket(
      _fbb,
      packet__);
}

struct DF_MapPacket FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_MapPacketBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_PACKET = 4
  };
  const ::flatbuffers::Vector<uint8_t> *packet() const {
    return GetPointer<const ::flatbuffers::Vector<uint8_t> *>(VT_PACKET);
  }
  const MECData::MSG_Map *packet_nested_root() const {
    const auto _f = packet();
    return _f ? ::flatbuffers::GetRoot<MECData::MSG_Map>(_f->Data())
              : nullptr;
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_PACKET) &&
           verifier.VerifyVector(packet()) &&
           verifier.VerifyNestedFlatBuffer<MECData::MSG_Map>(packet(), nullptr) &&
           verifier.EndTable();
  }
};

struct DF_MapPacketBuilder {
  typedef DF_MapPacket Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_packet(::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> packet) {
    fbb_.AddOffset(DF_MapPacket::VT_PACKET, packet);
  }
  explicit DF_MapPacketBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_MapPacket> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_MapPacket>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_MapPacket> CreateDF_MapPacket(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> packet = 0) {
  DF_MapPacketBuilder builder_(_fbb);
  builder_.add_packet(packet);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DF_MapPacket> CreateDF_MapPacketDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const std::vector<uint8_t> *packet = nullptr) {
  auto packet__ = packet ? _fbb.CreateVector<uint8_t>(*packet) : 0;
  return MECData::CreateDF_MapPacket(
      _fbb,
      packet__);
}

struct DF_RoadSideCoordinationPacket FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_RoadSideCoordinationPacketBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_PACKET = 4
  };
  const ::flatbuffers::Vector<uint8_t> *packet() const {
    return GetPointer<const ::flatbuffers::Vector<uint8_t> *>(VT_PACKET);
  }
  const MECData::MSG_RoadSideCoordination *packet_nested_root() const {
    const auto _f = packet();
    return _f ? ::flatbuffers::GetRoot<MECData::MSG_RoadSideCoordination>(_f->Data())
              : nullptr;
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_PACKET) &&
           verifier.VerifyVector(packet()) &&
           verifier.VerifyNestedFlatBuffer<MECData::MSG_RoadSideCoordination>(packet(), nullptr) &&
           verifier.EndTable();
  }
};

struct DF_RoadSideCoordinationPacketBuilder {
  typedef DF_RoadSideCoordinationPacket Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_packet(::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> packet) {
    fbb_.AddOffset(DF_RoadSideCoordinationPacket::VT_PACKET, packet);
  }
  explicit DF_RoadSideCoordinationPacketBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_RoadSideCoordinationPacket> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_RoadSideCoordinationPacket>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_RoadSideCoordinationPacket> CreateDF_RoadSideCoordinationPacket(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> packet = 0) {
  DF_RoadSideCoordinationPacketBuilder builder_(_fbb);
  builder_.add_packet(packet);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DF_RoadSideCoordinationPacket> CreateDF_RoadSideCoordinationPacketDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const std::vector<uint8_t> *packet = nullptr) {
  auto packet__ = packet ? _fbb.CreateVector<uint8_t>(*packet) : 0;
  return MECData::CreateDF_RoadSideCoordinationPacket(
      _fbb,
      packet__);
}

struct DF_RoadSideInformationPacket FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_RoadSideInformationPacketBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_PACKET = 4
  };
  const ::flatbuffers::Vector<uint8_t> *packet() const {
    return GetPointer<const ::flatbuffers::Vector<uint8_t> *>(VT_PACKET);
  }
  const MECData::MSG_RoadSideInformation *packet_nested_root() const {
    const auto _f = packet();
    return _f ? ::flatbuffers::GetRoot<MECData::MSG_RoadSideInformation>(_f->Data())
              : nullptr;
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_PACKET) &&
           verifier.VerifyVector(packet()) &&
           verifier.VerifyNestedFlatBuffer<MECData::MSG_RoadSideInformation>(packet(), nullptr) &&
           verifier.EndTable();
  }
};

struct DF_RoadSideInformationPacketBuilder {
  typedef DF_RoadSideInformationPacket Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_packet(::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> packet) {
    fbb_.AddOffset(DF_RoadSideInformationPacket::VT_PACKET, packet);
  }
  explicit DF_RoadSideInformationPacketBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_RoadSideInformationPacket> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_RoadSideInformationPacket>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_RoadSideInformationPacket> CreateDF_RoadSideInformationPacket(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> packet = 0) {
  DF_RoadSideInformationPacketBuilder builder_(_fbb);
  builder_.add_packet(packet);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DF_RoadSideInformationPacket> CreateDF_RoadSideInformationPacketDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const std::vector<uint8_t> *packet = nullptr) {
  auto packet__ = packet ? _fbb.CreateVector<uint8_t>(*packet) : 0;
  return MECData::CreateDF_RoadSideInformationPacket(
      _fbb,
      packet__);
}

struct DF_RoadsideSafetyMessagePacket FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_RoadsideSafetyMessagePacketBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_PACKET = 4
  };
  const ::flatbuffers::Vector<uint8_t> *packet() const {
    return GetPointer<const ::flatbuffers::Vector<uint8_t> *>(VT_PACKET);
  }
  const MECData::MSG_RoadsideSafetyMessage *packet_nested_root() const {
    const auto _f = packet();
    return _f ? ::flatbuffers::GetRoot<MECData::MSG_RoadsideSafetyMessage>(_f->Data())
              : nullptr;
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_PACKET) &&
           verifier.VerifyVector(packet()) &&
           verifier.VerifyNestedFlatBuffer<MECData::MSG_RoadsideSafetyMessage>(packet(), nullptr) &&
           verifier.EndTable();
  }
};

struct DF_RoadsideSafetyMessagePacketBuilder {
  typedef DF_RoadsideSafetyMessagePacket Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_packet(::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> packet) {
    fbb_.AddOffset(DF_RoadsideSafetyMessagePacket::VT_PACKET, packet);
  }
  explicit DF_RoadsideSafetyMessagePacketBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_RoadsideSafetyMessagePacket> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_RoadsideSafetyMessagePacket>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_RoadsideSafetyMessagePacket> CreateDF_RoadsideSafetyMessagePacket(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> packet = 0) {
  DF_RoadsideSafetyMessagePacketBuilder builder_(_fbb);
  builder_.add_packet(packet);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DF_RoadsideSafetyMessagePacket> CreateDF_RoadsideSafetyMessagePacketDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const std::vector<uint8_t> *packet = nullptr) {
  auto packet__ = packet ? _fbb.CreateVector<uint8_t>(*packet) : 0;
  return MECData::CreateDF_RoadsideSafetyMessagePacket(
      _fbb,
      packet__);
}

struct DF_VehIntentionAndRequestPacket FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_VehIntentionAndRequestPacketBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_PACKET = 4
  };
  const ::flatbuffers::Vector<uint8_t> *packet() const {
    return GetPointer<const ::flatbuffers::Vector<uint8_t> *>(VT_PACKET);
  }
  const MECData::MSG_VehIntentionAndRequest *packet_nested_root() const {
    const auto _f = packet();
    return _f ? ::flatbuffers::GetRoot<MECData::MSG_VehIntentionAndRequest>(_f->Data())
              : nullptr;
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_PACKET) &&
           verifier.VerifyVector(packet()) &&
           verifier.VerifyNestedFlatBuffer<MECData::MSG_VehIntentionAndRequest>(packet(), nullptr) &&
           verifier.EndTable();
  }
};

struct DF_VehIntentionAndRequestPacketBuilder {
  typedef DF_VehIntentionAndRequestPacket Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_packet(::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> packet) {
    fbb_.AddOffset(DF_VehIntentionAndRequestPacket::VT_PACKET, packet);
  }
  explicit DF_VehIntentionAndRequestPacketBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_VehIntentionAndRequestPacket> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_VehIntentionAndRequestPacket>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_VehIntentionAndRequestPacket> CreateDF_VehIntentionAndRequestPacket(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> packet = 0) {
  DF_VehIntentionAndRequestPacketBuilder builder_(_fbb);
  builder_.add_packet(packet);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DF_VehIntentionAndRequestPacket> CreateDF_VehIntentionAndRequestPacketDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const std::vector<uint8_t> *packet = nullptr) {
  auto packet__ = packet ? _fbb.CreateVector<uint8_t>(*packet) : 0;
  return MECData::CreateDF_VehIntentionAndRequestPacket(
      _fbb,
      packet__);
}

struct DF_DedicatedLaneControlPacket FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_DedicatedLaneControlPacketBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_PACKET = 4
  };
  const ::flatbuffers::Vector<uint8_t> *packet() const {
    return GetPointer<const ::flatbuffers::Vector<uint8_t> *>(VT_PACKET);
  }
  const MECData::MSG_DedicatedLaneControl *packet_nested_root() const {
    const auto _f = packet();
    return _f ? ::flatbuffers::GetRoot<MECData::MSG_DedicatedLaneControl>(_f->Data())
              : nullptr;
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_PACKET) &&
           verifier.VerifyVector(packet()) &&
           verifier.VerifyNestedFlatBuffer<MECData::MSG_DedicatedLaneControl>(packet(), nullptr) &&
           verifier.EndTable();
  }
};

struct DF_DedicatedLaneControlPacketBuilder {
  typedef DF_DedicatedLaneControlPacket Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_packet(::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> packet) {
    fbb_.AddOffset(DF_DedicatedLaneControlPacket::VT_PACKET, packet);
  }
  explicit DF_DedicatedLaneControlPacketBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_DedicatedLaneControlPacket> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_DedicatedLaneControlPacket>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_DedicatedLaneControlPacket> CreateDF_DedicatedLaneControlPacket(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> packet = 0) {
  DF_DedicatedLaneControlPacketBuilder builder_(_fbb);
  builder_.add_packet(packet);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DF_DedicatedLaneControlPacket> CreateDF_DedicatedLaneControlPacketDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const std::vector<uint8_t> *packet = nullptr) {
  auto packet__ = packet ? _fbb.CreateVector<uint8_t>(*packet) : 0;
  return MECData::CreateDF_DedicatedLaneControlPacket(
      _fbb,
      packet__);
}

struct DF_VariableSpeedLimitPacket FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_VariableSpeedLimitPacketBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_PACKET = 4
  };
  const ::flatbuffers::Vector<uint8_t> *packet() const {
    return GetPointer<const ::flatbuffers::Vector<uint8_t> *>(VT_PACKET);
  }
  const MECData::MSG_VariableSpeedLimit *packet_nested_root() const {
    const auto _f = packet();
    return _f ? ::flatbuffers::GetRoot<MECData::MSG_VariableSpeedLimit>(_f->Data())
              : nullptr;
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_PACKET) &&
           verifier.VerifyVector(packet()) &&
           verifier.VerifyNestedFlatBuffer<MECData::MSG_VariableSpeedLimit>(packet(), nullptr) &&
           verifier.EndTable();
  }
};

struct DF_VariableSpeedLimitPacketBuilder {
  typedef DF_VariableSpeedLimitPacket Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_packet(::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> packet) {
    fbb_.AddOffset(DF_VariableSpeedLimitPacket::VT_PACKET, packet);
  }
  explicit DF_VariableSpeedLimitPacketBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_VariableSpeedLimitPacket> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_VariableSpeedLimitPacket>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_VariableSpeedLimitPacket> CreateDF_VariableSpeedLimitPacket(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> packet = 0) {
  DF_VariableSpeedLimitPacketBuilder builder_(_fbb);
  builder_.add_packet(packet);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DF_VariableSpeedLimitPacket> CreateDF_VariableSpeedLimitPacketDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const std::vector<uint8_t> *packet = nullptr) {
  auto packet__ = packet ? _fbb.CreateVector<uint8_t>(*packet) : 0;
  return MECData::CreateDF_VariableSpeedLimitPacket(
      _fbb,
      packet__);
}

struct DF_RampMeteringPacket FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_RampMeteringPacketBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_PACKET = 4
  };
  const ::flatbuffers::Vector<uint8_t> *packet() const {
    return GetPointer<const ::flatbuffers::Vector<uint8_t> *>(VT_PACKET);
  }
  const MECData::MSG_RampMetering *packet_nested_root() const {
    const auto _f = packet();
    return _f ? ::flatbuffers::GetRoot<MECData::MSG_RampMetering>(_f->Data())
              : nullptr;
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_PACKET) &&
           verifier.VerifyVector(packet()) &&
           verifier.VerifyNestedFlatBuffer<MECData::MSG_RampMetering>(packet(), nullptr) &&
           verifier.EndTable();
  }
};

struct DF_RampMeteringPacketBuilder {
  typedef DF_RampMeteringPacket Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_packet(::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> packet) {
    fbb_.AddOffset(DF_RampMeteringPacket::VT_PACKET, packet);
  }
  explicit DF_RampMeteringPacketBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_RampMeteringPacket> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_RampMeteringPacket>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_RampMeteringPacket> CreateDF_RampMeteringPacket(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> packet = 0) {
  DF_RampMeteringPacketBuilder builder_(_fbb);
  builder_.add_packet(packet);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DF_RampMeteringPacket> CreateDF_RampMeteringPacketDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const std::vector<uint8_t> *packet = nullptr) {
  auto packet__ = packet ? _fbb.CreateVector<uint8_t>(*packet) : 0;
  return MECData::CreateDF_RampMeteringPacket(
      _fbb,
      packet__);
}

struct DF_NowWeatherPacket FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_NowWeatherPacketBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_PACKET = 4
  };
  const ::flatbuffers::Vector<uint8_t> *packet() const {
    return GetPointer<const ::flatbuffers::Vector<uint8_t> *>(VT_PACKET);
  }
  const MECData::MSG_NowWeather *packet_nested_root() const {
    const auto _f = packet();
    return _f ? ::flatbuffers::GetRoot<MECData::MSG_NowWeather>(_f->Data())
              : nullptr;
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_PACKET) &&
           verifier.VerifyVector(packet()) &&
           verifier.VerifyNestedFlatBuffer<MECData::MSG_NowWeather>(packet(), nullptr) &&
           verifier.EndTable();
  }
};

struct DF_NowWeatherPacketBuilder {
  typedef DF_NowWeatherPacket Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_packet(::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> packet) {
    fbb_.AddOffset(DF_NowWeatherPacket::VT_PACKET, packet);
  }
  explicit DF_NowWeatherPacketBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_NowWeatherPacket> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_NowWeatherPacket>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_NowWeatherPacket> CreateDF_NowWeatherPacket(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> packet = 0) {
  DF_NowWeatherPacketBuilder builder_(_fbb);
  builder_.add_packet(packet);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DF_NowWeatherPacket> CreateDF_NowWeatherPacketDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const std::vector<uint8_t> *packet = nullptr) {
  auto packet__ = packet ? _fbb.CreateVector<uint8_t>(*packet) : 0;
  return MECData::CreateDF_NowWeatherPacket(
      _fbb,
      packet__);
}

struct DF_ForecastWeatherPacket FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_ForecastWeatherPacketBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_PACKET = 4
  };
  const ::flatbuffers::Vector<uint8_t> *packet() const {
    return GetPointer<const ::flatbuffers::Vector<uint8_t> *>(VT_PACKET);
  }
  const MECData::MSG_ForecastWeather *packet_nested_root() const {
    const auto _f = packet();
    return _f ? ::flatbuffers::GetRoot<MECData::MSG_ForecastWeather>(_f->Data())
              : nullptr;
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_PACKET) &&
           verifier.VerifyVector(packet()) &&
           verifier.VerifyNestedFlatBuffer<MECData::MSG_ForecastWeather>(packet(), nullptr) &&
           verifier.EndTable();
  }
};

struct DF_ForecastWeatherPacketBuilder {
  typedef DF_ForecastWeatherPacket Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_packet(::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> packet) {
    fbb_.AddOffset(DF_ForecastWeatherPacket::VT_PACKET, packet);
  }
  explicit DF_ForecastWeatherPacketBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_ForecastWeatherPacket> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_ForecastWeatherPacket>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_ForecastWeatherPacket> CreateDF_ForecastWeatherPacket(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> packet = 0) {
  DF_ForecastWeatherPacketBuilder builder_(_fbb);
  builder_.add_packet(packet);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DF_ForecastWeatherPacket> CreateDF_ForecastWeatherPacketDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const std::vector<uint8_t> *packet = nullptr) {
  auto packet__ = packet ? _fbb.CreateVector<uint8_t>(*packet) : 0;
  return MECData::CreateDF_ForecastWeatherPacket(
      _fbb,
      packet__);
}

struct DF_ForecastRainPacket FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_ForecastRainPacketBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_PACKET = 4
  };
  const ::flatbuffers::Vector<uint8_t> *packet() const {
    return GetPointer<const ::flatbuffers::Vector<uint8_t> *>(VT_PACKET);
  }
  const MECData::MSG_ForecastRain *packet_nested_root() const {
    const auto _f = packet();
    return _f ? ::flatbuffers::GetRoot<MECData::MSG_ForecastRain>(_f->Data())
              : nullptr;
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_PACKET) &&
           verifier.VerifyVector(packet()) &&
           verifier.VerifyNestedFlatBuffer<MECData::MSG_ForecastRain>(packet(), nullptr) &&
           verifier.EndTable();
  }
};

struct DF_ForecastRainPacketBuilder {
  typedef DF_ForecastRainPacket Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_packet(::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> packet) {
    fbb_.AddOffset(DF_ForecastRainPacket::VT_PACKET, packet);
  }
  explicit DF_ForecastRainPacketBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_ForecastRainPacket> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_ForecastRainPacket>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_ForecastRainPacket> CreateDF_ForecastRainPacket(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> packet = 0) {
  DF_ForecastRainPacketBuilder builder_(_fbb);
  builder_.add_packet(packet);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DF_ForecastRainPacket> CreateDF_ForecastRainPacketDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const std::vector<uint8_t> *packet = nullptr) {
  auto packet__ = packet ? _fbb.CreateVector<uint8_t>(*packet) : 0;
  return MECData::CreateDF_ForecastRainPacket(
      _fbb,
      packet__);
}

struct DF_SignalSchemePacket FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_SignalSchemePacketBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_PACKET = 4
  };
  const ::flatbuffers::Vector<uint8_t> *packet() const {
    return GetPointer<const ::flatbuffers::Vector<uint8_t> *>(VT_PACKET);
  }
  const MECData::MSG_SignalScheme *packet_nested_root() const {
    const auto _f = packet();
    return _f ? ::flatbuffers::GetRoot<MECData::MSG_SignalScheme>(_f->Data())
              : nullptr;
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_PACKET) &&
           verifier.VerifyVector(packet()) &&
           verifier.VerifyNestedFlatBuffer<MECData::MSG_SignalScheme>(packet(), nullptr) &&
           verifier.EndTable();
  }
};

struct DF_SignalSchemePacketBuilder {
  typedef DF_SignalSchemePacket Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_packet(::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> packet) {
    fbb_.AddOffset(DF_SignalSchemePacket::VT_PACKET, packet);
  }
  explicit DF_SignalSchemePacketBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_SignalSchemePacket> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_SignalSchemePacket>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_SignalSchemePacket> CreateDF_SignalSchemePacket(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> packet = 0) {
  DF_SignalSchemePacketBuilder builder_(_fbb);
  builder_.add_packet(packet);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DF_SignalSchemePacket> CreateDF_SignalSchemePacketDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const std::vector<uint8_t> *packet = nullptr) {
  auto packet__ = packet ? _fbb.CreateVector<uint8_t>(*packet) : 0;
  return MECData::CreateDF_SignalSchemePacket(
      _fbb,
      packet__);
}

struct DF_TrafficFlowPacket FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_TrafficFlowPacketBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_PACKET = 4
  };
  const ::flatbuffers::Vector<uint8_t> *packet() const {
    return GetPointer<const ::flatbuffers::Vector<uint8_t> *>(VT_PACKET);
  }
  const MECData::MSG_TrafficFlow *packet_nested_root() const {
    const auto _f = packet();
    return _f ? ::flatbuffers::GetRoot<MECData::MSG_TrafficFlow>(_f->Data())
              : nullptr;
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_PACKET) &&
           verifier.VerifyVector(packet()) &&
           verifier.VerifyNestedFlatBuffer<MECData::MSG_TrafficFlow>(packet(), nullptr) &&
           verifier.EndTable();
  }
};

struct DF_TrafficFlowPacketBuilder {
  typedef DF_TrafficFlowPacket Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_packet(::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> packet) {
    fbb_.AddOffset(DF_TrafficFlowPacket::VT_PACKET, packet);
  }
  explicit DF_TrafficFlowPacketBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_TrafficFlowPacket> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_TrafficFlowPacket>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_TrafficFlowPacket> CreateDF_TrafficFlowPacket(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> packet = 0) {
  DF_TrafficFlowPacketBuilder builder_(_fbb);
  builder_.add_packet(packet);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DF_TrafficFlowPacket> CreateDF_TrafficFlowPacketDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const std::vector<uint8_t> *packet = nullptr) {
  auto packet__ = packet ? _fbb.CreateVector<uint8_t>(*packet) : 0;
  return MECData::CreateDF_TrafficFlowPacket(
      _fbb,
      packet__);
}

struct DF_TrajectoryPointsPacket FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_TrajectoryPointsPacketBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_PACKET = 4
  };
  const ::flatbuffers::Vector<uint8_t> *packet() const {
    return GetPointer<const ::flatbuffers::Vector<uint8_t> *>(VT_PACKET);
  }
  const MECData::MSG_TrajectoryPoints *packet_nested_root() const {
    const auto _f = packet();
    return _f ? ::flatbuffers::GetRoot<MECData::MSG_TrajectoryPoints>(_f->Data())
              : nullptr;
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_PACKET) &&
           verifier.VerifyVector(packet()) &&
           verifier.VerifyNestedFlatBuffer<MECData::MSG_TrajectoryPoints>(packet(), nullptr) &&
           verifier.EndTable();
  }
};

struct DF_TrajectoryPointsPacketBuilder {
  typedef DF_TrajectoryPointsPacket Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_packet(::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> packet) {
    fbb_.AddOffset(DF_TrajectoryPointsPacket::VT_PACKET, packet);
  }
  explicit DF_TrajectoryPointsPacketBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_TrajectoryPointsPacket> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_TrajectoryPointsPacket>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_TrajectoryPointsPacket> CreateDF_TrajectoryPointsPacket(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> packet = 0) {
  DF_TrajectoryPointsPacketBuilder builder_(_fbb);
  builder_.add_packet(packet);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DF_TrajectoryPointsPacket> CreateDF_TrajectoryPointsPacketDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const std::vector<uint8_t> *packet = nullptr) {
  auto packet__ = packet ? _fbb.CreateVector<uint8_t>(*packet) : 0;
  return MECData::CreateDF_TrajectoryPointsPacket(
      _fbb,
      packet__);
}

struct DF_QueueLengthPacket FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_QueueLengthPacketBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_PACKET = 4
  };
  const ::flatbuffers::Vector<uint8_t> *packet() const {
    return GetPointer<const ::flatbuffers::Vector<uint8_t> *>(VT_PACKET);
  }
  const MECData::MSG_QueueLength *packet_nested_root() const {
    const auto _f = packet();
    return _f ? ::flatbuffers::GetRoot<MECData::MSG_QueueLength>(_f->Data())
              : nullptr;
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_PACKET) &&
           verifier.VerifyVector(packet()) &&
           verifier.VerifyNestedFlatBuffer<MECData::MSG_QueueLength>(packet(), nullptr) &&
           verifier.EndTable();
  }
};

struct DF_QueueLengthPacketBuilder {
  typedef DF_QueueLengthPacket Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_packet(::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> packet) {
    fbb_.AddOffset(DF_QueueLengthPacket::VT_PACKET, packet);
  }
  explicit DF_QueueLengthPacketBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_QueueLengthPacket> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_QueueLengthPacket>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_QueueLengthPacket> CreateDF_QueueLengthPacket(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> packet = 0) {
  DF_QueueLengthPacketBuilder builder_(_fbb);
  builder_.add_packet(packet);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DF_QueueLengthPacket> CreateDF_QueueLengthPacketDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const std::vector<uint8_t> *packet = nullptr) {
  auto packet__ = packet ? _fbb.CreateVector<uint8_t>(*packet) : 0;
  return MECData::CreateDF_QueueLengthPacket(
      _fbb,
      packet__);
}

struct DF_VariableMessageSignPacket FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_VariableMessageSignPacketBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_PACKET = 4
  };
  const ::flatbuffers::Vector<uint8_t> *packet() const {
    return GetPointer<const ::flatbuffers::Vector<uint8_t> *>(VT_PACKET);
  }
  const MECData::MSG_VariableMessageSign *packet_nested_root() const {
    const auto _f = packet();
    return _f ? ::flatbuffers::GetRoot<MECData::MSG_VariableMessageSign>(_f->Data())
              : nullptr;
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_PACKET) &&
           verifier.VerifyVector(packet()) &&
           verifier.VerifyNestedFlatBuffer<MECData::MSG_VariableMessageSign>(packet(), nullptr) &&
           verifier.EndTable();
  }
};

struct DF_VariableMessageSignPacketBuilder {
  typedef DF_VariableMessageSignPacket Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_packet(::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> packet) {
    fbb_.AddOffset(DF_VariableMessageSignPacket::VT_PACKET, packet);
  }
  explicit DF_VariableMessageSignPacketBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_VariableMessageSignPacket> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_VariableMessageSignPacket>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_VariableMessageSignPacket> CreateDF_VariableMessageSignPacket(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> packet = 0) {
  DF_VariableMessageSignPacketBuilder builder_(_fbb);
  builder_.add_packet(packet);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DF_VariableMessageSignPacket> CreateDF_VariableMessageSignPacketDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const std::vector<uint8_t> *packet = nullptr) {
  auto packet__ = packet ? _fbb.CreateVector<uint8_t>(*packet) : 0;
  return MECData::CreateDF_VariableMessageSignPacket(
      _fbb,
      packet__);
}

struct DF_CarbonEmissionPacket FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_CarbonEmissionPacketBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_PACKET = 4
  };
  const ::flatbuffers::Vector<uint8_t> *packet() const {
    return GetPointer<const ::flatbuffers::Vector<uint8_t> *>(VT_PACKET);
  }
  const MECData::MSG_CarbonEmission *packet_nested_root() const {
    const auto _f = packet();
    return _f ? ::flatbuffers::GetRoot<MECData::MSG_CarbonEmission>(_f->Data())
              : nullptr;
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_PACKET) &&
           verifier.VerifyVector(packet()) &&
           verifier.VerifyNestedFlatBuffer<MECData::MSG_CarbonEmission>(packet(), nullptr) &&
           verifier.EndTable();
  }
};

struct DF_CarbonEmissionPacketBuilder {
  typedef DF_CarbonEmissionPacket Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_packet(::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> packet) {
    fbb_.AddOffset(DF_CarbonEmissionPacket::VT_PACKET, packet);
  }
  explicit DF_CarbonEmissionPacketBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_CarbonEmissionPacket> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_CarbonEmissionPacket>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_CarbonEmissionPacket> CreateDF_CarbonEmissionPacket(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> packet = 0) {
  DF_CarbonEmissionPacketBuilder builder_(_fbb);
  builder_.add_packet(packet);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DF_CarbonEmissionPacket> CreateDF_CarbonEmissionPacketDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const std::vector<uint8_t> *packet = nullptr) {
  auto packet__ = packet ? _fbb.CreateVector<uint8_t>(*packet) : 0;
  return MECData::CreateDF_CarbonEmissionPacket(
      _fbb,
      packet__);
}

struct DF_SignalExecutionPacket FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_SignalExecutionPacketBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_PACKET = 4
  };
  const ::flatbuffers::Vector<uint8_t> *packet() const {
    return GetPointer<const ::flatbuffers::Vector<uint8_t> *>(VT_PACKET);
  }
  const MECData::MSG_SignalExecution *packet_nested_root() const {
    const auto _f = packet();
    return _f ? ::flatbuffers::GetRoot<MECData::MSG_SignalExecution>(_f->Data())
              : nullptr;
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_PACKET) &&
           verifier.VerifyVector(packet()) &&
           verifier.VerifyNestedFlatBuffer<MECData::MSG_SignalExecution>(packet(), nullptr) &&
           verifier.EndTable();
  }
};

struct DF_SignalExecutionPacketBuilder {
  typedef DF_SignalExecutionPacket Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_packet(::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> packet) {
    fbb_.AddOffset(DF_SignalExecutionPacket::VT_PACKET, packet);
  }
  explicit DF_SignalExecutionPacketBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_SignalExecutionPacket> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_SignalExecutionPacket>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_SignalExecutionPacket> CreateDF_SignalExecutionPacket(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> packet = 0) {
  DF_SignalExecutionPacketBuilder builder_(_fbb);
  builder_.add_packet(packet);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DF_SignalExecutionPacket> CreateDF_SignalExecutionPacketDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const std::vector<uint8_t> *packet = nullptr) {
  auto packet__ = packet ? _fbb.CreateVector<uint8_t>(*packet) : 0;
  return MECData::CreateDF_SignalExecutionPacket(
      _fbb,
      packet__);
}

struct DF_SignalRequestPacket FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_SignalRequestPacketBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_PACKET = 4
  };
  const ::flatbuffers::Vector<uint8_t> *packet() const {
    return GetPointer<const ::flatbuffers::Vector<uint8_t> *>(VT_PACKET);
  }
  const MECData::MSG_SignalRequest *packet_nested_root() const {
    const auto _f = packet();
    return _f ? ::flatbuffers::GetRoot<MECData::MSG_SignalRequest>(_f->Data())
              : nullptr;
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_PACKET) &&
           verifier.VerifyVector(packet()) &&
           verifier.VerifyNestedFlatBuffer<MECData::MSG_SignalRequest>(packet(), nullptr) &&
           verifier.EndTable();
  }
};

struct DF_SignalRequestPacketBuilder {
  typedef DF_SignalRequestPacket Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_packet(::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> packet) {
    fbb_.AddOffset(DF_SignalRequestPacket::VT_PACKET, packet);
  }
  explicit DF_SignalRequestPacketBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_SignalRequestPacket> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_SignalRequestPacket>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_SignalRequestPacket> CreateDF_SignalRequestPacket(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> packet = 0) {
  DF_SignalRequestPacketBuilder builder_(_fbb);
  builder_.add_packet(packet);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DF_SignalRequestPacket> CreateDF_SignalRequestPacketDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const std::vector<uint8_t> *packet = nullptr) {
  auto packet__ = packet ? _fbb.CreateVector<uint8_t>(*packet) : 0;
  return MECData::CreateDF_SignalRequestPacket(
      _fbb,
      packet__);
}

struct DF_CollisionWarningPacket FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_CollisionWarningPacketBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_PACKET = 4
  };
  const ::flatbuffers::Vector<uint8_t> *packet() const {
    return GetPointer<const ::flatbuffers::Vector<uint8_t> *>(VT_PACKET);
  }
  const MECData::MSG_CollisionWarning *packet_nested_root() const {
    const auto _f = packet();
    return _f ? ::flatbuffers::GetRoot<MECData::MSG_CollisionWarning>(_f->Data())
              : nullptr;
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_PACKET) &&
           verifier.VerifyVector(packet()) &&
           verifier.VerifyNestedFlatBuffer<MECData::MSG_CollisionWarning>(packet(), nullptr) &&
           verifier.EndTable();
  }
};

struct DF_CollisionWarningPacketBuilder {
  typedef DF_CollisionWarningPacket Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_packet(::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> packet) {
    fbb_.AddOffset(DF_CollisionWarningPacket::VT_PACKET, packet);
  }
  explicit DF_CollisionWarningPacketBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_CollisionWarningPacket> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_CollisionWarningPacket>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_CollisionWarningPacket> CreateDF_CollisionWarningPacket(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> packet = 0) {
  DF_CollisionWarningPacketBuilder builder_(_fbb);
  builder_.add_packet(packet);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DF_CollisionWarningPacket> CreateDF_CollisionWarningPacketDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const std::vector<uint8_t> *packet = nullptr) {
  auto packet__ = packet ? _fbb.CreateVector<uint8_t>(*packet) : 0;
  return MECData::CreateDF_CollisionWarningPacket(
      _fbb,
      packet__);
}

struct DF_SpeedGuidePacket FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_SpeedGuidePacketBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_PACKET = 4
  };
  const ::flatbuffers::Vector<uint8_t> *packet() const {
    return GetPointer<const ::flatbuffers::Vector<uint8_t> *>(VT_PACKET);
  }
  const MECData::MSG_SpeedGuide *packet_nested_root() const {
    const auto _f = packet();
    return _f ? ::flatbuffers::GetRoot<MECData::MSG_SpeedGuide>(_f->Data())
              : nullptr;
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_PACKET) &&
           verifier.VerifyVector(packet()) &&
           verifier.VerifyNestedFlatBuffer<MECData::MSG_SpeedGuide>(packet(), nullptr) &&
           verifier.EndTable();
  }
};

struct DF_SpeedGuidePacketBuilder {
  typedef DF_SpeedGuidePacket Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_packet(::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> packet) {
    fbb_.AddOffset(DF_SpeedGuidePacket::VT_PACKET, packet);
  }
  explicit DF_SpeedGuidePacketBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_SpeedGuidePacket> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_SpeedGuidePacket>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_SpeedGuidePacket> CreateDF_SpeedGuidePacket(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> packet = 0) {
  DF_SpeedGuidePacketBuilder builder_(_fbb);
  builder_.add_packet(packet);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DF_SpeedGuidePacket> CreateDF_SpeedGuidePacketDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const std::vector<uint8_t> *packet = nullptr) {
  auto packet__ = packet ? _fbb.CreateVector<uint8_t>(*packet) : 0;
  return MECData::CreateDF_SpeedGuidePacket(
      _fbb,
      packet__);
}

struct DF_RoutePlanPacket FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_RoutePlanPacketBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_PACKET = 4
  };
  const ::flatbuffers::Vector<uint8_t> *packet() const {
    return GetPointer<const ::flatbuffers::Vector<uint8_t> *>(VT_PACKET);
  }
  const MECData::MSG_RoutePlan *packet_nested_root() const {
    const auto _f = packet();
    return _f ? ::flatbuffers::GetRoot<MECData::MSG_RoutePlan>(_f->Data())
              : nullptr;
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_PACKET) &&
           verifier.VerifyVector(packet()) &&
           verifier.VerifyNestedFlatBuffer<MECData::MSG_RoutePlan>(packet(), nullptr) &&
           verifier.EndTable();
  }
};

struct DF_RoutePlanPacketBuilder {
  typedef DF_RoutePlanPacket Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_packet(::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> packet) {
    fbb_.AddOffset(DF_RoutePlanPacket::VT_PACKET, packet);
  }
  explicit DF_RoutePlanPacketBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_RoutePlanPacket> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_RoutePlanPacket>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_RoutePlanPacket> CreateDF_RoutePlanPacket(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> packet = 0) {
  DF_RoutePlanPacketBuilder builder_(_fbb);
  builder_.add_packet(packet);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DF_RoutePlanPacket> CreateDF_RoutePlanPacketDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const std::vector<uint8_t> *packet = nullptr) {
  auto packet__ = packet ? _fbb.CreateVector<uint8_t>(*packet) : 0;
  return MECData::CreateDF_RoutePlanPacket(
      _fbb,
      packet__);
}

struct DF_PlatooningManagementMessagenPacket FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_PlatooningManagementMessagenPacketBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_PACKET = 4
  };
  const ::flatbuffers::Vector<uint8_t> *packet() const {
    return GetPointer<const ::flatbuffers::Vector<uint8_t> *>(VT_PACKET);
  }
  const MECData::MSG_PlatooningManagementMessage *packet_nested_root() const {
    const auto _f = packet();
    return _f ? ::flatbuffers::GetRoot<MECData::MSG_PlatooningManagementMessage>(_f->Data())
              : nullptr;
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_PACKET) &&
           verifier.VerifyVector(packet()) &&
           verifier.VerifyNestedFlatBuffer<MECData::MSG_PlatooningManagementMessage>(packet(), nullptr) &&
           verifier.EndTable();
  }
};

struct DF_PlatooningManagementMessagenPacketBuilder {
  typedef DF_PlatooningManagementMessagenPacket Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_packet(::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> packet) {
    fbb_.AddOffset(DF_PlatooningManagementMessagenPacket::VT_PACKET, packet);
  }
  explicit DF_PlatooningManagementMessagenPacketBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_PlatooningManagementMessagenPacket> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_PlatooningManagementMessagenPacket>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_PlatooningManagementMessagenPacket> CreateDF_PlatooningManagementMessagenPacket(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> packet = 0) {
  DF_PlatooningManagementMessagenPacketBuilder builder_(_fbb);
  builder_.add_packet(packet);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DF_PlatooningManagementMessagenPacket> CreateDF_PlatooningManagementMessagenPacketDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const std::vector<uint8_t> *packet = nullptr) {
  auto packet__ = packet ? _fbb.CreateVector<uint8_t>(*packet) : 0;
  return MECData::CreateDF_PlatooningManagementMessagenPacket(
      _fbb,
      packet__);
}

struct DF_TicketPacket FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_TicketPacketBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_PACKET = 4
  };
  const ::flatbuffers::Vector<uint8_t> *packet() const {
    return GetPointer<const ::flatbuffers::Vector<uint8_t> *>(VT_PACKET);
  }
  const MECData::MSG_Ticket *packet_nested_root() const {
    const auto _f = packet();
    return _f ? ::flatbuffers::GetRoot<MECData::MSG_Ticket>(_f->Data())
              : nullptr;
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_PACKET) &&
           verifier.VerifyVector(packet()) &&
           verifier.VerifyNestedFlatBuffer<MECData::MSG_Ticket>(packet(), nullptr) &&
           verifier.EndTable();
  }
};

struct DF_TicketPacketBuilder {
  typedef DF_TicketPacket Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_packet(::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> packet) {
    fbb_.AddOffset(DF_TicketPacket::VT_PACKET, packet);
  }
  explicit DF_TicketPacketBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_TicketPacket> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_TicketPacket>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_TicketPacket> CreateDF_TicketPacket(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> packet = 0) {
  DF_TicketPacketBuilder builder_(_fbb);
  builder_.add_packet(packet);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DF_TicketPacket> CreateDF_TicketPacketDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const std::vector<uint8_t> *packet = nullptr) {
  auto packet__ = packet ? _fbb.CreateVector<uint8_t>(*packet) : 0;
  return MECData::CreateDF_TicketPacket(
      _fbb,
      packet__);
}

struct DF_WildCardPacket FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_WildCardPacketBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_PACKET = 4
  };
  const ::flatbuffers::Vector<uint8_t> *packet() const {
    return GetPointer<const ::flatbuffers::Vector<uint8_t> *>(VT_PACKET);
  }
  const MECData::MSG_WildCard *packet_nested_root() const {
    const auto _f = packet();
    return _f ? ::flatbuffers::GetRoot<MECData::MSG_WildCard>(_f->Data())
              : nullptr;
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_PACKET) &&
           verifier.VerifyVector(packet()) &&
           verifier.VerifyNestedFlatBuffer<MECData::MSG_WildCard>(packet(), nullptr) &&
           verifier.EndTable();
  }
};

struct DF_WildCardPacketBuilder {
  typedef DF_WildCardPacket Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_packet(::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> packet) {
    fbb_.AddOffset(DF_WildCardPacket::VT_PACKET, packet);
  }
  explicit DF_WildCardPacketBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_WildCardPacket> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_WildCardPacket>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_WildCardPacket> CreateDF_WildCardPacket(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> packet = 0) {
  DF_WildCardPacketBuilder builder_(_fbb);
  builder_.add_packet(packet);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DF_WildCardPacket> CreateDF_WildCardPacketDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const std::vector<uint8_t> *packet = nullptr) {
  auto packet__ = packet ? _fbb.CreateVector<uint8_t>(*packet) : 0;
  return MECData::CreateDF_WildCardPacket(
      _fbb,
      packet__);
}

struct DF_ZBRSFeaturesPacket FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_ZBRSFeaturesPacketBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_PACKET = 4
  };
  const ::flatbuffers::Vector<uint8_t> *packet() const {
    return GetPointer<const ::flatbuffers::Vector<uint8_t> *>(VT_PACKET);
  }
  const MECData::MSG_ZBRSFeatures *packet_nested_root() const {
    const auto _f = packet();
    return _f ? ::flatbuffers::GetRoot<MECData::MSG_ZBRSFeatures>(_f->Data())
              : nullptr;
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_PACKET) &&
           verifier.VerifyVector(packet()) &&
           verifier.VerifyNestedFlatBuffer<MECData::MSG_ZBRSFeatures>(packet(), nullptr) &&
           verifier.EndTable();
  }
};

struct DF_ZBRSFeaturesPacketBuilder {
  typedef DF_ZBRSFeaturesPacket Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_packet(::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> packet) {
    fbb_.AddOffset(DF_ZBRSFeaturesPacket::VT_PACKET, packet);
  }
  explicit DF_ZBRSFeaturesPacketBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_ZBRSFeaturesPacket> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_ZBRSFeaturesPacket>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_ZBRSFeaturesPacket> CreateDF_ZBRSFeaturesPacket(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> packet = 0) {
  DF_ZBRSFeaturesPacketBuilder builder_(_fbb);
  builder_.add_packet(packet);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DF_ZBRSFeaturesPacket> CreateDF_ZBRSFeaturesPacketDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const std::vector<uint8_t> *packet = nullptr) {
  auto packet__ = packet ? _fbb.CreateVector<uint8_t>(*packet) : 0;
  return MECData::CreateDF_ZBRSFeaturesPacket(
      _fbb,
      packet__);
}

struct DF_MECSysResPacket FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_MECSysResPacketBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_PACKET = 4
  };
  const ::flatbuffers::Vector<uint8_t> *packet() const {
    return GetPointer<const ::flatbuffers::Vector<uint8_t> *>(VT_PACKET);
  }
  const MECData::MSG_MECSysRes *packet_nested_root() const {
    const auto _f = packet();
    return _f ? ::flatbuffers::GetRoot<MECData::MSG_MECSysRes>(_f->Data())
              : nullptr;
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_PACKET) &&
           verifier.VerifyVector(packet()) &&
           verifier.VerifyNestedFlatBuffer<MECData::MSG_MECSysRes>(packet(), nullptr) &&
           verifier.EndTable();
  }
};

struct DF_MECSysResPacketBuilder {
  typedef DF_MECSysResPacket Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_packet(::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> packet) {
    fbb_.AddOffset(DF_MECSysResPacket::VT_PACKET, packet);
  }
  explicit DF_MECSysResPacketBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_MECSysResPacket> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_MECSysResPacket>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_MECSysResPacket> CreateDF_MECSysResPacket(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> packet = 0) {
  DF_MECSysResPacketBuilder builder_(_fbb);
  builder_.add_packet(packet);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DF_MECSysResPacket> CreateDF_MECSysResPacketDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const std::vector<uint8_t> *packet = nullptr) {
  auto packet__ = packet ? _fbb.CreateVector<uint8_t>(*packet) : 0;
  return MECData::CreateDF_MECSysResPacket(
      _fbb,
      packet__);
}

struct MSG_InterceptedPacket FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef MSG_InterceptedPacketBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_SOURCE = 4,
    VT_DESTINATIONS = 6,
    VT_TIME = 8,
    VT_PACKET_TYPE = 10,
    VT_PACKET = 12
  };
  uint16_t source() const {
    return GetField<uint16_t>(VT_SOURCE, 0);
  }
  const ::flatbuffers::Vector<uint16_t> *destinations() const {
    return GetPointer<const ::flatbuffers::Vector<uint16_t> *>(VT_DESTINATIONS);
  }
  int64_t time() const {
    return GetField<int64_t>(VT_TIME, 0);
  }
  MECData::DF_PacketContent packet_type() const {
    return static_cast<MECData::DF_PacketContent>(GetField<uint8_t>(VT_PACKET_TYPE, 0));
  }
  const void *packet() const {
    return GetPointer<const void *>(VT_PACKET);
  }
  template<typename T> const T *packet_as() const;
  const MECData::DF_AppInfoPacket *packet_as_DF_AppInfoPacket() const {
    return packet_type() == MECData::DF_PacketContent_DF_AppInfoPacket ? static_cast<const MECData::DF_AppInfoPacket *>(packet()) : nullptr;
  }
  const MECData::DF_ErrorImmediateReportPacket *packet_as_DF_ErrorImmediateReportPacket() const {
    return packet_type() == MECData::DF_PacketContent_DF_ErrorImmediateReportPacket ? static_cast<const MECData::DF_ErrorImmediateReportPacket *>(packet()) : nullptr;
  }
  const MECData::DF_RootOTAPacket *packet_as_DF_RootOTAPacket() const {
    return packet_type() == MECData::DF_PacketContent_DF_RootOTAPacket ? static_cast<const MECData::DF_RootOTAPacket *>(packet()) : nullptr;
  }
  const MECData::DF_AppOTAPacket *packet_as_DF_AppOTAPacket() const {
    return packet_type() == MECData::DF_PacketContent_DF_AppOTAPacket ? static_cast<const MECData::DF_AppOTAPacket *>(packet()) : nullptr;
  }
  const MECData::DF_VolumeLanePacket *packet_as_DF_VolumeLanePacket() const {
    return packet_type() == MECData::DF_PacketContent_DF_VolumeLanePacket ? static_cast<const MECData::DF_VolumeLanePacket *>(packet()) : nullptr;
  }
  const MECData::DF_DensityLanePacket *packet_as_DF_DensityLanePacket() const {
    return packet_type() == MECData::DF_PacketContent_DF_DensityLanePacket ? static_cast<const MECData::DF_DensityLanePacket *>(packet()) : nullptr;
  }
  const MECData::DF_SpeedLaneAreaPacket *packet_as_DF_SpeedLaneAreaPacket() const {
    return packet_type() == MECData::DF_PacketContent_DF_SpeedLaneAreaPacket ? static_cast<const MECData::DF_SpeedLaneAreaPacket *>(packet()) : nullptr;
  }
  const MECData::DF_SpeedLanePointPacket *packet_as_DF_SpeedLanePointPacket() const {
    return packet_type() == MECData::DF_PacketContent_DF_SpeedLanePointPacket ? static_cast<const MECData::DF_SpeedLanePointPacket *>(packet()) : nullptr;
  }
  const MECData::DF_TrafficSignPacket *packet_as_DF_TrafficSignPacket() const {
    return packet_type() == MECData::DF_PacketContent_DF_TrafficSignPacket ? static_cast<const MECData::DF_TrafficSignPacket *>(packet()) : nullptr;
  }
  const MECData::DF_TrafficEventPacket *packet_as_DF_TrafficEventPacket() const {
    return packet_type() == MECData::DF_PacketContent_DF_TrafficEventPacket ? static_cast<const MECData::DF_TrafficEventPacket *>(packet()) : nullptr;
  }
  const MECData::DF_SafetyMessagePacket *packet_as_DF_SafetyMessagePacket() const {
    return packet_type() == MECData::DF_PacketContent_DF_SafetyMessagePacket ? static_cast<const MECData::DF_SafetyMessagePacket *>(packet()) : nullptr;
  }
  const MECData::DF_SignalPhaseAndTimingPacket *packet_as_DF_SignalPhaseAndTimingPacket() const {
    return packet_type() == MECData::DF_PacketContent_DF_SignalPhaseAndTimingPacket ? static_cast<const MECData::DF_SignalPhaseAndTimingPacket *>(packet()) : nullptr;
  }
  const MECData::DF_MapPacket *packet_as_DF_MapPacket() const {
    return packet_type() == MECData::DF_PacketContent_DF_MapPacket ? static_cast<const MECData::DF_MapPacket *>(packet()) : nullptr;
  }
  const MECData::DF_RoadSideCoordinationPacket *packet_as_DF_RoadSideCoordinationPacket() const {
    return packet_type() == MECData::DF_PacketContent_DF_RoadSideCoordinationPacket ? static_cast<const MECData::DF_RoadSideCoordinationPacket *>(packet()) : nullptr;
  }
  const MECData::DF_RoadSideInformationPacket *packet_as_DF_RoadSideInformationPacket() const {
    return packet_type() == MECData::DF_PacketContent_DF_RoadSideInformationPacket ? static_cast<const MECData::DF_RoadSideInformationPacket *>(packet()) : nullptr;
  }
  const MECData::DF_RoadsideSafetyMessagePacket *packet_as_DF_RoadsideSafetyMessagePacket() const {
    return packet_type() == MECData::DF_PacketContent_DF_RoadsideSafetyMessagePacket ? static_cast<const MECData::DF_RoadsideSafetyMessagePacket *>(packet()) : nullptr;
  }
  const MECData::DF_VehIntentionAndRequestPacket *packet_as_DF_VehIntentionAndRequestPacket() const {
    return packet_type() == MECData::DF_PacketContent_DF_VehIntentionAndRequestPacket ? static_cast<const MECData::DF_VehIntentionAndRequestPacket *>(packet()) : nullptr;
  }
  const MECData::DF_DedicatedLaneControlPacket *packet_as_DF_DedicatedLaneControlPacket() const {
    return packet_type() == MECData::DF_PacketContent_DF_DedicatedLaneControlPacket ? static_cast<const MECData::DF_DedicatedLaneControlPacket *>(packet()) : nullptr;
  }
  const MECData::DF_VariableSpeedLimitPacket *packet_as_DF_VariableSpeedLimitPacket() const {
    return packet_type() == MECData::DF_PacketContent_DF_VariableSpeedLimitPacket ? static_cast<const MECData::DF_VariableSpeedLimitPacket *>(packet()) : nullptr;
  }
  const MECData::DF_RampMeteringPacket *packet_as_DF_RampMeteringPacket() const {
    return packet_type() == MECData::DF_PacketContent_DF_RampMeteringPacket ? static_cast<const MECData::DF_RampMeteringPacket *>(packet()) : nullptr;
  }
  const MECData::DF_NowWeatherPacket *packet_as_DF_NowWeatherPacket() const {
    return packet_type() == MECData::DF_PacketContent_DF_NowWeatherPacket ? static_cast<const MECData::DF_NowWeatherPacket *>(packet()) : nullptr;
  }
  const MECData::DF_ForecastWeatherPacket *packet_as_DF_ForecastWeatherPacket() const {
    return packet_type() == MECData::DF_PacketContent_DF_ForecastWeatherPacket ? static_cast<const MECData::DF_ForecastWeatherPacket *>(packet()) : nullptr;
  }
  const MECData::DF_ForecastRainPacket *packet_as_DF_ForecastRainPacket() const {
    return packet_type() == MECData::DF_PacketContent_DF_ForecastRainPacket ? static_cast<const MECData::DF_ForecastRainPacket *>(packet()) : nullptr;
  }
  const MECData::DF_SignalSchemePacket *packet_as_DF_SignalSchemePacket() const {
    return packet_type() == MECData::DF_PacketContent_DF_SignalSchemePacket ? static_cast<const MECData::DF_SignalSchemePacket *>(packet()) : nullptr;
  }
  const MECData::DF_TrafficFlowPacket *packet_as_DF_TrafficFlowPacket() const {
    return packet_type() == MECData::DF_PacketContent_DF_TrafficFlowPacket ? static_cast<const MECData::DF_TrafficFlowPacket *>(packet()) : nullptr;
  }
  const MECData::DF_TrajectoryPointsPacket *packet_as_DF_TrajectoryPointsPacket() const {
    return packet_type() == MECData::DF_PacketContent_DF_TrajectoryPointsPacket ? static_cast<const MECData::DF_TrajectoryPointsPacket *>(packet()) : nullptr;
  }
  const MECData::DF_QueueLengthPacket *packet_as_DF_QueueLengthPacket() const {
    return packet_type() == MECData::DF_PacketContent_DF_QueueLengthPacket ? static_cast<const MECData::DF_QueueLengthPacket *>(packet()) : nullptr;
  }
  const MECData::DF_VariableMessageSignPacket *packet_as_DF_VariableMessageSignPacket() const {
    return packet_type() == MECData::DF_PacketContent_DF_VariableMessageSignPacket ? static_cast<const MECData::DF_VariableMessageSignPacket *>(packet()) : nullptr;
  }
  const MECData::DF_CarbonEmissionPacket *packet_as_DF_CarbonEmissionPacket() const {
    return packet_type() == MECData::DF_PacketContent_DF_CarbonEmissionPacket ? static_cast<const MECData::DF_CarbonEmissionPacket *>(packet()) : nullptr;
  }
  const MECData::DF_SignalExecutionPacket *packet_as_DF_SignalExecutionPacket() const {
    return packet_type() == MECData::DF_PacketContent_DF_SignalExecutionPacket ? static_cast<const MECData::DF_SignalExecutionPacket *>(packet()) : nullptr;
  }
  const MECData::DF_SignalRequestPacket *packet_as_DF_SignalRequestPacket() const {
    return packet_type() == MECData::DF_PacketContent_DF_SignalRequestPacket ? static_cast<const MECData::DF_SignalRequestPacket *>(packet()) : nullptr;
  }
  const MECData::DF_CollisionWarningPacket *packet_as_DF_CollisionWarningPacket() const {
    return packet_type() == MECData::DF_PacketContent_DF_CollisionWarningPacket ? static_cast<const MECData::DF_CollisionWarningPacket *>(packet()) : nullptr;
  }
  const MECData::DF_SpeedGuidePacket *packet_as_DF_SpeedGuidePacket() const {
    return packet_type() == MECData::DF_PacketContent_DF_SpeedGuidePacket ? static_cast<const MECData::DF_SpeedGuidePacket *>(packet()) : nullptr;
  }
  const MECData::DF_RoutePlanPacket *packet_as_DF_RoutePlanPacket() const {
    return packet_type() == MECData::DF_PacketContent_DF_RoutePlanPacket ? static_cast<const MECData::DF_RoutePlanPacket *>(packet()) : nullptr;
  }
  const MECData::DF_PlatooningManagementMessagenPacket *packet_as_DF_PlatooningManagementMessagenPacket() const {
    return packet_type() == MECData::DF_PacketContent_DF_PlatooningManagementMessagenPacket ? static_cast<const MECData::DF_PlatooningManagementMessagenPacket *>(packet()) : nullptr;
  }
  const MECData::DF_TicketPacket *packet_as_DF_TicketPacket() const {
    return packet_type() == MECData::DF_PacketContent_DF_TicketPacket ? static_cast<const MECData::DF_TicketPacket *>(packet()) : nullptr;
  }
  const MECData::DF_WildCardPacket *packet_as_DF_WildCardPacket() const {
    return packet_type() == MECData::DF_PacketContent_DF_WildCardPacket ? static_cast<const MECData::DF_WildCardPacket *>(packet()) : nullptr;
  }
  const MECData::DF_ZBRSFeaturesPacket *packet_as_DF_ZBRSFeaturesPacket() const {
    return packet_type() == MECData::DF_PacketContent_DF_ZBRSFeaturesPacket ? static_cast<const MECData::DF_ZBRSFeaturesPacket *>(packet()) : nullptr;
  }
  const MECData::DF_MECSysResPacket *packet_as_DF_MECSysResPacket() const {
    return packet_type() == MECData::DF_PacketContent_DF_MECSysResPacket ? static_cast<const MECData::DF_MECSysResPacket *>(packet()) : nullptr;
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<uint16_t>(verifier, VT_SOURCE, 2) &&
           VerifyOffset(verifier, VT_DESTINATIONS) &&
           verifier.VerifyVector(destinations()) &&
           VerifyField<int64_t>(verifier, VT_TIME, 8) &&
           VerifyField<uint8_t>(verifier, VT_PACKET_TYPE, 1) &&
           VerifyOffset(verifier, VT_PACKET) &&
           VerifyDF_PacketContent(verifier, packet(), packet_type()) &&
           verifier.EndTable();
  }
};

template<> inline const MECData::DF_AppInfoPacket *MSG_InterceptedPacket::packet_as<MECData::DF_AppInfoPacket>() const {
  return packet_as_DF_AppInfoPacket();
}

template<> inline const MECData::DF_ErrorImmediateReportPacket *MSG_InterceptedPacket::packet_as<MECData::DF_ErrorImmediateReportPacket>() const {
  return packet_as_DF_ErrorImmediateReportPacket();
}

template<> inline const MECData::DF_RootOTAPacket *MSG_InterceptedPacket::packet_as<MECData::DF_RootOTAPacket>() const {
  return packet_as_DF_RootOTAPacket();
}

template<> inline const MECData::DF_AppOTAPacket *MSG_InterceptedPacket::packet_as<MECData::DF_AppOTAPacket>() const {
  return packet_as_DF_AppOTAPacket();
}

template<> inline const MECData::DF_VolumeLanePacket *MSG_InterceptedPacket::packet_as<MECData::DF_VolumeLanePacket>() const {
  return packet_as_DF_VolumeLanePacket();
}

template<> inline const MECData::DF_DensityLanePacket *MSG_InterceptedPacket::packet_as<MECData::DF_DensityLanePacket>() const {
  return packet_as_DF_DensityLanePacket();
}

template<> inline const MECData::DF_SpeedLaneAreaPacket *MSG_InterceptedPacket::packet_as<MECData::DF_SpeedLaneAreaPacket>() const {
  return packet_as_DF_SpeedLaneAreaPacket();
}

template<> inline const MECData::DF_SpeedLanePointPacket *MSG_InterceptedPacket::packet_as<MECData::DF_SpeedLanePointPacket>() const {
  return packet_as_DF_SpeedLanePointPacket();
}

template<> inline const MECData::DF_TrafficSignPacket *MSG_InterceptedPacket::packet_as<MECData::DF_TrafficSignPacket>() const {
  return packet_as_DF_TrafficSignPacket();
}

template<> inline const MECData::DF_TrafficEventPacket *MSG_InterceptedPacket::packet_as<MECData::DF_TrafficEventPacket>() const {
  return packet_as_DF_TrafficEventPacket();
}

template<> inline const MECData::DF_SafetyMessagePacket *MSG_InterceptedPacket::packet_as<MECData::DF_SafetyMessagePacket>() const {
  return packet_as_DF_SafetyMessagePacket();
}

template<> inline const MECData::DF_SignalPhaseAndTimingPacket *MSG_InterceptedPacket::packet_as<MECData::DF_SignalPhaseAndTimingPacket>() const {
  return packet_as_DF_SignalPhaseAndTimingPacket();
}

template<> inline const MECData::DF_MapPacket *MSG_InterceptedPacket::packet_as<MECData::DF_MapPacket>() const {
  return packet_as_DF_MapPacket();
}

template<> inline const MECData::DF_RoadSideCoordinationPacket *MSG_InterceptedPacket::packet_as<MECData::DF_RoadSideCoordinationPacket>() const {
  return packet_as_DF_RoadSideCoordinationPacket();
}

template<> inline const MECData::DF_RoadSideInformationPacket *MSG_InterceptedPacket::packet_as<MECData::DF_RoadSideInformationPacket>() const {
  return packet_as_DF_RoadSideInformationPacket();
}

template<> inline const MECData::DF_RoadsideSafetyMessagePacket *MSG_InterceptedPacket::packet_as<MECData::DF_RoadsideSafetyMessagePacket>() const {
  return packet_as_DF_RoadsideSafetyMessagePacket();
}

template<> inline const MECData::DF_VehIntentionAndRequestPacket *MSG_InterceptedPacket::packet_as<MECData::DF_VehIntentionAndRequestPacket>() const {
  return packet_as_DF_VehIntentionAndRequestPacket();
}

template<> inline const MECData::DF_DedicatedLaneControlPacket *MSG_InterceptedPacket::packet_as<MECData::DF_DedicatedLaneControlPacket>() const {
  return packet_as_DF_DedicatedLaneControlPacket();
}

template<> inline const MECData::DF_VariableSpeedLimitPacket *MSG_InterceptedPacket::packet_as<MECData::DF_VariableSpeedLimitPacket>() const {
  return packet_as_DF_VariableSpeedLimitPacket();
}

template<> inline const MECData::DF_RampMeteringPacket *MSG_InterceptedPacket::packet_as<MECData::DF_RampMeteringPacket>() const {
  return packet_as_DF_RampMeteringPacket();
}

template<> inline const MECData::DF_NowWeatherPacket *MSG_InterceptedPacket::packet_as<MECData::DF_NowWeatherPacket>() const {
  return packet_as_DF_NowWeatherPacket();
}

template<> inline const MECData::DF_ForecastWeatherPacket *MSG_InterceptedPacket::packet_as<MECData::DF_ForecastWeatherPacket>() const {
  return packet_as_DF_ForecastWeatherPacket();
}

template<> inline const MECData::DF_ForecastRainPacket *MSG_InterceptedPacket::packet_as<MECData::DF_ForecastRainPacket>() const {
  return packet_as_DF_ForecastRainPacket();
}

template<> inline const MECData::DF_SignalSchemePacket *MSG_InterceptedPacket::packet_as<MECData::DF_SignalSchemePacket>() const {
  return packet_as_DF_SignalSchemePacket();
}

template<> inline const MECData::DF_TrafficFlowPacket *MSG_InterceptedPacket::packet_as<MECData::DF_TrafficFlowPacket>() const {
  return packet_as_DF_TrafficFlowPacket();
}

template<> inline const MECData::DF_TrajectoryPointsPacket *MSG_InterceptedPacket::packet_as<MECData::DF_TrajectoryPointsPacket>() const {
  return packet_as_DF_TrajectoryPointsPacket();
}

template<> inline const MECData::DF_QueueLengthPacket *MSG_InterceptedPacket::packet_as<MECData::DF_QueueLengthPacket>() const {
  return packet_as_DF_QueueLengthPacket();
}

template<> inline const MECData::DF_VariableMessageSignPacket *MSG_InterceptedPacket::packet_as<MECData::DF_VariableMessageSignPacket>() const {
  return packet_as_DF_VariableMessageSignPacket();
}

template<> inline const MECData::DF_CarbonEmissionPacket *MSG_InterceptedPacket::packet_as<MECData::DF_CarbonEmissionPacket>() const {
  return packet_as_DF_CarbonEmissionPacket();
}

template<> inline const MECData::DF_SignalExecutionPacket *MSG_InterceptedPacket::packet_as<MECData::DF_SignalExecutionPacket>() const {
  return packet_as_DF_SignalExecutionPacket();
}

template<> inline const MECData::DF_SignalRequestPacket *MSG_InterceptedPacket::packet_as<MECData::DF_SignalRequestPacket>() const {
  return packet_as_DF_SignalRequestPacket();
}

template<> inline const MECData::DF_CollisionWarningPacket *MSG_InterceptedPacket::packet_as<MECData::DF_CollisionWarningPacket>() const {
  return packet_as_DF_CollisionWarningPacket();
}

template<> inline const MECData::DF_SpeedGuidePacket *MSG_InterceptedPacket::packet_as<MECData::DF_SpeedGuidePacket>() const {
  return packet_as_DF_SpeedGuidePacket();
}

template<> inline const MECData::DF_RoutePlanPacket *MSG_InterceptedPacket::packet_as<MECData::DF_RoutePlanPacket>() const {
  return packet_as_DF_RoutePlanPacket();
}

template<> inline const MECData::DF_PlatooningManagementMessagenPacket *MSG_InterceptedPacket::packet_as<MECData::DF_PlatooningManagementMessagenPacket>() const {
  return packet_as_DF_PlatooningManagementMessagenPacket();
}

template<> inline const MECData::DF_TicketPacket *MSG_InterceptedPacket::packet_as<MECData::DF_TicketPacket>() const {
  return packet_as_DF_TicketPacket();
}

template<> inline const MECData::DF_WildCardPacket *MSG_InterceptedPacket::packet_as<MECData::DF_WildCardPacket>() const {
  return packet_as_DF_WildCardPacket();
}

template<> inline const MECData::DF_ZBRSFeaturesPacket *MSG_InterceptedPacket::packet_as<MECData::DF_ZBRSFeaturesPacket>() const {
  return packet_as_DF_ZBRSFeaturesPacket();
}

template<> inline const MECData::DF_MECSysResPacket *MSG_InterceptedPacket::packet_as<MECData::DF_MECSysResPacket>() const {
  return packet_as_DF_MECSysResPacket();
}

struct MSG_InterceptedPacketBuilder {
  typedef MSG_InterceptedPacket Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_source(uint16_t source) {
    fbb_.AddElement<uint16_t>(MSG_InterceptedPacket::VT_SOURCE, source, 0);
  }
  void add_destinations(::flatbuffers::Offset<::flatbuffers::Vector<uint16_t>> destinations) {
    fbb_.AddOffset(MSG_InterceptedPacket::VT_DESTINATIONS, destinations);
  }
  void add_time(int64_t time) {
    fbb_.AddElement<int64_t>(MSG_InterceptedPacket::VT_TIME, time, 0);
  }
  void add_packet_type(MECData::DF_PacketContent packet_type) {
    fbb_.AddElement<uint8_t>(MSG_InterceptedPacket::VT_PACKET_TYPE, static_cast<uint8_t>(packet_type), 0);
  }
  void add_packet(::flatbuffers::Offset<void> packet) {
    fbb_.AddOffset(MSG_InterceptedPacket::VT_PACKET, packet);
  }
  explicit MSG_InterceptedPacketBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<MSG_InterceptedPacket> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<MSG_InterceptedPacket>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<MSG_InterceptedPacket> CreateMSG_InterceptedPacket(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    uint16_t source = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<uint16_t>> destinations = 0,
    int64_t time = 0,
    MECData::DF_PacketContent packet_type = MECData::DF_PacketContent_NONE,
    ::flatbuffers::Offset<void> packet = 0) {
  MSG_InterceptedPacketBuilder builder_(_fbb);
  builder_.add_time(time);
  builder_.add_packet(packet);
  builder_.add_destinations(destinations);
  builder_.add_source(source);
  builder_.add_packet_type(packet_type);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<MSG_InterceptedPacket> CreateMSG_InterceptedPacketDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    uint16_t source = 0,
    const std::vector<uint16_t> *destinations = nullptr,
    int64_t time = 0,
    MECData::DF_PacketContent packet_type = MECData::DF_PacketContent_NONE,
    ::flatbuffers::Offset<void> packet = 0) {
  auto destinations__ = destinations ? _fbb.CreateVector<uint16_t>(*destinations) : 0;
  return MECData::CreateMSG_InterceptedPacket(
      _fbb,
      source,
      destinations__,
      time,
      packet_type,
      packet);
}

inline bool VerifyDF_PacketContent(::flatbuffers::Verifier &verifier, const void *obj, DF_PacketContent type) {
  switch (type) {
    case DF_PacketContent_NONE: {
      return true;
    }
    case DF_PacketContent_DF_AppInfoPacket: {
      auto ptr = reinterpret_cast<const MECData::DF_AppInfoPacket *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case DF_PacketContent_DF_ErrorImmediateReportPacket: {
      auto ptr = reinterpret_cast<const MECData::DF_ErrorImmediateReportPacket *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case DF_PacketContent_DF_RootOTAPacket: {
      auto ptr = reinterpret_cast<const MECData::DF_RootOTAPacket *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case DF_PacketContent_DF_AppOTAPacket: {
      auto ptr = reinterpret_cast<const MECData::DF_AppOTAPacket *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case DF_PacketContent_DF_VolumeLanePacket: {
      auto ptr = reinterpret_cast<const MECData::DF_VolumeLanePacket *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case DF_PacketContent_DF_DensityLanePacket: {
      auto ptr = reinterpret_cast<const MECData::DF_DensityLanePacket *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case DF_PacketContent_DF_SpeedLaneAreaPacket: {
      auto ptr = reinterpret_cast<const MECData::DF_SpeedLaneAreaPacket *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case DF_PacketContent_DF_SpeedLanePointPacket: {
      auto ptr = reinterpret_cast<const MECData::DF_SpeedLanePointPacket *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case DF_PacketContent_DF_TrafficSignPacket: {
      auto ptr = reinterpret_cast<const MECData::DF_TrafficSignPacket *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case DF_PacketContent_DF_TrafficEventPacket: {
      auto ptr = reinterpret_cast<const MECData::DF_TrafficEventPacket *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case DF_PacketContent_DF_SafetyMessagePacket: {
      auto ptr = reinterpret_cast<const MECData::DF_SafetyMessagePacket *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case DF_PacketContent_DF_SignalPhaseAndTimingPacket: {
      auto ptr = reinterpret_cast<const MECData::DF_SignalPhaseAndTimingPacket *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case DF_PacketContent_DF_MapPacket: {
      auto ptr = reinterpret_cast<const MECData::DF_MapPacket *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case DF_PacketContent_DF_RoadSideCoordinationPacket: {
      auto ptr = reinterpret_cast<const MECData::DF_RoadSideCoordinationPacket *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case DF_PacketContent_DF_RoadSideInformationPacket: {
      auto ptr = reinterpret_cast<const MECData::DF_RoadSideInformationPacket *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case DF_PacketContent_DF_RoadsideSafetyMessagePacket: {
      auto ptr = reinterpret_cast<const MECData::DF_RoadsideSafetyMessagePacket *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case DF_PacketContent_DF_VehIntentionAndRequestPacket: {
      auto ptr = reinterpret_cast<const MECData::DF_VehIntentionAndRequestPacket *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case DF_PacketContent_DF_DedicatedLaneControlPacket: {
      auto ptr = reinterpret_cast<const MECData::DF_DedicatedLaneControlPacket *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case DF_PacketContent_DF_VariableSpeedLimitPacket: {
      auto ptr = reinterpret_cast<const MECData::DF_VariableSpeedLimitPacket *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case DF_PacketContent_DF_RampMeteringPacket: {
      auto ptr = reinterpret_cast<const MECData::DF_RampMeteringPacket *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case DF_PacketContent_DF_NowWeatherPacket: {
      auto ptr = reinterpret_cast<const MECData::DF_NowWeatherPacket *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case DF_PacketContent_DF_ForecastWeatherPacket: {
      auto ptr = reinterpret_cast<const MECData::DF_ForecastWeatherPacket *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case DF_PacketContent_DF_ForecastRainPacket: {
      auto ptr = reinterpret_cast<const MECData::DF_ForecastRainPacket *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case DF_PacketContent_DF_SignalSchemePacket: {
      auto ptr = reinterpret_cast<const MECData::DF_SignalSchemePacket *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case DF_PacketContent_DF_TrafficFlowPacket: {
      auto ptr = reinterpret_cast<const MECData::DF_TrafficFlowPacket *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case DF_PacketContent_DF_TrajectoryPointsPacket: {
      auto ptr = reinterpret_cast<const MECData::DF_TrajectoryPointsPacket *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case DF_PacketContent_DF_QueueLengthPacket: {
      auto ptr = reinterpret_cast<const MECData::DF_QueueLengthPacket *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case DF_PacketContent_DF_VariableMessageSignPacket: {
      auto ptr = reinterpret_cast<const MECData::DF_VariableMessageSignPacket *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case DF_PacketContent_DF_CarbonEmissionPacket: {
      auto ptr = reinterpret_cast<const MECData::DF_CarbonEmissionPacket *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case DF_PacketContent_DF_SignalExecutionPacket: {
      auto ptr = reinterpret_cast<const MECData::DF_SignalExecutionPacket *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case DF_PacketContent_DF_SignalRequestPacket: {
      auto ptr = reinterpret_cast<const MECData::DF_SignalRequestPacket *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case DF_PacketContent_DF_CollisionWarningPacket: {
      auto ptr = reinterpret_cast<const MECData::DF_CollisionWarningPacket *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case DF_PacketContent_DF_SpeedGuidePacket: {
      auto ptr = reinterpret_cast<const MECData::DF_SpeedGuidePacket *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case DF_PacketContent_DF_RoutePlanPacket: {
      auto ptr = reinterpret_cast<const MECData::DF_RoutePlanPacket *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case DF_PacketContent_DF_PlatooningManagementMessagenPacket: {
      auto ptr = reinterpret_cast<const MECData::DF_PlatooningManagementMessagenPacket *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case DF_PacketContent_DF_TicketPacket: {
      auto ptr = reinterpret_cast<const MECData::DF_TicketPacket *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case DF_PacketContent_DF_WildCardPacket: {
      auto ptr = reinterpret_cast<const MECData::DF_WildCardPacket *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case DF_PacketContent_DF_ZBRSFeaturesPacket: {
      auto ptr = reinterpret_cast<const MECData::DF_ZBRSFeaturesPacket *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case DF_PacketContent_DF_MECSysResPacket: {
      auto ptr = reinterpret_cast<const MECData::DF_MECSysResPacket *>(obj);
      return verifier.VerifyTable(ptr);
    }
    default: return true;
  }
}

inline bool VerifyDF_PacketContentVector(::flatbuffers::Verifier &verifier, const ::flatbuffers::Vector<::flatbuffers::Offset<void>> *values, const ::flatbuffers::Vector<uint8_t> *types) {
  if (!values || !types) return !values && !types;
  if (values->size() != types->size()) return false;
  for (::flatbuffers::uoffset_t i = 0; i < values->size(); ++i) {
    if (!VerifyDF_PacketContent(
        verifier,  values->Get(i), types->GetEnum<DF_PacketContent>(i))) {
      return false;
    }
  }
  return true;
}

inline const MECData::MSG_InterceptedPacket *GetMSG_InterceptedPacket(const void *buf) {
  return ::flatbuffers::GetRoot<MECData::MSG_InterceptedPacket>(buf);
}

inline const MECData::MSG_InterceptedPacket *GetSizePrefixedMSG_InterceptedPacket(const void *buf) {
  return ::flatbuffers::GetSizePrefixedRoot<MECData::MSG_InterceptedPacket>(buf);
}

inline bool VerifyMSG_InterceptedPacketBuffer(
    ::flatbuffers::Verifier &verifier) {
  return verifier.VerifyBuffer<MECData::MSG_InterceptedPacket>(nullptr);
}

inline bool VerifySizePrefixedMSG_InterceptedPacketBuffer(
    ::flatbuffers::Verifier &verifier) {
  return verifier.VerifySizePrefixedBuffer<MECData::MSG_InterceptedPacket>(nullptr);
}

inline void FinishMSG_InterceptedPacketBuffer(
    ::flatbuffers::FlatBufferBuilder &fbb,
    ::flatbuffers::Offset<MECData::MSG_InterceptedPacket> root) {
  fbb.Finish(root);
}

inline void FinishSizePrefixedMSG_InterceptedPacketBuffer(
    ::flatbuffers::FlatBufferBuilder &fbb,
    ::flatbuffers::Offset<MECData::MSG_InterceptedPacket> root) {
  fbb.FinishSizePrefixed(root);
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_INTERCEPTEDPACKET_MECDATA_H_
