// automatically generated by the <PERSON>Buffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_LANEATTRIBUTES_MECDATA_H_
#define FLATBUFFERS_GENERATED_LANEATTRIBUTES_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

#include "LaneSharing_generated.h"
#include "LaneTypeAttributes_generated.h"

namespace MECData {

struct DF_LaneAttributes;
struct DF_LaneAttributesBuilder;

struct DF_LaneAttributes FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_LaneAttributesBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_SHAREWITH = 4,
    VT_LANETYPE_TYPE = 6,
    VT_LANETYPE = 8
  };
  const MECData::DF_LaneSharing *shareWith() const {
    return GetPointer<const MECData::DF_LaneSharing *>(VT_SHAREWITH);
  }
  MECData::DF_LaneTypeAttributes laneType_type() const {
    return static_cast<MECData::DF_LaneTypeAttributes>(GetField<uint8_t>(VT_LANETYPE_TYPE, 0));
  }
  const void *laneType() const {
    return GetPointer<const void *>(VT_LANETYPE);
  }
  template<typename T> const T *laneType_as() const;
  const MECData::DF_LaneAttributesVehicle *laneType_as_DF_LaneAttributesVehicle() const {
    return laneType_type() == MECData::DF_LaneTypeAttributes_DF_LaneAttributesVehicle ? static_cast<const MECData::DF_LaneAttributesVehicle *>(laneType()) : nullptr;
  }
  const MECData::DF_LaneAttributesCrosswalk *laneType_as_DF_LaneAttributesCrosswalk() const {
    return laneType_type() == MECData::DF_LaneTypeAttributes_DF_LaneAttributesCrosswalk ? static_cast<const MECData::DF_LaneAttributesCrosswalk *>(laneType()) : nullptr;
  }
  const MECData::DF_LaneAttributesBike *laneType_as_DF_LaneAttributesBike() const {
    return laneType_type() == MECData::DF_LaneTypeAttributes_DF_LaneAttributesBike ? static_cast<const MECData::DF_LaneAttributesBike *>(laneType()) : nullptr;
  }
  const MECData::DF_LaneAttributesSidewalk *laneType_as_DF_LaneAttributesSidewalk() const {
    return laneType_type() == MECData::DF_LaneTypeAttributes_DF_LaneAttributesSidewalk ? static_cast<const MECData::DF_LaneAttributesSidewalk *>(laneType()) : nullptr;
  }
  const MECData::DF_LaneAttributesBarrier *laneType_as_DF_LaneAttributesBarrier() const {
    return laneType_type() == MECData::DF_LaneTypeAttributes_DF_LaneAttributesBarrier ? static_cast<const MECData::DF_LaneAttributesBarrier *>(laneType()) : nullptr;
  }
  const MECData::DF_LaneAttributesStriping *laneType_as_DF_LaneAttributesStriping() const {
    return laneType_type() == MECData::DF_LaneTypeAttributes_DF_LaneAttributesStriping ? static_cast<const MECData::DF_LaneAttributesStriping *>(laneType()) : nullptr;
  }
  const MECData::DF_LaneAttributesTrackedVehicle *laneType_as_DF_LaneAttributesTrackedVehicle() const {
    return laneType_type() == MECData::DF_LaneTypeAttributes_DF_LaneAttributesTrackedVehicle ? static_cast<const MECData::DF_LaneAttributesTrackedVehicle *>(laneType()) : nullptr;
  }
  const MECData::DF_LaneAttributesParking *laneType_as_DF_LaneAttributesParking() const {
    return laneType_type() == MECData::DF_LaneTypeAttributes_DF_LaneAttributesParking ? static_cast<const MECData::DF_LaneAttributesParking *>(laneType()) : nullptr;
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_SHAREWITH) &&
           verifier.VerifyTable(shareWith()) &&
           VerifyField<uint8_t>(verifier, VT_LANETYPE_TYPE, 1) &&
           VerifyOffsetRequired(verifier, VT_LANETYPE) &&
           VerifyDF_LaneTypeAttributes(verifier, laneType(), laneType_type()) &&
           verifier.EndTable();
  }
};

template<> inline const MECData::DF_LaneAttributesVehicle *DF_LaneAttributes::laneType_as<MECData::DF_LaneAttributesVehicle>() const {
  return laneType_as_DF_LaneAttributesVehicle();
}

template<> inline const MECData::DF_LaneAttributesCrosswalk *DF_LaneAttributes::laneType_as<MECData::DF_LaneAttributesCrosswalk>() const {
  return laneType_as_DF_LaneAttributesCrosswalk();
}

template<> inline const MECData::DF_LaneAttributesBike *DF_LaneAttributes::laneType_as<MECData::DF_LaneAttributesBike>() const {
  return laneType_as_DF_LaneAttributesBike();
}

template<> inline const MECData::DF_LaneAttributesSidewalk *DF_LaneAttributes::laneType_as<MECData::DF_LaneAttributesSidewalk>() const {
  return laneType_as_DF_LaneAttributesSidewalk();
}

template<> inline const MECData::DF_LaneAttributesBarrier *DF_LaneAttributes::laneType_as<MECData::DF_LaneAttributesBarrier>() const {
  return laneType_as_DF_LaneAttributesBarrier();
}

template<> inline const MECData::DF_LaneAttributesStriping *DF_LaneAttributes::laneType_as<MECData::DF_LaneAttributesStriping>() const {
  return laneType_as_DF_LaneAttributesStriping();
}

template<> inline const MECData::DF_LaneAttributesTrackedVehicle *DF_LaneAttributes::laneType_as<MECData::DF_LaneAttributesTrackedVehicle>() const {
  return laneType_as_DF_LaneAttributesTrackedVehicle();
}

template<> inline const MECData::DF_LaneAttributesParking *DF_LaneAttributes::laneType_as<MECData::DF_LaneAttributesParking>() const {
  return laneType_as_DF_LaneAttributesParking();
}

struct DF_LaneAttributesBuilder {
  typedef DF_LaneAttributes Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_shareWith(::flatbuffers::Offset<MECData::DF_LaneSharing> shareWith) {
    fbb_.AddOffset(DF_LaneAttributes::VT_SHAREWITH, shareWith);
  }
  void add_laneType_type(MECData::DF_LaneTypeAttributes laneType_type) {
    fbb_.AddElement<uint8_t>(DF_LaneAttributes::VT_LANETYPE_TYPE, static_cast<uint8_t>(laneType_type), 0);
  }
  void add_laneType(::flatbuffers::Offset<void> laneType) {
    fbb_.AddOffset(DF_LaneAttributes::VT_LANETYPE, laneType);
  }
  explicit DF_LaneAttributesBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_LaneAttributes> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_LaneAttributes>(end);
    fbb_.Required(o, DF_LaneAttributes::VT_LANETYPE);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_LaneAttributes> CreateDF_LaneAttributes(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<MECData::DF_LaneSharing> shareWith = 0,
    MECData::DF_LaneTypeAttributes laneType_type = MECData::DF_LaneTypeAttributes_NONE,
    ::flatbuffers::Offset<void> laneType = 0) {
  DF_LaneAttributesBuilder builder_(_fbb);
  builder_.add_laneType(laneType);
  builder_.add_shareWith(shareWith);
  builder_.add_laneType_type(laneType_type);
  return builder_.Finish();
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_LANEATTRIBUTES_MECDATA_H_
