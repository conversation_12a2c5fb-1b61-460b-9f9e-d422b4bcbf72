// automatically generated by the <PERSON>Buffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_BRAKEAPPLIEDSTATUS_MECDATA_H_
#define FLATBUFFERS_GENERATED_BRAKEAPPLIEDSTATUS_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

namespace MECData {

struct DE_BrakeAppliedStatus;
struct DE_BrakeAppliedStatusBuilder;

struct DE_BrakeAppliedStatus FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DE_BrakeAppliedStatusBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_WHEELBRAKES = 4
  };
  int8_t wheelBrakes() const {
    return GetField<int8_t>(VT_WHEELBRAKES, 0);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<int8_t>(verifier, VT_WHEELBRAKES, 1) &&
           verifier.EndTable();
  }
};

struct DE_BrakeAppliedStatusBuilder {
  typedef DE_BrakeAppliedStatus Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_wheelBrakes(int8_t wheelBrakes) {
    fbb_.AddElement<int8_t>(DE_BrakeAppliedStatus::VT_WHEELBRAKES, wheelBrakes, 0);
  }
  explicit DE_BrakeAppliedStatusBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DE_BrakeAppliedStatus> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DE_BrakeAppliedStatus>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DE_BrakeAppliedStatus> CreateDE_BrakeAppliedStatus(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    int8_t wheelBrakes = 0) {
  DE_BrakeAppliedStatusBuilder builder_(_fbb);
  builder_.add_wheelBrakes(wheelBrakes);
  return builder_.Finish();
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_BRAKEAPPLIEDSTATUS_MECDATA_H_
