// automatically generated by the <PERSON>Buffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_ELEVATIONCONFIDENCE_MECDATA_H_
#define FLATBUFFERS_GENERATED_ELEVATIONCONFIDENCE_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

namespace MECData {

enum DE_ElevationConfidence : int8_t {
  DE_ElevationConfidence_unavailable = 0,
  DE_ElevationConfidence_elev50000 = 1,
  DE_ElevationConfidence_elev20000 = 2,
  DE_ElevationConfidence_elev10000 = 3,
  DE_ElevationConfidence_elev05000 = 4,
  DE_ElevationConfidence_elev02000 = 5,
  DE_ElevationConfidence_elev01000 = 6,
  DE_ElevationConfidence_elev00500 = 7,
  DE_ElevationConfidence_elev00200 = 8,
  DE_ElevationConfidence_elev00100 = 9,
  DE_ElevationConfidence_elev00050 = 10,
  DE_ElevationConfidence_elev00020 = 11,
  DE_ElevationConfidence_elev00010 = 12,
  DE_ElevationConfidence_elev00005 = 13,
  DE_ElevationConfidence_elev00002 = 14,
  DE_ElevationConfidence_elev00001 = 15,
  DE_ElevationConfidence_MIN = DE_ElevationConfidence_unavailable,
  DE_ElevationConfidence_MAX = DE_ElevationConfidence_elev00001
};

inline const DE_ElevationConfidence (&EnumValuesDE_ElevationConfidence())[16] {
  static const DE_ElevationConfidence values[] = {
    DE_ElevationConfidence_unavailable,
    DE_ElevationConfidence_elev50000,
    DE_ElevationConfidence_elev20000,
    DE_ElevationConfidence_elev10000,
    DE_ElevationConfidence_elev05000,
    DE_ElevationConfidence_elev02000,
    DE_ElevationConfidence_elev01000,
    DE_ElevationConfidence_elev00500,
    DE_ElevationConfidence_elev00200,
    DE_ElevationConfidence_elev00100,
    DE_ElevationConfidence_elev00050,
    DE_ElevationConfidence_elev00020,
    DE_ElevationConfidence_elev00010,
    DE_ElevationConfidence_elev00005,
    DE_ElevationConfidence_elev00002,
    DE_ElevationConfidence_elev00001
  };
  return values;
}

inline const char * const *EnumNamesDE_ElevationConfidence() {
  static const char * const names[17] = {
    "unavailable",
    "elev50000",
    "elev20000",
    "elev10000",
    "elev05000",
    "elev02000",
    "elev01000",
    "elev00500",
    "elev00200",
    "elev00100",
    "elev00050",
    "elev00020",
    "elev00010",
    "elev00005",
    "elev00002",
    "elev00001",
    nullptr
  };
  return names;
}

inline const char *EnumNameDE_ElevationConfidence(DE_ElevationConfidence e) {
  if (::flatbuffers::IsOutRange(e, DE_ElevationConfidence_unavailable, DE_ElevationConfidence_elev00001)) return "";
  const size_t index = static_cast<size_t>(e);
  return EnumNamesDE_ElevationConfidence()[index];
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_ELEVATIONCONFIDENCE_MECDATA_H_
