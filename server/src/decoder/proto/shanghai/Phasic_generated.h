// automatically generated by the FlatBuffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_PHASIC_MECDATA_H_
#define FLATBUFFERS_GENERATED_PHASIC_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

#include "LightState_generated.h"

namespace MECData {

struct DF_PhasicMovementOffset;
struct DF_PhasicMovementOffsetBuilder;

struct DF_GreenFlashTime;
struct DF_GreenFlashTimeBuilder;

struct DF_NonGreenROWLightState;
struct DF_NonGreenROWLightStateBuilder;

struct DF_Phasic;
struct DF_PhasicBuilder;

struct DF_PhasicMovementOffset FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_PhasicMovementOffsetBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_MOVEMENT_EXT_ID = 4,
    VT_OFFSET = 6
  };
  const ::flatbuffers::String *movement_ext_id() const {
    return GetPointer<const ::flatbuffers::String *>(VT_MOVEMENT_EXT_ID);
  }
  int16_t offset() const {
    return GetField<int16_t>(VT_OFFSET, 0);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_MOVEMENT_EXT_ID) &&
           verifier.VerifyString(movement_ext_id()) &&
           VerifyField<int16_t>(verifier, VT_OFFSET, 2) &&
           verifier.EndTable();
  }
};

struct DF_PhasicMovementOffsetBuilder {
  typedef DF_PhasicMovementOffset Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_movement_ext_id(::flatbuffers::Offset<::flatbuffers::String> movement_ext_id) {
    fbb_.AddOffset(DF_PhasicMovementOffset::VT_MOVEMENT_EXT_ID, movement_ext_id);
  }
  void add_offset(int16_t offset) {
    fbb_.AddElement<int16_t>(DF_PhasicMovementOffset::VT_OFFSET, offset, 0);
  }
  explicit DF_PhasicMovementOffsetBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_PhasicMovementOffset> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_PhasicMovementOffset>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_PhasicMovementOffset> CreateDF_PhasicMovementOffset(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::String> movement_ext_id = 0,
    int16_t offset = 0) {
  DF_PhasicMovementOffsetBuilder builder_(_fbb);
  builder_.add_movement_ext_id(movement_ext_id);
  builder_.add_offset(offset);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DF_PhasicMovementOffset> CreateDF_PhasicMovementOffsetDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const char *movement_ext_id = nullptr,
    int16_t offset = 0) {
  auto movement_ext_id__ = movement_ext_id ? _fbb.CreateString(movement_ext_id) : 0;
  return MECData::CreateDF_PhasicMovementOffset(
      _fbb,
      movement_ext_id__,
      offset);
}

struct DF_GreenFlashTime FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_GreenFlashTimeBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_MOVEMENT_EXT_ID = 4,
    VT_FLASH_TIME = 6
  };
  const ::flatbuffers::String *movement_ext_id() const {
    return GetPointer<const ::flatbuffers::String *>(VT_MOVEMENT_EXT_ID);
  }
  int16_t flash_time() const {
    return GetField<int16_t>(VT_FLASH_TIME, 0);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_MOVEMENT_EXT_ID) &&
           verifier.VerifyString(movement_ext_id()) &&
           VerifyField<int16_t>(verifier, VT_FLASH_TIME, 2) &&
           verifier.EndTable();
  }
};

struct DF_GreenFlashTimeBuilder {
  typedef DF_GreenFlashTime Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_movement_ext_id(::flatbuffers::Offset<::flatbuffers::String> movement_ext_id) {
    fbb_.AddOffset(DF_GreenFlashTime::VT_MOVEMENT_EXT_ID, movement_ext_id);
  }
  void add_flash_time(int16_t flash_time) {
    fbb_.AddElement<int16_t>(DF_GreenFlashTime::VT_FLASH_TIME, flash_time, 0);
  }
  explicit DF_GreenFlashTimeBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_GreenFlashTime> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_GreenFlashTime>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_GreenFlashTime> CreateDF_GreenFlashTime(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::String> movement_ext_id = 0,
    int16_t flash_time = 0) {
  DF_GreenFlashTimeBuilder builder_(_fbb);
  builder_.add_movement_ext_id(movement_ext_id);
  builder_.add_flash_time(flash_time);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DF_GreenFlashTime> CreateDF_GreenFlashTimeDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const char *movement_ext_id = nullptr,
    int16_t flash_time = 0) {
  auto movement_ext_id__ = movement_ext_id ? _fbb.CreateString(movement_ext_id) : 0;
  return MECData::CreateDF_GreenFlashTime(
      _fbb,
      movement_ext_id__,
      flash_time);
}

struct DF_NonGreenROWLightState FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_NonGreenROWLightStateBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_MOVEMENT_EXT_ID = 4,
    VT_LIGHT = 6
  };
  const ::flatbuffers::String *movement_ext_id() const {
    return GetPointer<const ::flatbuffers::String *>(VT_MOVEMENT_EXT_ID);
  }
  MECData::DE_LightState light() const {
    return static_cast<MECData::DE_LightState>(GetField<int8_t>(VT_LIGHT, 0));
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_MOVEMENT_EXT_ID) &&
           verifier.VerifyString(movement_ext_id()) &&
           VerifyField<int8_t>(verifier, VT_LIGHT, 1) &&
           verifier.EndTable();
  }
};

struct DF_NonGreenROWLightStateBuilder {
  typedef DF_NonGreenROWLightState Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_movement_ext_id(::flatbuffers::Offset<::flatbuffers::String> movement_ext_id) {
    fbb_.AddOffset(DF_NonGreenROWLightState::VT_MOVEMENT_EXT_ID, movement_ext_id);
  }
  void add_light(MECData::DE_LightState light) {
    fbb_.AddElement<int8_t>(DF_NonGreenROWLightState::VT_LIGHT, static_cast<int8_t>(light), 0);
  }
  explicit DF_NonGreenROWLightStateBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_NonGreenROWLightState> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_NonGreenROWLightState>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_NonGreenROWLightState> CreateDF_NonGreenROWLightState(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::String> movement_ext_id = 0,
    MECData::DE_LightState light = MECData::DE_LightState_unavailable) {
  DF_NonGreenROWLightStateBuilder builder_(_fbb);
  builder_.add_movement_ext_id(movement_ext_id);
  builder_.add_light(light);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DF_NonGreenROWLightState> CreateDF_NonGreenROWLightStateDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const char *movement_ext_id = nullptr,
    MECData::DE_LightState light = MECData::DE_LightState_unavailable) {
  auto movement_ext_id__ = movement_ext_id ? _fbb.CreateString(movement_ext_id) : 0;
  return MECData::CreateDF_NonGreenROWLightState(
      _fbb,
      movement_ext_id__,
      light);
}

struct DF_Phasic FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_PhasicBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_ID = 4,
    VT_ORDER = 6,
    VT_SCAT_NO = 8,
    VT_MOVEMENTS = 10,
    VT_GREEN = 12,
    VT_YELLOW = 14,
    VT_ALLRED = 16,
    VT_MIN_GREEN = 18,
    VT_MAX_GREEN = 20,
    VT_LATE_STARTS = 22,
    VT_EARLY_ENDS = 24,
    VT_NON_GREENS = 26,
    VT_GREEN_FLASH = 28,
    VT_ON_DEMAND = 30
  };
  int32_t id() const {
    return GetField<int32_t>(VT_ID, 0);
  }
  uint8_t order() const {
    return GetField<uint8_t>(VT_ORDER, 0);
  }
  const ::flatbuffers::String *scat_no() const {
    return GetPointer<const ::flatbuffers::String *>(VT_SCAT_NO);
  }
  const ::flatbuffers::Vector<::flatbuffers::Offset<::flatbuffers::String>> *movements() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<::flatbuffers::String>> *>(VT_MOVEMENTS);
  }
  int16_t green() const {
    return GetField<int16_t>(VT_GREEN, 0);
  }
  int16_t yellow() const {
    return GetField<int16_t>(VT_YELLOW, 0);
  }
  int16_t allred() const {
    return GetField<int16_t>(VT_ALLRED, 0);
  }
  int16_t min_green() const {
    return GetField<int16_t>(VT_MIN_GREEN, 0);
  }
  int16_t max_green() const {
    return GetField<int16_t>(VT_MAX_GREEN, 0);
  }
  const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_PhasicMovementOffset>> *late_starts() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_PhasicMovementOffset>> *>(VT_LATE_STARTS);
  }
  const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_PhasicMovementOffset>> *early_ends() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_PhasicMovementOffset>> *>(VT_EARLY_ENDS);
  }
  const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_NonGreenROWLightState>> *non_greens() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_NonGreenROWLightState>> *>(VT_NON_GREENS);
  }
  const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_GreenFlashTime>> *green_flash() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_GreenFlashTime>> *>(VT_GREEN_FLASH);
  }
  bool on_demand() const {
    return GetField<uint8_t>(VT_ON_DEMAND, 0) != 0;
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<int32_t>(verifier, VT_ID, 4) &&
           VerifyField<uint8_t>(verifier, VT_ORDER, 1) &&
           VerifyOffset(verifier, VT_SCAT_NO) &&
           verifier.VerifyString(scat_no()) &&
           VerifyOffset(verifier, VT_MOVEMENTS) &&
           verifier.VerifyVector(movements()) &&
           verifier.VerifyVectorOfStrings(movements()) &&
           VerifyField<int16_t>(verifier, VT_GREEN, 2) &&
           VerifyField<int16_t>(verifier, VT_YELLOW, 2) &&
           VerifyField<int16_t>(verifier, VT_ALLRED, 2) &&
           VerifyField<int16_t>(verifier, VT_MIN_GREEN, 2) &&
           VerifyField<int16_t>(verifier, VT_MAX_GREEN, 2) &&
           VerifyOffset(verifier, VT_LATE_STARTS) &&
           verifier.VerifyVector(late_starts()) &&
           verifier.VerifyVectorOfTables(late_starts()) &&
           VerifyOffset(verifier, VT_EARLY_ENDS) &&
           verifier.VerifyVector(early_ends()) &&
           verifier.VerifyVectorOfTables(early_ends()) &&
           VerifyOffset(verifier, VT_NON_GREENS) &&
           verifier.VerifyVector(non_greens()) &&
           verifier.VerifyVectorOfTables(non_greens()) &&
           VerifyOffset(verifier, VT_GREEN_FLASH) &&
           verifier.VerifyVector(green_flash()) &&
           verifier.VerifyVectorOfTables(green_flash()) &&
           VerifyField<uint8_t>(verifier, VT_ON_DEMAND, 1) &&
           verifier.EndTable();
  }
};

struct DF_PhasicBuilder {
  typedef DF_Phasic Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_id(int32_t id) {
    fbb_.AddElement<int32_t>(DF_Phasic::VT_ID, id, 0);
  }
  void add_order(uint8_t order) {
    fbb_.AddElement<uint8_t>(DF_Phasic::VT_ORDER, order, 0);
  }
  void add_scat_no(::flatbuffers::Offset<::flatbuffers::String> scat_no) {
    fbb_.AddOffset(DF_Phasic::VT_SCAT_NO, scat_no);
  }
  void add_movements(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<::flatbuffers::String>>> movements) {
    fbb_.AddOffset(DF_Phasic::VT_MOVEMENTS, movements);
  }
  void add_green(int16_t green) {
    fbb_.AddElement<int16_t>(DF_Phasic::VT_GREEN, green, 0);
  }
  void add_yellow(int16_t yellow) {
    fbb_.AddElement<int16_t>(DF_Phasic::VT_YELLOW, yellow, 0);
  }
  void add_allred(int16_t allred) {
    fbb_.AddElement<int16_t>(DF_Phasic::VT_ALLRED, allred, 0);
  }
  void add_min_green(int16_t min_green) {
    fbb_.AddElement<int16_t>(DF_Phasic::VT_MIN_GREEN, min_green, 0);
  }
  void add_max_green(int16_t max_green) {
    fbb_.AddElement<int16_t>(DF_Phasic::VT_MAX_GREEN, max_green, 0);
  }
  void add_late_starts(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_PhasicMovementOffset>>> late_starts) {
    fbb_.AddOffset(DF_Phasic::VT_LATE_STARTS, late_starts);
  }
  void add_early_ends(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_PhasicMovementOffset>>> early_ends) {
    fbb_.AddOffset(DF_Phasic::VT_EARLY_ENDS, early_ends);
  }
  void add_non_greens(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_NonGreenROWLightState>>> non_greens) {
    fbb_.AddOffset(DF_Phasic::VT_NON_GREENS, non_greens);
  }
  void add_green_flash(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_GreenFlashTime>>> green_flash) {
    fbb_.AddOffset(DF_Phasic::VT_GREEN_FLASH, green_flash);
  }
  void add_on_demand(bool on_demand) {
    fbb_.AddElement<uint8_t>(DF_Phasic::VT_ON_DEMAND, static_cast<uint8_t>(on_demand), 0);
  }
  explicit DF_PhasicBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_Phasic> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_Phasic>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_Phasic> CreateDF_Phasic(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    int32_t id = 0,
    uint8_t order = 0,
    ::flatbuffers::Offset<::flatbuffers::String> scat_no = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<::flatbuffers::String>>> movements = 0,
    int16_t green = 0,
    int16_t yellow = 0,
    int16_t allred = 0,
    int16_t min_green = 0,
    int16_t max_green = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_PhasicMovementOffset>>> late_starts = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_PhasicMovementOffset>>> early_ends = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_NonGreenROWLightState>>> non_greens = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_GreenFlashTime>>> green_flash = 0,
    bool on_demand = false) {
  DF_PhasicBuilder builder_(_fbb);
  builder_.add_green_flash(green_flash);
  builder_.add_non_greens(non_greens);
  builder_.add_early_ends(early_ends);
  builder_.add_late_starts(late_starts);
  builder_.add_movements(movements);
  builder_.add_scat_no(scat_no);
  builder_.add_id(id);
  builder_.add_max_green(max_green);
  builder_.add_min_green(min_green);
  builder_.add_allred(allred);
  builder_.add_yellow(yellow);
  builder_.add_green(green);
  builder_.add_on_demand(on_demand);
  builder_.add_order(order);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DF_Phasic> CreateDF_PhasicDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    int32_t id = 0,
    uint8_t order = 0,
    const char *scat_no = nullptr,
    const std::vector<::flatbuffers::Offset<::flatbuffers::String>> *movements = nullptr,
    int16_t green = 0,
    int16_t yellow = 0,
    int16_t allred = 0,
    int16_t min_green = 0,
    int16_t max_green = 0,
    const std::vector<::flatbuffers::Offset<MECData::DF_PhasicMovementOffset>> *late_starts = nullptr,
    const std::vector<::flatbuffers::Offset<MECData::DF_PhasicMovementOffset>> *early_ends = nullptr,
    const std::vector<::flatbuffers::Offset<MECData::DF_NonGreenROWLightState>> *non_greens = nullptr,
    const std::vector<::flatbuffers::Offset<MECData::DF_GreenFlashTime>> *green_flash = nullptr,
    bool on_demand = false) {
  auto scat_no__ = scat_no ? _fbb.CreateString(scat_no) : 0;
  auto movements__ = movements ? _fbb.CreateVector<::flatbuffers::Offset<::flatbuffers::String>>(*movements) : 0;
  auto late_starts__ = late_starts ? _fbb.CreateVector<::flatbuffers::Offset<MECData::DF_PhasicMovementOffset>>(*late_starts) : 0;
  auto early_ends__ = early_ends ? _fbb.CreateVector<::flatbuffers::Offset<MECData::DF_PhasicMovementOffset>>(*early_ends) : 0;
  auto non_greens__ = non_greens ? _fbb.CreateVector<::flatbuffers::Offset<MECData::DF_NonGreenROWLightState>>(*non_greens) : 0;
  auto green_flash__ = green_flash ? _fbb.CreateVector<::flatbuffers::Offset<MECData::DF_GreenFlashTime>>(*green_flash) : 0;
  return MECData::CreateDF_Phasic(
      _fbb,
      id,
      order,
      scat_no__,
      movements__,
      green,
      yellow,
      allred,
      min_green,
      max_green,
      late_starts__,
      early_ends__,
      non_greens__,
      green_flash__,
      on_demand);
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_PHASIC_MECDATA_H_
