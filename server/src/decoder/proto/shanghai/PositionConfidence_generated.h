// automatically generated by the FlatBuffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_POSITIONCONFIDENCE_MECDATA_H_
#define FLATBUFFERS_GENERATED_POSITIONCONFIDENCE_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

namespace MECData {

enum DE_PositionConfidence : int8_t {
  DE_PositionConfidence_unavailable = 0,
  DE_PositionConfidence_a500m = 1,
  DE_PositionConfidence_a200m = 2,
  DE_PositionConfidence_a100m = 3,
  DE_PositionConfidence_a50m = 4,
  DE_PositionConfidence_a20m = 5,
  DE_PositionConfidence_a10m = 6,
  DE_PositionConfidence_a5m = 7,
  DE_PositionConfidence_a2m = 8,
  DE_PositionConfidence_a1m = 9,
  DE_PositionConfidence_a50cm = 10,
  DE_PositionConfidence_a20cm = 11,
  DE_PositionConfidence_a10cm = 12,
  DE_PositionConfidence_a5cm = 13,
  DE_PositionConfidence_a2cm = 14,
  DE_PositionConfidence_alcm = 15,
  DE_PositionConfidence_MIN = DE_PositionConfidence_unavailable,
  DE_PositionConfidence_MAX = DE_PositionConfidence_alcm
};

inline const DE_PositionConfidence (&EnumValuesDE_PositionConfidence())[16] {
  static const DE_PositionConfidence values[] = {
    DE_PositionConfidence_unavailable,
    DE_PositionConfidence_a500m,
    DE_PositionConfidence_a200m,
    DE_PositionConfidence_a100m,
    DE_PositionConfidence_a50m,
    DE_PositionConfidence_a20m,
    DE_PositionConfidence_a10m,
    DE_PositionConfidence_a5m,
    DE_PositionConfidence_a2m,
    DE_PositionConfidence_a1m,
    DE_PositionConfidence_a50cm,
    DE_PositionConfidence_a20cm,
    DE_PositionConfidence_a10cm,
    DE_PositionConfidence_a5cm,
    DE_PositionConfidence_a2cm,
    DE_PositionConfidence_alcm
  };
  return values;
}

inline const char * const *EnumNamesDE_PositionConfidence() {
  static const char * const names[17] = {
    "unavailable",
    "a500m",
    "a200m",
    "a100m",
    "a50m",
    "a20m",
    "a10m",
    "a5m",
    "a2m",
    "a1m",
    "a50cm",
    "a20cm",
    "a10cm",
    "a5cm",
    "a2cm",
    "alcm",
    nullptr
  };
  return names;
}

inline const char *EnumNameDE_PositionConfidence(DE_PositionConfidence e) {
  if (::flatbuffers::IsOutRange(e, DE_PositionConfidence_unavailable, DE_PositionConfidence_alcm)) return "";
  const size_t index = static_cast<size_t>(e);
  return EnumNamesDE_PositionConfidence()[index];
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_POSITIONCONFIDENCE_MECDATA_H_
