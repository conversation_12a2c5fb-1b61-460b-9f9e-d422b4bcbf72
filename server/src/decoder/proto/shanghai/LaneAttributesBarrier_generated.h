// automatically generated by the <PERSON>Buffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_LANEATTRIBUTESBARRIER_MECDATA_H_
#define FLATBUFFERS_GENERATED_LANEATTRIBUTESBARRIER_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

namespace MECData {

struct DF_LaneAttributesBarrier;
struct DF_LaneAttributesBarrierBuilder;

struct DF_LaneAttributesBarrier FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_LaneAttributesBarrierBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_MEDIAN = 4
  };
  int16_t median() const {
    return GetField<int16_t>(VT_MEDIAN, 0);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<int16_t>(verifier, VT_MEDIAN, 2) &&
           verifier.EndTable();
  }
};

struct DF_LaneAttributesBarrierBuilder {
  typedef DF_LaneAttributesBarrier Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_median(int16_t median) {
    fbb_.AddElement<int16_t>(DF_LaneAttributesBarrier::VT_MEDIAN, median, 0);
  }
  explicit DF_LaneAttributesBarrierBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_LaneAttributesBarrier> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_LaneAttributesBarrier>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_LaneAttributesBarrier> CreateDF_LaneAttributesBarrier(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    int16_t median = 0) {
  DF_LaneAttributesBarrierBuilder builder_(_fbb);
  builder_.add_median(median);
  return builder_.Finish();
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_LANEATTRIBUTESBARRIER_MECDATA_H_
