// automatically generated by the <PERSON><PERSON>uffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_EVENTSOURCE_MECDATA_H_
#define FLATBUFFERS_GENERATED_EVENTSOURCE_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

#include "EventSourceClass_generated.h"

namespace MECData {

struct DF_EventSource;
struct DF_EventSourceBuilder;

struct DF_EventSource FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_EventSourceBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_ENTITY_ID = 4,
    VT_CLASS_ = 6,
    VT_DESCRIPTION = 8
  };
  uint32_t entity_id() const {
    return GetField<uint32_t>(VT_ENTITY_ID, 0);
  }
  MECData::DE_EventSourceClass class_() const {
    return static_cast<MECData::DE_EventSourceClass>(GetField<uint8_t>(VT_CLASS_, 0));
  }
  const ::flatbuffers::String *description() const {
    return GetPointer<const ::flatbuffers::String *>(VT_DESCRIPTION);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<uint32_t>(verifier, VT_ENTITY_ID, 4) &&
           VerifyField<uint8_t>(verifier, VT_CLASS_, 1) &&
           VerifyOffset(verifier, VT_DESCRIPTION) &&
           verifier.VerifyString(description()) &&
           verifier.EndTable();
  }
};

struct DF_EventSourceBuilder {
  typedef DF_EventSource Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_entity_id(uint32_t entity_id) {
    fbb_.AddElement<uint32_t>(DF_EventSource::VT_ENTITY_ID, entity_id, 0);
  }
  void add_class_(MECData::DE_EventSourceClass class_) {
    fbb_.AddElement<uint8_t>(DF_EventSource::VT_CLASS_, static_cast<uint8_t>(class_), 0);
  }
  void add_description(::flatbuffers::Offset<::flatbuffers::String> description) {
    fbb_.AddOffset(DF_EventSource::VT_DESCRIPTION, description);
  }
  explicit DF_EventSourceBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_EventSource> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_EventSource>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_EventSource> CreateDF_EventSource(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    uint32_t entity_id = 0,
    MECData::DE_EventSourceClass class_ = MECData::DE_EventSourceClass_UNKNOWN,
    ::flatbuffers::Offset<::flatbuffers::String> description = 0) {
  DF_EventSourceBuilder builder_(_fbb);
  builder_.add_description(description);
  builder_.add_entity_id(entity_id);
  builder_.add_class_(class_);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DF_EventSource> CreateDF_EventSourceDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    uint32_t entity_id = 0,
    MECData::DE_EventSourceClass class_ = MECData::DE_EventSourceClass_UNKNOWN,
    const char *description = nullptr) {
  auto description__ = description ? _fbb.CreateString(description) : 0;
  return MECData::CreateDF_EventSource(
      _fbb,
      entity_id,
      class_,
      description__);
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_EVENTSOURCE_MECDATA_H_
