// automatically generated by the <PERSON><PERSON>uffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_LANECOORDINATION_MECDATA_H_
#define FLATBUFFERS_GENERATED_LANECOORDINATION_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

#include "CoordinationInfo_generated.h"
#include "DDateTime_generated.h"
#include "Description_generated.h"
#include "DriveBehavior_generated.h"
#include "ReferenceLink_generated.h"
#include "ReferencePath_generated.h"

namespace MECData {

struct DF_LaneCoordination;
struct DF_LaneCoordinationBuilder;

struct DF_LaneCoordination FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_LaneCoordinationBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_TARGETLANE = 4,
    VT_RELATEDPATH = 6,
    VT_TBEGIN = 8,
    VT_TEND = 10,
    VT_RECOMMENDEDSPEED = 12,
    VT_RECOMMENDEDBEHAVIOR = 14,
    VT_INFO = 16,
    VT_DESCRIPTION_TYPE = 18,
    VT_DESCRIPTION = 20
  };
  const MECData::DF_ReferenceLink *targetLane() const {
    return GetPointer<const MECData::DF_ReferenceLink *>(VT_TARGETLANE);
  }
  const MECData::DF_ReferencePath *relatedPath() const {
    return GetPointer<const MECData::DF_ReferencePath *>(VT_RELATEDPATH);
  }
  const MECData::DF_DDateTime *tBegin() const {
    return GetPointer<const MECData::DF_DDateTime *>(VT_TBEGIN);
  }
  const MECData::DF_DDateTime *tEnd() const {
    return GetPointer<const MECData::DF_DDateTime *>(VT_TEND);
  }
  int16_t recommendedSpeed() const {
    return GetField<int16_t>(VT_RECOMMENDEDSPEED, 0);
  }
  const MECData::DE_DriveBehavior *recommendedBehavior() const {
    return GetPointer<const MECData::DE_DriveBehavior *>(VT_RECOMMENDEDBEHAVIOR);
  }
  const MECData::DE_CoordinationInfo *info() const {
    return GetPointer<const MECData::DE_CoordinationInfo *>(VT_INFO);
  }
  MECData::DF_Description description_type() const {
    return static_cast<MECData::DF_Description>(GetField<uint8_t>(VT_DESCRIPTION_TYPE, 0));
  }
  const void *description() const {
    return GetPointer<const void *>(VT_DESCRIPTION);
  }
  template<typename T> const T *description_as() const;
  const MECData::DF_TextString *description_as_DF_TextString() const {
    return description_type() == MECData::DF_Description_DF_TextString ? static_cast<const MECData::DF_TextString *>(description()) : nullptr;
  }
  const MECData::DF_TextGB2312 *description_as_DF_TextGB2312() const {
    return description_type() == MECData::DF_Description_DF_TextGB2312 ? static_cast<const MECData::DF_TextGB2312 *>(description()) : nullptr;
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffsetRequired(verifier, VT_TARGETLANE) &&
           verifier.VerifyTable(targetLane()) &&
           VerifyOffset(verifier, VT_RELATEDPATH) &&
           verifier.VerifyTable(relatedPath()) &&
           VerifyOffset(verifier, VT_TBEGIN) &&
           verifier.VerifyTable(tBegin()) &&
           VerifyOffset(verifier, VT_TEND) &&
           verifier.VerifyTable(tEnd()) &&
           VerifyField<int16_t>(verifier, VT_RECOMMENDEDSPEED, 2) &&
           VerifyOffset(verifier, VT_RECOMMENDEDBEHAVIOR) &&
           verifier.VerifyTable(recommendedBehavior()) &&
           VerifyOffset(verifier, VT_INFO) &&
           verifier.VerifyTable(info()) &&
           VerifyField<uint8_t>(verifier, VT_DESCRIPTION_TYPE, 1) &&
           VerifyOffset(verifier, VT_DESCRIPTION) &&
           VerifyDF_Description(verifier, description(), description_type()) &&
           verifier.EndTable();
  }
};

template<> inline const MECData::DF_TextString *DF_LaneCoordination::description_as<MECData::DF_TextString>() const {
  return description_as_DF_TextString();
}

template<> inline const MECData::DF_TextGB2312 *DF_LaneCoordination::description_as<MECData::DF_TextGB2312>() const {
  return description_as_DF_TextGB2312();
}

struct DF_LaneCoordinationBuilder {
  typedef DF_LaneCoordination Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_targetLane(::flatbuffers::Offset<MECData::DF_ReferenceLink> targetLane) {
    fbb_.AddOffset(DF_LaneCoordination::VT_TARGETLANE, targetLane);
  }
  void add_relatedPath(::flatbuffers::Offset<MECData::DF_ReferencePath> relatedPath) {
    fbb_.AddOffset(DF_LaneCoordination::VT_RELATEDPATH, relatedPath);
  }
  void add_tBegin(::flatbuffers::Offset<MECData::DF_DDateTime> tBegin) {
    fbb_.AddOffset(DF_LaneCoordination::VT_TBEGIN, tBegin);
  }
  void add_tEnd(::flatbuffers::Offset<MECData::DF_DDateTime> tEnd) {
    fbb_.AddOffset(DF_LaneCoordination::VT_TEND, tEnd);
  }
  void add_recommendedSpeed(int16_t recommendedSpeed) {
    fbb_.AddElement<int16_t>(DF_LaneCoordination::VT_RECOMMENDEDSPEED, recommendedSpeed, 0);
  }
  void add_recommendedBehavior(::flatbuffers::Offset<MECData::DE_DriveBehavior> recommendedBehavior) {
    fbb_.AddOffset(DF_LaneCoordination::VT_RECOMMENDEDBEHAVIOR, recommendedBehavior);
  }
  void add_info(::flatbuffers::Offset<MECData::DE_CoordinationInfo> info) {
    fbb_.AddOffset(DF_LaneCoordination::VT_INFO, info);
  }
  void add_description_type(MECData::DF_Description description_type) {
    fbb_.AddElement<uint8_t>(DF_LaneCoordination::VT_DESCRIPTION_TYPE, static_cast<uint8_t>(description_type), 0);
  }
  void add_description(::flatbuffers::Offset<void> description) {
    fbb_.AddOffset(DF_LaneCoordination::VT_DESCRIPTION, description);
  }
  explicit DF_LaneCoordinationBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_LaneCoordination> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_LaneCoordination>(end);
    fbb_.Required(o, DF_LaneCoordination::VT_TARGETLANE);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_LaneCoordination> CreateDF_LaneCoordination(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<MECData::DF_ReferenceLink> targetLane = 0,
    ::flatbuffers::Offset<MECData::DF_ReferencePath> relatedPath = 0,
    ::flatbuffers::Offset<MECData::DF_DDateTime> tBegin = 0,
    ::flatbuffers::Offset<MECData::DF_DDateTime> tEnd = 0,
    int16_t recommendedSpeed = 0,
    ::flatbuffers::Offset<MECData::DE_DriveBehavior> recommendedBehavior = 0,
    ::flatbuffers::Offset<MECData::DE_CoordinationInfo> info = 0,
    MECData::DF_Description description_type = MECData::DF_Description_NONE,
    ::flatbuffers::Offset<void> description = 0) {
  DF_LaneCoordinationBuilder builder_(_fbb);
  builder_.add_description(description);
  builder_.add_info(info);
  builder_.add_recommendedBehavior(recommendedBehavior);
  builder_.add_tEnd(tEnd);
  builder_.add_tBegin(tBegin);
  builder_.add_relatedPath(relatedPath);
  builder_.add_targetLane(targetLane);
  builder_.add_recommendedSpeed(recommendedSpeed);
  builder_.add_description_type(description_type);
  return builder_.Finish();
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_LANECOORDINATION_MECDATA_H_
