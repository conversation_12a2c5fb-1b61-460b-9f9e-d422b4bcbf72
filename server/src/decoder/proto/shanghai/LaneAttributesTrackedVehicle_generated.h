// automatically generated by the FlatBuffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_LANEATTRIBUTESTRACKEDVEHICLE_MECDATA_H_
#define FLATBUFFERS_GENERATED_LANEATTRIBUTESTRACKEDVEHICLE_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

namespace MECData {

struct DF_LaneAttributesTrackedVehicle;
struct DF_LaneAttributesTrackedVehicleBuilder;

struct DF_LaneAttributesTrackedVehicle FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_LaneAttributesTrackedVehicleBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_TRACKED = 4
  };
  int8_t tracked() const {
    return GetField<int8_t>(VT_TRACKED, 0);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<int8_t>(verifier, VT_TRACKED, 1) &&
           verifier.EndTable();
  }
};

struct DF_LaneAttributesTrackedVehicleBuilder {
  typedef DF_LaneAttributesTrackedVehicle Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_tracked(int8_t tracked) {
    fbb_.AddElement<int8_t>(DF_LaneAttributesTrackedVehicle::VT_TRACKED, tracked, 0);
  }
  explicit DF_LaneAttributesTrackedVehicleBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_LaneAttributesTrackedVehicle> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_LaneAttributesTrackedVehicle>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_LaneAttributesTrackedVehicle> CreateDF_LaneAttributesTrackedVehicle(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    int8_t tracked = 0) {
  DF_LaneAttributesTrackedVehicleBuilder builder_(_fbb);
  builder_.add_tracked(tracked);
  return builder_.Finish();
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_LANEATTRIBUTESTRACKEDVEHICLE_MECDATA_H_
