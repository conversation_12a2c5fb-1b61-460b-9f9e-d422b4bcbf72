// automatically generated by the <PERSON>Buffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_OPERATIONTAGS_MECDATA_H_
#define FLATBUFFERS_GENERATED_OPERATIONTAGS_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

namespace MECData {

struct DE_OperationTags;
struct DE_OperationTagsBuilder;

struct DE_OperationTags FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DE_OperationTagsBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_TAGS = 4
  };
  const ::flatbuffers::Vector<::flatbuffers::Offset<::flatbuffers::String>> *tags() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<::flatbuffers::String>> *>(VT_TAGS);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_TAGS) &&
           verifier.VerifyVector(tags()) &&
           verifier.VerifyVectorOfStrings(tags()) &&
           verifier.EndTable();
  }
};

struct DE_OperationTagsBuilder {
  typedef DE_OperationTags Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_tags(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<::flatbuffers::String>>> tags) {
    fbb_.AddOffset(DE_OperationTags::VT_TAGS, tags);
  }
  explicit DE_OperationTagsBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DE_OperationTags> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DE_OperationTags>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DE_OperationTags> CreateDE_OperationTags(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<::flatbuffers::String>>> tags = 0) {
  DE_OperationTagsBuilder builder_(_fbb);
  builder_.add_tags(tags);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DE_OperationTags> CreateDE_OperationTagsDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const std::vector<::flatbuffers::Offset<::flatbuffers::String>> *tags = nullptr) {
  auto tags__ = tags ? _fbb.CreateVector<::flatbuffers::Offset<::flatbuffers::String>>(*tags) : 0;
  return MECData::CreateDE_OperationTags(
      _fbb,
      tags__);
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_OPERATIONTAGS_MECDATA_H_
