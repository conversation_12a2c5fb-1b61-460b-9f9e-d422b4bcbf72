// automatically generated by the <PERSON><PERSON>uffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_PHASESTATE_MECDATA_H_
#define FLATBUFFERS_GENERATED_PHASESTATE_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

#include "LightState_generated.h"
#include "TimeChangeDetails_generated.h"

namespace MECData {

struct DF_PhaseState;
struct DF_PhaseStateBuilder;

struct DF_PhaseState FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_PhaseStateBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_LIGHT = 4,
    VT_TIMING_TYPE = 6,
    VT_TIMING = 8
  };
  MECData::DE_LightState light() const {
    return static_cast<MECData::DE_LightState>(GetField<int8_t>(VT_LIGHT, 0));
  }
  MECData::DF_TimeChangeDetails timing_type() const {
    return static_cast<MECData::DF_TimeChangeDetails>(GetField<uint8_t>(VT_TIMING_TYPE, 0));
  }
  const void *timing() const {
    return GetPointer<const void *>(VT_TIMING);
  }
  template<typename T> const T *timing_as() const;
  const MECData::DF_TimeCountingDown *timing_as_DF_TimeCountingDown() const {
    return timing_type() == MECData::DF_TimeChangeDetails_DF_TimeCountingDown ? static_cast<const MECData::DF_TimeCountingDown *>(timing()) : nullptr;
  }
  const MECData::DF_UTCTiming *timing_as_DF_UTCTiming() const {
    return timing_type() == MECData::DF_TimeChangeDetails_DF_UTCTiming ? static_cast<const MECData::DF_UTCTiming *>(timing()) : nullptr;
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<int8_t>(verifier, VT_LIGHT, 1) &&
           VerifyField<uint8_t>(verifier, VT_TIMING_TYPE, 1) &&
           VerifyOffset(verifier, VT_TIMING) &&
           VerifyDF_TimeChangeDetails(verifier, timing(), timing_type()) &&
           verifier.EndTable();
  }
};

template<> inline const MECData::DF_TimeCountingDown *DF_PhaseState::timing_as<MECData::DF_TimeCountingDown>() const {
  return timing_as_DF_TimeCountingDown();
}

template<> inline const MECData::DF_UTCTiming *DF_PhaseState::timing_as<MECData::DF_UTCTiming>() const {
  return timing_as_DF_UTCTiming();
}

struct DF_PhaseStateBuilder {
  typedef DF_PhaseState Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_light(MECData::DE_LightState light) {
    fbb_.AddElement<int8_t>(DF_PhaseState::VT_LIGHT, static_cast<int8_t>(light), 0);
  }
  void add_timing_type(MECData::DF_TimeChangeDetails timing_type) {
    fbb_.AddElement<uint8_t>(DF_PhaseState::VT_TIMING_TYPE, static_cast<uint8_t>(timing_type), 0);
  }
  void add_timing(::flatbuffers::Offset<void> timing) {
    fbb_.AddOffset(DF_PhaseState::VT_TIMING, timing);
  }
  explicit DF_PhaseStateBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_PhaseState> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_PhaseState>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_PhaseState> CreateDF_PhaseState(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    MECData::DE_LightState light = MECData::DE_LightState_unavailable,
    MECData::DF_TimeChangeDetails timing_type = MECData::DF_TimeChangeDetails_NONE,
    ::flatbuffers::Offset<void> timing = 0) {
  DF_PhaseStateBuilder builder_(_fbb);
  builder_.add_timing(timing);
  builder_.add_timing_type(timing_type);
  builder_.add_light(light);
  return builder_.Finish();
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_PHASESTATE_MECDATA_H_
