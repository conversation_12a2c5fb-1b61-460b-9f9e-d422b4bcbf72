// automatically generated by the <PERSON>Buffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_LANESHARING_MECDATA_H_
#define FLATBUFFERS_GENERATED_LANESHARING_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

namespace MECData {

struct DF_LaneSharing;
struct DF_LaneSharingBuilder;

struct DF_LaneSharing FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_LaneSharingBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_SHAREWITH = 4
  };
  int16_t shareWith() const {
    return GetField<int16_t>(VT_SHAREWITH, 0);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<int16_t>(verifier, VT_SHAREWITH, 2) &&
           verifier.EndTable();
  }
};

struct DF_LaneSharingBuilder {
  typedef DF_LaneSharing Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_shareWith(int16_t shareWith) {
    fbb_.AddElement<int16_t>(DF_LaneSharing::VT_SHAREWITH, shareWith, 0);
  }
  explicit DF_LaneSharingBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_LaneSharing> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_LaneSharing>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_LaneSharing> CreateDF_LaneSharing(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    int16_t shareWith = 0) {
  DF_LaneSharingBuilder builder_(_fbb);
  builder_.add_shareWith(shareWith);
  return builder_.Finish();
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_LANESHARING_MECDATA_H_
