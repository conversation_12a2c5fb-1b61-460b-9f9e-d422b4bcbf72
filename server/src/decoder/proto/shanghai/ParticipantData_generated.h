// automatically generated by the <PERSON><PERSON>uffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_PARTICIPANTDATA_MECDATA_H_
#define FLATBUFFERS_GENERATED_PARTICIPANTDATA_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

#include "AccelerationSet4Way_generated.h"
#include "BrakeSystemStatus_generated.h"
#include "MotionConfidenceSet_generated.h"
#include "NodeReferenceID_generated.h"
#include "PositionConfidenceSet_generated.h"
#include "PositionOffsetLLV_generated.h"
#include "ReferPosition_generated.h"
#include "SourceType_generated.h"
#include "TransmissionState_generated.h"
#include "VehicleCharEx_generated.h"
#include "VehicleClassification_generated.h"
#include "VehicleSafetyExtensions_generated.h"
#include "VehicleSize_generated.h"

namespace MECData {

struct DF_ParticipantData;
struct DF_ParticipantDataBuilder;

struct DF_ParticipantData FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_ParticipantDataBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_PTCTYPE = 4,
    VT_PTCID = 6,
    VT_SOURCE = 8,
    VT_ID = 10,
    VT_PLATENO = 12,
    VT_SECMARK = 14,
    VT_POS = 16,
    VT_POSCONFIDENCE = 18,
    VT_TRANSMISSION = 20,
    VT_SPEED = 22,
    VT_HEADING = 24,
    VT_ANGLE = 26,
    VT_MOTIONCFD = 28,
    VT_ACCELSET = 30,
    VT_SIZE = 32,
    VT_VEHICLECLASS = 34,
    VT_REFERPOS = 36,
    VT_DEVICE = 38,
    VT_MOY = 40,
    VT_NODEID = 42,
    VT_SECTIONID = 44,
    VT_LANEID = 46,
    VT_SECTION_EXT_ID = 48,
    VT_LANE_EXT_ID = 50,
    VT_VEHCHAREX = 52,
    VT_BRAKES = 54,
    VT_SAFETYEXT = 56,
    VT_MSG_ID = 58
  };
  uint8_t ptcType() const {
    return GetField<uint8_t>(VT_PTCTYPE, 255);
  }
  uint16_t ptcId() const {
    return GetField<uint16_t>(VT_PTCID, 65535);
  }
  MECData::DE_SourceType source() const {
    return static_cast<MECData::DE_SourceType>(GetField<uint8_t>(VT_SOURCE, 0));
  }
  const ::flatbuffers::Vector<uint8_t> *id() const {
    return GetPointer<const ::flatbuffers::Vector<uint8_t> *>(VT_ID);
  }
  const ::flatbuffers::String *plateNo() const {
    return GetPointer<const ::flatbuffers::String *>(VT_PLATENO);
  }
  uint16_t secMark() const {
    return GetField<uint16_t>(VT_SECMARK, 65535);
  }
  const MECData::DF_PositionOffsetLLV *pos() const {
    return GetPointer<const MECData::DF_PositionOffsetLLV *>(VT_POS);
  }
  const MECData::DF_PositionConfidenceSet *posConfidence() const {
    return GetPointer<const MECData::DF_PositionConfidenceSet *>(VT_POSCONFIDENCE);
  }
  MECData::DE_TransmissionState transmission() const {
    return static_cast<MECData::DE_TransmissionState>(GetField<int8_t>(VT_TRANSMISSION, 0));
  }
  uint16_t speed() const {
    return GetField<uint16_t>(VT_SPEED, 65535);
  }
  int16_t heading() const {
    return GetField<int16_t>(VT_HEADING, 32767);
  }
  int8_t angle() const {
    return GetField<int8_t>(VT_ANGLE, 0);
  }
  const MECData::DF_MotionConfidenceSet *motionCfd() const {
    return GetPointer<const MECData::DF_MotionConfidenceSet *>(VT_MOTIONCFD);
  }
  const MECData::DF_AccelerationSet4Way *accelSet() const {
    return GetPointer<const MECData::DF_AccelerationSet4Way *>(VT_ACCELSET);
  }
  const MECData::DF_VehicleSize *size() const {
    return GetPointer<const MECData::DF_VehicleSize *>(VT_SIZE);
  }
  const MECData::DF_VehicleClassification *vehicleClass() const {
    return GetPointer<const MECData::DF_VehicleClassification *>(VT_VEHICLECLASS);
  }
  const MECData::DF_ReferPosition *referPos() const {
    return GetPointer<const MECData::DF_ReferPosition *>(VT_REFERPOS);
  }
  const ::flatbuffers::Vector<uint16_t> *device() const {
    return GetPointer<const ::flatbuffers::Vector<uint16_t> *>(VT_DEVICE);
  }
  uint32_t moy() const {
    return GetField<uint32_t>(VT_MOY, 4294967295);
  }
  const MECData::DF_NodeReferenceID *nodeId() const {
    return GetPointer<const MECData::DF_NodeReferenceID *>(VT_NODEID);
  }
  uint8_t sectionId() const {
    return GetField<uint8_t>(VT_SECTIONID, 0);
  }
  int8_t laneId() const {
    return GetField<int8_t>(VT_LANEID, 0);
  }
  const ::flatbuffers::String *section_ext_id() const {
    return GetPointer<const ::flatbuffers::String *>(VT_SECTION_EXT_ID);
  }
  const ::flatbuffers::String *lane_ext_id() const {
    return GetPointer<const ::flatbuffers::String *>(VT_LANE_EXT_ID);
  }
  const MECData::DF_VehicleCharEx *vehCharEx() const {
    return GetPointer<const MECData::DF_VehicleCharEx *>(VT_VEHCHAREX);
  }
  const MECData::DF_BrakeSystemStatus *brakes() const {
    return GetPointer<const MECData::DF_BrakeSystemStatus *>(VT_BRAKES);
  }
  const MECData::DF_VehicleSafetyExtensions *safetyExt() const {
    return GetPointer<const MECData::DF_VehicleSafetyExtensions *>(VT_SAFETYEXT);
  }
  int64_t msg_id() const {
    return GetField<int64_t>(VT_MSG_ID, 0);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<uint8_t>(verifier, VT_PTCTYPE, 1) &&
           VerifyField<uint16_t>(verifier, VT_PTCID, 2) &&
           VerifyField<uint8_t>(verifier, VT_SOURCE, 1) &&
           VerifyOffset(verifier, VT_ID) &&
           verifier.VerifyVector(id()) &&
           VerifyOffset(verifier, VT_PLATENO) &&
           verifier.VerifyString(plateNo()) &&
           VerifyField<uint16_t>(verifier, VT_SECMARK, 2) &&
           VerifyOffsetRequired(verifier, VT_POS) &&
           verifier.VerifyTable(pos()) &&
           VerifyOffsetRequired(verifier, VT_POSCONFIDENCE) &&
           verifier.VerifyTable(posConfidence()) &&
           VerifyField<int8_t>(verifier, VT_TRANSMISSION, 1) &&
           VerifyField<uint16_t>(verifier, VT_SPEED, 2) &&
           VerifyField<int16_t>(verifier, VT_HEADING, 2) &&
           VerifyField<int8_t>(verifier, VT_ANGLE, 1) &&
           VerifyOffset(verifier, VT_MOTIONCFD) &&
           verifier.VerifyTable(motionCfd()) &&
           VerifyOffset(verifier, VT_ACCELSET) &&
           verifier.VerifyTable(accelSet()) &&
           VerifyOffsetRequired(verifier, VT_SIZE) &&
           verifier.VerifyTable(size()) &&
           VerifyOffset(verifier, VT_VEHICLECLASS) &&
           verifier.VerifyTable(vehicleClass()) &&
           VerifyOffset(verifier, VT_REFERPOS) &&
           verifier.VerifyTable(referPos()) &&
           VerifyOffsetRequired(verifier, VT_DEVICE) &&
           verifier.VerifyVector(device()) &&
           VerifyField<uint32_t>(verifier, VT_MOY, 4) &&
           VerifyOffset(verifier, VT_NODEID) &&
           verifier.VerifyTable(nodeId()) &&
           VerifyField<uint8_t>(verifier, VT_SECTIONID, 1) &&
           VerifyField<int8_t>(verifier, VT_LANEID, 1) &&
           VerifyOffset(verifier, VT_SECTION_EXT_ID) &&
           verifier.VerifyString(section_ext_id()) &&
           VerifyOffset(verifier, VT_LANE_EXT_ID) &&
           verifier.VerifyString(lane_ext_id()) &&
           VerifyOffset(verifier, VT_VEHCHAREX) &&
           verifier.VerifyTable(vehCharEx()) &&
           VerifyOffset(verifier, VT_BRAKES) &&
           verifier.VerifyTable(brakes()) &&
           VerifyOffset(verifier, VT_SAFETYEXT) &&
           verifier.VerifyTable(safetyExt()) &&
           VerifyField<int64_t>(verifier, VT_MSG_ID, 8) &&
           verifier.EndTable();
  }
};

struct DF_ParticipantDataBuilder {
  typedef DF_ParticipantData Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_ptcType(uint8_t ptcType) {
    fbb_.AddElement<uint8_t>(DF_ParticipantData::VT_PTCTYPE, ptcType, 255);
  }
  void add_ptcId(uint16_t ptcId) {
    fbb_.AddElement<uint16_t>(DF_ParticipantData::VT_PTCID, ptcId, 65535);
  }
  void add_source(MECData::DE_SourceType source) {
    fbb_.AddElement<uint8_t>(DF_ParticipantData::VT_SOURCE, static_cast<uint8_t>(source), 0);
  }
  void add_id(::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> id) {
    fbb_.AddOffset(DF_ParticipantData::VT_ID, id);
  }
  void add_plateNo(::flatbuffers::Offset<::flatbuffers::String> plateNo) {
    fbb_.AddOffset(DF_ParticipantData::VT_PLATENO, plateNo);
  }
  void add_secMark(uint16_t secMark) {
    fbb_.AddElement<uint16_t>(DF_ParticipantData::VT_SECMARK, secMark, 65535);
  }
  void add_pos(::flatbuffers::Offset<MECData::DF_PositionOffsetLLV> pos) {
    fbb_.AddOffset(DF_ParticipantData::VT_POS, pos);
  }
  void add_posConfidence(::flatbuffers::Offset<MECData::DF_PositionConfidenceSet> posConfidence) {
    fbb_.AddOffset(DF_ParticipantData::VT_POSCONFIDENCE, posConfidence);
  }
  void add_transmission(MECData::DE_TransmissionState transmission) {
    fbb_.AddElement<int8_t>(DF_ParticipantData::VT_TRANSMISSION, static_cast<int8_t>(transmission), 0);
  }
  void add_speed(uint16_t speed) {
    fbb_.AddElement<uint16_t>(DF_ParticipantData::VT_SPEED, speed, 65535);
  }
  void add_heading(int16_t heading) {
    fbb_.AddElement<int16_t>(DF_ParticipantData::VT_HEADING, heading, 32767);
  }
  void add_angle(int8_t angle) {
    fbb_.AddElement<int8_t>(DF_ParticipantData::VT_ANGLE, angle, 0);
  }
  void add_motionCfd(::flatbuffers::Offset<MECData::DF_MotionConfidenceSet> motionCfd) {
    fbb_.AddOffset(DF_ParticipantData::VT_MOTIONCFD, motionCfd);
  }
  void add_accelSet(::flatbuffers::Offset<MECData::DF_AccelerationSet4Way> accelSet) {
    fbb_.AddOffset(DF_ParticipantData::VT_ACCELSET, accelSet);
  }
  void add_size(::flatbuffers::Offset<MECData::DF_VehicleSize> size) {
    fbb_.AddOffset(DF_ParticipantData::VT_SIZE, size);
  }
  void add_vehicleClass(::flatbuffers::Offset<MECData::DF_VehicleClassification> vehicleClass) {
    fbb_.AddOffset(DF_ParticipantData::VT_VEHICLECLASS, vehicleClass);
  }
  void add_referPos(::flatbuffers::Offset<MECData::DF_ReferPosition> referPos) {
    fbb_.AddOffset(DF_ParticipantData::VT_REFERPOS, referPos);
  }
  void add_device(::flatbuffers::Offset<::flatbuffers::Vector<uint16_t>> device) {
    fbb_.AddOffset(DF_ParticipantData::VT_DEVICE, device);
  }
  void add_moy(uint32_t moy) {
    fbb_.AddElement<uint32_t>(DF_ParticipantData::VT_MOY, moy, 4294967295);
  }
  void add_nodeId(::flatbuffers::Offset<MECData::DF_NodeReferenceID> nodeId) {
    fbb_.AddOffset(DF_ParticipantData::VT_NODEID, nodeId);
  }
  void add_sectionId(uint8_t sectionId) {
    fbb_.AddElement<uint8_t>(DF_ParticipantData::VT_SECTIONID, sectionId, 0);
  }
  void add_laneId(int8_t laneId) {
    fbb_.AddElement<int8_t>(DF_ParticipantData::VT_LANEID, laneId, 0);
  }
  void add_section_ext_id(::flatbuffers::Offset<::flatbuffers::String> section_ext_id) {
    fbb_.AddOffset(DF_ParticipantData::VT_SECTION_EXT_ID, section_ext_id);
  }
  void add_lane_ext_id(::flatbuffers::Offset<::flatbuffers::String> lane_ext_id) {
    fbb_.AddOffset(DF_ParticipantData::VT_LANE_EXT_ID, lane_ext_id);
  }
  void add_vehCharEx(::flatbuffers::Offset<MECData::DF_VehicleCharEx> vehCharEx) {
    fbb_.AddOffset(DF_ParticipantData::VT_VEHCHAREX, vehCharEx);
  }
  void add_brakes(::flatbuffers::Offset<MECData::DF_BrakeSystemStatus> brakes) {
    fbb_.AddOffset(DF_ParticipantData::VT_BRAKES, brakes);
  }
  void add_safetyExt(::flatbuffers::Offset<MECData::DF_VehicleSafetyExtensions> safetyExt) {
    fbb_.AddOffset(DF_ParticipantData::VT_SAFETYEXT, safetyExt);
  }
  void add_msg_id(int64_t msg_id) {
    fbb_.AddElement<int64_t>(DF_ParticipantData::VT_MSG_ID, msg_id, 0);
  }
  explicit DF_ParticipantDataBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_ParticipantData> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_ParticipantData>(end);
    fbb_.Required(o, DF_ParticipantData::VT_POS);
    fbb_.Required(o, DF_ParticipantData::VT_POSCONFIDENCE);
    fbb_.Required(o, DF_ParticipantData::VT_SIZE);
    fbb_.Required(o, DF_ParticipantData::VT_DEVICE);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_ParticipantData> CreateDF_ParticipantData(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    uint8_t ptcType = 255,
    uint16_t ptcId = 65535,
    MECData::DE_SourceType source = MECData::DE_SourceType_unknown,
    ::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> id = 0,
    ::flatbuffers::Offset<::flatbuffers::String> plateNo = 0,
    uint16_t secMark = 65535,
    ::flatbuffers::Offset<MECData::DF_PositionOffsetLLV> pos = 0,
    ::flatbuffers::Offset<MECData::DF_PositionConfidenceSet> posConfidence = 0,
    MECData::DE_TransmissionState transmission = MECData::DE_TransmissionState_neutral,
    uint16_t speed = 65535,
    int16_t heading = 32767,
    int8_t angle = 0,
    ::flatbuffers::Offset<MECData::DF_MotionConfidenceSet> motionCfd = 0,
    ::flatbuffers::Offset<MECData::DF_AccelerationSet4Way> accelSet = 0,
    ::flatbuffers::Offset<MECData::DF_VehicleSize> size = 0,
    ::flatbuffers::Offset<MECData::DF_VehicleClassification> vehicleClass = 0,
    ::flatbuffers::Offset<MECData::DF_ReferPosition> referPos = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<uint16_t>> device = 0,
    uint32_t moy = 4294967295,
    ::flatbuffers::Offset<MECData::DF_NodeReferenceID> nodeId = 0,
    uint8_t sectionId = 0,
    int8_t laneId = 0,
    ::flatbuffers::Offset<::flatbuffers::String> section_ext_id = 0,
    ::flatbuffers::Offset<::flatbuffers::String> lane_ext_id = 0,
    ::flatbuffers::Offset<MECData::DF_VehicleCharEx> vehCharEx = 0,
    ::flatbuffers::Offset<MECData::DF_BrakeSystemStatus> brakes = 0,
    ::flatbuffers::Offset<MECData::DF_VehicleSafetyExtensions> safetyExt = 0,
    int64_t msg_id = 0) {
  DF_ParticipantDataBuilder builder_(_fbb);
  builder_.add_msg_id(msg_id);
  builder_.add_safetyExt(safetyExt);
  builder_.add_brakes(brakes);
  builder_.add_vehCharEx(vehCharEx);
  builder_.add_lane_ext_id(lane_ext_id);
  builder_.add_section_ext_id(section_ext_id);
  builder_.add_nodeId(nodeId);
  builder_.add_moy(moy);
  builder_.add_device(device);
  builder_.add_referPos(referPos);
  builder_.add_vehicleClass(vehicleClass);
  builder_.add_size(size);
  builder_.add_accelSet(accelSet);
  builder_.add_motionCfd(motionCfd);
  builder_.add_posConfidence(posConfidence);
  builder_.add_pos(pos);
  builder_.add_plateNo(plateNo);
  builder_.add_id(id);
  builder_.add_heading(heading);
  builder_.add_speed(speed);
  builder_.add_secMark(secMark);
  builder_.add_ptcId(ptcId);
  builder_.add_laneId(laneId);
  builder_.add_sectionId(sectionId);
  builder_.add_angle(angle);
  builder_.add_transmission(transmission);
  builder_.add_source(source);
  builder_.add_ptcType(ptcType);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DF_ParticipantData> CreateDF_ParticipantDataDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    uint8_t ptcType = 255,
    uint16_t ptcId = 65535,
    MECData::DE_SourceType source = MECData::DE_SourceType_unknown,
    const std::vector<uint8_t> *id = nullptr,
    const char *plateNo = nullptr,
    uint16_t secMark = 65535,
    ::flatbuffers::Offset<MECData::DF_PositionOffsetLLV> pos = 0,
    ::flatbuffers::Offset<MECData::DF_PositionConfidenceSet> posConfidence = 0,
    MECData::DE_TransmissionState transmission = MECData::DE_TransmissionState_neutral,
    uint16_t speed = 65535,
    int16_t heading = 32767,
    int8_t angle = 0,
    ::flatbuffers::Offset<MECData::DF_MotionConfidenceSet> motionCfd = 0,
    ::flatbuffers::Offset<MECData::DF_AccelerationSet4Way> accelSet = 0,
    ::flatbuffers::Offset<MECData::DF_VehicleSize> size = 0,
    ::flatbuffers::Offset<MECData::DF_VehicleClassification> vehicleClass = 0,
    ::flatbuffers::Offset<MECData::DF_ReferPosition> referPos = 0,
    const std::vector<uint16_t> *device = nullptr,
    uint32_t moy = 4294967295,
    ::flatbuffers::Offset<MECData::DF_NodeReferenceID> nodeId = 0,
    uint8_t sectionId = 0,
    int8_t laneId = 0,
    const char *section_ext_id = nullptr,
    const char *lane_ext_id = nullptr,
    ::flatbuffers::Offset<MECData::DF_VehicleCharEx> vehCharEx = 0,
    ::flatbuffers::Offset<MECData::DF_BrakeSystemStatus> brakes = 0,
    ::flatbuffers::Offset<MECData::DF_VehicleSafetyExtensions> safetyExt = 0,
    int64_t msg_id = 0) {
  auto id__ = id ? _fbb.CreateVector<uint8_t>(*id) : 0;
  auto plateNo__ = plateNo ? _fbb.CreateString(plateNo) : 0;
  auto device__ = device ? _fbb.CreateVector<uint16_t>(*device) : 0;
  auto section_ext_id__ = section_ext_id ? _fbb.CreateString(section_ext_id) : 0;
  auto lane_ext_id__ = lane_ext_id ? _fbb.CreateString(lane_ext_id) : 0;
  return MECData::CreateDF_ParticipantData(
      _fbb,
      ptcType,
      ptcId,
      source,
      id__,
      plateNo__,
      secMark,
      pos,
      posConfidence,
      transmission,
      speed,
      heading,
      angle,
      motionCfd,
      accelSet,
      size,
      vehicleClass,
      referPos,
      device__,
      moy,
      nodeId,
      sectionId,
      laneId,
      section_ext_id__,
      lane_ext_id__,
      vehCharEx,
      brakes,
      safetyExt,
      msg_id);
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_PARTICIPANTDATA_MECDATA_H_
