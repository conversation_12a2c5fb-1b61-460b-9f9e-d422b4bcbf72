// automatically generated by the <PERSON>Buffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_DRIVESUGGESTION_MECDATA_H_
#define FLATBUFFERS_GENERATED_DRIVESUGGESTION_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

#include "DriveBehavior_generated.h"
#include "ReferenceLink_generated.h"
#include "ReferencePath_generated.h"

namespace MECData {

struct DF_DriveSuggestion;
struct DF_DriveSuggestionBuilder;

struct DF_DriveSuggestion FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_DriveSuggestionBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_SUGGESTION = 4,
    VT_LIFETIME = 6,
    VT_RELATEDLINK = 8,
    VT_RELATEDPATH = 10
  };
  const MECData::DE_DriveBehavior *suggestion() const {
    return GetPointer<const MECData::DE_DriveBehavior *>(VT_SUGGESTION);
  }
  uint16_t lifeTime() const {
    return GetField<uint16_t>(VT_LIFETIME, 0);
  }
  const MECData::DF_ReferenceLink *relatedLink() const {
    return GetPointer<const MECData::DF_ReferenceLink *>(VT_RELATEDLINK);
  }
  const MECData::DF_ReferencePath *relatedPath() const {
    return GetPointer<const MECData::DF_ReferencePath *>(VT_RELATEDPATH);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffsetRequired(verifier, VT_SUGGESTION) &&
           verifier.VerifyTable(suggestion()) &&
           VerifyField<uint16_t>(verifier, VT_LIFETIME, 2) &&
           VerifyOffset(verifier, VT_RELATEDLINK) &&
           verifier.VerifyTable(relatedLink()) &&
           VerifyOffset(verifier, VT_RELATEDPATH) &&
           verifier.VerifyTable(relatedPath()) &&
           verifier.EndTable();
  }
};

struct DF_DriveSuggestionBuilder {
  typedef DF_DriveSuggestion Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_suggestion(::flatbuffers::Offset<MECData::DE_DriveBehavior> suggestion) {
    fbb_.AddOffset(DF_DriveSuggestion::VT_SUGGESTION, suggestion);
  }
  void add_lifeTime(uint16_t lifeTime) {
    fbb_.AddElement<uint16_t>(DF_DriveSuggestion::VT_LIFETIME, lifeTime, 0);
  }
  void add_relatedLink(::flatbuffers::Offset<MECData::DF_ReferenceLink> relatedLink) {
    fbb_.AddOffset(DF_DriveSuggestion::VT_RELATEDLINK, relatedLink);
  }
  void add_relatedPath(::flatbuffers::Offset<MECData::DF_ReferencePath> relatedPath) {
    fbb_.AddOffset(DF_DriveSuggestion::VT_RELATEDPATH, relatedPath);
  }
  explicit DF_DriveSuggestionBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_DriveSuggestion> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_DriveSuggestion>(end);
    fbb_.Required(o, DF_DriveSuggestion::VT_SUGGESTION);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_DriveSuggestion> CreateDF_DriveSuggestion(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<MECData::DE_DriveBehavior> suggestion = 0,
    uint16_t lifeTime = 0,
    ::flatbuffers::Offset<MECData::DF_ReferenceLink> relatedLink = 0,
    ::flatbuffers::Offset<MECData::DF_ReferencePath> relatedPath = 0) {
  DF_DriveSuggestionBuilder builder_(_fbb);
  builder_.add_relatedPath(relatedPath);
  builder_.add_relatedLink(relatedLink);
  builder_.add_suggestion(suggestion);
  builder_.add_lifeTime(lifeTime);
  return builder_.Finish();
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_DRIVESUGGESTION_MECDATA_H_
