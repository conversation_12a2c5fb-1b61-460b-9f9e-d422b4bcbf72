// automatically generated by the <PERSON><PERSON>uffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_INTERSECTIONSTATUSOBJECT_MECDATA_H_
#define FLATBUFFERS_GENERATED_INTERSECTIONSTATUSOBJECT_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

namespace MECData {

struct DE_IntersectionStatusObject;
struct DE_IntersectionStatusObjectBuilder;

struct DE_IntersectionStatusObject FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DE_IntersectionStatusObjectBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_STATUS = 4
  };
  int16_t status() const {
    return GetField<int16_t>(VT_STATUS, 0);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<int16_t>(verifier, VT_STATUS, 2) &&
           verifier.EndTable();
  }
};

struct DE_IntersectionStatusObjectBuilder {
  typedef DE_IntersectionStatusObject Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_status(int16_t status) {
    fbb_.AddElement<int16_t>(DE_IntersectionStatusObject::VT_STATUS, status, 0);
  }
  explicit DE_IntersectionStatusObjectBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DE_IntersectionStatusObject> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DE_IntersectionStatusObject>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DE_IntersectionStatusObject> CreateDE_IntersectionStatusObject(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    int16_t status = 0) {
  DE_IntersectionStatusObjectBuilder builder_(_fbb);
  builder_.add_status(status);
  return builder_.Finish();
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_INTERSECTIONSTATUSOBJECT_MECDATA_H_
