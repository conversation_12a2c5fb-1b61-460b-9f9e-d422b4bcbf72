// automatically generated by the <PERSON>Buffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_ERRORCOLLECTION_MECDATA_H_
#define FLATBUFFERS_GENERATED_ERRORCOLLECTION_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

#include "ErrorImmediateReport_generated.h"

namespace MECData {

struct DF_ErrorCollectionElement;
struct DF_ErrorCollectionElementBuilder;

struct MSG_ErrorCollection;
struct MSG_ErrorCollectionBuilder;

struct DF_ErrorCollectionElement FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_ErrorCollectionElementBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_ERROR = 4
  };
  const ::flatbuffers::Vector<uint8_t> *error() const {
    return GetPointer<const ::flatbuffers::Vector<uint8_t> *>(VT_ERROR);
  }
  const MECData::MSG_ErrorImmediateReport *error_nested_root() const {
    const auto _f = error();
    return _f ? ::flatbuffers::GetRoot<MECData::MSG_ErrorImmediateReport>(_f->Data())
              : nullptr;
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_ERROR) &&
           verifier.VerifyVector(error()) &&
           verifier.VerifyNestedFlatBuffer<MECData::MSG_ErrorImmediateReport>(error(), nullptr) &&
           verifier.EndTable();
  }
};

struct DF_ErrorCollectionElementBuilder {
  typedef DF_ErrorCollectionElement Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_error(::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> error) {
    fbb_.AddOffset(DF_ErrorCollectionElement::VT_ERROR, error);
  }
  explicit DF_ErrorCollectionElementBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_ErrorCollectionElement> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_ErrorCollectionElement>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_ErrorCollectionElement> CreateDF_ErrorCollectionElement(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> error = 0) {
  DF_ErrorCollectionElementBuilder builder_(_fbb);
  builder_.add_error(error);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DF_ErrorCollectionElement> CreateDF_ErrorCollectionElementDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const std::vector<uint8_t> *error = nullptr) {
  auto error__ = error ? _fbb.CreateVector<uint8_t>(*error) : 0;
  return MECData::CreateDF_ErrorCollectionElement(
      _fbb,
      error__);
}

struct MSG_ErrorCollection FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef MSG_ErrorCollectionBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_ERRORS = 4,
    VT_MSG_ID = 6
  };
  const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_ErrorCollectionElement>> *errors() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_ErrorCollectionElement>> *>(VT_ERRORS);
  }
  int64_t msg_id() const {
    return GetField<int64_t>(VT_MSG_ID, 0);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_ERRORS) &&
           verifier.VerifyVector(errors()) &&
           verifier.VerifyVectorOfTables(errors()) &&
           VerifyField<int64_t>(verifier, VT_MSG_ID, 8) &&
           verifier.EndTable();
  }
};

struct MSG_ErrorCollectionBuilder {
  typedef MSG_ErrorCollection Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_errors(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_ErrorCollectionElement>>> errors) {
    fbb_.AddOffset(MSG_ErrorCollection::VT_ERRORS, errors);
  }
  void add_msg_id(int64_t msg_id) {
    fbb_.AddElement<int64_t>(MSG_ErrorCollection::VT_MSG_ID, msg_id, 0);
  }
  explicit MSG_ErrorCollectionBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<MSG_ErrorCollection> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<MSG_ErrorCollection>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<MSG_ErrorCollection> CreateMSG_ErrorCollection(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_ErrorCollectionElement>>> errors = 0,
    int64_t msg_id = 0) {
  MSG_ErrorCollectionBuilder builder_(_fbb);
  builder_.add_msg_id(msg_id);
  builder_.add_errors(errors);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<MSG_ErrorCollection> CreateMSG_ErrorCollectionDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const std::vector<::flatbuffers::Offset<MECData::DF_ErrorCollectionElement>> *errors = nullptr,
    int64_t msg_id = 0) {
  auto errors__ = errors ? _fbb.CreateVector<::flatbuffers::Offset<MECData::DF_ErrorCollectionElement>>(*errors) : 0;
  return MECData::CreateMSG_ErrorCollection(
      _fbb,
      errors__,
      msg_id);
}

inline const MECData::MSG_ErrorCollection *GetMSG_ErrorCollection(const void *buf) {
  return ::flatbuffers::GetRoot<MECData::MSG_ErrorCollection>(buf);
}

inline const MECData::MSG_ErrorCollection *GetSizePrefixedMSG_ErrorCollection(const void *buf) {
  return ::flatbuffers::GetSizePrefixedRoot<MECData::MSG_ErrorCollection>(buf);
}

inline bool VerifyMSG_ErrorCollectionBuffer(
    ::flatbuffers::Verifier &verifier) {
  return verifier.VerifyBuffer<MECData::MSG_ErrorCollection>(nullptr);
}

inline bool VerifySizePrefixedMSG_ErrorCollectionBuffer(
    ::flatbuffers::Verifier &verifier) {
  return verifier.VerifySizePrefixedBuffer<MECData::MSG_ErrorCollection>(nullptr);
}

inline void FinishMSG_ErrorCollectionBuffer(
    ::flatbuffers::FlatBufferBuilder &fbb,
    ::flatbuffers::Offset<MECData::MSG_ErrorCollection> root) {
  fbb.Finish(root);
}

inline void FinishSizePrefixedMSG_ErrorCollectionBuffer(
    ::flatbuffers::FlatBufferBuilder &fbb,
    ::flatbuffers::Offset<MECData::MSG_ErrorCollection> root) {
  fbb.FinishSizePrefixed(root);
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_ERRORCOLLECTION_MECDATA_H_
