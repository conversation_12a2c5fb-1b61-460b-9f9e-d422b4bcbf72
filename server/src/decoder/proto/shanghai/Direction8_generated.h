// automatically generated by the FlatBuffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_DIRECTION8_MECDATA_H_
#define FLATBUFFERS_GENERATED_DIRECTION8_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

namespace MECData {

enum DE_Direction8 : uint8_t {
  DE_Direction8_N = 0,
  DE_Direction8_NE = 1,
  DE_Direction8_E = 2,
  DE_Direction8_SE = 3,
  DE_Direction8_S = 4,
  DE_Direction8_SW = 5,
  DE_Direction8_W = 6,
  DE_Direction8_NW = 7,
  DE_Direction8_MIN = DE_Direction8_N,
  DE_Direction8_MAX = DE_Direction8_NW
};

inline const DE_Direction8 (&EnumValuesDE_Direction8())[8] {
  static const DE_Direction8 values[] = {
    DE_Direction8_N,
    DE_Direction8_NE,
    DE_Direction8_E,
    DE_Direction8_SE,
    DE_Direction8_S,
    DE_Direction8_SW,
    DE_Direction8_W,
    DE_Direction8_NW
  };
  return values;
}

inline const char * const *EnumNamesDE_Direction8() {
  static const char * const names[9] = {
    "N",
    "NE",
    "E",
    "SE",
    "S",
    "SW",
    "W",
    "NW",
    nullptr
  };
  return names;
}

inline const char *EnumNameDE_Direction8(DE_Direction8 e) {
  if (::flatbuffers::IsOutRange(e, DE_Direction8_N, DE_Direction8_NW)) return "";
  const size_t index = static_cast<size_t>(e);
  return EnumNamesDE_Direction8()[index];
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_DIRECTION8_MECDATA_H_
