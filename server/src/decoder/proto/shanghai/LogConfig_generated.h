// automatically generated by the FlatBuffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_LOGCONFIG_MECDATA_H_
#define FLATBUFFERS_GENERATED_LOGCONFIG_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

#include "ErrorLevel_generated.h"

namespace MECData {

struct DE_LogServerConfig;
struct DE_LogServerConfigBuilder;

struct DF_LogConfig;
struct DF_LogConfigBuilder;

struct DE_LogServerConfig FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DE_LogServerConfigBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_SERVER_HOST = 4,
    VT_SERVER_PORT = 6
  };
  const ::flatbuffers::String *server_host() const {
    return GetPointer<const ::flatbuffers::String *>(VT_SERVER_HOST);
  }
  uint16_t server_port() const {
    return GetField<uint16_t>(VT_SERVER_PORT, 0);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_SERVER_HOST) &&
           verifier.VerifyString(server_host()) &&
           VerifyField<uint16_t>(verifier, VT_SERVER_PORT, 2) &&
           verifier.EndTable();
  }
};

struct DE_LogServerConfigBuilder {
  typedef DE_LogServerConfig Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_server_host(::flatbuffers::Offset<::flatbuffers::String> server_host) {
    fbb_.AddOffset(DE_LogServerConfig::VT_SERVER_HOST, server_host);
  }
  void add_server_port(uint16_t server_port) {
    fbb_.AddElement<uint16_t>(DE_LogServerConfig::VT_SERVER_PORT, server_port, 0);
  }
  explicit DE_LogServerConfigBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DE_LogServerConfig> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DE_LogServerConfig>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DE_LogServerConfig> CreateDE_LogServerConfig(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::String> server_host = 0,
    uint16_t server_port = 0) {
  DE_LogServerConfigBuilder builder_(_fbb);
  builder_.add_server_host(server_host);
  builder_.add_server_port(server_port);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DE_LogServerConfig> CreateDE_LogServerConfigDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const char *server_host = nullptr,
    uint16_t server_port = 0) {
  auto server_host__ = server_host ? _fbb.CreateString(server_host) : 0;
  return MECData::CreateDE_LogServerConfig(
      _fbb,
      server_host__,
      server_port);
}

struct DF_LogConfig FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_LogConfigBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_LOG_LEVEL = 4,
    VT_LOG_PATH = 6,
    VT_MAX_FILE_SIZE = 8,
    VT_MAX_FILE_DURATION = 10,
    VT_LOG_SERVER_CONFIG = 12,
    VT_MAX_DAYS_TO_KEEP = 14
  };
  MECData::DE_ErrorLevel log_level() const {
    return static_cast<MECData::DE_ErrorLevel>(GetField<int8_t>(VT_LOG_LEVEL, 0));
  }
  const ::flatbuffers::String *log_path() const {
    return GetPointer<const ::flatbuffers::String *>(VT_LOG_PATH);
  }
  uint64_t max_file_size() const {
    return GetField<uint64_t>(VT_MAX_FILE_SIZE, 0);
  }
  uint64_t max_file_duration() const {
    return GetField<uint64_t>(VT_MAX_FILE_DURATION, 0);
  }
  const MECData::DE_LogServerConfig *log_server_config() const {
    return GetPointer<const MECData::DE_LogServerConfig *>(VT_LOG_SERVER_CONFIG);
  }
  uint16_t max_days_to_keep() const {
    return GetField<uint16_t>(VT_MAX_DAYS_TO_KEEP, 0);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<int8_t>(verifier, VT_LOG_LEVEL, 1) &&
           VerifyOffset(verifier, VT_LOG_PATH) &&
           verifier.VerifyString(log_path()) &&
           VerifyField<uint64_t>(verifier, VT_MAX_FILE_SIZE, 8) &&
           VerifyField<uint64_t>(verifier, VT_MAX_FILE_DURATION, 8) &&
           VerifyOffset(verifier, VT_LOG_SERVER_CONFIG) &&
           verifier.VerifyTable(log_server_config()) &&
           VerifyField<uint16_t>(verifier, VT_MAX_DAYS_TO_KEEP, 2) &&
           verifier.EndTable();
  }
};

struct DF_LogConfigBuilder {
  typedef DF_LogConfig Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_log_level(MECData::DE_ErrorLevel log_level) {
    fbb_.AddElement<int8_t>(DF_LogConfig::VT_LOG_LEVEL, static_cast<int8_t>(log_level), 0);
  }
  void add_log_path(::flatbuffers::Offset<::flatbuffers::String> log_path) {
    fbb_.AddOffset(DF_LogConfig::VT_LOG_PATH, log_path);
  }
  void add_max_file_size(uint64_t max_file_size) {
    fbb_.AddElement<uint64_t>(DF_LogConfig::VT_MAX_FILE_SIZE, max_file_size, 0);
  }
  void add_max_file_duration(uint64_t max_file_duration) {
    fbb_.AddElement<uint64_t>(DF_LogConfig::VT_MAX_FILE_DURATION, max_file_duration, 0);
  }
  void add_log_server_config(::flatbuffers::Offset<MECData::DE_LogServerConfig> log_server_config) {
    fbb_.AddOffset(DF_LogConfig::VT_LOG_SERVER_CONFIG, log_server_config);
  }
  void add_max_days_to_keep(uint16_t max_days_to_keep) {
    fbb_.AddElement<uint16_t>(DF_LogConfig::VT_MAX_DAYS_TO_KEEP, max_days_to_keep, 0);
  }
  explicit DF_LogConfigBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_LogConfig> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_LogConfig>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_LogConfig> CreateDF_LogConfig(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    MECData::DE_ErrorLevel log_level = MECData::DE_ErrorLevel_DEBUG,
    ::flatbuffers::Offset<::flatbuffers::String> log_path = 0,
    uint64_t max_file_size = 0,
    uint64_t max_file_duration = 0,
    ::flatbuffers::Offset<MECData::DE_LogServerConfig> log_server_config = 0,
    uint16_t max_days_to_keep = 0) {
  DF_LogConfigBuilder builder_(_fbb);
  builder_.add_max_file_duration(max_file_duration);
  builder_.add_max_file_size(max_file_size);
  builder_.add_log_server_config(log_server_config);
  builder_.add_log_path(log_path);
  builder_.add_max_days_to_keep(max_days_to_keep);
  builder_.add_log_level(log_level);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DF_LogConfig> CreateDF_LogConfigDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    MECData::DE_ErrorLevel log_level = MECData::DE_ErrorLevel_DEBUG,
    const char *log_path = nullptr,
    uint64_t max_file_size = 0,
    uint64_t max_file_duration = 0,
    ::flatbuffers::Offset<MECData::DE_LogServerConfig> log_server_config = 0,
    uint16_t max_days_to_keep = 0) {
  auto log_path__ = log_path ? _fbb.CreateString(log_path) : 0;
  return MECData::CreateDF_LogConfig(
      _fbb,
      log_level,
      log_path__,
      max_file_size,
      max_file_duration,
      log_server_config,
      max_days_to_keep);
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_LOGCONFIG_MECDATA_H_
