// automatically generated by the <PERSON><PERSON>uffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_EVENTSOURCECLASS_MECDATA_H_
#define FLATBUFFERS_GENERATED_EVENTSOURCECLASS_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

namespace MECData {

enum DE_EventSourceClass : uint8_t {
  DE_EventSourceClass_UNKNOWN = 0,
  DE_EventSourceClass_POLICE = 1,
  DE_EventSourceClass_GOVERNMENT = 2,
  DE_EventSourceClass_METEORLOGICAL = 3,
  DE_EventSourceClass_INTERNET_SERVICES = 4,
  DE_EventSourceClass_LOCAL_DETECTION = 5,
  DE_EventSourceClass_MIN = DE_EventSourceClass_UNKNOWN,
  DE_EventSourceClass_MAX = DE_EventSourceClass_LOCAL_DETECTION
};

inline const DE_EventSourceClass (&EnumValuesDE_EventSourceClass())[6] {
  static const DE_EventSourceClass values[] = {
    DE_EventSourceClass_UNKNOWN,
    DE_EventSourceClass_POLICE,
    DE_EventSourceClass_GOVERNMENT,
    DE_EventSourceClass_METEORLOGICAL,
    DE_EventSourceClass_INTERNET_SERVICES,
    DE_EventSourceClass_LOCAL_DETECTION
  };
  return values;
}

inline const char * const *EnumNamesDE_EventSourceClass() {
  static const char * const names[7] = {
    "UNKNOWN",
    "POLICE",
    "GOVERNMENT",
    "METEORLOGICAL",
    "INTERNET_SERVICES",
    "LOCAL_DETECTION",
    nullptr
  };
  return names;
}

inline const char *EnumNameDE_EventSourceClass(DE_EventSourceClass e) {
  if (::flatbuffers::IsOutRange(e, DE_EventSourceClass_UNKNOWN, DE_EventSourceClass_LOCAL_DETECTION)) return "";
  const size_t index = static_cast<size_t>(e);
  return EnumNamesDE_EventSourceClass()[index];
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_EVENTSOURCECLASS_MECDATA_H_
