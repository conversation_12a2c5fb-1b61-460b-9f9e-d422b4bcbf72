// automatically generated by the FlatBuffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_DEBUGTIMERECORDS_MECDATA_H_
#define FLATBUFFERS_GENERATED_DEBUGTIMERECORDS_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

namespace MECData {

struct DE_ArrivalTimeStamp;
struct DE_ArrivalTimeStampBuilder;

struct DF_DebugTimeRecords;
struct DF_DebugTimeRecordsBuilder;

struct DE_ArrivalTimeStamp FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DE_ArrivalTimeStampBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_MODULE_ID = 4,
    VT_TIMESTAMP = 6,
    VT_PACKAGE_ID = 8
  };
  uint16_t module_id() const {
    return GetField<uint16_t>(VT_MODULE_ID, 0);
  }
  uint64_t timestamp() const {
    return GetField<uint64_t>(VT_TIMESTAMP, 0);
  }
  const ::flatbuffers::String *package_id() const {
    return GetPointer<const ::flatbuffers::String *>(VT_PACKAGE_ID);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<uint16_t>(verifier, VT_MODULE_ID, 2) &&
           VerifyField<uint64_t>(verifier, VT_TIMESTAMP, 8) &&
           VerifyOffset(verifier, VT_PACKAGE_ID) &&
           verifier.VerifyString(package_id()) &&
           verifier.EndTable();
  }
};

struct DE_ArrivalTimeStampBuilder {
  typedef DE_ArrivalTimeStamp Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_module_id(uint16_t module_id) {
    fbb_.AddElement<uint16_t>(DE_ArrivalTimeStamp::VT_MODULE_ID, module_id, 0);
  }
  void add_timestamp(uint64_t timestamp) {
    fbb_.AddElement<uint64_t>(DE_ArrivalTimeStamp::VT_TIMESTAMP, timestamp, 0);
  }
  void add_package_id(::flatbuffers::Offset<::flatbuffers::String> package_id) {
    fbb_.AddOffset(DE_ArrivalTimeStamp::VT_PACKAGE_ID, package_id);
  }
  explicit DE_ArrivalTimeStampBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DE_ArrivalTimeStamp> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DE_ArrivalTimeStamp>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DE_ArrivalTimeStamp> CreateDE_ArrivalTimeStamp(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    uint16_t module_id = 0,
    uint64_t timestamp = 0,
    ::flatbuffers::Offset<::flatbuffers::String> package_id = 0) {
  DE_ArrivalTimeStampBuilder builder_(_fbb);
  builder_.add_timestamp(timestamp);
  builder_.add_package_id(package_id);
  builder_.add_module_id(module_id);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DE_ArrivalTimeStamp> CreateDE_ArrivalTimeStampDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    uint16_t module_id = 0,
    uint64_t timestamp = 0,
    const char *package_id = nullptr) {
  auto package_id__ = package_id ? _fbb.CreateString(package_id) : 0;
  return MECData::CreateDE_ArrivalTimeStamp(
      _fbb,
      module_id,
      timestamp,
      package_id__);
}

struct DF_DebugTimeRecords FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_DebugTimeRecordsBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_RECORDS = 4
  };
  const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DE_ArrivalTimeStamp>> *records() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DE_ArrivalTimeStamp>> *>(VT_RECORDS);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_RECORDS) &&
           verifier.VerifyVector(records()) &&
           verifier.VerifyVectorOfTables(records()) &&
           verifier.EndTable();
  }
};

struct DF_DebugTimeRecordsBuilder {
  typedef DF_DebugTimeRecords Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_records(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DE_ArrivalTimeStamp>>> records) {
    fbb_.AddOffset(DF_DebugTimeRecords::VT_RECORDS, records);
  }
  explicit DF_DebugTimeRecordsBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_DebugTimeRecords> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_DebugTimeRecords>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_DebugTimeRecords> CreateDF_DebugTimeRecords(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DE_ArrivalTimeStamp>>> records = 0) {
  DF_DebugTimeRecordsBuilder builder_(_fbb);
  builder_.add_records(records);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DF_DebugTimeRecords> CreateDF_DebugTimeRecordsDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const std::vector<::flatbuffers::Offset<MECData::DE_ArrivalTimeStamp>> *records = nullptr) {
  auto records__ = records ? _fbb.CreateVector<::flatbuffers::Offset<MECData::DE_ArrivalTimeStamp>>(*records) : 0;
  return MECData::CreateDF_DebugTimeRecords(
      _fbb,
      records__);
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_DEBUGTIMERECORDS_MECDATA_H_
