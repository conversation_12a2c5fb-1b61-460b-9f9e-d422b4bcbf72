// automatically generated by the <PERSON>Buffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_LOCALTIMEPOINT_MECDATA_H_
#define FLATBUFFERS_GENERATED_LOCALTIMEPOINT_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

namespace MECData {

struct DF_LocalTimePoint;
struct DF_LocalTimePointBuilder;

struct DF_LocalTimePoint FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_LocalTimePointBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_HH = 4,
    VT_MM = 6,
    VT_SS = 8
  };
  uint8_t hh() const {
    return GetField<uint8_t>(VT_HH, 0);
  }
  uint8_t mm() const {
    return GetField<uint8_t>(VT_MM, 0);
  }
  uint8_t ss() const {
    return GetField<uint8_t>(VT_SS, 0);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<uint8_t>(verifier, VT_HH, 1) &&
           VerifyField<uint8_t>(verifier, VT_MM, 1) &&
           VerifyField<uint8_t>(verifier, VT_SS, 1) &&
           verifier.EndTable();
  }
};

struct DF_LocalTimePointBuilder {
  typedef DF_LocalTimePoint Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_hh(uint8_t hh) {
    fbb_.AddElement<uint8_t>(DF_LocalTimePoint::VT_HH, hh, 0);
  }
  void add_mm(uint8_t mm) {
    fbb_.AddElement<uint8_t>(DF_LocalTimePoint::VT_MM, mm, 0);
  }
  void add_ss(uint8_t ss) {
    fbb_.AddElement<uint8_t>(DF_LocalTimePoint::VT_SS, ss, 0);
  }
  explicit DF_LocalTimePointBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_LocalTimePoint> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_LocalTimePoint>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_LocalTimePoint> CreateDF_LocalTimePoint(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    uint8_t hh = 0,
    uint8_t mm = 0,
    uint8_t ss = 0) {
  DF_LocalTimePointBuilder builder_(_fbb);
  builder_.add_ss(ss);
  builder_.add_mm(mm);
  builder_.add_hh(hh);
  return builder_.Finish();
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_LOCALTIMEPOINT_MECDATA_H_
