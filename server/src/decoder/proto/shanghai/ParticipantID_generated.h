// automatically generated by the <PERSON>Buffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_PARTICIPANTID_MECDATA_H_
#define FLATBUFFERS_GENERATED_PARTICIPANTID_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

namespace MECData {

struct DF_ParticipantID;
struct DF_ParticipantIDBuilder;

struct DF_ParticipantID FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_ParticipantIDBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_PTC_ID = 4,
    VT_OBU_ID = 6
  };
  uint16_t ptc_id() const {
    return GetField<uint16_t>(VT_PTC_ID, 0);
  }
  const ::flatbuffers::Vector<uint8_t> *obu_id() const {
    return GetPointer<const ::flatbuffers::Vector<uint8_t> *>(VT_OBU_ID);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<uint16_t>(verifier, VT_PTC_ID, 2) &&
           VerifyOffset(verifier, VT_OBU_ID) &&
           verifier.VerifyVector(obu_id()) &&
           verifier.EndTable();
  }
};

struct DF_ParticipantIDBuilder {
  typedef DF_ParticipantID Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_ptc_id(uint16_t ptc_id) {
    fbb_.AddElement<uint16_t>(DF_ParticipantID::VT_PTC_ID, ptc_id, 0);
  }
  void add_obu_id(::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> obu_id) {
    fbb_.AddOffset(DF_ParticipantID::VT_OBU_ID, obu_id);
  }
  explicit DF_ParticipantIDBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_ParticipantID> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_ParticipantID>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_ParticipantID> CreateDF_ParticipantID(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    uint16_t ptc_id = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> obu_id = 0) {
  DF_ParticipantIDBuilder builder_(_fbb);
  builder_.add_obu_id(obu_id);
  builder_.add_ptc_id(ptc_id);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DF_ParticipantID> CreateDF_ParticipantIDDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    uint16_t ptc_id = 0,
    const std::vector<uint8_t> *obu_id = nullptr) {
  auto obu_id__ = obu_id ? _fbb.CreateVector<uint8_t>(*obu_id) : 0;
  return MECData::CreateDF_ParticipantID(
      _fbb,
      ptc_id,
      obu_id__);
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_PARTICIPANTID_MECDATA_H_
