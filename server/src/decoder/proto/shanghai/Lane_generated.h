// automatically generated by the FlatBuffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_LANE_MECDATA_H_
#define FLATBUFFERS_GENERATED_LANE_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

#include "AllowedManeuvers_generated.h"
#include "Connection_generated.h"
#include "LaneAttributes_generated.h"
#include "RegulatorySpeedLimit_generated.h"
#include "RoadPoint_generated.h"

namespace MECData {

struct DF_Lane;
struct DF_LaneBuilder;

struct DF_Lane FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_LaneBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_LANEID = 4,
    VT_LANEWIDTH = 6,
    VT_LANEATTRIBUTES = 8,
    VT_MANEUVERS = 10,
    VT_CONNECTSTO = 12,
    VT_SPEEDLIMITS = 14,
    VT_POINTS = 16
  };
  uint8_t laneId() const {
    return GetField<uint8_t>(VT_LANEID, 255);
  }
  uint16_t laneWidth() const {
    return GetField<uint16_t>(VT_LANEWIDTH, 0);
  }
  const MECData::DF_LaneAttributes *laneAttributes() const {
    return GetPointer<const MECData::DF_LaneAttributes *>(VT_LANEATTRIBUTES);
  }
  const MECData::DE_AllowedManeuvers *maneuvers() const {
    return GetPointer<const MECData::DE_AllowedManeuvers *>(VT_MANEUVERS);
  }
  const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_Connection>> *connectsTo() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_Connection>> *>(VT_CONNECTSTO);
  }
  const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_RegulatorySpeedLimit>> *speedLimits() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_RegulatorySpeedLimit>> *>(VT_SPEEDLIMITS);
  }
  const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_RoadPoint>> *points() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_RoadPoint>> *>(VT_POINTS);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<uint8_t>(verifier, VT_LANEID, 1) &&
           VerifyField<uint16_t>(verifier, VT_LANEWIDTH, 2) &&
           VerifyOffset(verifier, VT_LANEATTRIBUTES) &&
           verifier.VerifyTable(laneAttributes()) &&
           VerifyOffset(verifier, VT_MANEUVERS) &&
           verifier.VerifyTable(maneuvers()) &&
           VerifyOffset(verifier, VT_CONNECTSTO) &&
           verifier.VerifyVector(connectsTo()) &&
           verifier.VerifyVectorOfTables(connectsTo()) &&
           VerifyOffset(verifier, VT_SPEEDLIMITS) &&
           verifier.VerifyVector(speedLimits()) &&
           verifier.VerifyVectorOfTables(speedLimits()) &&
           VerifyOffset(verifier, VT_POINTS) &&
           verifier.VerifyVector(points()) &&
           verifier.VerifyVectorOfTables(points()) &&
           verifier.EndTable();
  }
};

struct DF_LaneBuilder {
  typedef DF_Lane Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_laneId(uint8_t laneId) {
    fbb_.AddElement<uint8_t>(DF_Lane::VT_LANEID, laneId, 255);
  }
  void add_laneWidth(uint16_t laneWidth) {
    fbb_.AddElement<uint16_t>(DF_Lane::VT_LANEWIDTH, laneWidth, 0);
  }
  void add_laneAttributes(::flatbuffers::Offset<MECData::DF_LaneAttributes> laneAttributes) {
    fbb_.AddOffset(DF_Lane::VT_LANEATTRIBUTES, laneAttributes);
  }
  void add_maneuvers(::flatbuffers::Offset<MECData::DE_AllowedManeuvers> maneuvers) {
    fbb_.AddOffset(DF_Lane::VT_MANEUVERS, maneuvers);
  }
  void add_connectsTo(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_Connection>>> connectsTo) {
    fbb_.AddOffset(DF_Lane::VT_CONNECTSTO, connectsTo);
  }
  void add_speedLimits(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_RegulatorySpeedLimit>>> speedLimits) {
    fbb_.AddOffset(DF_Lane::VT_SPEEDLIMITS, speedLimits);
  }
  void add_points(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_RoadPoint>>> points) {
    fbb_.AddOffset(DF_Lane::VT_POINTS, points);
  }
  explicit DF_LaneBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_Lane> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_Lane>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_Lane> CreateDF_Lane(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    uint8_t laneId = 255,
    uint16_t laneWidth = 0,
    ::flatbuffers::Offset<MECData::DF_LaneAttributes> laneAttributes = 0,
    ::flatbuffers::Offset<MECData::DE_AllowedManeuvers> maneuvers = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_Connection>>> connectsTo = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_RegulatorySpeedLimit>>> speedLimits = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_RoadPoint>>> points = 0) {
  DF_LaneBuilder builder_(_fbb);
  builder_.add_points(points);
  builder_.add_speedLimits(speedLimits);
  builder_.add_connectsTo(connectsTo);
  builder_.add_maneuvers(maneuvers);
  builder_.add_laneAttributes(laneAttributes);
  builder_.add_laneWidth(laneWidth);
  builder_.add_laneId(laneId);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DF_Lane> CreateDF_LaneDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    uint8_t laneId = 255,
    uint16_t laneWidth = 0,
    ::flatbuffers::Offset<MECData::DF_LaneAttributes> laneAttributes = 0,
    ::flatbuffers::Offset<MECData::DE_AllowedManeuvers> maneuvers = 0,
    const std::vector<::flatbuffers::Offset<MECData::DF_Connection>> *connectsTo = nullptr,
    const std::vector<::flatbuffers::Offset<MECData::DF_RegulatorySpeedLimit>> *speedLimits = nullptr,
    const std::vector<::flatbuffers::Offset<MECData::DF_RoadPoint>> *points = nullptr) {
  auto connectsTo__ = connectsTo ? _fbb.CreateVector<::flatbuffers::Offset<MECData::DF_Connection>>(*connectsTo) : 0;
  auto speedLimits__ = speedLimits ? _fbb.CreateVector<::flatbuffers::Offset<MECData::DF_RegulatorySpeedLimit>>(*speedLimits) : 0;
  auto points__ = points ? _fbb.CreateVector<::flatbuffers::Offset<MECData::DF_RoadPoint>>(*points) : 0;
  return MECData::CreateDF_Lane(
      _fbb,
      laneId,
      laneWidth,
      laneAttributes,
      maneuvers,
      connectsTo__,
      speedLimits__,
      points__);
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_LANE_MECDATA_H_
