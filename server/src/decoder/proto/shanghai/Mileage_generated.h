// automatically generated by the <PERSON>Buffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_MILEAGE_MECDATA_H_
#define FLATBUFFERS_GENERATED_MILEAGE_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

namespace MECData {

struct DF_Mileage;
struct DF_MileageBuilder;

struct DF_Mileage FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_MileageBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_INBOUND = 4,
    VT_K = 6,
    VT_M = 8
  };
  bool inbound() const {
    return GetField<uint8_t>(VT_INBOUND, 0) != 0;
  }
  uint16_t k() const {
    return GetField<uint16_t>(VT_K, 0);
  }
  uint16_t m() const {
    return GetField<uint16_t>(VT_M, 0);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<uint8_t>(verifier, VT_INBOUND, 1) &&
           VerifyField<uint16_t>(verifier, VT_K, 2) &&
           VerifyField<uint16_t>(verifier, VT_M, 2) &&
           verifier.EndTable();
  }
};

struct DF_MileageBuilder {
  typedef DF_Mileage Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_inbound(bool inbound) {
    fbb_.AddElement<uint8_t>(DF_Mileage::VT_INBOUND, static_cast<uint8_t>(inbound), 0);
  }
  void add_k(uint16_t k) {
    fbb_.AddElement<uint16_t>(DF_Mileage::VT_K, k, 0);
  }
  void add_m(uint16_t m) {
    fbb_.AddElement<uint16_t>(DF_Mileage::VT_M, m, 0);
  }
  explicit DF_MileageBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_Mileage> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_Mileage>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_Mileage> CreateDF_Mileage(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    bool inbound = false,
    uint16_t k = 0,
    uint16_t m = 0) {
  DF_MileageBuilder builder_(_fbb);
  builder_.add_m(m);
  builder_.add_k(k);
  builder_.add_inbound(inbound);
  return builder_.Finish();
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_MILEAGE_MECDATA_H_
