// automatically generated by the <PERSON>Buffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_MECSYSRES_MECDATA_H_
#define FLATBUFFERS_GENERATED_MECSYSRES_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

namespace MECData {

struct DE_CPUCore;
struct DE_CPUCoreBuilder;

struct DF_GPU;
struct DF_GPUBuilder;

struct DF_ProcessStatus;
struct DF_ProcessStatusBuilder;

struct DF_LocalStorage;
struct DF_LocalStorageBuilder;

struct MSG_MECSysRes;
struct MSG_MECSysResBuilder;

enum DE_ProcessState : uint8_t {
  DE_ProcessState_R = 0,
  DE_ProcessState_S = 1,
  DE_ProcessState_D = 2,
  DE_ProcessState_Z = 3,
  DE_ProcessState_T = 4,
  DE_ProcessState_W = 5,
  DE_ProcessState_MIN = DE_ProcessState_R,
  DE_ProcessState_MAX = DE_ProcessState_W
};

inline const DE_ProcessState (&EnumValuesDE_ProcessState())[6] {
  static const DE_ProcessState values[] = {
    DE_ProcessState_R,
    DE_ProcessState_S,
    DE_ProcessState_D,
    DE_ProcessState_Z,
    DE_ProcessState_T,
    DE_ProcessState_W
  };
  return values;
}

inline const char * const *EnumNamesDE_ProcessState() {
  static const char * const names[7] = {
    "R",
    "S",
    "D",
    "Z",
    "T",
    "W",
    nullptr
  };
  return names;
}

inline const char *EnumNameDE_ProcessState(DE_ProcessState e) {
  if (::flatbuffers::IsOutRange(e, DE_ProcessState_R, DE_ProcessState_W)) return "";
  const size_t index = static_cast<size_t>(e);
  return EnumNamesDE_ProcessState()[index];
}

struct DE_CPUCore FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DE_CPUCoreBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_ID = 4,
    VT_OCCUPANCY = 6,
    VT_TEMPERATURE = 8,
    VT_VENDOR = 10,
    VT_MODEL = 12,
    VT_CLOCK_SPEED = 14,
    VT_MAX_CLOCK_SPEED = 16
  };
  uint8_t id() const {
    return GetField<uint8_t>(VT_ID, 0);
  }
  uint16_t occupancy() const {
    return GetField<uint16_t>(VT_OCCUPANCY, 0);
  }
  uint8_t temperature() const {
    return GetField<uint8_t>(VT_TEMPERATURE, 0);
  }
  const ::flatbuffers::String *vendor() const {
    return GetPointer<const ::flatbuffers::String *>(VT_VENDOR);
  }
  const ::flatbuffers::String *model() const {
    return GetPointer<const ::flatbuffers::String *>(VT_MODEL);
  }
  uint16_t clock_speed() const {
    return GetField<uint16_t>(VT_CLOCK_SPEED, 65535);
  }
  uint16_t max_clock_speed() const {
    return GetField<uint16_t>(VT_MAX_CLOCK_SPEED, 65535);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<uint8_t>(verifier, VT_ID, 1) &&
           VerifyField<uint16_t>(verifier, VT_OCCUPANCY, 2) &&
           VerifyField<uint8_t>(verifier, VT_TEMPERATURE, 1) &&
           VerifyOffset(verifier, VT_VENDOR) &&
           verifier.VerifyString(vendor()) &&
           VerifyOffset(verifier, VT_MODEL) &&
           verifier.VerifyString(model()) &&
           VerifyField<uint16_t>(verifier, VT_CLOCK_SPEED, 2) &&
           VerifyField<uint16_t>(verifier, VT_MAX_CLOCK_SPEED, 2) &&
           verifier.EndTable();
  }
};

struct DE_CPUCoreBuilder {
  typedef DE_CPUCore Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_id(uint8_t id) {
    fbb_.AddElement<uint8_t>(DE_CPUCore::VT_ID, id, 0);
  }
  void add_occupancy(uint16_t occupancy) {
    fbb_.AddElement<uint16_t>(DE_CPUCore::VT_OCCUPANCY, occupancy, 0);
  }
  void add_temperature(uint8_t temperature) {
    fbb_.AddElement<uint8_t>(DE_CPUCore::VT_TEMPERATURE, temperature, 0);
  }
  void add_vendor(::flatbuffers::Offset<::flatbuffers::String> vendor) {
    fbb_.AddOffset(DE_CPUCore::VT_VENDOR, vendor);
  }
  void add_model(::flatbuffers::Offset<::flatbuffers::String> model) {
    fbb_.AddOffset(DE_CPUCore::VT_MODEL, model);
  }
  void add_clock_speed(uint16_t clock_speed) {
    fbb_.AddElement<uint16_t>(DE_CPUCore::VT_CLOCK_SPEED, clock_speed, 65535);
  }
  void add_max_clock_speed(uint16_t max_clock_speed) {
    fbb_.AddElement<uint16_t>(DE_CPUCore::VT_MAX_CLOCK_SPEED, max_clock_speed, 65535);
  }
  explicit DE_CPUCoreBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DE_CPUCore> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DE_CPUCore>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DE_CPUCore> CreateDE_CPUCore(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    uint8_t id = 0,
    uint16_t occupancy = 0,
    uint8_t temperature = 0,
    ::flatbuffers::Offset<::flatbuffers::String> vendor = 0,
    ::flatbuffers::Offset<::flatbuffers::String> model = 0,
    uint16_t clock_speed = 65535,
    uint16_t max_clock_speed = 65535) {
  DE_CPUCoreBuilder builder_(_fbb);
  builder_.add_model(model);
  builder_.add_vendor(vendor);
  builder_.add_max_clock_speed(max_clock_speed);
  builder_.add_clock_speed(clock_speed);
  builder_.add_occupancy(occupancy);
  builder_.add_temperature(temperature);
  builder_.add_id(id);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DE_CPUCore> CreateDE_CPUCoreDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    uint8_t id = 0,
    uint16_t occupancy = 0,
    uint8_t temperature = 0,
    const char *vendor = nullptr,
    const char *model = nullptr,
    uint16_t clock_speed = 65535,
    uint16_t max_clock_speed = 65535) {
  auto vendor__ = vendor ? _fbb.CreateString(vendor) : 0;
  auto model__ = model ? _fbb.CreateString(model) : 0;
  return MECData::CreateDE_CPUCore(
      _fbb,
      id,
      occupancy,
      temperature,
      vendor__,
      model__,
      clock_speed,
      max_clock_speed);
}

struct DF_GPU FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_GPUBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_NAME = 4,
    VT_OCCUPANCY = 6,
    VT_TEMPERATURE = 8
  };
  const ::flatbuffers::String *name() const {
    return GetPointer<const ::flatbuffers::String *>(VT_NAME);
  }
  uint16_t occupancy() const {
    return GetField<uint16_t>(VT_OCCUPANCY, 0);
  }
  uint8_t temperature() const {
    return GetField<uint8_t>(VT_TEMPERATURE, 0);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_NAME) &&
           verifier.VerifyString(name()) &&
           VerifyField<uint16_t>(verifier, VT_OCCUPANCY, 2) &&
           VerifyField<uint8_t>(verifier, VT_TEMPERATURE, 1) &&
           verifier.EndTable();
  }
};

struct DF_GPUBuilder {
  typedef DF_GPU Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_name(::flatbuffers::Offset<::flatbuffers::String> name) {
    fbb_.AddOffset(DF_GPU::VT_NAME, name);
  }
  void add_occupancy(uint16_t occupancy) {
    fbb_.AddElement<uint16_t>(DF_GPU::VT_OCCUPANCY, occupancy, 0);
  }
  void add_temperature(uint8_t temperature) {
    fbb_.AddElement<uint8_t>(DF_GPU::VT_TEMPERATURE, temperature, 0);
  }
  explicit DF_GPUBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_GPU> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_GPU>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_GPU> CreateDF_GPU(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::String> name = 0,
    uint16_t occupancy = 0,
    uint8_t temperature = 0) {
  DF_GPUBuilder builder_(_fbb);
  builder_.add_name(name);
  builder_.add_occupancy(occupancy);
  builder_.add_temperature(temperature);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DF_GPU> CreateDF_GPUDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const char *name = nullptr,
    uint16_t occupancy = 0,
    uint8_t temperature = 0) {
  auto name__ = name ? _fbb.CreateString(name) : 0;
  return MECData::CreateDF_GPU(
      _fbb,
      name__,
      occupancy,
      temperature);
}

struct DF_ProcessStatus FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_ProcessStatusBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_PID = 4,
    VT_STATE = 6,
    VT_NAME = 8,
    VT_CPU = 10,
    VT_MEM = 12
  };
  uint16_t pid() const {
    return GetField<uint16_t>(VT_PID, 0);
  }
  MECData::DE_ProcessState state() const {
    return static_cast<MECData::DE_ProcessState>(GetField<uint8_t>(VT_STATE, 0));
  }
  const ::flatbuffers::String *name() const {
    return GetPointer<const ::flatbuffers::String *>(VT_NAME);
  }
  uint16_t cpu() const {
    return GetField<uint16_t>(VT_CPU, 0);
  }
  uint64_t mem() const {
    return GetField<uint64_t>(VT_MEM, 0);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<uint16_t>(verifier, VT_PID, 2) &&
           VerifyField<uint8_t>(verifier, VT_STATE, 1) &&
           VerifyOffset(verifier, VT_NAME) &&
           verifier.VerifyString(name()) &&
           VerifyField<uint16_t>(verifier, VT_CPU, 2) &&
           VerifyField<uint64_t>(verifier, VT_MEM, 8) &&
           verifier.EndTable();
  }
};

struct DF_ProcessStatusBuilder {
  typedef DF_ProcessStatus Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_pid(uint16_t pid) {
    fbb_.AddElement<uint16_t>(DF_ProcessStatus::VT_PID, pid, 0);
  }
  void add_state(MECData::DE_ProcessState state) {
    fbb_.AddElement<uint8_t>(DF_ProcessStatus::VT_STATE, static_cast<uint8_t>(state), 0);
  }
  void add_name(::flatbuffers::Offset<::flatbuffers::String> name) {
    fbb_.AddOffset(DF_ProcessStatus::VT_NAME, name);
  }
  void add_cpu(uint16_t cpu) {
    fbb_.AddElement<uint16_t>(DF_ProcessStatus::VT_CPU, cpu, 0);
  }
  void add_mem(uint64_t mem) {
    fbb_.AddElement<uint64_t>(DF_ProcessStatus::VT_MEM, mem, 0);
  }
  explicit DF_ProcessStatusBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_ProcessStatus> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_ProcessStatus>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_ProcessStatus> CreateDF_ProcessStatus(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    uint16_t pid = 0,
    MECData::DE_ProcessState state = MECData::DE_ProcessState_R,
    ::flatbuffers::Offset<::flatbuffers::String> name = 0,
    uint16_t cpu = 0,
    uint64_t mem = 0) {
  DF_ProcessStatusBuilder builder_(_fbb);
  builder_.add_mem(mem);
  builder_.add_name(name);
  builder_.add_cpu(cpu);
  builder_.add_pid(pid);
  builder_.add_state(state);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DF_ProcessStatus> CreateDF_ProcessStatusDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    uint16_t pid = 0,
    MECData::DE_ProcessState state = MECData::DE_ProcessState_R,
    const char *name = nullptr,
    uint16_t cpu = 0,
    uint64_t mem = 0) {
  auto name__ = name ? _fbb.CreateString(name) : 0;
  return MECData::CreateDF_ProcessStatus(
      _fbb,
      pid,
      state,
      name__,
      cpu,
      mem);
}

struct DF_LocalStorage FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_LocalStorageBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_FILE_SYSTEM = 4,
    VT_MOUNT_POINT = 6,
    VT_TOTAL_BLOCKS = 8,
    VT_USED_BLOCKS = 10,
    VT_AVAILABLE_BLOCKS = 12,
    VT_USED = 14
  };
  const ::flatbuffers::String *file_system() const {
    return GetPointer<const ::flatbuffers::String *>(VT_FILE_SYSTEM);
  }
  const ::flatbuffers::String *mount_point() const {
    return GetPointer<const ::flatbuffers::String *>(VT_MOUNT_POINT);
  }
  uint64_t total_blocks() const {
    return GetField<uint64_t>(VT_TOTAL_BLOCKS, 0);
  }
  uint64_t used_blocks() const {
    return GetField<uint64_t>(VT_USED_BLOCKS, 0);
  }
  uint64_t available_blocks() const {
    return GetField<uint64_t>(VT_AVAILABLE_BLOCKS, 0);
  }
  uint8_t used() const {
    return GetField<uint8_t>(VT_USED, 0);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_FILE_SYSTEM) &&
           verifier.VerifyString(file_system()) &&
           VerifyOffset(verifier, VT_MOUNT_POINT) &&
           verifier.VerifyString(mount_point()) &&
           VerifyField<uint64_t>(verifier, VT_TOTAL_BLOCKS, 8) &&
           VerifyField<uint64_t>(verifier, VT_USED_BLOCKS, 8) &&
           VerifyField<uint64_t>(verifier, VT_AVAILABLE_BLOCKS, 8) &&
           VerifyField<uint8_t>(verifier, VT_USED, 1) &&
           verifier.EndTable();
  }
};

struct DF_LocalStorageBuilder {
  typedef DF_LocalStorage Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_file_system(::flatbuffers::Offset<::flatbuffers::String> file_system) {
    fbb_.AddOffset(DF_LocalStorage::VT_FILE_SYSTEM, file_system);
  }
  void add_mount_point(::flatbuffers::Offset<::flatbuffers::String> mount_point) {
    fbb_.AddOffset(DF_LocalStorage::VT_MOUNT_POINT, mount_point);
  }
  void add_total_blocks(uint64_t total_blocks) {
    fbb_.AddElement<uint64_t>(DF_LocalStorage::VT_TOTAL_BLOCKS, total_blocks, 0);
  }
  void add_used_blocks(uint64_t used_blocks) {
    fbb_.AddElement<uint64_t>(DF_LocalStorage::VT_USED_BLOCKS, used_blocks, 0);
  }
  void add_available_blocks(uint64_t available_blocks) {
    fbb_.AddElement<uint64_t>(DF_LocalStorage::VT_AVAILABLE_BLOCKS, available_blocks, 0);
  }
  void add_used(uint8_t used) {
    fbb_.AddElement<uint8_t>(DF_LocalStorage::VT_USED, used, 0);
  }
  explicit DF_LocalStorageBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_LocalStorage> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_LocalStorage>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_LocalStorage> CreateDF_LocalStorage(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::String> file_system = 0,
    ::flatbuffers::Offset<::flatbuffers::String> mount_point = 0,
    uint64_t total_blocks = 0,
    uint64_t used_blocks = 0,
    uint64_t available_blocks = 0,
    uint8_t used = 0) {
  DF_LocalStorageBuilder builder_(_fbb);
  builder_.add_available_blocks(available_blocks);
  builder_.add_used_blocks(used_blocks);
  builder_.add_total_blocks(total_blocks);
  builder_.add_mount_point(mount_point);
  builder_.add_file_system(file_system);
  builder_.add_used(used);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DF_LocalStorage> CreateDF_LocalStorageDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const char *file_system = nullptr,
    const char *mount_point = nullptr,
    uint64_t total_blocks = 0,
    uint64_t used_blocks = 0,
    uint64_t available_blocks = 0,
    uint8_t used = 0) {
  auto file_system__ = file_system ? _fbb.CreateString(file_system) : 0;
  auto mount_point__ = mount_point ? _fbb.CreateString(mount_point) : 0;
  return MECData::CreateDF_LocalStorage(
      _fbb,
      file_system__,
      mount_point__,
      total_blocks,
      used_blocks,
      available_blocks,
      used);
}

struct MSG_MECSysRes FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef MSG_MECSysResBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_TIME = 4,
    VT_CYCLE = 6,
    VT_CPU = 8,
    VT_MEM_OCCUPIED = 10,
    VT_MEM_TOTAL = 12,
    VT_STORAGE = 14,
    VT_PROCESSES = 16,
    VT_GPUS = 18,
    VT_CPUS = 20,
    VT_GPU = 22,
    VT_STORAGES = 24,
    VT_MSG_ID = 26
  };
  uint64_t time() const {
    return GetField<uint64_t>(VT_TIME, 0);
  }
  uint8_t cycle() const {
    return GetField<uint8_t>(VT_CYCLE, 0);
  }
  uint16_t cpu() const {
    return GetField<uint16_t>(VT_CPU, 0);
  }
  uint64_t mem_occupied() const {
    return GetField<uint64_t>(VT_MEM_OCCUPIED, 0);
  }
  uint64_t mem_total() const {
    return GetField<uint64_t>(VT_MEM_TOTAL, 0);
  }
  uint64_t storage() const {
    return GetField<uint64_t>(VT_STORAGE, 0);
  }
  const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_ProcessStatus>> *processes() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_ProcessStatus>> *>(VT_PROCESSES);
  }
  const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_GPU>> *gpus() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_GPU>> *>(VT_GPUS);
  }
  const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DE_CPUCore>> *cpus() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DE_CPUCore>> *>(VT_CPUS);
  }
  uint16_t gpu() const {
    return GetField<uint16_t>(VT_GPU, 0);
  }
  const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_LocalStorage>> *storages() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_LocalStorage>> *>(VT_STORAGES);
  }
  int64_t msg_id() const {
    return GetField<int64_t>(VT_MSG_ID, 0);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<uint64_t>(verifier, VT_TIME, 8) &&
           VerifyField<uint8_t>(verifier, VT_CYCLE, 1) &&
           VerifyField<uint16_t>(verifier, VT_CPU, 2) &&
           VerifyField<uint64_t>(verifier, VT_MEM_OCCUPIED, 8) &&
           VerifyField<uint64_t>(verifier, VT_MEM_TOTAL, 8) &&
           VerifyField<uint64_t>(verifier, VT_STORAGE, 8) &&
           VerifyOffset(verifier, VT_PROCESSES) &&
           verifier.VerifyVector(processes()) &&
           verifier.VerifyVectorOfTables(processes()) &&
           VerifyOffset(verifier, VT_GPUS) &&
           verifier.VerifyVector(gpus()) &&
           verifier.VerifyVectorOfTables(gpus()) &&
           VerifyOffset(verifier, VT_CPUS) &&
           verifier.VerifyVector(cpus()) &&
           verifier.VerifyVectorOfTables(cpus()) &&
           VerifyField<uint16_t>(verifier, VT_GPU, 2) &&
           VerifyOffset(verifier, VT_STORAGES) &&
           verifier.VerifyVector(storages()) &&
           verifier.VerifyVectorOfTables(storages()) &&
           VerifyField<int64_t>(verifier, VT_MSG_ID, 8) &&
           verifier.EndTable();
  }
};

struct MSG_MECSysResBuilder {
  typedef MSG_MECSysRes Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_time(uint64_t time) {
    fbb_.AddElement<uint64_t>(MSG_MECSysRes::VT_TIME, time, 0);
  }
  void add_cycle(uint8_t cycle) {
    fbb_.AddElement<uint8_t>(MSG_MECSysRes::VT_CYCLE, cycle, 0);
  }
  void add_cpu(uint16_t cpu) {
    fbb_.AddElement<uint16_t>(MSG_MECSysRes::VT_CPU, cpu, 0);
  }
  void add_mem_occupied(uint64_t mem_occupied) {
    fbb_.AddElement<uint64_t>(MSG_MECSysRes::VT_MEM_OCCUPIED, mem_occupied, 0);
  }
  void add_mem_total(uint64_t mem_total) {
    fbb_.AddElement<uint64_t>(MSG_MECSysRes::VT_MEM_TOTAL, mem_total, 0);
  }
  void add_storage(uint64_t storage) {
    fbb_.AddElement<uint64_t>(MSG_MECSysRes::VT_STORAGE, storage, 0);
  }
  void add_processes(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_ProcessStatus>>> processes) {
    fbb_.AddOffset(MSG_MECSysRes::VT_PROCESSES, processes);
  }
  void add_gpus(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_GPU>>> gpus) {
    fbb_.AddOffset(MSG_MECSysRes::VT_GPUS, gpus);
  }
  void add_cpus(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DE_CPUCore>>> cpus) {
    fbb_.AddOffset(MSG_MECSysRes::VT_CPUS, cpus);
  }
  void add_gpu(uint16_t gpu) {
    fbb_.AddElement<uint16_t>(MSG_MECSysRes::VT_GPU, gpu, 0);
  }
  void add_storages(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_LocalStorage>>> storages) {
    fbb_.AddOffset(MSG_MECSysRes::VT_STORAGES, storages);
  }
  void add_msg_id(int64_t msg_id) {
    fbb_.AddElement<int64_t>(MSG_MECSysRes::VT_MSG_ID, msg_id, 0);
  }
  explicit MSG_MECSysResBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<MSG_MECSysRes> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<MSG_MECSysRes>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<MSG_MECSysRes> CreateMSG_MECSysRes(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    uint64_t time = 0,
    uint8_t cycle = 0,
    uint16_t cpu = 0,
    uint64_t mem_occupied = 0,
    uint64_t mem_total = 0,
    uint64_t storage = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_ProcessStatus>>> processes = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_GPU>>> gpus = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DE_CPUCore>>> cpus = 0,
    uint16_t gpu = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_LocalStorage>>> storages = 0,
    int64_t msg_id = 0) {
  MSG_MECSysResBuilder builder_(_fbb);
  builder_.add_msg_id(msg_id);
  builder_.add_storage(storage);
  builder_.add_mem_total(mem_total);
  builder_.add_mem_occupied(mem_occupied);
  builder_.add_time(time);
  builder_.add_storages(storages);
  builder_.add_cpus(cpus);
  builder_.add_gpus(gpus);
  builder_.add_processes(processes);
  builder_.add_gpu(gpu);
  builder_.add_cpu(cpu);
  builder_.add_cycle(cycle);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<MSG_MECSysRes> CreateMSG_MECSysResDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    uint64_t time = 0,
    uint8_t cycle = 0,
    uint16_t cpu = 0,
    uint64_t mem_occupied = 0,
    uint64_t mem_total = 0,
    uint64_t storage = 0,
    const std::vector<::flatbuffers::Offset<MECData::DF_ProcessStatus>> *processes = nullptr,
    const std::vector<::flatbuffers::Offset<MECData::DF_GPU>> *gpus = nullptr,
    const std::vector<::flatbuffers::Offset<MECData::DE_CPUCore>> *cpus = nullptr,
    uint16_t gpu = 0,
    const std::vector<::flatbuffers::Offset<MECData::DF_LocalStorage>> *storages = nullptr,
    int64_t msg_id = 0) {
  auto processes__ = processes ? _fbb.CreateVector<::flatbuffers::Offset<MECData::DF_ProcessStatus>>(*processes) : 0;
  auto gpus__ = gpus ? _fbb.CreateVector<::flatbuffers::Offset<MECData::DF_GPU>>(*gpus) : 0;
  auto cpus__ = cpus ? _fbb.CreateVector<::flatbuffers::Offset<MECData::DE_CPUCore>>(*cpus) : 0;
  auto storages__ = storages ? _fbb.CreateVector<::flatbuffers::Offset<MECData::DF_LocalStorage>>(*storages) : 0;
  return MECData::CreateMSG_MECSysRes(
      _fbb,
      time,
      cycle,
      cpu,
      mem_occupied,
      mem_total,
      storage,
      processes__,
      gpus__,
      cpus__,
      gpu,
      storages__,
      msg_id);
}

inline const MECData::MSG_MECSysRes *GetMSG_MECSysRes(const void *buf) {
  return ::flatbuffers::GetRoot<MECData::MSG_MECSysRes>(buf);
}

inline const MECData::MSG_MECSysRes *GetSizePrefixedMSG_MECSysRes(const void *buf) {
  return ::flatbuffers::GetSizePrefixedRoot<MECData::MSG_MECSysRes>(buf);
}

inline bool VerifyMSG_MECSysResBuffer(
    ::flatbuffers::Verifier &verifier) {
  return verifier.VerifyBuffer<MECData::MSG_MECSysRes>(nullptr);
}

inline bool VerifySizePrefixedMSG_MECSysResBuffer(
    ::flatbuffers::Verifier &verifier) {
  return verifier.VerifySizePrefixedBuffer<MECData::MSG_MECSysRes>(nullptr);
}

inline void FinishMSG_MECSysResBuffer(
    ::flatbuffers::FlatBufferBuilder &fbb,
    ::flatbuffers::Offset<MECData::MSG_MECSysRes> root) {
  fbb.Finish(root);
}

inline void FinishSizePrefixedMSG_MECSysResBuffer(
    ::flatbuffers::FlatBufferBuilder &fbb,
    ::flatbuffers::Offset<MECData::MSG_MECSysRes> root) {
  fbb.FinishSizePrefixed(root);
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_MECSYSRES_MECDATA_H_
