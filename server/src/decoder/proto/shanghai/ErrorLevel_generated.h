// automatically generated by the FlatBuffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_ERRORLEVEL_MECDATA_H_
#define FLATBUFFERS_GENERATED_ERRORLEVEL_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

namespace MECData {

enum DE_ErrorLevel : int8_t {
  DE_ErrorLevel_DEBUG = 0,
  DE_ErrorLevel_INFO = 1,
  DE_ErrorLevel_WARNING = 2,
  DE_ErrorLevel_ERROR = 3,
  DE_ErrorLevel_FATAL = 4,
  DE_ErrorLevel_MIN = DE_ErrorLevel_DEBUG,
  DE_ErrorLevel_MAX = DE_ErrorLevel_FATAL
};

inline const DE_ErrorLevel (&EnumValuesDE_ErrorLevel())[5] {
  static const DE_ErrorLevel values[] = {
    DE_ErrorLevel_DEBUG,
    DE_ErrorLevel_INFO,
    DE_ErrorLevel_WARNING,
    DE_ErrorLevel_ERROR,
    DE_ErrorLevel_FATAL
  };
  return values;
}

inline const char * const *EnumNamesDE_ErrorLevel() {
  static const char * const names[6] = {
    "DEBUG",
    "INFO",
    "WARNING",
    "ERROR",
    "FATAL",
    nullptr
  };
  return names;
}

inline const char *EnumNameDE_ErrorLevel(DE_ErrorLevel e) {
  if (::flatbuffers::IsOutRange(e, DE_ErrorLevel_DEBUG, DE_ErrorLevel_FATAL)) return "";
  const size_t index = static_cast<size_t>(e);
  return EnumNamesDE_ErrorLevel()[index];
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_ERRORLEVEL_MECDATA_H_
