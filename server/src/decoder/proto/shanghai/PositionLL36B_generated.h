// automatically generated by the <PERSON>Buffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_POSITIONLL36B_MECDATA_H_
#define FLATBUFFERS_GENERATED_POSITIONLL36B_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

namespace MECData {

struct DF_PositionLL36B;
struct DF_PositionLL36BBuilder;

struct DF_PositionLL36B FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_PositionLL36BBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_LON = 4,
    VT_LAT = 6
  };
  int32_t lon() const {
    return GetField<int32_t>(VT_LON, 0);
  }
  int32_t lat() const {
    return GetField<int32_t>(VT_LAT, 0);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<int32_t>(verifier, VT_LON, 4) &&
           VerifyField<int32_t>(verifier, VT_LAT, 4) &&
           verifier.EndTable();
  }
};

struct DF_PositionLL36BBuilder {
  typedef DF_PositionLL36B Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_lon(int32_t lon) {
    fbb_.AddElement<int32_t>(DF_PositionLL36B::VT_LON, lon, 0);
  }
  void add_lat(int32_t lat) {
    fbb_.AddElement<int32_t>(DF_PositionLL36B::VT_LAT, lat, 0);
  }
  explicit DF_PositionLL36BBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_PositionLL36B> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_PositionLL36B>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_PositionLL36B> CreateDF_PositionLL36B(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    int32_t lon = 0,
    int32_t lat = 0) {
  DF_PositionLL36BBuilder builder_(_fbb);
  builder_.add_lat(lat);
  builder_.add_lon(lon);
  return builder_.Finish();
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_POSITIONLL36B_MECDATA_H_
