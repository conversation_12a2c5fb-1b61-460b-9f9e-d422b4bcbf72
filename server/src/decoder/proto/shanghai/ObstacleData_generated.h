// automatically generated by the <PERSON>Buffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_OBSTACLEDATA_MECDATA_H_
#define FLATBUFFERS_GENERATED_OBSTACLEDATA_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

#include "HeadingConfidence_generated.h"
#include "ObjectSize_generated.h"
#include "ObstacleType_generated.h"
#include "Polygon_generated.h"
#include "PositionConfidenceSet_generated.h"
#include "PositionOffsetLLV_generated.h"
#include "SizeValueConfidence_generated.h"
#include "SourceType_generated.h"
#include "SpeedConfidence_generated.h"

namespace MECData {

struct DF_ObstacleData;
struct DF_ObstacleDataBuilder;

struct DF_ObstacleData FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_ObstacleDataBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_OBS_TYPE = 4,
    VT_OBJ_TYPE_CONFIDENCE = 6,
    VT_OBS_ID = 8,
    VT_SOURCE = 10,
    VT_MOY = 12,
    VT_SECMARK = 14,
    VT_POS = 16,
    VT_POS_CONFIDENCE = 18,
    VT_SPEED = 20,
    VT_SPEED_CONFIDENCE = 22,
    VT_HEADING = 24,
    VT_HEADING_CONFIDENCE = 26,
    VT_VER_SPEED = 28,
    VT_VER_SPEED_CONFIDENCE = 30,
    VT_SIZE = 32,
    VT_OBJ_SIZE_CONFIDENCE = 34,
    VT_TRACKING = 36,
    VT_POLYGON = 38
  };
  MECData::DE_ObstacleType obs_type() const {
    return static_cast<MECData::DE_ObstacleType>(GetField<uint8_t>(VT_OBS_TYPE, 0));
  }
  uint8_t obj_type_confidence() const {
    return GetField<uint8_t>(VT_OBJ_TYPE_CONFIDENCE, 0);
  }
  uint16_t obs_id() const {
    return GetField<uint16_t>(VT_OBS_ID, 0);
  }
  MECData::DE_SourceType source() const {
    return static_cast<MECData::DE_SourceType>(GetField<uint8_t>(VT_SOURCE, 0));
  }
  uint32_t moy() const {
    return GetField<uint32_t>(VT_MOY, 0);
  }
  uint16_t secmark() const {
    return GetField<uint16_t>(VT_SECMARK, 0);
  }
  const MECData::DF_PositionOffsetLLV *pos() const {
    return GetPointer<const MECData::DF_PositionOffsetLLV *>(VT_POS);
  }
  const MECData::DF_PositionConfidenceSet *pos_confidence() const {
    return GetPointer<const MECData::DF_PositionConfidenceSet *>(VT_POS_CONFIDENCE);
  }
  uint16_t speed() const {
    return GetField<uint16_t>(VT_SPEED, 0);
  }
  MECData::DE_SpeedConfidence speed_confidence() const {
    return static_cast<MECData::DE_SpeedConfidence>(GetField<int8_t>(VT_SPEED_CONFIDENCE, 0));
  }
  int16_t heading() const {
    return GetField<int16_t>(VT_HEADING, 0);
  }
  MECData::DE_HeadingConfidence heading_confidence() const {
    return static_cast<MECData::DE_HeadingConfidence>(GetField<int8_t>(VT_HEADING_CONFIDENCE, 0));
  }
  uint16_t ver_speed() const {
    return GetField<uint16_t>(VT_VER_SPEED, 0);
  }
  MECData::DE_SpeedConfidence ver_speed_confidence() const {
    return static_cast<MECData::DE_SpeedConfidence>(GetField<int8_t>(VT_VER_SPEED_CONFIDENCE, 0));
  }
  const MECData::DF_ObjectSize *size() const {
    return GetPointer<const MECData::DF_ObjectSize *>(VT_SIZE);
  }
  MECData::DE_SizeValueConfidence obj_size_confidence() const {
    return static_cast<MECData::DE_SizeValueConfidence>(GetField<uint8_t>(VT_OBJ_SIZE_CONFIDENCE, 0));
  }
  uint16_t tracking() const {
    return GetField<uint16_t>(VT_TRACKING, 0);
  }
  const MECData::DF_Polygon *polygon() const {
    return GetPointer<const MECData::DF_Polygon *>(VT_POLYGON);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<uint8_t>(verifier, VT_OBS_TYPE, 1) &&
           VerifyField<uint8_t>(verifier, VT_OBJ_TYPE_CONFIDENCE, 1) &&
           VerifyField<uint16_t>(verifier, VT_OBS_ID, 2) &&
           VerifyField<uint8_t>(verifier, VT_SOURCE, 1) &&
           VerifyField<uint32_t>(verifier, VT_MOY, 4) &&
           VerifyField<uint16_t>(verifier, VT_SECMARK, 2) &&
           VerifyOffset(verifier, VT_POS) &&
           verifier.VerifyTable(pos()) &&
           VerifyOffset(verifier, VT_POS_CONFIDENCE) &&
           verifier.VerifyTable(pos_confidence()) &&
           VerifyField<uint16_t>(verifier, VT_SPEED, 2) &&
           VerifyField<int8_t>(verifier, VT_SPEED_CONFIDENCE, 1) &&
           VerifyField<int16_t>(verifier, VT_HEADING, 2) &&
           VerifyField<int8_t>(verifier, VT_HEADING_CONFIDENCE, 1) &&
           VerifyField<uint16_t>(verifier, VT_VER_SPEED, 2) &&
           VerifyField<int8_t>(verifier, VT_VER_SPEED_CONFIDENCE, 1) &&
           VerifyOffset(verifier, VT_SIZE) &&
           verifier.VerifyTable(size()) &&
           VerifyField<uint8_t>(verifier, VT_OBJ_SIZE_CONFIDENCE, 1) &&
           VerifyField<uint16_t>(verifier, VT_TRACKING, 2) &&
           VerifyOffset(verifier, VT_POLYGON) &&
           verifier.VerifyTable(polygon()) &&
           verifier.EndTable();
  }
};

struct DF_ObstacleDataBuilder {
  typedef DF_ObstacleData Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_obs_type(MECData::DE_ObstacleType obs_type) {
    fbb_.AddElement<uint8_t>(DF_ObstacleData::VT_OBS_TYPE, static_cast<uint8_t>(obs_type), 0);
  }
  void add_obj_type_confidence(uint8_t obj_type_confidence) {
    fbb_.AddElement<uint8_t>(DF_ObstacleData::VT_OBJ_TYPE_CONFIDENCE, obj_type_confidence, 0);
  }
  void add_obs_id(uint16_t obs_id) {
    fbb_.AddElement<uint16_t>(DF_ObstacleData::VT_OBS_ID, obs_id, 0);
  }
  void add_source(MECData::DE_SourceType source) {
    fbb_.AddElement<uint8_t>(DF_ObstacleData::VT_SOURCE, static_cast<uint8_t>(source), 0);
  }
  void add_moy(uint32_t moy) {
    fbb_.AddElement<uint32_t>(DF_ObstacleData::VT_MOY, moy, 0);
  }
  void add_secmark(uint16_t secmark) {
    fbb_.AddElement<uint16_t>(DF_ObstacleData::VT_SECMARK, secmark, 0);
  }
  void add_pos(::flatbuffers::Offset<MECData::DF_PositionOffsetLLV> pos) {
    fbb_.AddOffset(DF_ObstacleData::VT_POS, pos);
  }
  void add_pos_confidence(::flatbuffers::Offset<MECData::DF_PositionConfidenceSet> pos_confidence) {
    fbb_.AddOffset(DF_ObstacleData::VT_POS_CONFIDENCE, pos_confidence);
  }
  void add_speed(uint16_t speed) {
    fbb_.AddElement<uint16_t>(DF_ObstacleData::VT_SPEED, speed, 0);
  }
  void add_speed_confidence(MECData::DE_SpeedConfidence speed_confidence) {
    fbb_.AddElement<int8_t>(DF_ObstacleData::VT_SPEED_CONFIDENCE, static_cast<int8_t>(speed_confidence), 0);
  }
  void add_heading(int16_t heading) {
    fbb_.AddElement<int16_t>(DF_ObstacleData::VT_HEADING, heading, 0);
  }
  void add_heading_confidence(MECData::DE_HeadingConfidence heading_confidence) {
    fbb_.AddElement<int8_t>(DF_ObstacleData::VT_HEADING_CONFIDENCE, static_cast<int8_t>(heading_confidence), 0);
  }
  void add_ver_speed(uint16_t ver_speed) {
    fbb_.AddElement<uint16_t>(DF_ObstacleData::VT_VER_SPEED, ver_speed, 0);
  }
  void add_ver_speed_confidence(MECData::DE_SpeedConfidence ver_speed_confidence) {
    fbb_.AddElement<int8_t>(DF_ObstacleData::VT_VER_SPEED_CONFIDENCE, static_cast<int8_t>(ver_speed_confidence), 0);
  }
  void add_size(::flatbuffers::Offset<MECData::DF_ObjectSize> size) {
    fbb_.AddOffset(DF_ObstacleData::VT_SIZE, size);
  }
  void add_obj_size_confidence(MECData::DE_SizeValueConfidence obj_size_confidence) {
    fbb_.AddElement<uint8_t>(DF_ObstacleData::VT_OBJ_SIZE_CONFIDENCE, static_cast<uint8_t>(obj_size_confidence), 0);
  }
  void add_tracking(uint16_t tracking) {
    fbb_.AddElement<uint16_t>(DF_ObstacleData::VT_TRACKING, tracking, 0);
  }
  void add_polygon(::flatbuffers::Offset<MECData::DF_Polygon> polygon) {
    fbb_.AddOffset(DF_ObstacleData::VT_POLYGON, polygon);
  }
  explicit DF_ObstacleDataBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_ObstacleData> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_ObstacleData>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_ObstacleData> CreateDF_ObstacleData(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    MECData::DE_ObstacleType obs_type = MECData::DE_ObstacleType_unknown,
    uint8_t obj_type_confidence = 0,
    uint16_t obs_id = 0,
    MECData::DE_SourceType source = MECData::DE_SourceType_unknown,
    uint32_t moy = 0,
    uint16_t secmark = 0,
    ::flatbuffers::Offset<MECData::DF_PositionOffsetLLV> pos = 0,
    ::flatbuffers::Offset<MECData::DF_PositionConfidenceSet> pos_confidence = 0,
    uint16_t speed = 0,
    MECData::DE_SpeedConfidence speed_confidence = MECData::DE_SpeedConfidence_unavailable,
    int16_t heading = 0,
    MECData::DE_HeadingConfidence heading_confidence = MECData::DE_HeadingConfidence_unavailable,
    uint16_t ver_speed = 0,
    MECData::DE_SpeedConfidence ver_speed_confidence = MECData::DE_SpeedConfidence_unavailable,
    ::flatbuffers::Offset<MECData::DF_ObjectSize> size = 0,
    MECData::DE_SizeValueConfidence obj_size_confidence = MECData::DE_SizeValueConfidence_unavailable,
    uint16_t tracking = 0,
    ::flatbuffers::Offset<MECData::DF_Polygon> polygon = 0) {
  DF_ObstacleDataBuilder builder_(_fbb);
  builder_.add_polygon(polygon);
  builder_.add_size(size);
  builder_.add_pos_confidence(pos_confidence);
  builder_.add_pos(pos);
  builder_.add_moy(moy);
  builder_.add_tracking(tracking);
  builder_.add_ver_speed(ver_speed);
  builder_.add_heading(heading);
  builder_.add_speed(speed);
  builder_.add_secmark(secmark);
  builder_.add_obs_id(obs_id);
  builder_.add_obj_size_confidence(obj_size_confidence);
  builder_.add_ver_speed_confidence(ver_speed_confidence);
  builder_.add_heading_confidence(heading_confidence);
  builder_.add_speed_confidence(speed_confidence);
  builder_.add_source(source);
  builder_.add_obj_type_confidence(obj_type_confidence);
  builder_.add_obs_type(obs_type);
  return builder_.Finish();
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_OBSTACLEDATA_MECDATA_H_
