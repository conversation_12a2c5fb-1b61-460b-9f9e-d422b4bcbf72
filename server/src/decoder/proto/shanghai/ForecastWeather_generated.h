// automatically generated by the FlatBuffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_FORECASTWEATHER_MECDATA_H_
#define FLATBUFFERS_GENERATED_FORECASTWEATHER_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

#include "HourlyForecast_generated.h"
#include "Position3D_generated.h"

namespace MECData {

struct MSG_ForecastWeather;
struct MSG_ForecastWeatherBuilder;

struct MSG_ForecastWeather FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef MSG_ForecastWeatherBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_DIVISIONCODE = 4,
    VT_TIMEZONE = 6,
    VT_POS = 8,
    VT_UPDATETIME = 10,
    VT_FORECAST = 12,
    VT_MSG_ID = 14
  };
  uint32_t divisionCode() const {
    return GetField<uint32_t>(VT_DIVISIONCODE, 4294967295);
  }
  int16_t timezone() const {
    return GetField<int16_t>(VT_TIMEZONE, 32767);
  }
  const MECData::DF_Position3D *pos() const {
    return GetPointer<const MECData::DF_Position3D *>(VT_POS);
  }
  uint32_t updateTime() const {
    return GetField<uint32_t>(VT_UPDATETIME, 4294967295);
  }
  const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_HourlyForecast>> *forecast() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_HourlyForecast>> *>(VT_FORECAST);
  }
  int64_t msg_id() const {
    return GetField<int64_t>(VT_MSG_ID, 0);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<uint32_t>(verifier, VT_DIVISIONCODE, 4) &&
           VerifyField<int16_t>(verifier, VT_TIMEZONE, 2) &&
           VerifyOffset(verifier, VT_POS) &&
           verifier.VerifyTable(pos()) &&
           VerifyField<uint32_t>(verifier, VT_UPDATETIME, 4) &&
           VerifyOffsetRequired(verifier, VT_FORECAST) &&
           verifier.VerifyVector(forecast()) &&
           verifier.VerifyVectorOfTables(forecast()) &&
           VerifyField<int64_t>(verifier, VT_MSG_ID, 8) &&
           verifier.EndTable();
  }
};

struct MSG_ForecastWeatherBuilder {
  typedef MSG_ForecastWeather Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_divisionCode(uint32_t divisionCode) {
    fbb_.AddElement<uint32_t>(MSG_ForecastWeather::VT_DIVISIONCODE, divisionCode, 4294967295);
  }
  void add_timezone(int16_t timezone) {
    fbb_.AddElement<int16_t>(MSG_ForecastWeather::VT_TIMEZONE, timezone, 32767);
  }
  void add_pos(::flatbuffers::Offset<MECData::DF_Position3D> pos) {
    fbb_.AddOffset(MSG_ForecastWeather::VT_POS, pos);
  }
  void add_updateTime(uint32_t updateTime) {
    fbb_.AddElement<uint32_t>(MSG_ForecastWeather::VT_UPDATETIME, updateTime, 4294967295);
  }
  void add_forecast(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_HourlyForecast>>> forecast) {
    fbb_.AddOffset(MSG_ForecastWeather::VT_FORECAST, forecast);
  }
  void add_msg_id(int64_t msg_id) {
    fbb_.AddElement<int64_t>(MSG_ForecastWeather::VT_MSG_ID, msg_id, 0);
  }
  explicit MSG_ForecastWeatherBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<MSG_ForecastWeather> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<MSG_ForecastWeather>(end);
    fbb_.Required(o, MSG_ForecastWeather::VT_FORECAST);
    return o;
  }
};

inline ::flatbuffers::Offset<MSG_ForecastWeather> CreateMSG_ForecastWeather(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    uint32_t divisionCode = 4294967295,
    int16_t timezone = 32767,
    ::flatbuffers::Offset<MECData::DF_Position3D> pos = 0,
    uint32_t updateTime = 4294967295,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_HourlyForecast>>> forecast = 0,
    int64_t msg_id = 0) {
  MSG_ForecastWeatherBuilder builder_(_fbb);
  builder_.add_msg_id(msg_id);
  builder_.add_forecast(forecast);
  builder_.add_updateTime(updateTime);
  builder_.add_pos(pos);
  builder_.add_divisionCode(divisionCode);
  builder_.add_timezone(timezone);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<MSG_ForecastWeather> CreateMSG_ForecastWeatherDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    uint32_t divisionCode = 4294967295,
    int16_t timezone = 32767,
    ::flatbuffers::Offset<MECData::DF_Position3D> pos = 0,
    uint32_t updateTime = 4294967295,
    const std::vector<::flatbuffers::Offset<MECData::DF_HourlyForecast>> *forecast = nullptr,
    int64_t msg_id = 0) {
  auto forecast__ = forecast ? _fbb.CreateVector<::flatbuffers::Offset<MECData::DF_HourlyForecast>>(*forecast) : 0;
  return MECData::CreateMSG_ForecastWeather(
      _fbb,
      divisionCode,
      timezone,
      pos,
      updateTime,
      forecast__,
      msg_id);
}

inline const MECData::MSG_ForecastWeather *GetMSG_ForecastWeather(const void *buf) {
  return ::flatbuffers::GetRoot<MECData::MSG_ForecastWeather>(buf);
}

inline const MECData::MSG_ForecastWeather *GetSizePrefixedMSG_ForecastWeather(const void *buf) {
  return ::flatbuffers::GetSizePrefixedRoot<MECData::MSG_ForecastWeather>(buf);
}

inline bool VerifyMSG_ForecastWeatherBuffer(
    ::flatbuffers::Verifier &verifier) {
  return verifier.VerifyBuffer<MECData::MSG_ForecastWeather>(nullptr);
}

inline bool VerifySizePrefixedMSG_ForecastWeatherBuffer(
    ::flatbuffers::Verifier &verifier) {
  return verifier.VerifySizePrefixedBuffer<MECData::MSG_ForecastWeather>(nullptr);
}

inline void FinishMSG_ForecastWeatherBuffer(
    ::flatbuffers::FlatBufferBuilder &fbb,
    ::flatbuffers::Offset<MECData::MSG_ForecastWeather> root) {
  fbb.Finish(root);
}

inline void FinishSizePrefixedMSG_ForecastWeatherBuffer(
    ::flatbuffers::FlatBufferBuilder &fbb,
    ::flatbuffers::Offset<MECData::MSG_ForecastWeather> root) {
  fbb.FinishSizePrefixed(root);
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_FORECASTWEATHER_MECDATA_H_
