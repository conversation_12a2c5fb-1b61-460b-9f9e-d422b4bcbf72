// automatically generated by the <PERSON>Buffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_EVENTACTION_MECDATA_H_
#define FLATBUFFERS_GENERATED_EVENTACTION_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

namespace MECData {

enum DE_EventAction : uint8_t {
  DE_EventAction_OCCURRED = 0,
  DE_EventAction_EXTEND = 1,
  DE_EventAction_CANCEL = 2,
  DE_EventAction_UPDATE = 3,
  DE_EventAction_MIN = DE_EventAction_OCCURRED,
  DE_EventAction_MAX = DE_EventAction_UPDATE
};

inline const DE_EventAction (&EnumValuesDE_EventAction())[4] {
  static const DE_EventAction values[] = {
    DE_EventAction_OCCURRED,
    DE_EventAction_EXTEND,
    DE_EventAction_CANCEL,
    DE_EventAction_UPDATE
  };
  return values;
}

inline const char * const *EnumNamesDE_EventAction() {
  static const char * const names[5] = {
    "OCCURRED",
    "EXTEND",
    "CANCEL",
    "UPDATE",
    nullptr
  };
  return names;
}

inline const char *EnumNameDE_EventAction(DE_EventAction e) {
  if (::flatbuffers::IsOutRange(e, DE_EventAction_OCCURRED, DE_EventAction_UPDATE)) return "";
  const size_t index = static_cast<size_t>(e);
  return EnumNamesDE_EventAction()[index];
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_EVENTACTION_MECDATA_H_
