// automatically generated by the <PERSON><PERSON>uffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_PHASE_MECDATA_H_
#define FLATBUFFERS_GENERATED_PHASE_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

#include "PhaseCharacteristics_generated.h"
#include "PhaseState_generated.h"

namespace MECData {

struct DF_Phase;
struct DF_PhaseBuilder;

struct DF_Phase FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_PhaseBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_ID = 4,
    VT_PHASESTATES = 6,
    VT_PHASE_CHAR = 8
  };
  uint8_t id() const {
    return GetField<uint8_t>(VT_ID, 255);
  }
  const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_PhaseState>> *phaseStates() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_PhaseState>> *>(VT_PHASESTATES);
  }
  const MECData::DF_PhaseCharacteristics *phase_char() const {
    return GetPointer<const MECData::DF_PhaseCharacteristics *>(VT_PHASE_CHAR);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<uint8_t>(verifier, VT_ID, 1) &&
           VerifyOffsetRequired(verifier, VT_PHASESTATES) &&
           verifier.VerifyVector(phaseStates()) &&
           verifier.VerifyVectorOfTables(phaseStates()) &&
           VerifyOffset(verifier, VT_PHASE_CHAR) &&
           verifier.VerifyTable(phase_char()) &&
           verifier.EndTable();
  }
};

struct DF_PhaseBuilder {
  typedef DF_Phase Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_id(uint8_t id) {
    fbb_.AddElement<uint8_t>(DF_Phase::VT_ID, id, 255);
  }
  void add_phaseStates(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_PhaseState>>> phaseStates) {
    fbb_.AddOffset(DF_Phase::VT_PHASESTATES, phaseStates);
  }
  void add_phase_char(::flatbuffers::Offset<MECData::DF_PhaseCharacteristics> phase_char) {
    fbb_.AddOffset(DF_Phase::VT_PHASE_CHAR, phase_char);
  }
  explicit DF_PhaseBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_Phase> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_Phase>(end);
    fbb_.Required(o, DF_Phase::VT_PHASESTATES);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_Phase> CreateDF_Phase(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    uint8_t id = 255,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_PhaseState>>> phaseStates = 0,
    ::flatbuffers::Offset<MECData::DF_PhaseCharacteristics> phase_char = 0) {
  DF_PhaseBuilder builder_(_fbb);
  builder_.add_phase_char(phase_char);
  builder_.add_phaseStates(phaseStates);
  builder_.add_id(id);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DF_Phase> CreateDF_PhaseDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    uint8_t id = 255,
    const std::vector<::flatbuffers::Offset<MECData::DF_PhaseState>> *phaseStates = nullptr,
    ::flatbuffers::Offset<MECData::DF_PhaseCharacteristics> phase_char = 0) {
  auto phaseStates__ = phaseStates ? _fbb.CreateVector<::flatbuffers::Offset<MECData::DF_PhaseState>>(*phaseStates) : 0;
  return MECData::CreateDF_Phase(
      _fbb,
      id,
      phaseStates__,
      phase_char);
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_PHASE_MECDATA_H_
