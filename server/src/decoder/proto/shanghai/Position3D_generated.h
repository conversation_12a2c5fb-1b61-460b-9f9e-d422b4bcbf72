// automatically generated by the <PERSON>Buffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_POSITION3D_MECDATA_H_
#define FLATBUFFERS_GENERATED_POSITION3D_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

namespace MECData {

struct DF_Position3D;
struct DF_Position3DBuilder;

struct DF_Position3D FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_Position3DBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_LAT = 4,
    VT_LON = 6,
    VT_ELE = 8
  };
  int32_t lat() const {
    return GetField<int32_t>(VT_LAT, 2147483647);
  }
  int32_t lon() const {
    return GetField<int32_t>(VT_LON, 2147483647);
  }
  int16_t ele() const {
    return GetField<int16_t>(VT_ELE, 0);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<int32_t>(verifier, VT_LAT, 4) &&
           VerifyField<int32_t>(verifier, VT_LON, 4) &&
           VerifyField<int16_t>(verifier, VT_ELE, 2) &&
           verifier.EndTable();
  }
};

struct DF_Position3DBuilder {
  typedef DF_Position3D Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_lat(int32_t lat) {
    fbb_.AddElement<int32_t>(DF_Position3D::VT_LAT, lat, 2147483647);
  }
  void add_lon(int32_t lon) {
    fbb_.AddElement<int32_t>(DF_Position3D::VT_LON, lon, 2147483647);
  }
  void add_ele(int16_t ele) {
    fbb_.AddElement<int16_t>(DF_Position3D::VT_ELE, ele, 0);
  }
  explicit DF_Position3DBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_Position3D> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_Position3D>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_Position3D> CreateDF_Position3D(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    int32_t lat = 2147483647,
    int32_t lon = 2147483647,
    int16_t ele = 0) {
  DF_Position3DBuilder builder_(_fbb);
  builder_.add_lon(lon);
  builder_.add_lat(lat);
  builder_.add_ele(ele);
  return builder_.Finish();
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_POSITION3D_MECDATA_H_
