// automatically generated by the <PERSON>Buffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_POSITIONLL28B_MECDATA_H_
#define FLATBUFFERS_GENERATED_POSITIONLL28B_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

namespace MECData {

struct DF_PositionLL28B;
struct DF_PositionLL28BBuilder;

struct DF_PositionLL28B FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_PositionLL28BBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_LON = 4,
    VT_LAT = 6
  };
  int16_t lon() const {
    return GetField<int16_t>(VT_LON, 0);
  }
  int16_t lat() const {
    return GetField<int16_t>(VT_LAT, 0);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<int16_t>(verifier, VT_LON, 2) &&
           VerifyField<int16_t>(verifier, VT_LAT, 2) &&
           verifier.EndTable();
  }
};

struct DF_PositionLL28BBuilder {
  typedef DF_PositionLL28B Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_lon(int16_t lon) {
    fbb_.AddElement<int16_t>(DF_PositionLL28B::VT_LON, lon, 0);
  }
  void add_lat(int16_t lat) {
    fbb_.AddElement<int16_t>(DF_PositionLL28B::VT_LAT, lat, 0);
  }
  explicit DF_PositionLL28BBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_PositionLL28B> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_PositionLL28B>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_PositionLL28B> CreateDF_PositionLL28B(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    int16_t lon = 0,
    int16_t lat = 0) {
  DF_PositionLL28BBuilder builder_(_fbb);
  builder_.add_lat(lat);
  builder_.add_lon(lon);
  return builder_.Finish();
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_POSITIONLL28B_MECDATA_H_
