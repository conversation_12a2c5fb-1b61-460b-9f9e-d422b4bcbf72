// automatically generated by the <PERSON>Buffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_LIGHTSTATE_MECDATA_H_
#define FLATBUFFERS_GENERATED_LIGHTSTATE_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

namespace MECData {

enum DE_LightState : int8_t {
  DE_LightState_unavailable = 0,
  DE_LightState_dark = 1,
  DE_LightState_stopThenProceed = 2,
  DE_LightState_stopAndRemain = 3,
  DE_LightState_preMovement = 4,
  DE_LightState_permissiveMovementAllowed = 5,
  DE_LightState_protectedMovementAllowed = 6,
  DE_LightState_intersectionClearance = 7,
  DE_LightState_cautionConflictingTraffic = 8,
  DE_LightState_MIN = DE_LightState_unavailable,
  DE_LightState_MAX = DE_LightState_cautionConflictingTraffic
};

inline const DE_LightState (&EnumValuesDE_LightState())[9] {
  static const DE_LightState values[] = {
    DE_LightState_unavailable,
    DE_LightState_dark,
    DE_LightState_stopThenProceed,
    DE_LightState_stopAndRemain,
    DE_LightState_preMovement,
    DE_LightState_permissiveMovementAllowed,
    DE_LightState_protectedMovementAllowed,
    DE_LightState_intersectionClearance,
    DE_LightState_cautionConflictingTraffic
  };
  return values;
}

inline const char * const *EnumNamesDE_LightState() {
  static const char * const names[10] = {
    "unavailable",
    "dark",
    "stopThenProceed",
    "stopAndRemain",
    "preMovement",
    "permissiveMovementAllowed",
    "protectedMovementAllowed",
    "intersectionClearance",
    "cautionConflictingTraffic",
    nullptr
  };
  return names;
}

inline const char *EnumNameDE_LightState(DE_LightState e) {
  if (::flatbuffers::IsOutRange(e, DE_LightState_unavailable, DE_LightState_cautionConflictingTraffic)) return "";
  const size_t index = static_cast<size_t>(e);
  return EnumNamesDE_LightState()[index];
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_LIGHTSTATE_MECDATA_H_
