// automatically generated by the FlatBuffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_ACCCONFIDENCE_MECDATA_H_
#define FLATBUFFERS_GENERATED_ACCCONFIDENCE_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

namespace MECData {

enum DE_AccConfidence : int8_t {
  DE_AccConfidence_unavailable = 0,
  DE_AccConfidence_prec100deg = 1,
  DE_AccConfidence_prec10deg = 2,
  DE_AccConfidence_prec5deg = 3,
  DE_AccConfidence_prec1deg = 4,
  DE_AccConfidence_prec01deg = 5,
  DE_AccConfidence_prec005deg = 6,
  DE_AccConfidence_prec001deg = 7,
  DE_AccConfidence_MIN = DE_AccConfidence_unavailable,
  DE_AccConfidence_MAX = DE_AccConfidence_prec001deg
};

inline const DE_AccConfidence (&EnumValuesDE_AccConfidence())[8] {
  static const DE_AccConfidence values[] = {
    DE_AccConfidence_unavailable,
    DE_AccConfidence_prec100deg,
    DE_AccConfidence_prec10deg,
    DE_AccConfidence_prec5deg,
    DE_AccConfidence_prec1deg,
    DE_AccConfidence_prec01deg,
    DE_AccConfidence_prec005deg,
    DE_AccConfidence_prec001deg
  };
  return values;
}

inline const char * const *EnumNamesDE_AccConfidence() {
  static const char * const names[9] = {
    "unavailable",
    "prec100deg",
    "prec10deg",
    "prec5deg",
    "prec1deg",
    "prec01deg",
    "prec005deg",
    "prec001deg",
    nullptr
  };
  return names;
}

inline const char *EnumNameDE_AccConfidence(DE_AccConfidence e) {
  if (::flatbuffers::IsOutRange(e, DE_AccConfidence_unavailable, DE_AccConfidence_prec001deg)) return "";
  const size_t index = static_cast<size_t>(e);
  return EnumNamesDE_AccConfidence()[index];
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_ACCCONFIDENCE_MECDATA_H_
