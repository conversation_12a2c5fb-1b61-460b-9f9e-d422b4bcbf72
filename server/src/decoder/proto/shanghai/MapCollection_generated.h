// automatically generated by the <PERSON><PERSON>uffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_MAPCOLLECTION_MECDATA_H_
#define FLATBUFFERS_GENERATED_MAPCOLLECTION_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

#include "Map_generated.h"
#include "NodeReferenceID_generated.h"

namespace MECData {

struct DF_MapContent;
struct DF_MapContentBuilder;

struct DE_MapCollection;
struct DE_MapCollectionBuilder;

enum DE_MapContentType : int8_t {
  DE_MapContentType_UNKNOWN = 0,
  DE_MapContentType_JSON = 1,
  DE_MapContentType_FLATBUFFERS = 2,
  DE_MapContentType_PROTOBUF = 3,
  DE_MapContentType_MIN = DE_MapContentType_UNKNOWN,
  DE_MapContentType_MAX = DE_MapContentType_PROTOBUF
};

inline const DE_MapContentType (&EnumValuesDE_MapContentType())[4] {
  static const DE_MapContentType values[] = {
    DE_MapContentType_UNKNOWN,
    DE_MapContentType_JSON,
    DE_MapContentType_FLATBUFFERS,
    DE_MapContentType_PROTOBUF
  };
  return values;
}

inline const char * const *EnumNamesDE_MapContentType() {
  static const char * const names[5] = {
    "UNKNOWN",
    "JSON",
    "FLATBUFFERS",
    "PROTOBUF",
    nullptr
  };
  return names;
}

inline const char *EnumNameDE_MapContentType(DE_MapContentType e) {
  if (::flatbuffers::IsOutRange(e, DE_MapContentType_UNKNOWN, DE_MapContentType_PROTOBUF)) return "";
  const size_t index = static_cast<size_t>(e);
  return EnumNamesDE_MapContentType()[index];
}

struct DF_MapContent FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_MapContentBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_NAME = 4,
    VT_VERSION = 6,
    VT_TIME = 8,
    VT_NODE = 10,
    VT_MAP_CONTENT_TYPE = 12,
    VT_MAP_CONTENT = 14
  };
  const ::flatbuffers::String *name() const {
    return GetPointer<const ::flatbuffers::String *>(VT_NAME);
  }
  const ::flatbuffers::String *version() const {
    return GetPointer<const ::flatbuffers::String *>(VT_VERSION);
  }
  int64_t time() const {
    return GetField<int64_t>(VT_TIME, 0);
  }
  const MECData::DF_NodeReferenceID *node() const {
    return GetPointer<const MECData::DF_NodeReferenceID *>(VT_NODE);
  }
  MECData::DE_MapContentType map_content_type() const {
    return static_cast<MECData::DE_MapContentType>(GetField<int8_t>(VT_MAP_CONTENT_TYPE, 0));
  }
  const ::flatbuffers::Vector<uint8_t> *map_content() const {
    return GetPointer<const ::flatbuffers::Vector<uint8_t> *>(VT_MAP_CONTENT);
  }
  const MECData::MSG_Map *map_content_nested_root() const {
    const auto _f = map_content();
    return _f ? ::flatbuffers::GetRoot<MECData::MSG_Map>(_f->Data())
              : nullptr;
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffsetRequired(verifier, VT_NAME) &&
           verifier.VerifyString(name()) &&
           VerifyOffsetRequired(verifier, VT_VERSION) &&
           verifier.VerifyString(version()) &&
           VerifyField<int64_t>(verifier, VT_TIME, 8) &&
           VerifyOffset(verifier, VT_NODE) &&
           verifier.VerifyTable(node()) &&
           VerifyField<int8_t>(verifier, VT_MAP_CONTENT_TYPE, 1) &&
           VerifyOffsetRequired(verifier, VT_MAP_CONTENT) &&
           verifier.VerifyVector(map_content()) &&
           verifier.VerifyNestedFlatBuffer<MECData::MSG_Map>(map_content(), nullptr) &&
           verifier.EndTable();
  }
};

struct DF_MapContentBuilder {
  typedef DF_MapContent Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_name(::flatbuffers::Offset<::flatbuffers::String> name) {
    fbb_.AddOffset(DF_MapContent::VT_NAME, name);
  }
  void add_version(::flatbuffers::Offset<::flatbuffers::String> version) {
    fbb_.AddOffset(DF_MapContent::VT_VERSION, version);
  }
  void add_time(int64_t time) {
    fbb_.AddElement<int64_t>(DF_MapContent::VT_TIME, time, 0);
  }
  void add_node(::flatbuffers::Offset<MECData::DF_NodeReferenceID> node) {
    fbb_.AddOffset(DF_MapContent::VT_NODE, node);
  }
  void add_map_content_type(MECData::DE_MapContentType map_content_type) {
    fbb_.AddElement<int8_t>(DF_MapContent::VT_MAP_CONTENT_TYPE, static_cast<int8_t>(map_content_type), 0);
  }
  void add_map_content(::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> map_content) {
    fbb_.AddOffset(DF_MapContent::VT_MAP_CONTENT, map_content);
  }
  explicit DF_MapContentBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_MapContent> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_MapContent>(end);
    fbb_.Required(o, DF_MapContent::VT_NAME);
    fbb_.Required(o, DF_MapContent::VT_VERSION);
    fbb_.Required(o, DF_MapContent::VT_MAP_CONTENT);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_MapContent> CreateDF_MapContent(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::String> name = 0,
    ::flatbuffers::Offset<::flatbuffers::String> version = 0,
    int64_t time = 0,
    ::flatbuffers::Offset<MECData::DF_NodeReferenceID> node = 0,
    MECData::DE_MapContentType map_content_type = MECData::DE_MapContentType_UNKNOWN,
    ::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> map_content = 0) {
  DF_MapContentBuilder builder_(_fbb);
  builder_.add_time(time);
  builder_.add_map_content(map_content);
  builder_.add_node(node);
  builder_.add_version(version);
  builder_.add_name(name);
  builder_.add_map_content_type(map_content_type);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DF_MapContent> CreateDF_MapContentDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const char *name = nullptr,
    const char *version = nullptr,
    int64_t time = 0,
    ::flatbuffers::Offset<MECData::DF_NodeReferenceID> node = 0,
    MECData::DE_MapContentType map_content_type = MECData::DE_MapContentType_UNKNOWN,
    const std::vector<uint8_t> *map_content = nullptr) {
  auto name__ = name ? _fbb.CreateString(name) : 0;
  auto version__ = version ? _fbb.CreateString(version) : 0;
  auto map_content__ = map_content ? _fbb.CreateVector<uint8_t>(*map_content) : 0;
  return MECData::CreateDF_MapContent(
      _fbb,
      name__,
      version__,
      time,
      node,
      map_content_type,
      map_content__);
}

struct DE_MapCollection FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DE_MapCollectionBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_MAPS = 4
  };
  const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_MapContent>> *maps() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_MapContent>> *>(VT_MAPS);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_MAPS) &&
           verifier.VerifyVector(maps()) &&
           verifier.VerifyVectorOfTables(maps()) &&
           verifier.EndTable();
  }
};

struct DE_MapCollectionBuilder {
  typedef DE_MapCollection Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_maps(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_MapContent>>> maps) {
    fbb_.AddOffset(DE_MapCollection::VT_MAPS, maps);
  }
  explicit DE_MapCollectionBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DE_MapCollection> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DE_MapCollection>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DE_MapCollection> CreateDE_MapCollection(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_MapContent>>> maps = 0) {
  DE_MapCollectionBuilder builder_(_fbb);
  builder_.add_maps(maps);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DE_MapCollection> CreateDE_MapCollectionDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const std::vector<::flatbuffers::Offset<MECData::DF_MapContent>> *maps = nullptr) {
  auto maps__ = maps ? _fbb.CreateVector<::flatbuffers::Offset<MECData::DF_MapContent>>(*maps) : 0;
  return MECData::CreateDE_MapCollection(
      _fbb,
      maps__);
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_MAPCOLLECTION_MECDATA_H_
