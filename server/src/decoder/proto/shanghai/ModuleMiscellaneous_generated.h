// automatically generated by the <PERSON>Buffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_MODULEMISCELLANEOUS_MECDATA_H_
#define FLATBUFFERS_GENERATED_MODULEMISCELLANEOUS_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

namespace MECData {

struct DF_CrashRestartConfig;
struct DF_CrashRestartConfigBuilder;

struct DF_ModuleWebUI;
struct DF_ModuleWebUIBuilder;

struct DF_IORecorder;
struct DF_IORecorderBuilder;

enum DE_ModuleCategory : uint8_t {
  DE_ModuleCategory_DEVICE_INTERFACE = 0,
  DE_ModuleCategory_APPLICATION_ALGORITHM = 1,
  DE_ModuleCategory_MIN = DE_ModuleCategory_DEVICE_INTERFACE,
  DE_ModuleCategory_MAX = DE_ModuleCategory_APPLICATION_ALGORITHM
};

inline const DE_ModuleCategory (&EnumValuesDE_ModuleCategory())[2] {
  static const DE_ModuleCategory values[] = {
    DE_ModuleCategory_DEVICE_INTERFACE,
    DE_ModuleCategory_APPLICATION_ALGORITHM
  };
  return values;
}

inline const char * const *EnumNamesDE_ModuleCategory() {
  static const char * const names[3] = {
    "DEVICE_INTERFACE",
    "APPLICATION_ALGORITHM",
    nullptr
  };
  return names;
}

inline const char *EnumNameDE_ModuleCategory(DE_ModuleCategory e) {
  if (::flatbuffers::IsOutRange(e, DE_ModuleCategory_DEVICE_INTERFACE, DE_ModuleCategory_APPLICATION_ALGORITHM)) return "";
  const size_t index = static_cast<size_t>(e);
  return EnumNamesDE_ModuleCategory()[index];
}

enum DE_CrashRestartPolicy : uint8_t {
  DE_CrashRestartPolicy_NO_RESTART = 0,
  DE_CrashRestartPolicy_INFINITE_RESTART = 1,
  DE_CrashRestartPolicy_LIMITED_RESTART = 2,
  DE_CrashRestartPolicy_MIN = DE_CrashRestartPolicy_NO_RESTART,
  DE_CrashRestartPolicy_MAX = DE_CrashRestartPolicy_LIMITED_RESTART
};

inline const DE_CrashRestartPolicy (&EnumValuesDE_CrashRestartPolicy())[3] {
  static const DE_CrashRestartPolicy values[] = {
    DE_CrashRestartPolicy_NO_RESTART,
    DE_CrashRestartPolicy_INFINITE_RESTART,
    DE_CrashRestartPolicy_LIMITED_RESTART
  };
  return values;
}

inline const char * const *EnumNamesDE_CrashRestartPolicy() {
  static const char * const names[4] = {
    "NO_RESTART",
    "INFINITE_RESTART",
    "LIMITED_RESTART",
    nullptr
  };
  return names;
}

inline const char *EnumNameDE_CrashRestartPolicy(DE_CrashRestartPolicy e) {
  if (::flatbuffers::IsOutRange(e, DE_CrashRestartPolicy_NO_RESTART, DE_CrashRestartPolicy_LIMITED_RESTART)) return "";
  const size_t index = static_cast<size_t>(e);
  return EnumNamesDE_CrashRestartPolicy()[index];
}

enum DE_ModuleStatus : uint8_t {
  DE_ModuleStatus_UNINITIALIZED = 0,
  DE_ModuleStatus_INITIALIZED = 1,
  DE_ModuleStatus_RUNNING = 2,
  DE_ModuleStatus_PAUSED = 3,
  DE_ModuleStatus_STOPPED = 4,
  DE_ModuleStatus_MIN = DE_ModuleStatus_UNINITIALIZED,
  DE_ModuleStatus_MAX = DE_ModuleStatus_STOPPED
};

inline const DE_ModuleStatus (&EnumValuesDE_ModuleStatus())[5] {
  static const DE_ModuleStatus values[] = {
    DE_ModuleStatus_UNINITIALIZED,
    DE_ModuleStatus_INITIALIZED,
    DE_ModuleStatus_RUNNING,
    DE_ModuleStatus_PAUSED,
    DE_ModuleStatus_STOPPED
  };
  return values;
}

inline const char * const *EnumNamesDE_ModuleStatus() {
  static const char * const names[6] = {
    "UNINITIALIZED",
    "INITIALIZED",
    "RUNNING",
    "PAUSED",
    "STOPPED",
    nullptr
  };
  return names;
}

inline const char *EnumNameDE_ModuleStatus(DE_ModuleStatus e) {
  if (::flatbuffers::IsOutRange(e, DE_ModuleStatus_UNINITIALIZED, DE_ModuleStatus_STOPPED)) return "";
  const size_t index = static_cast<size_t>(e);
  return EnumNamesDE_ModuleStatus()[index];
}

enum DE_DeploymentType : uint8_t {
  DE_DeploymentType_UNKNOWN = 0,
  DE_DeploymentType_NATIVE_CLASS = 1,
  DE_DeploymentType_SHARED_OBJECT = 2,
  DE_DeploymentType_DOCKER_IMAGE = 3,
  DE_DeploymentType_STAND_ALONE = 4,
  DE_DeploymentType_MIN = DE_DeploymentType_UNKNOWN,
  DE_DeploymentType_MAX = DE_DeploymentType_STAND_ALONE
};

inline const DE_DeploymentType (&EnumValuesDE_DeploymentType())[5] {
  static const DE_DeploymentType values[] = {
    DE_DeploymentType_UNKNOWN,
    DE_DeploymentType_NATIVE_CLASS,
    DE_DeploymentType_SHARED_OBJECT,
    DE_DeploymentType_DOCKER_IMAGE,
    DE_DeploymentType_STAND_ALONE
  };
  return values;
}

inline const char * const *EnumNamesDE_DeploymentType() {
  static const char * const names[6] = {
    "UNKNOWN",
    "NATIVE_CLASS",
    "SHARED_OBJECT",
    "DOCKER_IMAGE",
    "STAND_ALONE",
    nullptr
  };
  return names;
}

inline const char *EnumNameDE_DeploymentType(DE_DeploymentType e) {
  if (::flatbuffers::IsOutRange(e, DE_DeploymentType_UNKNOWN, DE_DeploymentType_STAND_ALONE)) return "";
  const size_t index = static_cast<size_t>(e);
  return EnumNamesDE_DeploymentType()[index];
}

struct DF_CrashRestartConfig FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_CrashRestartConfigBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_POLICY = 4,
    VT_MAX_RETRY = 6
  };
  MECData::DE_CrashRestartPolicy policy() const {
    return static_cast<MECData::DE_CrashRestartPolicy>(GetField<uint8_t>(VT_POLICY, 0));
  }
  uint16_t max_retry() const {
    return GetField<uint16_t>(VT_MAX_RETRY, 5);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<uint8_t>(verifier, VT_POLICY, 1) &&
           VerifyField<uint16_t>(verifier, VT_MAX_RETRY, 2) &&
           verifier.EndTable();
  }
};

struct DF_CrashRestartConfigBuilder {
  typedef DF_CrashRestartConfig Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_policy(MECData::DE_CrashRestartPolicy policy) {
    fbb_.AddElement<uint8_t>(DF_CrashRestartConfig::VT_POLICY, static_cast<uint8_t>(policy), 0);
  }
  void add_max_retry(uint16_t max_retry) {
    fbb_.AddElement<uint16_t>(DF_CrashRestartConfig::VT_MAX_RETRY, max_retry, 5);
  }
  explicit DF_CrashRestartConfigBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_CrashRestartConfig> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_CrashRestartConfig>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_CrashRestartConfig> CreateDF_CrashRestartConfig(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    MECData::DE_CrashRestartPolicy policy = MECData::DE_CrashRestartPolicy_NO_RESTART,
    uint16_t max_retry = 5) {
  DF_CrashRestartConfigBuilder builder_(_fbb);
  builder_.add_max_retry(max_retry);
  builder_.add_policy(policy);
  return builder_.Finish();
}

struct DF_ModuleWebUI FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_ModuleWebUIBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_SUPPORTED = 4,
    VT_NAME = 6,
    VT_RELATIVE_PATH = 8
  };
  bool supported() const {
    return GetField<uint8_t>(VT_SUPPORTED, 0) != 0;
  }
  const ::flatbuffers::String *name() const {
    return GetPointer<const ::flatbuffers::String *>(VT_NAME);
  }
  const ::flatbuffers::String *relative_path() const {
    return GetPointer<const ::flatbuffers::String *>(VT_RELATIVE_PATH);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<uint8_t>(verifier, VT_SUPPORTED, 1) &&
           VerifyOffset(verifier, VT_NAME) &&
           verifier.VerifyString(name()) &&
           VerifyOffset(verifier, VT_RELATIVE_PATH) &&
           verifier.VerifyString(relative_path()) &&
           verifier.EndTable();
  }
};

struct DF_ModuleWebUIBuilder {
  typedef DF_ModuleWebUI Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_supported(bool supported) {
    fbb_.AddElement<uint8_t>(DF_ModuleWebUI::VT_SUPPORTED, static_cast<uint8_t>(supported), 0);
  }
  void add_name(::flatbuffers::Offset<::flatbuffers::String> name) {
    fbb_.AddOffset(DF_ModuleWebUI::VT_NAME, name);
  }
  void add_relative_path(::flatbuffers::Offset<::flatbuffers::String> relative_path) {
    fbb_.AddOffset(DF_ModuleWebUI::VT_RELATIVE_PATH, relative_path);
  }
  explicit DF_ModuleWebUIBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_ModuleWebUI> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_ModuleWebUI>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_ModuleWebUI> CreateDF_ModuleWebUI(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    bool supported = false,
    ::flatbuffers::Offset<::flatbuffers::String> name = 0,
    ::flatbuffers::Offset<::flatbuffers::String> relative_path = 0) {
  DF_ModuleWebUIBuilder builder_(_fbb);
  builder_.add_relative_path(relative_path);
  builder_.add_name(name);
  builder_.add_supported(supported);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DF_ModuleWebUI> CreateDF_ModuleWebUIDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    bool supported = false,
    const char *name = nullptr,
    const char *relative_path = nullptr) {
  auto name__ = name ? _fbb.CreateString(name) : 0;
  auto relative_path__ = relative_path ? _fbb.CreateString(relative_path) : 0;
  return MECData::CreateDF_ModuleWebUI(
      _fbb,
      supported,
      name__,
      relative_path__);
}

struct DF_IORecorder FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_IORecorderBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_RECORD = 4,
    VT_RECORD_TYPE_CODES = 6
  };
  bool record() const {
    return GetField<uint8_t>(VT_RECORD, 0) != 0;
  }
  const ::flatbuffers::Vector<uint16_t> *record_type_codes() const {
    return GetPointer<const ::flatbuffers::Vector<uint16_t> *>(VT_RECORD_TYPE_CODES);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<uint8_t>(verifier, VT_RECORD, 1) &&
           VerifyOffset(verifier, VT_RECORD_TYPE_CODES) &&
           verifier.VerifyVector(record_type_codes()) &&
           verifier.EndTable();
  }
};

struct DF_IORecorderBuilder {
  typedef DF_IORecorder Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_record(bool record) {
    fbb_.AddElement<uint8_t>(DF_IORecorder::VT_RECORD, static_cast<uint8_t>(record), 0);
  }
  void add_record_type_codes(::flatbuffers::Offset<::flatbuffers::Vector<uint16_t>> record_type_codes) {
    fbb_.AddOffset(DF_IORecorder::VT_RECORD_TYPE_CODES, record_type_codes);
  }
  explicit DF_IORecorderBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_IORecorder> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_IORecorder>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_IORecorder> CreateDF_IORecorder(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    bool record = false,
    ::flatbuffers::Offset<::flatbuffers::Vector<uint16_t>> record_type_codes = 0) {
  DF_IORecorderBuilder builder_(_fbb);
  builder_.add_record_type_codes(record_type_codes);
  builder_.add_record(record);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DF_IORecorder> CreateDF_IORecorderDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    bool record = false,
    const std::vector<uint16_t> *record_type_codes = nullptr) {
  auto record_type_codes__ = record_type_codes ? _fbb.CreateVector<uint16_t>(*record_type_codes) : 0;
  return MECData::CreateDF_IORecorder(
      _fbb,
      record,
      record_type_codes__);
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_MODULEMISCELLANEOUS_MECDATA_H_
