// automatically generated by the <PERSON><PERSON>uffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_DEVICESET_MECDATA_H_
#define FLATBUFFERS_GENERATED_DEVICESET_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

namespace MECData {

struct MSG_DeviceSet;
struct MSG_DeviceSetBuilder;

struct MSG_DeviceSet FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef MSG_DeviceSetBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_DEVICE_ID = 4,
    VT_DATA = 6,
    VT_MSG_ID = 8
  };
  uint16_t device_id() const {
    return GetField<uint16_t>(VT_DEVICE_ID, 0);
  }
  const ::flatbuffers::Vector<uint8_t> *data() const {
    return GetPointer<const ::flatbuffers::Vector<uint8_t> *>(VT_DATA);
  }
  int64_t msg_id() const {
    return GetField<int64_t>(VT_MSG_ID, 0);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<uint16_t>(verifier, VT_DEVICE_ID, 2) &&
           VerifyOffset(verifier, VT_DATA) &&
           verifier.VerifyVector(data()) &&
           VerifyField<int64_t>(verifier, VT_MSG_ID, 8) &&
           verifier.EndTable();
  }
};

struct MSG_DeviceSetBuilder {
  typedef MSG_DeviceSet Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_device_id(uint16_t device_id) {
    fbb_.AddElement<uint16_t>(MSG_DeviceSet::VT_DEVICE_ID, device_id, 0);
  }
  void add_data(::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> data) {
    fbb_.AddOffset(MSG_DeviceSet::VT_DATA, data);
  }
  void add_msg_id(int64_t msg_id) {
    fbb_.AddElement<int64_t>(MSG_DeviceSet::VT_MSG_ID, msg_id, 0);
  }
  explicit MSG_DeviceSetBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<MSG_DeviceSet> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<MSG_DeviceSet>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<MSG_DeviceSet> CreateMSG_DeviceSet(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    uint16_t device_id = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> data = 0,
    int64_t msg_id = 0) {
  MSG_DeviceSetBuilder builder_(_fbb);
  builder_.add_msg_id(msg_id);
  builder_.add_data(data);
  builder_.add_device_id(device_id);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<MSG_DeviceSet> CreateMSG_DeviceSetDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    uint16_t device_id = 0,
    const std::vector<uint8_t> *data = nullptr,
    int64_t msg_id = 0) {
  auto data__ = data ? _fbb.CreateVector<uint8_t>(*data) : 0;
  return MECData::CreateMSG_DeviceSet(
      _fbb,
      device_id,
      data__,
      msg_id);
}

inline const MECData::MSG_DeviceSet *GetMSG_DeviceSet(const void *buf) {
  return ::flatbuffers::GetRoot<MECData::MSG_DeviceSet>(buf);
}

inline const MECData::MSG_DeviceSet *GetSizePrefixedMSG_DeviceSet(const void *buf) {
  return ::flatbuffers::GetSizePrefixedRoot<MECData::MSG_DeviceSet>(buf);
}

inline bool VerifyMSG_DeviceSetBuffer(
    ::flatbuffers::Verifier &verifier) {
  return verifier.VerifyBuffer<MECData::MSG_DeviceSet>(nullptr);
}

inline bool VerifySizePrefixedMSG_DeviceSetBuffer(
    ::flatbuffers::Verifier &verifier) {
  return verifier.VerifySizePrefixedBuffer<MECData::MSG_DeviceSet>(nullptr);
}

inline void FinishMSG_DeviceSetBuffer(
    ::flatbuffers::FlatBufferBuilder &fbb,
    ::flatbuffers::Offset<MECData::MSG_DeviceSet> root) {
  fbb.Finish(root);
}

inline void FinishSizePrefixedMSG_DeviceSetBuffer(
    ::flatbuffers::FlatBufferBuilder &fbb,
    ::flatbuffers::Offset<MECData::MSG_DeviceSet> root) {
  fbb.FinishSizePrefixed(root);
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_DEVICESET_MECDATA_H_
