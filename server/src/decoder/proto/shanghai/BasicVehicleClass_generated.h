// automatically generated by the FlatBuffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_BASICVEHICLECLASS_MECDATA_H_
#define FLATBUFFERS_GENERATED_BASICVEHICLECLASS_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

namespace MECData {

enum DE_BasicVehicleClass : uint8_t {
  DE_BasicVehicleClass_unknownVehicleClass = 0,
  DE_BasicVehicleClass_specialVehicleClass = 1,
  DE_BasicVehicleClass_passenger_Vehicle_TypeUnknown = 10,
  DE_BasicVehicleClass_passenger_Vehicle_TypeOther = 11,
  DE_BasicVehicleClass_lightTruck_Vehicle_TypeUnknown = 20,
  DE_BasicVehicleClass_lightTruck_Vehicle_TypeOther = 21,
  DE_BasicVehicleClass_truck_Vehicle_TypeUnknown = 25,
  DE_BasicVehicleClass_truck_Vehicle_TypeOther = 26,
  DE_BasicVehicleClass_truck_axleCnt2 = 27,
  DE_BasicVehicleClass_truck_axleCnt3 = 28,
  DE_BasicVehicleClass_truck_axleCnt4 = 29,
  DE_BasicVehicleClass_truck_axleCnt4Trailer = 30,
  DE_BasicVehicleClass_truck_axleCnt5Trailer = 31,
  DE_BasicVehicleClass_truck_axleCnt6Trailer = 32,
  DE_BasicVehicleClass_truck_axleCnt5MultiTrailer = 33,
  DE_BasicVehicleClass_truck_axleCnt6MultiTrailer = 34,
  DE_BasicVehicleClass_truck_axleCnt7MultiTrailer = 35,
  DE_BasicVehicleClass_motorcycle_TypeUnknown = 40,
  DE_BasicVehicleClass_motorcycle_TypeOther = 41,
  DE_BasicVehicleClass_motorcycle_Cruiser_Standard = 42,
  DE_BasicVehicleClass_motorcycle_SportUnclad = 43,
  DE_BasicVehicleClass_motorcycle_SportTouring = 44,
  DE_BasicVehicleClass_motorcycle_SuperSport = 45,
  DE_BasicVehicleClass_motorcycle_Touring = 46,
  DE_BasicVehicleClass_motorcycle_Trike = 47,
  DE_BasicVehicleClass_motorcycle_wPassengers = 48,
  DE_BasicVehicleClass_transit_TypeUnknown = 50,
  DE_BasicVehicleClass_transit_TypeOther = 51,
  DE_BasicVehicleClass_transit_BRT = 52,
  DE_BasicVehicleClass_transit_ExpressBus = 53,
  DE_BasicVehicleClass_transit_LocalBus = 54,
  DE_BasicVehicleClass_transit_SchoolBus = 55,
  DE_BasicVehicleClass_transit_FixedGuideway = 56,
  DE_BasicVehicleClass_transit_Paratransit = 57,
  DE_BasicVehicleClass_transit_Paratransit_Ambulance = 58,
  DE_BasicVehicleClass_emergency_TypeUnknown = 60,
  DE_BasicVehicleClass_emergency_TypeOther = 61,
  DE_BasicVehicleClass_emergency_Fire_Light_Vehicle = 62,
  DE_BasicVehicleClass_emergency_Fire_Heavy_Vehicle = 63,
  DE_BasicVehicleClass_emergency_Fire_Paramedic_Vehicle = 64,
  DE_BasicVehicleClass_emergency_Fire_Ambulance_Vehicle = 65,
  DE_BasicVehicleClass_emergency_Police_Light_Vehicle = 66,
  DE_BasicVehicleClass_emergency_Police_Heavy_Vehicle = 67,
  DE_BasicVehicleClass_emergency_Other_Responder = 68,
  DE_BasicVehicleClass_emergency_Other_Ambulance = 69,
  DE_BasicVehicleClass_otherTraveler_TypeUnknown = 80,
  DE_BasicVehicleClass_otherTraveler_TypeOther = 81,
  DE_BasicVehicleClass_otherTraveler_Pedestrian = 82,
  DE_BasicVehicleClass_otherTraveler_Visually_Disabled = 83,
  DE_BasicVehicleClass_otherTraveler_Physically_Disabled = 84,
  DE_BasicVehicleClass_otherTraveler_Bicycle = 85,
  DE_BasicVehicleClass_otherTraveler_Vulnerable_Roadworker = 86,
  DE_BasicVehicleClass_infrastructure_TypeUnknown = 90,
  DE_BasicVehicleClass_infrastructure_Fixed = 91,
  DE_BasicVehicleClass_infrastructure_Movable = 92,
  DE_BasicVehicleClass_equipped_CargoTrailer = 93,
  DE_BasicVehicleClass_MIN = DE_BasicVehicleClass_unknownVehicleClass,
  DE_BasicVehicleClass_MAX = DE_BasicVehicleClass_equipped_CargoTrailer
};

inline const DE_BasicVehicleClass (&EnumValuesDE_BasicVehicleClass())[56] {
  static const DE_BasicVehicleClass values[] = {
    DE_BasicVehicleClass_unknownVehicleClass,
    DE_BasicVehicleClass_specialVehicleClass,
    DE_BasicVehicleClass_passenger_Vehicle_TypeUnknown,
    DE_BasicVehicleClass_passenger_Vehicle_TypeOther,
    DE_BasicVehicleClass_lightTruck_Vehicle_TypeUnknown,
    DE_BasicVehicleClass_lightTruck_Vehicle_TypeOther,
    DE_BasicVehicleClass_truck_Vehicle_TypeUnknown,
    DE_BasicVehicleClass_truck_Vehicle_TypeOther,
    DE_BasicVehicleClass_truck_axleCnt2,
    DE_BasicVehicleClass_truck_axleCnt3,
    DE_BasicVehicleClass_truck_axleCnt4,
    DE_BasicVehicleClass_truck_axleCnt4Trailer,
    DE_BasicVehicleClass_truck_axleCnt5Trailer,
    DE_BasicVehicleClass_truck_axleCnt6Trailer,
    DE_BasicVehicleClass_truck_axleCnt5MultiTrailer,
    DE_BasicVehicleClass_truck_axleCnt6MultiTrailer,
    DE_BasicVehicleClass_truck_axleCnt7MultiTrailer,
    DE_BasicVehicleClass_motorcycle_TypeUnknown,
    DE_BasicVehicleClass_motorcycle_TypeOther,
    DE_BasicVehicleClass_motorcycle_Cruiser_Standard,
    DE_BasicVehicleClass_motorcycle_SportUnclad,
    DE_BasicVehicleClass_motorcycle_SportTouring,
    DE_BasicVehicleClass_motorcycle_SuperSport,
    DE_BasicVehicleClass_motorcycle_Touring,
    DE_BasicVehicleClass_motorcycle_Trike,
    DE_BasicVehicleClass_motorcycle_wPassengers,
    DE_BasicVehicleClass_transit_TypeUnknown,
    DE_BasicVehicleClass_transit_TypeOther,
    DE_BasicVehicleClass_transit_BRT,
    DE_BasicVehicleClass_transit_ExpressBus,
    DE_BasicVehicleClass_transit_LocalBus,
    DE_BasicVehicleClass_transit_SchoolBus,
    DE_BasicVehicleClass_transit_FixedGuideway,
    DE_BasicVehicleClass_transit_Paratransit,
    DE_BasicVehicleClass_transit_Paratransit_Ambulance,
    DE_BasicVehicleClass_emergency_TypeUnknown,
    DE_BasicVehicleClass_emergency_TypeOther,
    DE_BasicVehicleClass_emergency_Fire_Light_Vehicle,
    DE_BasicVehicleClass_emergency_Fire_Heavy_Vehicle,
    DE_BasicVehicleClass_emergency_Fire_Paramedic_Vehicle,
    DE_BasicVehicleClass_emergency_Fire_Ambulance_Vehicle,
    DE_BasicVehicleClass_emergency_Police_Light_Vehicle,
    DE_BasicVehicleClass_emergency_Police_Heavy_Vehicle,
    DE_BasicVehicleClass_emergency_Other_Responder,
    DE_BasicVehicleClass_emergency_Other_Ambulance,
    DE_BasicVehicleClass_otherTraveler_TypeUnknown,
    DE_BasicVehicleClass_otherTraveler_TypeOther,
    DE_BasicVehicleClass_otherTraveler_Pedestrian,
    DE_BasicVehicleClass_otherTraveler_Visually_Disabled,
    DE_BasicVehicleClass_otherTraveler_Physically_Disabled,
    DE_BasicVehicleClass_otherTraveler_Bicycle,
    DE_BasicVehicleClass_otherTraveler_Vulnerable_Roadworker,
    DE_BasicVehicleClass_infrastructure_TypeUnknown,
    DE_BasicVehicleClass_infrastructure_Fixed,
    DE_BasicVehicleClass_infrastructure_Movable,
    DE_BasicVehicleClass_equipped_CargoTrailer
  };
  return values;
}

inline const char * const *EnumNamesDE_BasicVehicleClass() {
  static const char * const names[95] = {
    "unknownVehicleClass",
    "specialVehicleClass",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "passenger_Vehicle_TypeUnknown",
    "passenger_Vehicle_TypeOther",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "lightTruck_Vehicle_TypeUnknown",
    "lightTruck_Vehicle_TypeOther",
    "",
    "",
    "",
    "truck_Vehicle_TypeUnknown",
    "truck_Vehicle_TypeOther",
    "truck_axleCnt2",
    "truck_axleCnt3",
    "truck_axleCnt4",
    "truck_axleCnt4Trailer",
    "truck_axleCnt5Trailer",
    "truck_axleCnt6Trailer",
    "truck_axleCnt5MultiTrailer",
    "truck_axleCnt6MultiTrailer",
    "truck_axleCnt7MultiTrailer",
    "",
    "",
    "",
    "",
    "motorcycle_TypeUnknown",
    "motorcycle_TypeOther",
    "motorcycle_Cruiser_Standard",
    "motorcycle_SportUnclad",
    "motorcycle_SportTouring",
    "motorcycle_SuperSport",
    "motorcycle_Touring",
    "motorcycle_Trike",
    "motorcycle_wPassengers",
    "",
    "transit_TypeUnknown",
    "transit_TypeOther",
    "transit_BRT",
    "transit_ExpressBus",
    "transit_LocalBus",
    "transit_SchoolBus",
    "transit_FixedGuideway",
    "transit_Paratransit",
    "transit_Paratransit_Ambulance",
    "",
    "emergency_TypeUnknown",
    "emergency_TypeOther",
    "emergency_Fire_Light_Vehicle",
    "emergency_Fire_Heavy_Vehicle",
    "emergency_Fire_Paramedic_Vehicle",
    "emergency_Fire_Ambulance_Vehicle",
    "emergency_Police_Light_Vehicle",
    "emergency_Police_Heavy_Vehicle",
    "emergency_Other_Responder",
    "emergency_Other_Ambulance",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "otherTraveler_TypeUnknown",
    "otherTraveler_TypeOther",
    "otherTraveler_Pedestrian",
    "otherTraveler_Visually_Disabled",
    "otherTraveler_Physically_Disabled",
    "otherTraveler_Bicycle",
    "otherTraveler_Vulnerable_Roadworker",
    "",
    "",
    "",
    "infrastructure_TypeUnknown",
    "infrastructure_Fixed",
    "infrastructure_Movable",
    "equipped_CargoTrailer",
    nullptr
  };
  return names;
}

inline const char *EnumNameDE_BasicVehicleClass(DE_BasicVehicleClass e) {
  if (::flatbuffers::IsOutRange(e, DE_BasicVehicleClass_unknownVehicleClass, DE_BasicVehicleClass_equipped_CargoTrailer)) return "";
  const size_t index = static_cast<size_t>(e);
  return EnumNamesDE_BasicVehicleClass()[index];
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_BASICVEHICLECLASS_MECDATA_H_
