// automatically generated by the <PERSON><PERSON>uffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_ALLOWEDMANEUVERS_MECDATA_H_
#define FLATBUFFERS_GENERATED_ALLOWEDMANEUVERS_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

namespace MECData {

struct DE_AllowedManeuvers;
struct DE_AllowedManeuversBuilder;

struct DE_AllowedManeuvers FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DE_AllowedManeuversBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_BEHAVIOR = 4
  };
  int16_t behavior() const {
    return GetField<int16_t>(VT_BEHAVIOR, 0);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<int16_t>(verifier, VT_BEHAVIOR, 2) &&
           verifier.EndTable();
  }
};

struct DE_AllowedManeuversBuilder {
  typedef DE_AllowedManeuvers Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_behavior(int16_t behavior) {
    fbb_.AddElement<int16_t>(DE_AllowedManeuvers::VT_BEHAVIOR, behavior, 0);
  }
  explicit DE_AllowedManeuversBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DE_AllowedManeuvers> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DE_AllowedManeuvers>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DE_AllowedManeuvers> CreateDE_AllowedManeuvers(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    int16_t behavior = 0) {
  DE_AllowedManeuversBuilder builder_(_fbb);
  builder_.add_behavior(behavior);
  return builder_.Finish();
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_ALLOWEDMANEUVERS_MECDATA_H_
