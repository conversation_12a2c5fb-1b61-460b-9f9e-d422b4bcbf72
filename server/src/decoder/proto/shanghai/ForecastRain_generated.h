// automatically generated by the <PERSON><PERSON>uffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_FORECASTRAIN_MECDATA_H_
#define FLATBUFFERS_GENERATED_FORECASTRAIN_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

#include "Position3D_generated.h"

namespace MECData {

struct MSG_ForecastRain;
struct MSG_ForecastRainBuilder;

struct MSG_ForecastRain FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef MSG_ForecastRainBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_DIVISIONCODE = 4,
    VT_TIMEZONE = 6,
    VT_POS = 8,
    VT_UPDATETIME = 10,
    VT_DESCRIPTION = 12,
    VT_PRECIPITATION = 14,
    VT_MSG_ID = 16
  };
  uint32_t divisionCode() const {
    return GetField<uint32_t>(VT_DIVISIONCODE, 4294967295);
  }
  int16_t timezone() const {
    return GetField<int16_t>(VT_TIMEZONE, 32767);
  }
  const MECData::DF_Position3D *pos() const {
    return GetPointer<const MECData::DF_Position3D *>(VT_POS);
  }
  uint32_t updateTime() const {
    return GetField<uint32_t>(VT_UPDATETIME, 4294967295);
  }
  const ::flatbuffers::String *description() const {
    return GetPointer<const ::flatbuffers::String *>(VT_DESCRIPTION);
  }
  const ::flatbuffers::Vector<uint16_t> *precipitation() const {
    return GetPointer<const ::flatbuffers::Vector<uint16_t> *>(VT_PRECIPITATION);
  }
  int64_t msg_id() const {
    return GetField<int64_t>(VT_MSG_ID, 0);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<uint32_t>(verifier, VT_DIVISIONCODE, 4) &&
           VerifyField<int16_t>(verifier, VT_TIMEZONE, 2) &&
           VerifyOffset(verifier, VT_POS) &&
           verifier.VerifyTable(pos()) &&
           VerifyField<uint32_t>(verifier, VT_UPDATETIME, 4) &&
           VerifyOffset(verifier, VT_DESCRIPTION) &&
           verifier.VerifyString(description()) &&
           VerifyOffset(verifier, VT_PRECIPITATION) &&
           verifier.VerifyVector(precipitation()) &&
           VerifyField<int64_t>(verifier, VT_MSG_ID, 8) &&
           verifier.EndTable();
  }
};

struct MSG_ForecastRainBuilder {
  typedef MSG_ForecastRain Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_divisionCode(uint32_t divisionCode) {
    fbb_.AddElement<uint32_t>(MSG_ForecastRain::VT_DIVISIONCODE, divisionCode, 4294967295);
  }
  void add_timezone(int16_t timezone) {
    fbb_.AddElement<int16_t>(MSG_ForecastRain::VT_TIMEZONE, timezone, 32767);
  }
  void add_pos(::flatbuffers::Offset<MECData::DF_Position3D> pos) {
    fbb_.AddOffset(MSG_ForecastRain::VT_POS, pos);
  }
  void add_updateTime(uint32_t updateTime) {
    fbb_.AddElement<uint32_t>(MSG_ForecastRain::VT_UPDATETIME, updateTime, 4294967295);
  }
  void add_description(::flatbuffers::Offset<::flatbuffers::String> description) {
    fbb_.AddOffset(MSG_ForecastRain::VT_DESCRIPTION, description);
  }
  void add_precipitation(::flatbuffers::Offset<::flatbuffers::Vector<uint16_t>> precipitation) {
    fbb_.AddOffset(MSG_ForecastRain::VT_PRECIPITATION, precipitation);
  }
  void add_msg_id(int64_t msg_id) {
    fbb_.AddElement<int64_t>(MSG_ForecastRain::VT_MSG_ID, msg_id, 0);
  }
  explicit MSG_ForecastRainBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<MSG_ForecastRain> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<MSG_ForecastRain>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<MSG_ForecastRain> CreateMSG_ForecastRain(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    uint32_t divisionCode = 4294967295,
    int16_t timezone = 32767,
    ::flatbuffers::Offset<MECData::DF_Position3D> pos = 0,
    uint32_t updateTime = 4294967295,
    ::flatbuffers::Offset<::flatbuffers::String> description = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<uint16_t>> precipitation = 0,
    int64_t msg_id = 0) {
  MSG_ForecastRainBuilder builder_(_fbb);
  builder_.add_msg_id(msg_id);
  builder_.add_precipitation(precipitation);
  builder_.add_description(description);
  builder_.add_updateTime(updateTime);
  builder_.add_pos(pos);
  builder_.add_divisionCode(divisionCode);
  builder_.add_timezone(timezone);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<MSG_ForecastRain> CreateMSG_ForecastRainDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    uint32_t divisionCode = 4294967295,
    int16_t timezone = 32767,
    ::flatbuffers::Offset<MECData::DF_Position3D> pos = 0,
    uint32_t updateTime = 4294967295,
    const char *description = nullptr,
    const std::vector<uint16_t> *precipitation = nullptr,
    int64_t msg_id = 0) {
  auto description__ = description ? _fbb.CreateString(description) : 0;
  auto precipitation__ = precipitation ? _fbb.CreateVector<uint16_t>(*precipitation) : 0;
  return MECData::CreateMSG_ForecastRain(
      _fbb,
      divisionCode,
      timezone,
      pos,
      updateTime,
      description__,
      precipitation__,
      msg_id);
}

inline const MECData::MSG_ForecastRain *GetMSG_ForecastRain(const void *buf) {
  return ::flatbuffers::GetRoot<MECData::MSG_ForecastRain>(buf);
}

inline const MECData::MSG_ForecastRain *GetSizePrefixedMSG_ForecastRain(const void *buf) {
  return ::flatbuffers::GetSizePrefixedRoot<MECData::MSG_ForecastRain>(buf);
}

inline bool VerifyMSG_ForecastRainBuffer(
    ::flatbuffers::Verifier &verifier) {
  return verifier.VerifyBuffer<MECData::MSG_ForecastRain>(nullptr);
}

inline bool VerifySizePrefixedMSG_ForecastRainBuffer(
    ::flatbuffers::Verifier &verifier) {
  return verifier.VerifySizePrefixedBuffer<MECData::MSG_ForecastRain>(nullptr);
}

inline void FinishMSG_ForecastRainBuffer(
    ::flatbuffers::FlatBufferBuilder &fbb,
    ::flatbuffers::Offset<MECData::MSG_ForecastRain> root) {
  fbb.Finish(root);
}

inline void FinishSizePrefixedMSG_ForecastRainBuffer(
    ::flatbuffers::FlatBufferBuilder &fbb,
    ::flatbuffers::Offset<MECData::MSG_ForecastRain> root) {
  fbb.FinishSizePrefixed(root);
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_FORECASTRAIN_MECDATA_H_
