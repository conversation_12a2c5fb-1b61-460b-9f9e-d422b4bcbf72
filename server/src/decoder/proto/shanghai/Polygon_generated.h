// automatically generated by the <PERSON>Buffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_POLYGON_MECDATA_H_
#define FLATBUFFERS_GENERATED_POLYGON_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

#include "PositionOffsetLLV_generated.h"

namespace MECData {

struct DF_Polygon;
struct DF_PolygonBuilder;

struct DF_Polygon FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_PolygonBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_POLYGON = 4
  };
  const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_PositionOffsetLLV>> *polygon() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_PositionOffsetLLV>> *>(VT_POLYGON);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_POLYGON) &&
           verifier.VerifyVector(polygon()) &&
           verifier.VerifyVectorOfTables(polygon()) &&
           verifier.EndTable();
  }
};

struct DF_PolygonBuilder {
  typedef DF_Polygon Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_polygon(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_PositionOffsetLLV>>> polygon) {
    fbb_.AddOffset(DF_Polygon::VT_POLYGON, polygon);
  }
  explicit DF_PolygonBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_Polygon> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_Polygon>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_Polygon> CreateDF_Polygon(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_PositionOffsetLLV>>> polygon = 0) {
  DF_PolygonBuilder builder_(_fbb);
  builder_.add_polygon(polygon);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DF_Polygon> CreateDF_PolygonDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const std::vector<::flatbuffers::Offset<MECData::DF_PositionOffsetLLV>> *polygon = nullptr) {
  auto polygon__ = polygon ? _fbb.CreateVector<::flatbuffers::Offset<MECData::DF_PositionOffsetLLV>>(*polygon) : 0;
  return MECData::CreateDF_Polygon(
      _fbb,
      polygon__);
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_POLYGON_MECDATA_H_
