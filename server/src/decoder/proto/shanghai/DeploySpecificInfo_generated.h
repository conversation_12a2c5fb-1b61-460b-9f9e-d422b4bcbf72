// automatically generated by the FlatBuffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_DEPLOYSPECIFICINFO_MECDATA_H_
#define FLATBUFFERS_GENERATED_DEPLOYSPECIFICINFO_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

namespace MECData {

struct DE_EnvironmentVariable;
struct DE_EnvironmentVariableBuilder;

struct DE_ContainerPortMapping;
struct DE_ContainerPortMappingBuilder;

struct DE_ContainerVolumeMapping;
struct DE_ContainerVolumeMappingBuilder;

struct DF_DockerModuleInfo;
struct DF_DockerModuleInfoBuilder;

struct DF_SharedObjectModuleInfo;
struct DF_SharedObjectModuleInfoBuilder;

struct DF_StandaloneProgramModuleInfo;
struct DF_StandaloneProgramModuleInfoBuilder;

struct DF_NativeClassModuleInfo;
struct DF_NativeClassModuleInfoBuilder;

enum DE_ContainerStatus : int8_t {
  DE_ContainerStatus_CREATED = 0,
  DE_ContainerStatus_RUNNING = 1,
  DE_ContainerStatus_PAUSED = 2,
  DE_ContainerStatus_STOPPED = 3,
  DE_ContainerStatus_REMOVED = 4,
  DE_ContainerStatus_MIN = DE_ContainerStatus_CREATED,
  DE_ContainerStatus_MAX = DE_ContainerStatus_REMOVED
};

inline const DE_ContainerStatus (&EnumValuesDE_ContainerStatus())[5] {
  static const DE_ContainerStatus values[] = {
    DE_ContainerStatus_CREATED,
    DE_ContainerStatus_RUNNING,
    DE_ContainerStatus_PAUSED,
    DE_ContainerStatus_STOPPED,
    DE_ContainerStatus_REMOVED
  };
  return values;
}

inline const char * const *EnumNamesDE_ContainerStatus() {
  static const char * const names[6] = {
    "CREATED",
    "RUNNING",
    "PAUSED",
    "STOPPED",
    "REMOVED",
    nullptr
  };
  return names;
}

inline const char *EnumNameDE_ContainerStatus(DE_ContainerStatus e) {
  if (::flatbuffers::IsOutRange(e, DE_ContainerStatus_CREATED, DE_ContainerStatus_REMOVED)) return "";
  const size_t index = static_cast<size_t>(e);
  return EnumNamesDE_ContainerStatus()[index];
}

enum DE_NetworkMode : int8_t {
  DE_NetworkMode_BRIDGE = 0,
  DE_NetworkMode_HOST = 1,
  DE_NetworkMode_NONE = 2,
  DE_NetworkMode_OVERLAY = 3,
  DE_NetworkMode_MACVLAN = 4,
  DE_NetworkMode_MIN = DE_NetworkMode_BRIDGE,
  DE_NetworkMode_MAX = DE_NetworkMode_MACVLAN
};

inline const DE_NetworkMode (&EnumValuesDE_NetworkMode())[5] {
  static const DE_NetworkMode values[] = {
    DE_NetworkMode_BRIDGE,
    DE_NetworkMode_HOST,
    DE_NetworkMode_NONE,
    DE_NetworkMode_OVERLAY,
    DE_NetworkMode_MACVLAN
  };
  return values;
}

inline const char * const *EnumNamesDE_NetworkMode() {
  static const char * const names[6] = {
    "BRIDGE",
    "HOST",
    "NONE",
    "OVERLAY",
    "MACVLAN",
    nullptr
  };
  return names;
}

inline const char *EnumNameDE_NetworkMode(DE_NetworkMode e) {
  if (::flatbuffers::IsOutRange(e, DE_NetworkMode_BRIDGE, DE_NetworkMode_MACVLAN)) return "";
  const size_t index = static_cast<size_t>(e);
  return EnumNamesDE_NetworkMode()[index];
}

enum DF_DeploySpecificInfo : uint8_t {
  DF_DeploySpecificInfo_NONE = 0,
  DF_DeploySpecificInfo_DF_NativeClassModuleInfo = 1,
  DF_DeploySpecificInfo_DF_SharedObjectModuleInfo = 2,
  DF_DeploySpecificInfo_DF_DockerModuleInfo = 3,
  DF_DeploySpecificInfo_DF_StandaloneProgramModuleInfo = 4,
  DF_DeploySpecificInfo_MIN = DF_DeploySpecificInfo_NONE,
  DF_DeploySpecificInfo_MAX = DF_DeploySpecificInfo_DF_StandaloneProgramModuleInfo
};

inline const DF_DeploySpecificInfo (&EnumValuesDF_DeploySpecificInfo())[5] {
  static const DF_DeploySpecificInfo values[] = {
    DF_DeploySpecificInfo_NONE,
    DF_DeploySpecificInfo_DF_NativeClassModuleInfo,
    DF_DeploySpecificInfo_DF_SharedObjectModuleInfo,
    DF_DeploySpecificInfo_DF_DockerModuleInfo,
    DF_DeploySpecificInfo_DF_StandaloneProgramModuleInfo
  };
  return values;
}

inline const char * const *EnumNamesDF_DeploySpecificInfo() {
  static const char * const names[6] = {
    "NONE",
    "DF_NativeClassModuleInfo",
    "DF_SharedObjectModuleInfo",
    "DF_DockerModuleInfo",
    "DF_StandaloneProgramModuleInfo",
    nullptr
  };
  return names;
}

inline const char *EnumNameDF_DeploySpecificInfo(DF_DeploySpecificInfo e) {
  if (::flatbuffers::IsOutRange(e, DF_DeploySpecificInfo_NONE, DF_DeploySpecificInfo_DF_StandaloneProgramModuleInfo)) return "";
  const size_t index = static_cast<size_t>(e);
  return EnumNamesDF_DeploySpecificInfo()[index];
}

template<typename T> struct DF_DeploySpecificInfoTraits {
  static const DF_DeploySpecificInfo enum_value = DF_DeploySpecificInfo_NONE;
};

template<> struct DF_DeploySpecificInfoTraits<MECData::DF_NativeClassModuleInfo> {
  static const DF_DeploySpecificInfo enum_value = DF_DeploySpecificInfo_DF_NativeClassModuleInfo;
};

template<> struct DF_DeploySpecificInfoTraits<MECData::DF_SharedObjectModuleInfo> {
  static const DF_DeploySpecificInfo enum_value = DF_DeploySpecificInfo_DF_SharedObjectModuleInfo;
};

template<> struct DF_DeploySpecificInfoTraits<MECData::DF_DockerModuleInfo> {
  static const DF_DeploySpecificInfo enum_value = DF_DeploySpecificInfo_DF_DockerModuleInfo;
};

template<> struct DF_DeploySpecificInfoTraits<MECData::DF_StandaloneProgramModuleInfo> {
  static const DF_DeploySpecificInfo enum_value = DF_DeploySpecificInfo_DF_StandaloneProgramModuleInfo;
};

bool VerifyDF_DeploySpecificInfo(::flatbuffers::Verifier &verifier, const void *obj, DF_DeploySpecificInfo type);
bool VerifyDF_DeploySpecificInfoVector(::flatbuffers::Verifier &verifier, const ::flatbuffers::Vector<::flatbuffers::Offset<void>> *values, const ::flatbuffers::Vector<uint8_t> *types);

struct DE_EnvironmentVariable FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DE_EnvironmentVariableBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_ENV = 4,
    VT_VALUE = 6
  };
  const ::flatbuffers::String *env() const {
    return GetPointer<const ::flatbuffers::String *>(VT_ENV);
  }
  const ::flatbuffers::String *value() const {
    return GetPointer<const ::flatbuffers::String *>(VT_VALUE);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_ENV) &&
           verifier.VerifyString(env()) &&
           VerifyOffset(verifier, VT_VALUE) &&
           verifier.VerifyString(value()) &&
           verifier.EndTable();
  }
};

struct DE_EnvironmentVariableBuilder {
  typedef DE_EnvironmentVariable Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_env(::flatbuffers::Offset<::flatbuffers::String> env) {
    fbb_.AddOffset(DE_EnvironmentVariable::VT_ENV, env);
  }
  void add_value(::flatbuffers::Offset<::flatbuffers::String> value) {
    fbb_.AddOffset(DE_EnvironmentVariable::VT_VALUE, value);
  }
  explicit DE_EnvironmentVariableBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DE_EnvironmentVariable> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DE_EnvironmentVariable>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DE_EnvironmentVariable> CreateDE_EnvironmentVariable(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::String> env = 0,
    ::flatbuffers::Offset<::flatbuffers::String> value = 0) {
  DE_EnvironmentVariableBuilder builder_(_fbb);
  builder_.add_value(value);
  builder_.add_env(env);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DE_EnvironmentVariable> CreateDE_EnvironmentVariableDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const char *env = nullptr,
    const char *value = nullptr) {
  auto env__ = env ? _fbb.CreateString(env) : 0;
  auto value__ = value ? _fbb.CreateString(value) : 0;
  return MECData::CreateDE_EnvironmentVariable(
      _fbb,
      env__,
      value__);
}

struct DE_ContainerPortMapping FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DE_ContainerPortMappingBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_HOST_PORT = 4,
    VT_CONTAINER_PORT = 6
  };
  uint16_t host_port() const {
    return GetField<uint16_t>(VT_HOST_PORT, 0);
  }
  uint16_t container_port() const {
    return GetField<uint16_t>(VT_CONTAINER_PORT, 0);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<uint16_t>(verifier, VT_HOST_PORT, 2) &&
           VerifyField<uint16_t>(verifier, VT_CONTAINER_PORT, 2) &&
           verifier.EndTable();
  }
};

struct DE_ContainerPortMappingBuilder {
  typedef DE_ContainerPortMapping Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_host_port(uint16_t host_port) {
    fbb_.AddElement<uint16_t>(DE_ContainerPortMapping::VT_HOST_PORT, host_port, 0);
  }
  void add_container_port(uint16_t container_port) {
    fbb_.AddElement<uint16_t>(DE_ContainerPortMapping::VT_CONTAINER_PORT, container_port, 0);
  }
  explicit DE_ContainerPortMappingBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DE_ContainerPortMapping> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DE_ContainerPortMapping>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DE_ContainerPortMapping> CreateDE_ContainerPortMapping(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    uint16_t host_port = 0,
    uint16_t container_port = 0) {
  DE_ContainerPortMappingBuilder builder_(_fbb);
  builder_.add_container_port(container_port);
  builder_.add_host_port(host_port);
  return builder_.Finish();
}

struct DE_ContainerVolumeMapping FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DE_ContainerVolumeMappingBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_PATH_ON_HOST = 4,
    VT_PATH_IN_CONTAINER = 6
  };
  const ::flatbuffers::String *path_on_host() const {
    return GetPointer<const ::flatbuffers::String *>(VT_PATH_ON_HOST);
  }
  const ::flatbuffers::String *path_in_container() const {
    return GetPointer<const ::flatbuffers::String *>(VT_PATH_IN_CONTAINER);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_PATH_ON_HOST) &&
           verifier.VerifyString(path_on_host()) &&
           VerifyOffset(verifier, VT_PATH_IN_CONTAINER) &&
           verifier.VerifyString(path_in_container()) &&
           verifier.EndTable();
  }
};

struct DE_ContainerVolumeMappingBuilder {
  typedef DE_ContainerVolumeMapping Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_path_on_host(::flatbuffers::Offset<::flatbuffers::String> path_on_host) {
    fbb_.AddOffset(DE_ContainerVolumeMapping::VT_PATH_ON_HOST, path_on_host);
  }
  void add_path_in_container(::flatbuffers::Offset<::flatbuffers::String> path_in_container) {
    fbb_.AddOffset(DE_ContainerVolumeMapping::VT_PATH_IN_CONTAINER, path_in_container);
  }
  explicit DE_ContainerVolumeMappingBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DE_ContainerVolumeMapping> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DE_ContainerVolumeMapping>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DE_ContainerVolumeMapping> CreateDE_ContainerVolumeMapping(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::String> path_on_host = 0,
    ::flatbuffers::Offset<::flatbuffers::String> path_in_container = 0) {
  DE_ContainerVolumeMappingBuilder builder_(_fbb);
  builder_.add_path_in_container(path_in_container);
  builder_.add_path_on_host(path_on_host);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DE_ContainerVolumeMapping> CreateDE_ContainerVolumeMappingDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const char *path_on_host = nullptr,
    const char *path_in_container = nullptr) {
  auto path_on_host__ = path_on_host ? _fbb.CreateString(path_on_host) : 0;
  auto path_in_container__ = path_in_container ? _fbb.CreateString(path_in_container) : 0;
  return MECData::CreateDE_ContainerVolumeMapping(
      _fbb,
      path_on_host__,
      path_in_container__);
}

struct DF_DockerModuleInfo FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_DockerModuleInfoBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_IMAGE_NAME = 4,
    VT_TAG = 6,
    VT_IMAGE_ID = 8,
    VT_CONTAINER_ID = 10,
    VT_STATE = 12,
    VT_STATUS = 14,
    VT_PORT_MAPPING = 16,
    VT_VOLUME_MAPPING = 18,
    VT_NETWORK_MODE = 20,
    VT_CMD = 22,
    VT_WORKING_DIR = 24,
    VT_IMAGE_SHA256SUM = 26
  };
  const ::flatbuffers::String *image_name() const {
    return GetPointer<const ::flatbuffers::String *>(VT_IMAGE_NAME);
  }
  const ::flatbuffers::String *tag() const {
    return GetPointer<const ::flatbuffers::String *>(VT_TAG);
  }
  const ::flatbuffers::String *image_id() const {
    return GetPointer<const ::flatbuffers::String *>(VT_IMAGE_ID);
  }
  const ::flatbuffers::String *container_id() const {
    return GetPointer<const ::flatbuffers::String *>(VT_CONTAINER_ID);
  }
  MECData::DE_ContainerStatus state() const {
    return static_cast<MECData::DE_ContainerStatus>(GetField<int8_t>(VT_STATE, 0));
  }
  const ::flatbuffers::String *status() const {
    return GetPointer<const ::flatbuffers::String *>(VT_STATUS);
  }
  const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DE_ContainerPortMapping>> *port_mapping() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DE_ContainerPortMapping>> *>(VT_PORT_MAPPING);
  }
  const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DE_ContainerVolumeMapping>> *volume_mapping() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DE_ContainerVolumeMapping>> *>(VT_VOLUME_MAPPING);
  }
  MECData::DE_NetworkMode network_mode() const {
    return static_cast<MECData::DE_NetworkMode>(GetField<int8_t>(VT_NETWORK_MODE, 0));
  }
  const ::flatbuffers::Vector<::flatbuffers::Offset<::flatbuffers::String>> *cmd() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<::flatbuffers::String>> *>(VT_CMD);
  }
  const ::flatbuffers::String *working_dir() const {
    return GetPointer<const ::flatbuffers::String *>(VT_WORKING_DIR);
  }
  const ::flatbuffers::String *image_sha256sum() const {
    return GetPointer<const ::flatbuffers::String *>(VT_IMAGE_SHA256SUM);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_IMAGE_NAME) &&
           verifier.VerifyString(image_name()) &&
           VerifyOffset(verifier, VT_TAG) &&
           verifier.VerifyString(tag()) &&
           VerifyOffset(verifier, VT_IMAGE_ID) &&
           verifier.VerifyString(image_id()) &&
           VerifyOffset(verifier, VT_CONTAINER_ID) &&
           verifier.VerifyString(container_id()) &&
           VerifyField<int8_t>(verifier, VT_STATE, 1) &&
           VerifyOffset(verifier, VT_STATUS) &&
           verifier.VerifyString(status()) &&
           VerifyOffset(verifier, VT_PORT_MAPPING) &&
           verifier.VerifyVector(port_mapping()) &&
           verifier.VerifyVectorOfTables(port_mapping()) &&
           VerifyOffset(verifier, VT_VOLUME_MAPPING) &&
           verifier.VerifyVector(volume_mapping()) &&
           verifier.VerifyVectorOfTables(volume_mapping()) &&
           VerifyField<int8_t>(verifier, VT_NETWORK_MODE, 1) &&
           VerifyOffset(verifier, VT_CMD) &&
           verifier.VerifyVector(cmd()) &&
           verifier.VerifyVectorOfStrings(cmd()) &&
           VerifyOffset(verifier, VT_WORKING_DIR) &&
           verifier.VerifyString(working_dir()) &&
           VerifyOffset(verifier, VT_IMAGE_SHA256SUM) &&
           verifier.VerifyString(image_sha256sum()) &&
           verifier.EndTable();
  }
};

struct DF_DockerModuleInfoBuilder {
  typedef DF_DockerModuleInfo Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_image_name(::flatbuffers::Offset<::flatbuffers::String> image_name) {
    fbb_.AddOffset(DF_DockerModuleInfo::VT_IMAGE_NAME, image_name);
  }
  void add_tag(::flatbuffers::Offset<::flatbuffers::String> tag) {
    fbb_.AddOffset(DF_DockerModuleInfo::VT_TAG, tag);
  }
  void add_image_id(::flatbuffers::Offset<::flatbuffers::String> image_id) {
    fbb_.AddOffset(DF_DockerModuleInfo::VT_IMAGE_ID, image_id);
  }
  void add_container_id(::flatbuffers::Offset<::flatbuffers::String> container_id) {
    fbb_.AddOffset(DF_DockerModuleInfo::VT_CONTAINER_ID, container_id);
  }
  void add_state(MECData::DE_ContainerStatus state) {
    fbb_.AddElement<int8_t>(DF_DockerModuleInfo::VT_STATE, static_cast<int8_t>(state), 0);
  }
  void add_status(::flatbuffers::Offset<::flatbuffers::String> status) {
    fbb_.AddOffset(DF_DockerModuleInfo::VT_STATUS, status);
  }
  void add_port_mapping(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DE_ContainerPortMapping>>> port_mapping) {
    fbb_.AddOffset(DF_DockerModuleInfo::VT_PORT_MAPPING, port_mapping);
  }
  void add_volume_mapping(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DE_ContainerVolumeMapping>>> volume_mapping) {
    fbb_.AddOffset(DF_DockerModuleInfo::VT_VOLUME_MAPPING, volume_mapping);
  }
  void add_network_mode(MECData::DE_NetworkMode network_mode) {
    fbb_.AddElement<int8_t>(DF_DockerModuleInfo::VT_NETWORK_MODE, static_cast<int8_t>(network_mode), 0);
  }
  void add_cmd(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<::flatbuffers::String>>> cmd) {
    fbb_.AddOffset(DF_DockerModuleInfo::VT_CMD, cmd);
  }
  void add_working_dir(::flatbuffers::Offset<::flatbuffers::String> working_dir) {
    fbb_.AddOffset(DF_DockerModuleInfo::VT_WORKING_DIR, working_dir);
  }
  void add_image_sha256sum(::flatbuffers::Offset<::flatbuffers::String> image_sha256sum) {
    fbb_.AddOffset(DF_DockerModuleInfo::VT_IMAGE_SHA256SUM, image_sha256sum);
  }
  explicit DF_DockerModuleInfoBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_DockerModuleInfo> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_DockerModuleInfo>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_DockerModuleInfo> CreateDF_DockerModuleInfo(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::String> image_name = 0,
    ::flatbuffers::Offset<::flatbuffers::String> tag = 0,
    ::flatbuffers::Offset<::flatbuffers::String> image_id = 0,
    ::flatbuffers::Offset<::flatbuffers::String> container_id = 0,
    MECData::DE_ContainerStatus state = MECData::DE_ContainerStatus_CREATED,
    ::flatbuffers::Offset<::flatbuffers::String> status = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DE_ContainerPortMapping>>> port_mapping = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DE_ContainerVolumeMapping>>> volume_mapping = 0,
    MECData::DE_NetworkMode network_mode = MECData::DE_NetworkMode_BRIDGE,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<::flatbuffers::String>>> cmd = 0,
    ::flatbuffers::Offset<::flatbuffers::String> working_dir = 0,
    ::flatbuffers::Offset<::flatbuffers::String> image_sha256sum = 0) {
  DF_DockerModuleInfoBuilder builder_(_fbb);
  builder_.add_image_sha256sum(image_sha256sum);
  builder_.add_working_dir(working_dir);
  builder_.add_cmd(cmd);
  builder_.add_volume_mapping(volume_mapping);
  builder_.add_port_mapping(port_mapping);
  builder_.add_status(status);
  builder_.add_container_id(container_id);
  builder_.add_image_id(image_id);
  builder_.add_tag(tag);
  builder_.add_image_name(image_name);
  builder_.add_network_mode(network_mode);
  builder_.add_state(state);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DF_DockerModuleInfo> CreateDF_DockerModuleInfoDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const char *image_name = nullptr,
    const char *tag = nullptr,
    const char *image_id = nullptr,
    const char *container_id = nullptr,
    MECData::DE_ContainerStatus state = MECData::DE_ContainerStatus_CREATED,
    const char *status = nullptr,
    const std::vector<::flatbuffers::Offset<MECData::DE_ContainerPortMapping>> *port_mapping = nullptr,
    const std::vector<::flatbuffers::Offset<MECData::DE_ContainerVolumeMapping>> *volume_mapping = nullptr,
    MECData::DE_NetworkMode network_mode = MECData::DE_NetworkMode_BRIDGE,
    const std::vector<::flatbuffers::Offset<::flatbuffers::String>> *cmd = nullptr,
    const char *working_dir = nullptr,
    const char *image_sha256sum = nullptr) {
  auto image_name__ = image_name ? _fbb.CreateString(image_name) : 0;
  auto tag__ = tag ? _fbb.CreateString(tag) : 0;
  auto image_id__ = image_id ? _fbb.CreateString(image_id) : 0;
  auto container_id__ = container_id ? _fbb.CreateString(container_id) : 0;
  auto status__ = status ? _fbb.CreateString(status) : 0;
  auto port_mapping__ = port_mapping ? _fbb.CreateVector<::flatbuffers::Offset<MECData::DE_ContainerPortMapping>>(*port_mapping) : 0;
  auto volume_mapping__ = volume_mapping ? _fbb.CreateVector<::flatbuffers::Offset<MECData::DE_ContainerVolumeMapping>>(*volume_mapping) : 0;
  auto cmd__ = cmd ? _fbb.CreateVector<::flatbuffers::Offset<::flatbuffers::String>>(*cmd) : 0;
  auto working_dir__ = working_dir ? _fbb.CreateString(working_dir) : 0;
  auto image_sha256sum__ = image_sha256sum ? _fbb.CreateString(image_sha256sum) : 0;
  return MECData::CreateDF_DockerModuleInfo(
      _fbb,
      image_name__,
      tag__,
      image_id__,
      container_id__,
      state,
      status__,
      port_mapping__,
      volume_mapping__,
      network_mode,
      cmd__,
      working_dir__,
      image_sha256sum__);
}

struct DF_SharedObjectModuleInfo FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_SharedObjectModuleInfoBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_SERVICE_HOST_PID = 4,
    VT_SO_FILENAME = 6,
    VT_SO_FILE_SHA256SUM = 8,
    VT_SO_DEPENDENCY_LIST = 10
  };
  uint64_t service_host_pid() const {
    return GetField<uint64_t>(VT_SERVICE_HOST_PID, 0);
  }
  const ::flatbuffers::String *so_filename() const {
    return GetPointer<const ::flatbuffers::String *>(VT_SO_FILENAME);
  }
  const ::flatbuffers::String *so_file_sha256sum() const {
    return GetPointer<const ::flatbuffers::String *>(VT_SO_FILE_SHA256SUM);
  }
  const ::flatbuffers::Vector<::flatbuffers::Offset<::flatbuffers::String>> *so_dependency_list() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<::flatbuffers::String>> *>(VT_SO_DEPENDENCY_LIST);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<uint64_t>(verifier, VT_SERVICE_HOST_PID, 8) &&
           VerifyOffset(verifier, VT_SO_FILENAME) &&
           verifier.VerifyString(so_filename()) &&
           VerifyOffset(verifier, VT_SO_FILE_SHA256SUM) &&
           verifier.VerifyString(so_file_sha256sum()) &&
           VerifyOffset(verifier, VT_SO_DEPENDENCY_LIST) &&
           verifier.VerifyVector(so_dependency_list()) &&
           verifier.VerifyVectorOfStrings(so_dependency_list()) &&
           verifier.EndTable();
  }
};

struct DF_SharedObjectModuleInfoBuilder {
  typedef DF_SharedObjectModuleInfo Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_service_host_pid(uint64_t service_host_pid) {
    fbb_.AddElement<uint64_t>(DF_SharedObjectModuleInfo::VT_SERVICE_HOST_PID, service_host_pid, 0);
  }
  void add_so_filename(::flatbuffers::Offset<::flatbuffers::String> so_filename) {
    fbb_.AddOffset(DF_SharedObjectModuleInfo::VT_SO_FILENAME, so_filename);
  }
  void add_so_file_sha256sum(::flatbuffers::Offset<::flatbuffers::String> so_file_sha256sum) {
    fbb_.AddOffset(DF_SharedObjectModuleInfo::VT_SO_FILE_SHA256SUM, so_file_sha256sum);
  }
  void add_so_dependency_list(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<::flatbuffers::String>>> so_dependency_list) {
    fbb_.AddOffset(DF_SharedObjectModuleInfo::VT_SO_DEPENDENCY_LIST, so_dependency_list);
  }
  explicit DF_SharedObjectModuleInfoBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_SharedObjectModuleInfo> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_SharedObjectModuleInfo>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_SharedObjectModuleInfo> CreateDF_SharedObjectModuleInfo(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    uint64_t service_host_pid = 0,
    ::flatbuffers::Offset<::flatbuffers::String> so_filename = 0,
    ::flatbuffers::Offset<::flatbuffers::String> so_file_sha256sum = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<::flatbuffers::String>>> so_dependency_list = 0) {
  DF_SharedObjectModuleInfoBuilder builder_(_fbb);
  builder_.add_service_host_pid(service_host_pid);
  builder_.add_so_dependency_list(so_dependency_list);
  builder_.add_so_file_sha256sum(so_file_sha256sum);
  builder_.add_so_filename(so_filename);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DF_SharedObjectModuleInfo> CreateDF_SharedObjectModuleInfoDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    uint64_t service_host_pid = 0,
    const char *so_filename = nullptr,
    const char *so_file_sha256sum = nullptr,
    const std::vector<::flatbuffers::Offset<::flatbuffers::String>> *so_dependency_list = nullptr) {
  auto so_filename__ = so_filename ? _fbb.CreateString(so_filename) : 0;
  auto so_file_sha256sum__ = so_file_sha256sum ? _fbb.CreateString(so_file_sha256sum) : 0;
  auto so_dependency_list__ = so_dependency_list ? _fbb.CreateVector<::flatbuffers::Offset<::flatbuffers::String>>(*so_dependency_list) : 0;
  return MECData::CreateDF_SharedObjectModuleInfo(
      _fbb,
      service_host_pid,
      so_filename__,
      so_file_sha256sum__,
      so_dependency_list__);
}

struct DF_StandaloneProgramModuleInfo FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_StandaloneProgramModuleInfoBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_WORKING_DIRECTORY = 4,
    VT_VENV_ACTIVATE = 6,
    VT_ENTRYPOINT = 8,
    VT_ENVIRONMENT_VARIABLES = 10
  };
  const ::flatbuffers::String *working_directory() const {
    return GetPointer<const ::flatbuffers::String *>(VT_WORKING_DIRECTORY);
  }
  const ::flatbuffers::String *venv_activate() const {
    return GetPointer<const ::flatbuffers::String *>(VT_VENV_ACTIVATE);
  }
  const ::flatbuffers::String *entrypoint() const {
    return GetPointer<const ::flatbuffers::String *>(VT_ENTRYPOINT);
  }
  const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DE_EnvironmentVariable>> *environment_variables() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DE_EnvironmentVariable>> *>(VT_ENVIRONMENT_VARIABLES);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_WORKING_DIRECTORY) &&
           verifier.VerifyString(working_directory()) &&
           VerifyOffset(verifier, VT_VENV_ACTIVATE) &&
           verifier.VerifyString(venv_activate()) &&
           VerifyOffset(verifier, VT_ENTRYPOINT) &&
           verifier.VerifyString(entrypoint()) &&
           VerifyOffset(verifier, VT_ENVIRONMENT_VARIABLES) &&
           verifier.VerifyVector(environment_variables()) &&
           verifier.VerifyVectorOfTables(environment_variables()) &&
           verifier.EndTable();
  }
};

struct DF_StandaloneProgramModuleInfoBuilder {
  typedef DF_StandaloneProgramModuleInfo Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_working_directory(::flatbuffers::Offset<::flatbuffers::String> working_directory) {
    fbb_.AddOffset(DF_StandaloneProgramModuleInfo::VT_WORKING_DIRECTORY, working_directory);
  }
  void add_venv_activate(::flatbuffers::Offset<::flatbuffers::String> venv_activate) {
    fbb_.AddOffset(DF_StandaloneProgramModuleInfo::VT_VENV_ACTIVATE, venv_activate);
  }
  void add_entrypoint(::flatbuffers::Offset<::flatbuffers::String> entrypoint) {
    fbb_.AddOffset(DF_StandaloneProgramModuleInfo::VT_ENTRYPOINT, entrypoint);
  }
  void add_environment_variables(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DE_EnvironmentVariable>>> environment_variables) {
    fbb_.AddOffset(DF_StandaloneProgramModuleInfo::VT_ENVIRONMENT_VARIABLES, environment_variables);
  }
  explicit DF_StandaloneProgramModuleInfoBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_StandaloneProgramModuleInfo> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_StandaloneProgramModuleInfo>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_StandaloneProgramModuleInfo> CreateDF_StandaloneProgramModuleInfo(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::String> working_directory = 0,
    ::flatbuffers::Offset<::flatbuffers::String> venv_activate = 0,
    ::flatbuffers::Offset<::flatbuffers::String> entrypoint = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DE_EnvironmentVariable>>> environment_variables = 0) {
  DF_StandaloneProgramModuleInfoBuilder builder_(_fbb);
  builder_.add_environment_variables(environment_variables);
  builder_.add_entrypoint(entrypoint);
  builder_.add_venv_activate(venv_activate);
  builder_.add_working_directory(working_directory);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DF_StandaloneProgramModuleInfo> CreateDF_StandaloneProgramModuleInfoDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const char *working_directory = nullptr,
    const char *venv_activate = nullptr,
    const char *entrypoint = nullptr,
    const std::vector<::flatbuffers::Offset<MECData::DE_EnvironmentVariable>> *environment_variables = nullptr) {
  auto working_directory__ = working_directory ? _fbb.CreateString(working_directory) : 0;
  auto venv_activate__ = venv_activate ? _fbb.CreateString(venv_activate) : 0;
  auto entrypoint__ = entrypoint ? _fbb.CreateString(entrypoint) : 0;
  auto environment_variables__ = environment_variables ? _fbb.CreateVector<::flatbuffers::Offset<MECData::DE_EnvironmentVariable>>(*environment_variables) : 0;
  return MECData::CreateDF_StandaloneProgramModuleInfo(
      _fbb,
      working_directory__,
      venv_activate__,
      entrypoint__,
      environment_variables__);
}

struct DF_NativeClassModuleInfo FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_NativeClassModuleInfoBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_CLASS_NAME = 4
  };
  const ::flatbuffers::String *class_name() const {
    return GetPointer<const ::flatbuffers::String *>(VT_CLASS_NAME);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_CLASS_NAME) &&
           verifier.VerifyString(class_name()) &&
           verifier.EndTable();
  }
};

struct DF_NativeClassModuleInfoBuilder {
  typedef DF_NativeClassModuleInfo Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_class_name(::flatbuffers::Offset<::flatbuffers::String> class_name) {
    fbb_.AddOffset(DF_NativeClassModuleInfo::VT_CLASS_NAME, class_name);
  }
  explicit DF_NativeClassModuleInfoBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_NativeClassModuleInfo> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_NativeClassModuleInfo>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_NativeClassModuleInfo> CreateDF_NativeClassModuleInfo(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::String> class_name = 0) {
  DF_NativeClassModuleInfoBuilder builder_(_fbb);
  builder_.add_class_name(class_name);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DF_NativeClassModuleInfo> CreateDF_NativeClassModuleInfoDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const char *class_name = nullptr) {
  auto class_name__ = class_name ? _fbb.CreateString(class_name) : 0;
  return MECData::CreateDF_NativeClassModuleInfo(
      _fbb,
      class_name__);
}

inline bool VerifyDF_DeploySpecificInfo(::flatbuffers::Verifier &verifier, const void *obj, DF_DeploySpecificInfo type) {
  switch (type) {
    case DF_DeploySpecificInfo_NONE: {
      return true;
    }
    case DF_DeploySpecificInfo_DF_NativeClassModuleInfo: {
      auto ptr = reinterpret_cast<const MECData::DF_NativeClassModuleInfo *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case DF_DeploySpecificInfo_DF_SharedObjectModuleInfo: {
      auto ptr = reinterpret_cast<const MECData::DF_SharedObjectModuleInfo *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case DF_DeploySpecificInfo_DF_DockerModuleInfo: {
      auto ptr = reinterpret_cast<const MECData::DF_DockerModuleInfo *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case DF_DeploySpecificInfo_DF_StandaloneProgramModuleInfo: {
      auto ptr = reinterpret_cast<const MECData::DF_StandaloneProgramModuleInfo *>(obj);
      return verifier.VerifyTable(ptr);
    }
    default: return true;
  }
}

inline bool VerifyDF_DeploySpecificInfoVector(::flatbuffers::Verifier &verifier, const ::flatbuffers::Vector<::flatbuffers::Offset<void>> *values, const ::flatbuffers::Vector<uint8_t> *types) {
  if (!values || !types) return !values && !types;
  if (values->size() != types->size()) return false;
  for (::flatbuffers::uoffset_t i = 0; i < values->size(); ++i) {
    if (!VerifyDF_DeploySpecificInfo(
        verifier,  values->Get(i), types->GetEnum<DF_DeploySpecificInfo>(i))) {
      return false;
    }
  }
  return true;
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_DEPLOYSPECIFICINFO_MECDATA_H_
