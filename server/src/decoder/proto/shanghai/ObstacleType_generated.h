// automatically generated by the <PERSON>Buffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_OBSTACLETYPE_MECDATA_H_
#define FLATBUFFERS_GENERATED_OBSTACLETYPE_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

namespace MECData {

enum DE_ObstacleType : uint8_t {
  DE_ObstacleType_unknown = 0,
  DE_ObstacleType_rockfall = 1,
  DE_ObstacleType_landslide = 2,
  DE_ObstacleType_animal_intrusion = 3,
  DE_ObstacleType_liquid_spill = 4,
  DE_ObstacleType_goods_scattered = 5,
  DE_ObstacleType_trafficcone = 6,
  DE_ObstacleType_safety_triangle = 7,
  DE_ObstacleType_traffic_roadblock = 8,
  DE_ObstacleType_inspection_shaft_without_cover = 9,
  DE_ObstacleType_unknown_fragments = 10,
  DE_ObstacleType_unknown_hard_object = 11,
  DE_ObstacleType_unknown_soft_object = 12,
  DE_ObstacleType_MIN = DE_ObstacleType_unknown,
  DE_ObstacleType_MAX = DE_ObstacleType_unknown_soft_object
};

inline const DE_ObstacleType (&EnumValuesDE_ObstacleType())[13] {
  static const DE_ObstacleType values[] = {
    DE_ObstacleType_unknown,
    DE_ObstacleType_rockfall,
    DE_ObstacleType_landslide,
    DE_ObstacleType_animal_intrusion,
    DE_ObstacleType_liquid_spill,
    DE_ObstacleType_goods_scattered,
    DE_ObstacleType_trafficcone,
    DE_ObstacleType_safety_triangle,
    DE_ObstacleType_traffic_roadblock,
    DE_ObstacleType_inspection_shaft_without_cover,
    DE_ObstacleType_unknown_fragments,
    DE_ObstacleType_unknown_hard_object,
    DE_ObstacleType_unknown_soft_object
  };
  return values;
}

inline const char * const *EnumNamesDE_ObstacleType() {
  static const char * const names[14] = {
    "unknown",
    "rockfall",
    "landslide",
    "animal_intrusion",
    "liquid_spill",
    "goods_scattered",
    "trafficcone",
    "safety_triangle",
    "traffic_roadblock",
    "inspection_shaft_without_cover",
    "unknown_fragments",
    "unknown_hard_object",
    "unknown_soft_object",
    nullptr
  };
  return names;
}

inline const char *EnumNameDE_ObstacleType(DE_ObstacleType e) {
  if (::flatbuffers::IsOutRange(e, DE_ObstacleType_unknown, DE_ObstacleType_unknown_soft_object)) return "";
  const size_t index = static_cast<size_t>(e);
  return EnumNamesDE_ObstacleType()[index];
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_OBSTACLETYPE_MECDATA_H_
