// automatically generated by the <PERSON><PERSON>uffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_MOVEMENTEX_MECDATA_H_
#define FLATBUFFERS_GENERATED_MOVEMENTEX_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

#include "Maneuver_generated.h"
#include "NodeReferenceID_generated.h"

namespace MECData {

struct DF_MovementEx;
struct DF_MovementExBuilder;

struct DF_MovementEx FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_MovementExBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_REMOTEINTERSECTION = 4,
    VT_PHASEID = 6,
    VT_TURNDIRECTION = 8,
    VT_EXT_ID = 10
  };
  const MECData::DF_NodeReferenceID *remoteIntersection() const {
    return GetPointer<const MECData::DF_NodeReferenceID *>(VT_REMOTEINTERSECTION);
  }
  uint8_t phaseId() const {
    return GetField<uint8_t>(VT_PHASEID, 0);
  }
  MECData::DE_Maneuver turnDirection() const {
    return static_cast<MECData::DE_Maneuver>(GetField<uint8_t>(VT_TURNDIRECTION, 0));
  }
  const ::flatbuffers::String *ext_id() const {
    return GetPointer<const ::flatbuffers::String *>(VT_EXT_ID);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffsetRequired(verifier, VT_REMOTEINTERSECTION) &&
           verifier.VerifyTable(remoteIntersection()) &&
           VerifyField<uint8_t>(verifier, VT_PHASEID, 1) &&
           VerifyField<uint8_t>(verifier, VT_TURNDIRECTION, 1) &&
           VerifyOffset(verifier, VT_EXT_ID) &&
           verifier.VerifyString(ext_id()) &&
           verifier.EndTable();
  }
};

struct DF_MovementExBuilder {
  typedef DF_MovementEx Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_remoteIntersection(::flatbuffers::Offset<MECData::DF_NodeReferenceID> remoteIntersection) {
    fbb_.AddOffset(DF_MovementEx::VT_REMOTEINTERSECTION, remoteIntersection);
  }
  void add_phaseId(uint8_t phaseId) {
    fbb_.AddElement<uint8_t>(DF_MovementEx::VT_PHASEID, phaseId, 0);
  }
  void add_turnDirection(MECData::DE_Maneuver turnDirection) {
    fbb_.AddElement<uint8_t>(DF_MovementEx::VT_TURNDIRECTION, static_cast<uint8_t>(turnDirection), 0);
  }
  void add_ext_id(::flatbuffers::Offset<::flatbuffers::String> ext_id) {
    fbb_.AddOffset(DF_MovementEx::VT_EXT_ID, ext_id);
  }
  explicit DF_MovementExBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_MovementEx> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_MovementEx>(end);
    fbb_.Required(o, DF_MovementEx::VT_REMOTEINTERSECTION);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_MovementEx> CreateDF_MovementEx(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<MECData::DF_NodeReferenceID> remoteIntersection = 0,
    uint8_t phaseId = 0,
    MECData::DE_Maneuver turnDirection = MECData::DE_Maneuver_maneuverStraight,
    ::flatbuffers::Offset<::flatbuffers::String> ext_id = 0) {
  DF_MovementExBuilder builder_(_fbb);
  builder_.add_ext_id(ext_id);
  builder_.add_remoteIntersection(remoteIntersection);
  builder_.add_turnDirection(turnDirection);
  builder_.add_phaseId(phaseId);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DF_MovementEx> CreateDF_MovementExDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<MECData::DF_NodeReferenceID> remoteIntersection = 0,
    uint8_t phaseId = 0,
    MECData::DE_Maneuver turnDirection = MECData::DE_Maneuver_maneuverStraight,
    const char *ext_id = nullptr) {
  auto ext_id__ = ext_id ? _fbb.CreateString(ext_id) : 0;
  return MECData::CreateDF_MovementEx(
      _fbb,
      remoteIntersection,
      phaseId,
      turnDirection,
      ext_id__);
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_MOVEMENTEX_MECDATA_H_
