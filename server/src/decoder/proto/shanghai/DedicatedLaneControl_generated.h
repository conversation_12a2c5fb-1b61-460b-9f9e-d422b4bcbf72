// automatically generated by the <PERSON><PERSON>uffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_DEDICATEDLANECONTROL_MECDATA_H_
#define FLATBUFFERS_GENERATED_DEDICATEDLANECONTROL_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

#include "DLScheme_generated.h"

namespace MECData {

struct MSG_DedicatedLaneControl;
struct MSG_DedicatedLaneControlBuilder;

struct MSG_DedicatedLaneControl FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef MSG_DedicatedLaneControlBuilder Builder;
  enum <PERSON>BuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_MOY = 4,
    VT_SEC_MARK = 6,
    VT_SCHEMES = 8,
    VT_MSG_ID = 10
  };
  uint32_t moy() const {
    return GetField<uint32_t>(VT_MOY, 4294967295);
  }
  uint16_t sec_mark() const {
    return GetField<uint16_t>(VT_SEC_MARK, 65535);
  }
  const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_DLScheme>> *schemes() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_DLScheme>> *>(VT_SCHEMES);
  }
  int64_t msg_id() const {
    return GetField<int64_t>(VT_MSG_ID, 0);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<uint32_t>(verifier, VT_MOY, 4) &&
           VerifyField<uint16_t>(verifier, VT_SEC_MARK, 2) &&
           VerifyOffsetRequired(verifier, VT_SCHEMES) &&
           verifier.VerifyVector(schemes()) &&
           verifier.VerifyVectorOfTables(schemes()) &&
           VerifyField<int64_t>(verifier, VT_MSG_ID, 8) &&
           verifier.EndTable();
  }
};

struct MSG_DedicatedLaneControlBuilder {
  typedef MSG_DedicatedLaneControl Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_moy(uint32_t moy) {
    fbb_.AddElement<uint32_t>(MSG_DedicatedLaneControl::VT_MOY, moy, 4294967295);
  }
  void add_sec_mark(uint16_t sec_mark) {
    fbb_.AddElement<uint16_t>(MSG_DedicatedLaneControl::VT_SEC_MARK, sec_mark, 65535);
  }
  void add_schemes(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_DLScheme>>> schemes) {
    fbb_.AddOffset(MSG_DedicatedLaneControl::VT_SCHEMES, schemes);
  }
  void add_msg_id(int64_t msg_id) {
    fbb_.AddElement<int64_t>(MSG_DedicatedLaneControl::VT_MSG_ID, msg_id, 0);
  }
  explicit MSG_DedicatedLaneControlBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<MSG_DedicatedLaneControl> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<MSG_DedicatedLaneControl>(end);
    fbb_.Required(o, MSG_DedicatedLaneControl::VT_SCHEMES);
    return o;
  }
};

inline ::flatbuffers::Offset<MSG_DedicatedLaneControl> CreateMSG_DedicatedLaneControl(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    uint32_t moy = 4294967295,
    uint16_t sec_mark = 65535,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_DLScheme>>> schemes = 0,
    int64_t msg_id = 0) {
  MSG_DedicatedLaneControlBuilder builder_(_fbb);
  builder_.add_msg_id(msg_id);
  builder_.add_schemes(schemes);
  builder_.add_moy(moy);
  builder_.add_sec_mark(sec_mark);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<MSG_DedicatedLaneControl> CreateMSG_DedicatedLaneControlDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    uint32_t moy = 4294967295,
    uint16_t sec_mark = 65535,
    const std::vector<::flatbuffers::Offset<MECData::DF_DLScheme>> *schemes = nullptr,
    int64_t msg_id = 0) {
  auto schemes__ = schemes ? _fbb.CreateVector<::flatbuffers::Offset<MECData::DF_DLScheme>>(*schemes) : 0;
  return MECData::CreateMSG_DedicatedLaneControl(
      _fbb,
      moy,
      sec_mark,
      schemes__,
      msg_id);
}

inline const MECData::MSG_DedicatedLaneControl *GetMSG_DedicatedLaneControl(const void *buf) {
  return ::flatbuffers::GetRoot<MECData::MSG_DedicatedLaneControl>(buf);
}

inline const MECData::MSG_DedicatedLaneControl *GetSizePrefixedMSG_DedicatedLaneControl(const void *buf) {
  return ::flatbuffers::GetSizePrefixedRoot<MECData::MSG_DedicatedLaneControl>(buf);
}

inline bool VerifyMSG_DedicatedLaneControlBuffer(
    ::flatbuffers::Verifier &verifier) {
  return verifier.VerifyBuffer<MECData::MSG_DedicatedLaneControl>(nullptr);
}

inline bool VerifySizePrefixedMSG_DedicatedLaneControlBuffer(
    ::flatbuffers::Verifier &verifier) {
  return verifier.VerifySizePrefixedBuffer<MECData::MSG_DedicatedLaneControl>(nullptr);
}

inline void FinishMSG_DedicatedLaneControlBuffer(
    ::flatbuffers::FlatBufferBuilder &fbb,
    ::flatbuffers::Offset<MECData::MSG_DedicatedLaneControl> root) {
  fbb.Finish(root);
}

inline void FinishSizePrefixedMSG_DedicatedLaneControlBuffer(
    ::flatbuffers::FlatBufferBuilder &fbb,
    ::flatbuffers::Offset<MECData::MSG_DedicatedLaneControl> root) {
  fbb.FinishSizePrefixed(root);
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_DEDICATEDLANECONTROL_MECDATA_H_
