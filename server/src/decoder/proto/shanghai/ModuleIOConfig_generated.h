// automatically generated by the <PERSON><PERSON>uffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_MODULEIOCONFIG_MECDATA_H_
#define FLATBUFFERS_GENERATED_MODULEIOCONFIG_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

namespace MECData {

struct DF_ModuleIOConfig;
struct DF_ModuleIOConfigBuilder;

struct DF_ModuleIOConfig FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_ModuleIOConfigBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_INPUT_TYPES = 4,
    VT_OUTPUT_TYPES = 6,
    VT_PRIORITY = 8
  };
  const ::flatbuffers::Vector<uint16_t> *input_types() const {
    return GetPointer<const ::flatbuffers::Vector<uint16_t> *>(VT_INPUT_TYPES);
  }
  const ::flatbuffers::Vector<uint16_t> *output_types() const {
    return GetPointer<const ::flatbuffers::Vector<uint16_t> *>(VT_OUTPUT_TYPES);
  }
  uint16_t priority() const {
    return GetField<uint16_t>(VT_PRIORITY, 0);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_INPUT_TYPES) &&
           verifier.VerifyVector(input_types()) &&
           VerifyOffset(verifier, VT_OUTPUT_TYPES) &&
           verifier.VerifyVector(output_types()) &&
           VerifyField<uint16_t>(verifier, VT_PRIORITY, 2) &&
           verifier.EndTable();
  }
};

struct DF_ModuleIOConfigBuilder {
  typedef DF_ModuleIOConfig Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_input_types(::flatbuffers::Offset<::flatbuffers::Vector<uint16_t>> input_types) {
    fbb_.AddOffset(DF_ModuleIOConfig::VT_INPUT_TYPES, input_types);
  }
  void add_output_types(::flatbuffers::Offset<::flatbuffers::Vector<uint16_t>> output_types) {
    fbb_.AddOffset(DF_ModuleIOConfig::VT_OUTPUT_TYPES, output_types);
  }
  void add_priority(uint16_t priority) {
    fbb_.AddElement<uint16_t>(DF_ModuleIOConfig::VT_PRIORITY, priority, 0);
  }
  explicit DF_ModuleIOConfigBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_ModuleIOConfig> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_ModuleIOConfig>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_ModuleIOConfig> CreateDF_ModuleIOConfig(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::Vector<uint16_t>> input_types = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<uint16_t>> output_types = 0,
    uint16_t priority = 0) {
  DF_ModuleIOConfigBuilder builder_(_fbb);
  builder_.add_output_types(output_types);
  builder_.add_input_types(input_types);
  builder_.add_priority(priority);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DF_ModuleIOConfig> CreateDF_ModuleIOConfigDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const std::vector<uint16_t> *input_types = nullptr,
    const std::vector<uint16_t> *output_types = nullptr,
    uint16_t priority = 0) {
  auto input_types__ = input_types ? _fbb.CreateVector<uint16_t>(*input_types) : 0;
  auto output_types__ = output_types ? _fbb.CreateVector<uint16_t>(*output_types) : 0;
  return MECData::CreateDF_ModuleIOConfig(
      _fbb,
      input_types__,
      output_types__,
      priority);
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_MODULEIOCONFIG_MECDATA_H_
