// automatically generated by the FlatBuffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_APPSTATUS_MECDATA_H_
#define FLATBUFFERS_GENERATED_APPSTATUS_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

#include "AppInfo_generated.h"

namespace MECData {

struct DF_AppInfoElement;
struct DF_AppInfoElementBuilder;

struct MSG_AppStatus;
struct MSG_AppStatusBuilder;

struct DF_AppInfoElement FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_AppInfoElementBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_APP_INFO = 4
  };
  const ::flatbuffers::Vector<uint8_t> *app_info() const {
    return GetPointer<const ::flatbuffers::Vector<uint8_t> *>(VT_APP_INFO);
  }
  const MECData::MSG_AppInfo *app_info_nested_root() const {
    const auto _f = app_info();
    return _f ? ::flatbuffers::GetRoot<MECData::MSG_AppInfo>(_f->Data())
              : nullptr;
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_APP_INFO) &&
           verifier.VerifyVector(app_info()) &&
           verifier.VerifyNestedFlatBuffer<MECData::MSG_AppInfo>(app_info(), nullptr) &&
           verifier.EndTable();
  }
};

struct DF_AppInfoElementBuilder {
  typedef DF_AppInfoElement Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_app_info(::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> app_info) {
    fbb_.AddOffset(DF_AppInfoElement::VT_APP_INFO, app_info);
  }
  explicit DF_AppInfoElementBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_AppInfoElement> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_AppInfoElement>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_AppInfoElement> CreateDF_AppInfoElement(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> app_info = 0) {
  DF_AppInfoElementBuilder builder_(_fbb);
  builder_.add_app_info(app_info);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DF_AppInfoElement> CreateDF_AppInfoElementDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const std::vector<uint8_t> *app_info = nullptr) {
  auto app_info__ = app_info ? _fbb.CreateVector<uint8_t>(*app_info) : 0;
  return MECData::CreateDF_AppInfoElement(
      _fbb,
      app_info__);
}

struct MSG_AppStatus FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef MSG_AppStatusBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_MEC_ID = 4,
    VT_APP_STATUS = 6,
    VT_MSG_ID = 8
  };
  const ::flatbuffers::String *mec_id() const {
    return GetPointer<const ::flatbuffers::String *>(VT_MEC_ID);
  }
  const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_AppInfoElement>> *app_status() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_AppInfoElement>> *>(VT_APP_STATUS);
  }
  int64_t msg_id() const {
    return GetField<int64_t>(VT_MSG_ID, 0);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_MEC_ID) &&
           verifier.VerifyString(mec_id()) &&
           VerifyOffset(verifier, VT_APP_STATUS) &&
           verifier.VerifyVector(app_status()) &&
           verifier.VerifyVectorOfTables(app_status()) &&
           VerifyField<int64_t>(verifier, VT_MSG_ID, 8) &&
           verifier.EndTable();
  }
};

struct MSG_AppStatusBuilder {
  typedef MSG_AppStatus Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_mec_id(::flatbuffers::Offset<::flatbuffers::String> mec_id) {
    fbb_.AddOffset(MSG_AppStatus::VT_MEC_ID, mec_id);
  }
  void add_app_status(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_AppInfoElement>>> app_status) {
    fbb_.AddOffset(MSG_AppStatus::VT_APP_STATUS, app_status);
  }
  void add_msg_id(int64_t msg_id) {
    fbb_.AddElement<int64_t>(MSG_AppStatus::VT_MSG_ID, msg_id, 0);
  }
  explicit MSG_AppStatusBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<MSG_AppStatus> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<MSG_AppStatus>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<MSG_AppStatus> CreateMSG_AppStatus(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::String> mec_id = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_AppInfoElement>>> app_status = 0,
    int64_t msg_id = 0) {
  MSG_AppStatusBuilder builder_(_fbb);
  builder_.add_msg_id(msg_id);
  builder_.add_app_status(app_status);
  builder_.add_mec_id(mec_id);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<MSG_AppStatus> CreateMSG_AppStatusDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const char *mec_id = nullptr,
    const std::vector<::flatbuffers::Offset<MECData::DF_AppInfoElement>> *app_status = nullptr,
    int64_t msg_id = 0) {
  auto mec_id__ = mec_id ? _fbb.CreateString(mec_id) : 0;
  auto app_status__ = app_status ? _fbb.CreateVector<::flatbuffers::Offset<MECData::DF_AppInfoElement>>(*app_status) : 0;
  return MECData::CreateMSG_AppStatus(
      _fbb,
      mec_id__,
      app_status__,
      msg_id);
}

inline const MECData::MSG_AppStatus *GetMSG_AppStatus(const void *buf) {
  return ::flatbuffers::GetRoot<MECData::MSG_AppStatus>(buf);
}

inline const MECData::MSG_AppStatus *GetSizePrefixedMSG_AppStatus(const void *buf) {
  return ::flatbuffers::GetSizePrefixedRoot<MECData::MSG_AppStatus>(buf);
}

inline bool VerifyMSG_AppStatusBuffer(
    ::flatbuffers::Verifier &verifier) {
  return verifier.VerifyBuffer<MECData::MSG_AppStatus>(nullptr);
}

inline bool VerifySizePrefixedMSG_AppStatusBuffer(
    ::flatbuffers::Verifier &verifier) {
  return verifier.VerifySizePrefixedBuffer<MECData::MSG_AppStatus>(nullptr);
}

inline void FinishMSG_AppStatusBuffer(
    ::flatbuffers::FlatBufferBuilder &fbb,
    ::flatbuffers::Offset<MECData::MSG_AppStatus> root) {
  fbb.Finish(root);
}

inline void FinishSizePrefixedMSG_AppStatusBuffer(
    ::flatbuffers::FlatBufferBuilder &fbb,
    ::flatbuffers::Offset<MECData::MSG_AppStatus> root) {
  fbb.FinishSizePrefixed(root);
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_APPSTATUS_MECDATA_H_
