// automatically generated by the <PERSON>Buffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_LANEATTRIBUTESSIDEWALK_MECDATA_H_
#define FLATBUFFERS_GENERATED_LANEATTRIBUTESSIDEWALK_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

namespace MECData {

struct DF_LaneAttributesSidewalk;
struct DF_LaneAttributesSidewalkBuilder;

struct DF_LaneAttributesSidewalk FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_LaneAttributesSidewalkBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_SIDEWALK = 4
  };
  int8_t sidewalk() const {
    return GetField<int8_t>(VT_SIDEWALK, 0);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<int8_t>(verifier, VT_SIDEWALK, 1) &&
           verifier.EndTable();
  }
};

struct DF_LaneAttributesSidewalkBuilder {
  typedef DF_LaneAttributesSidewalk Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_sidewalk(int8_t sidewalk) {
    fbb_.AddElement<int8_t>(DF_LaneAttributesSidewalk::VT_SIDEWALK, sidewalk, 0);
  }
  explicit DF_LaneAttributesSidewalkBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_LaneAttributesSidewalk> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_LaneAttributesSidewalk>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_LaneAttributesSidewalk> CreateDF_LaneAttributesSidewalk(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    int8_t sidewalk = 0) {
  DF_LaneAttributesSidewalkBuilder builder_(_fbb);
  builder_.add_sidewalk(sidewalk);
  return builder_.Finish();
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_LANEATTRIBUTESSIDEWALK_MECDATA_H_
