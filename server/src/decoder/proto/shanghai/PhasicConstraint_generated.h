// automatically generated by the <PERSON><PERSON>uffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_PHASICCONSTRAINT_MECDATA_H_
#define FLATBUFFERS_GENERATED_PHASICCONSTRAINT_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

namespace MECData {

struct DF_PhasicConstraint;
struct DF_PhasicConstraintBuilder;

struct DF_PhasicConstraint FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_PhasicConstraintBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_ID = 4,
    VT_EXPECTED_ORDER = 6,
    VT_SCAT_NO = 8,
    VT_MIN_GREEN = 10,
    VT_MAX_GREEN = 12
  };
  int32_t id() const {
    return GetField<int32_t>(VT_ID, 0);
  }
  uint8_t expected_order() const {
    return GetField<uint8_t>(VT_EXPECTED_ORDER, 0);
  }
  const ::flatbuffers::String *scat_no() const {
    return GetPointer<const ::flatbuffers::String *>(VT_SCAT_NO);
  }
  int16_t min_green() const {
    return GetField<int16_t>(VT_MIN_GREEN, 0);
  }
  int16_t max_green() const {
    return GetField<int16_t>(VT_MAX_GREEN, 0);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<int32_t>(verifier, VT_ID, 4) &&
           VerifyField<uint8_t>(verifier, VT_EXPECTED_ORDER, 1) &&
           VerifyOffset(verifier, VT_SCAT_NO) &&
           verifier.VerifyString(scat_no()) &&
           VerifyField<int16_t>(verifier, VT_MIN_GREEN, 2) &&
           VerifyField<int16_t>(verifier, VT_MAX_GREEN, 2) &&
           verifier.EndTable();
  }
};

struct DF_PhasicConstraintBuilder {
  typedef DF_PhasicConstraint Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_id(int32_t id) {
    fbb_.AddElement<int32_t>(DF_PhasicConstraint::VT_ID, id, 0);
  }
  void add_expected_order(uint8_t expected_order) {
    fbb_.AddElement<uint8_t>(DF_PhasicConstraint::VT_EXPECTED_ORDER, expected_order, 0);
  }
  void add_scat_no(::flatbuffers::Offset<::flatbuffers::String> scat_no) {
    fbb_.AddOffset(DF_PhasicConstraint::VT_SCAT_NO, scat_no);
  }
  void add_min_green(int16_t min_green) {
    fbb_.AddElement<int16_t>(DF_PhasicConstraint::VT_MIN_GREEN, min_green, 0);
  }
  void add_max_green(int16_t max_green) {
    fbb_.AddElement<int16_t>(DF_PhasicConstraint::VT_MAX_GREEN, max_green, 0);
  }
  explicit DF_PhasicConstraintBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_PhasicConstraint> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_PhasicConstraint>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_PhasicConstraint> CreateDF_PhasicConstraint(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    int32_t id = 0,
    uint8_t expected_order = 0,
    ::flatbuffers::Offset<::flatbuffers::String> scat_no = 0,
    int16_t min_green = 0,
    int16_t max_green = 0) {
  DF_PhasicConstraintBuilder builder_(_fbb);
  builder_.add_scat_no(scat_no);
  builder_.add_id(id);
  builder_.add_max_green(max_green);
  builder_.add_min_green(min_green);
  builder_.add_expected_order(expected_order);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DF_PhasicConstraint> CreateDF_PhasicConstraintDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    int32_t id = 0,
    uint8_t expected_order = 0,
    const char *scat_no = nullptr,
    int16_t min_green = 0,
    int16_t max_green = 0) {
  auto scat_no__ = scat_no ? _fbb.CreateString(scat_no) : 0;
  return MECData::CreateDF_PhasicConstraint(
      _fbb,
      id,
      expected_order,
      scat_no__,
      min_green,
      max_green);
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_PHASICCONSTRAINT_MECDATA_H_
