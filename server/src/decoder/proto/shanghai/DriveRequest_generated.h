// automatically generated by the <PERSON><PERSON>uffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_DRIVEREQUEST_MECDATA_H_
#define FLATBUFFERS_GENERATED_DRIVEREQUEST_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

#include "ReqInfo_generated.h"
#include "ReqStatus_generated.h"

namespace MECData {

struct DF_DriveRequest;
struct DF_DriveRequestBuilder;

struct DF_DriveRequest FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_DriveRequestBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_REQID = 4,
    VT_STATUS = 6,
    VT_REQPRIORITY = 8,
    VT_TARGETVEH = 10,
    VT_TARGETRSU = 12,
    VT_INFO_TYPE = 14,
    VT_INFO = 16,
    VT_LIFETIME = 18
  };
  uint8_t reqID() const {
    return GetField<uint8_t>(VT_REQID, 255);
  }
  MECData::DE_ReqStatus status() const {
    return static_cast<MECData::DE_ReqStatus>(GetField<int8_t>(VT_STATUS, 0));
  }
  const ::flatbuffers::String *reqPriority() const {
    return GetPointer<const ::flatbuffers::String *>(VT_REQPRIORITY);
  }
  const ::flatbuffers::String *targetVeh() const {
    return GetPointer<const ::flatbuffers::String *>(VT_TARGETVEH);
  }
  const ::flatbuffers::String *targetRSU() const {
    return GetPointer<const ::flatbuffers::String *>(VT_TARGETRSU);
  }
  MECData::DF_ReqInfo info_type() const {
    return static_cast<MECData::DF_ReqInfo>(GetField<uint8_t>(VT_INFO_TYPE, 0));
  }
  const void *info() const {
    return GetPointer<const void *>(VT_INFO);
  }
  template<typename T> const T *info_as() const;
  const MECData::DF_ReqLaneChange *info_as_DF_ReqLaneChange() const {
    return info_type() == MECData::DF_ReqInfo_DF_ReqLaneChange ? static_cast<const MECData::DF_ReqLaneChange *>(info()) : nullptr;
  }
  const MECData::DF_ReqClearTheWay *info_as_DF_ReqClearTheWay() const {
    return info_type() == MECData::DF_ReqInfo_DF_ReqClearTheWay ? static_cast<const MECData::DF_ReqClearTheWay *>(info()) : nullptr;
  }
  const MECData::DF_ReqSignalPriority *info_as_DF_ReqSignalPriority() const {
    return info_type() == MECData::DF_ReqInfo_DF_ReqSignalPriority ? static_cast<const MECData::DF_ReqSignalPriority *>(info()) : nullptr;
  }
  const MECData::DF_ReqSensorSharing *info_as_DF_ReqSensorSharing() const {
    return info_type() == MECData::DF_ReqInfo_DF_ReqSensorSharing ? static_cast<const MECData::DF_ReqSensorSharing *>(info()) : nullptr;
  }
  const MECData::DF_ReqParkingArea *info_as_DF_ReqParkingArea() const {
    return info_type() == MECData::DF_ReqInfo_DF_ReqParkingArea ? static_cast<const MECData::DF_ReqParkingArea *>(info()) : nullptr;
  }
  uint16_t lifeTime() const {
    return GetField<uint16_t>(VT_LIFETIME, 0);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<uint8_t>(verifier, VT_REQID, 1) &&
           VerifyField<int8_t>(verifier, VT_STATUS, 1) &&
           VerifyOffset(verifier, VT_REQPRIORITY) &&
           verifier.VerifyString(reqPriority()) &&
           VerifyOffset(verifier, VT_TARGETVEH) &&
           verifier.VerifyString(targetVeh()) &&
           VerifyOffset(verifier, VT_TARGETRSU) &&
           verifier.VerifyString(targetRSU()) &&
           VerifyField<uint8_t>(verifier, VT_INFO_TYPE, 1) &&
           VerifyOffset(verifier, VT_INFO) &&
           VerifyDF_ReqInfo(verifier, info(), info_type()) &&
           VerifyField<uint16_t>(verifier, VT_LIFETIME, 2) &&
           verifier.EndTable();
  }
};

template<> inline const MECData::DF_ReqLaneChange *DF_DriveRequest::info_as<MECData::DF_ReqLaneChange>() const {
  return info_as_DF_ReqLaneChange();
}

template<> inline const MECData::DF_ReqClearTheWay *DF_DriveRequest::info_as<MECData::DF_ReqClearTheWay>() const {
  return info_as_DF_ReqClearTheWay();
}

template<> inline const MECData::DF_ReqSignalPriority *DF_DriveRequest::info_as<MECData::DF_ReqSignalPriority>() const {
  return info_as_DF_ReqSignalPriority();
}

template<> inline const MECData::DF_ReqSensorSharing *DF_DriveRequest::info_as<MECData::DF_ReqSensorSharing>() const {
  return info_as_DF_ReqSensorSharing();
}

template<> inline const MECData::DF_ReqParkingArea *DF_DriveRequest::info_as<MECData::DF_ReqParkingArea>() const {
  return info_as_DF_ReqParkingArea();
}

struct DF_DriveRequestBuilder {
  typedef DF_DriveRequest Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_reqID(uint8_t reqID) {
    fbb_.AddElement<uint8_t>(DF_DriveRequest::VT_REQID, reqID, 255);
  }
  void add_status(MECData::DE_ReqStatus status) {
    fbb_.AddElement<int8_t>(DF_DriveRequest::VT_STATUS, static_cast<int8_t>(status), 0);
  }
  void add_reqPriority(::flatbuffers::Offset<::flatbuffers::String> reqPriority) {
    fbb_.AddOffset(DF_DriveRequest::VT_REQPRIORITY, reqPriority);
  }
  void add_targetVeh(::flatbuffers::Offset<::flatbuffers::String> targetVeh) {
    fbb_.AddOffset(DF_DriveRequest::VT_TARGETVEH, targetVeh);
  }
  void add_targetRSU(::flatbuffers::Offset<::flatbuffers::String> targetRSU) {
    fbb_.AddOffset(DF_DriveRequest::VT_TARGETRSU, targetRSU);
  }
  void add_info_type(MECData::DF_ReqInfo info_type) {
    fbb_.AddElement<uint8_t>(DF_DriveRequest::VT_INFO_TYPE, static_cast<uint8_t>(info_type), 0);
  }
  void add_info(::flatbuffers::Offset<void> info) {
    fbb_.AddOffset(DF_DriveRequest::VT_INFO, info);
  }
  void add_lifeTime(uint16_t lifeTime) {
    fbb_.AddElement<uint16_t>(DF_DriveRequest::VT_LIFETIME, lifeTime, 0);
  }
  explicit DF_DriveRequestBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_DriveRequest> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_DriveRequest>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_DriveRequest> CreateDF_DriveRequest(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    uint8_t reqID = 255,
    MECData::DE_ReqStatus status = MECData::DE_ReqStatus_unknown,
    ::flatbuffers::Offset<::flatbuffers::String> reqPriority = 0,
    ::flatbuffers::Offset<::flatbuffers::String> targetVeh = 0,
    ::flatbuffers::Offset<::flatbuffers::String> targetRSU = 0,
    MECData::DF_ReqInfo info_type = MECData::DF_ReqInfo_NONE,
    ::flatbuffers::Offset<void> info = 0,
    uint16_t lifeTime = 0) {
  DF_DriveRequestBuilder builder_(_fbb);
  builder_.add_info(info);
  builder_.add_targetRSU(targetRSU);
  builder_.add_targetVeh(targetVeh);
  builder_.add_reqPriority(reqPriority);
  builder_.add_lifeTime(lifeTime);
  builder_.add_info_type(info_type);
  builder_.add_status(status);
  builder_.add_reqID(reqID);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DF_DriveRequest> CreateDF_DriveRequestDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    uint8_t reqID = 255,
    MECData::DE_ReqStatus status = MECData::DE_ReqStatus_unknown,
    const char *reqPriority = nullptr,
    const char *targetVeh = nullptr,
    const char *targetRSU = nullptr,
    MECData::DF_ReqInfo info_type = MECData::DF_ReqInfo_NONE,
    ::flatbuffers::Offset<void> info = 0,
    uint16_t lifeTime = 0) {
  auto reqPriority__ = reqPriority ? _fbb.CreateString(reqPriority) : 0;
  auto targetVeh__ = targetVeh ? _fbb.CreateString(targetVeh) : 0;
  auto targetRSU__ = targetRSU ? _fbb.CreateString(targetRSU) : 0;
  return MECData::CreateDF_DriveRequest(
      _fbb,
      reqID,
      status,
      reqPriority__,
      targetVeh__,
      targetRSU__,
      info_type,
      info,
      lifeTime);
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_DRIVEREQUEST_MECDATA_H_
