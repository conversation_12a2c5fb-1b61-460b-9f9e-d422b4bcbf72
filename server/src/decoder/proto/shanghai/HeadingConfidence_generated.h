// automatically generated by the FlatBuffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_HEADINGCONFIDENCE_MECDATA_H_
#define FLATBUFFERS_GENERATED_HEADINGCONFIDENCE_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

namespace MECData {

enum DE_HeadingConfidence : int8_t {
  DE_HeadingConfidence_unavailable = 0,
  DE_HeadingConfidence_prec10deg = 1,
  DE_HeadingConfidence_prec05deg = 2,
  DE_HeadingConfidence_prec01deg = 3,
  DE_HeadingConfidence_prec_1deg = 4,
  DE_HeadingConfidence_prec0_05deg = 5,
  DE_HeadingConfidence_prec0_01deg = 6,
  DE_HeadingConfidence_prec0_0125deg = 7,
  DE_HeadingConfidence_MIN = DE_HeadingConfidence_unavailable,
  DE_HeadingConfidence_MAX = DE_HeadingConfidence_prec0_0125deg
};

inline const DE_HeadingConfidence (&EnumValuesDE_HeadingConfidence())[8] {
  static const DE_HeadingConfidence values[] = {
    DE_HeadingConfidence_unavailable,
    DE_HeadingConfidence_prec10deg,
    DE_HeadingConfidence_prec05deg,
    DE_HeadingConfidence_prec01deg,
    DE_HeadingConfidence_prec_1deg,
    DE_HeadingConfidence_prec0_05deg,
    DE_HeadingConfidence_prec0_01deg,
    DE_HeadingConfidence_prec0_0125deg
  };
  return values;
}

inline const char * const *EnumNamesDE_HeadingConfidence() {
  static const char * const names[9] = {
    "unavailable",
    "prec10deg",
    "prec05deg",
    "prec01deg",
    "prec_1deg",
    "prec0_05deg",
    "prec0_01deg",
    "prec0_0125deg",
    nullptr
  };
  return names;
}

inline const char *EnumNameDE_HeadingConfidence(DE_HeadingConfidence e) {
  if (::flatbuffers::IsOutRange(e, DE_HeadingConfidence_unavailable, DE_HeadingConfidence_prec0_0125deg)) return "";
  const size_t index = static_cast<size_t>(e);
  return EnumNamesDE_HeadingConfidence()[index];
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_HEADINGCONFIDENCE_MECDATA_H_
