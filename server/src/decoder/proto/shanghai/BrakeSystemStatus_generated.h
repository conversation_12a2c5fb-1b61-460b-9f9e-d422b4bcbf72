// automatically generated by the <PERSON><PERSON>uffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_BRAKESYSTEMSTATUS_MECDATA_H_
#define FLATBUFFERS_GENERATED_BRAKESYSTEMSTATUS_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

#include "AntiLockBrakeStatus_generated.h"
#include "AuxiliaryBrakeStatus_generated.h"
#include "BrakeAppliedStatus_generated.h"
#include "BrakeBoostApplied_generated.h"
#include "BrakePedalStatus_generated.h"
#include "StabilityControlStatus_generated.h"
#include "TractionControlStatus_generated.h"

namespace MECData {

struct DF_BrakeSystemStatus;
struct DF_BrakeSystemStatusBuilder;

struct DF_BrakeSystemStatus FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_BrakeSystemStatusBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_BRAKEPADEL = 4,
    VT_WHEELBRAKES = 6,
    VT_TRACTION = 8,
    VT_ABS = 10,
    VT_SCS = 12,
    VT_BRAKEBOOST = 14,
    VT_AUXBRAKES = 16
  };
  MECData::DE_BrakePedalStatus brakePadel() const {
    return static_cast<MECData::DE_BrakePedalStatus>(GetField<int8_t>(VT_BRAKEPADEL, 0));
  }
  const MECData::DE_BrakeAppliedStatus *wheelBrakes() const {
    return GetPointer<const MECData::DE_BrakeAppliedStatus *>(VT_WHEELBRAKES);
  }
  MECData::DE_TractionControlStatus traction() const {
    return static_cast<MECData::DE_TractionControlStatus>(GetField<int8_t>(VT_TRACTION, 0));
  }
  MECData::DE_AntiLockBrakeStatus abs() const {
    return static_cast<MECData::DE_AntiLockBrakeStatus>(GetField<int8_t>(VT_ABS, 0));
  }
  MECData::DE_StabilityControlStatus scs() const {
    return static_cast<MECData::DE_StabilityControlStatus>(GetField<int8_t>(VT_SCS, 0));
  }
  MECData::DE_BrakeBoostApplied brakeBoost() const {
    return static_cast<MECData::DE_BrakeBoostApplied>(GetField<int8_t>(VT_BRAKEBOOST, 0));
  }
  MECData::DE_AuxiliaryBrakeStatus auxBrakes() const {
    return static_cast<MECData::DE_AuxiliaryBrakeStatus>(GetField<int8_t>(VT_AUXBRAKES, 0));
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<int8_t>(verifier, VT_BRAKEPADEL, 1) &&
           VerifyOffset(verifier, VT_WHEELBRAKES) &&
           verifier.VerifyTable(wheelBrakes()) &&
           VerifyField<int8_t>(verifier, VT_TRACTION, 1) &&
           VerifyField<int8_t>(verifier, VT_ABS, 1) &&
           VerifyField<int8_t>(verifier, VT_SCS, 1) &&
           VerifyField<int8_t>(verifier, VT_BRAKEBOOST, 1) &&
           VerifyField<int8_t>(verifier, VT_AUXBRAKES, 1) &&
           verifier.EndTable();
  }
};

struct DF_BrakeSystemStatusBuilder {
  typedef DF_BrakeSystemStatus Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_brakePadel(MECData::DE_BrakePedalStatus brakePadel) {
    fbb_.AddElement<int8_t>(DF_BrakeSystemStatus::VT_BRAKEPADEL, static_cast<int8_t>(brakePadel), 0);
  }
  void add_wheelBrakes(::flatbuffers::Offset<MECData::DE_BrakeAppliedStatus> wheelBrakes) {
    fbb_.AddOffset(DF_BrakeSystemStatus::VT_WHEELBRAKES, wheelBrakes);
  }
  void add_traction(MECData::DE_TractionControlStatus traction) {
    fbb_.AddElement<int8_t>(DF_BrakeSystemStatus::VT_TRACTION, static_cast<int8_t>(traction), 0);
  }
  void add_abs(MECData::DE_AntiLockBrakeStatus abs) {
    fbb_.AddElement<int8_t>(DF_BrakeSystemStatus::VT_ABS, static_cast<int8_t>(abs), 0);
  }
  void add_scs(MECData::DE_StabilityControlStatus scs) {
    fbb_.AddElement<int8_t>(DF_BrakeSystemStatus::VT_SCS, static_cast<int8_t>(scs), 0);
  }
  void add_brakeBoost(MECData::DE_BrakeBoostApplied brakeBoost) {
    fbb_.AddElement<int8_t>(DF_BrakeSystemStatus::VT_BRAKEBOOST, static_cast<int8_t>(brakeBoost), 0);
  }
  void add_auxBrakes(MECData::DE_AuxiliaryBrakeStatus auxBrakes) {
    fbb_.AddElement<int8_t>(DF_BrakeSystemStatus::VT_AUXBRAKES, static_cast<int8_t>(auxBrakes), 0);
  }
  explicit DF_BrakeSystemStatusBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_BrakeSystemStatus> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_BrakeSystemStatus>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_BrakeSystemStatus> CreateDF_BrakeSystemStatus(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    MECData::DE_BrakePedalStatus brakePadel = MECData::DE_BrakePedalStatus_unavailable,
    ::flatbuffers::Offset<MECData::DE_BrakeAppliedStatus> wheelBrakes = 0,
    MECData::DE_TractionControlStatus traction = MECData::DE_TractionControlStatus_unavailable,
    MECData::DE_AntiLockBrakeStatus abs = MECData::DE_AntiLockBrakeStatus_unavailable,
    MECData::DE_StabilityControlStatus scs = MECData::DE_StabilityControlStatus_unavailable,
    MECData::DE_BrakeBoostApplied brakeBoost = MECData::DE_BrakeBoostApplied_unavailable,
    MECData::DE_AuxiliaryBrakeStatus auxBrakes = MECData::DE_AuxiliaryBrakeStatus_unavailable) {
  DF_BrakeSystemStatusBuilder builder_(_fbb);
  builder_.add_wheelBrakes(wheelBrakes);
  builder_.add_auxBrakes(auxBrakes);
  builder_.add_brakeBoost(brakeBoost);
  builder_.add_scs(scs);
  builder_.add_abs(abs);
  builder_.add_traction(traction);
  builder_.add_brakePadel(brakePadel);
  return builder_.Finish();
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_BRAKESYSTEMSTATUS_MECDATA_H_
