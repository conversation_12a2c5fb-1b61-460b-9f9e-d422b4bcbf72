// automatically generated by the FlatBuffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_DEVICESPECIFICINFO_MECDATA_H_
#define FLATBUFFERS_GENERATED_DEVICESPECIFICINFO_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

#include "CommunicationProtocol_generated.h"
#include "Direction8_generated.h"
#include "NodeReferenceID_generated.h"
#include "Position3D_generated.h"
#include "SSLCertificate_generated.h"
#include "SignalControlMode_generated.h"
#include "SignalControllerConfig_generated.h"

namespace MECData {

struct DF_CamExternalParameters;
struct DF_CamExternalParametersBuilder;

struct DF_CameraModuleInfo;
struct DF_CameraModuleInfoBuilder;

struct DF_MMWRadarModuleInfo;
struct DF_MMWRadarModuleInfoBuilder;

struct DE_TrafficSignalSupportedCommand;
struct DE_TrafficSignalSupportedCommandBuilder;

struct DF_TrafficSignalStatusInfo;
struct DF_TrafficSignalStatusInfoBuilder;

struct DF_TrafficSignalModuleInfo;
struct DF_TrafficSignalModuleInfoBuilder;

struct DF_V2XMsgConfig;
struct DF_V2XMsgConfigBuilder;

struct DF_RSUBroadcastConfig;
struct DF_RSUBroadcastConfigBuilder;

struct DF_RSUStatus;
struct DF_RSUStatusBuilder;

struct DF_RSUModuleInfo;
struct DF_RSUModuleInfoBuilder;

struct DF_CloudInfo;
struct DF_CloudInfoBuilder;

struct DF_LidarModuleInfo;
struct DF_LidarModuleInfoBuilder;

enum DE_V2XMessageStandard : int8_t {
  DE_V2XMessageStandard_T_CSAE_53_2020 = 0,
  DE_V2XMessageStandard_T_CSAE_157_2020 = 1,
  DE_V2XMessageStandard_MIN = DE_V2XMessageStandard_T_CSAE_53_2020,
  DE_V2XMessageStandard_MAX = DE_V2XMessageStandard_T_CSAE_157_2020
};

inline const DE_V2XMessageStandard (&EnumValuesDE_V2XMessageStandard())[2] {
  static const DE_V2XMessageStandard values[] = {
    DE_V2XMessageStandard_T_CSAE_53_2020,
    DE_V2XMessageStandard_T_CSAE_157_2020
  };
  return values;
}

inline const char * const *EnumNamesDE_V2XMessageStandard() {
  static const char * const names[3] = {
    "T_CSAE_53_2020",
    "T_CSAE_157_2020",
    nullptr
  };
  return names;
}

inline const char *EnumNameDE_V2XMessageStandard(DE_V2XMessageStandard e) {
  if (::flatbuffers::IsOutRange(e, DE_V2XMessageStandard_T_CSAE_53_2020, DE_V2XMessageStandard_T_CSAE_157_2020)) return "";
  const size_t index = static_cast<size_t>(e);
  return EnumNamesDE_V2XMessageStandard()[index];
}

enum DE_V2XMsgType : uint16_t {
  DE_V2XMsgType_UNKNOWN = 0,
  DE_V2XMsgType_BSM = 8705,
  DE_V2XMsgType_RSM = 8706,
  DE_V2XMsgType_MAP = 8707,
  DE_V2XMsgType_SPAT = 8708,
  DE_V2XMsgType_RSI_STATIC = 8709,
  DE_V2XMsgType_RSI_HALF_DYNAMIC = 8710,
  DE_V2XMsgType_RSI_DYNAMIC = 8711,
  DE_V2XMsgType_RSC = 8712,
  DE_V2XMsgType_VIR = 8713,
  DE_V2XMsgType_PAM = 8714,
  DE_V2XMsgType_CLPMM = 8715,
  DE_V2XMsgType_SSM = 8716,
  DE_V2XMsgType_PSM = 8717,
  DE_V2XMsgType_VPM = 8718,
  DE_V2XMsgType_TEST = 8959,
  DE_V2XMsgType_MIN = DE_V2XMsgType_UNKNOWN,
  DE_V2XMsgType_MAX = DE_V2XMsgType_TEST
};

inline const DE_V2XMsgType (&EnumValuesDE_V2XMsgType())[16] {
  static const DE_V2XMsgType values[] = {
    DE_V2XMsgType_UNKNOWN,
    DE_V2XMsgType_BSM,
    DE_V2XMsgType_RSM,
    DE_V2XMsgType_MAP,
    DE_V2XMsgType_SPAT,
    DE_V2XMsgType_RSI_STATIC,
    DE_V2XMsgType_RSI_HALF_DYNAMIC,
    DE_V2XMsgType_RSI_DYNAMIC,
    DE_V2XMsgType_RSC,
    DE_V2XMsgType_VIR,
    DE_V2XMsgType_PAM,
    DE_V2XMsgType_CLPMM,
    DE_V2XMsgType_SSM,
    DE_V2XMsgType_PSM,
    DE_V2XMsgType_VPM,
    DE_V2XMsgType_TEST
  };
  return values;
}

inline const char *EnumNameDE_V2XMsgType(DE_V2XMsgType e) {
  switch (e) {
    case DE_V2XMsgType_UNKNOWN: return "UNKNOWN";
    case DE_V2XMsgType_BSM: return "BSM";
    case DE_V2XMsgType_RSM: return "RSM";
    case DE_V2XMsgType_MAP: return "MAP";
    case DE_V2XMsgType_SPAT: return "SPAT";
    case DE_V2XMsgType_RSI_STATIC: return "RSI_STATIC";
    case DE_V2XMsgType_RSI_HALF_DYNAMIC: return "RSI_HALF_DYNAMIC";
    case DE_V2XMsgType_RSI_DYNAMIC: return "RSI_DYNAMIC";
    case DE_V2XMsgType_RSC: return "RSC";
    case DE_V2XMsgType_VIR: return "VIR";
    case DE_V2XMsgType_PAM: return "PAM";
    case DE_V2XMsgType_CLPMM: return "CLPMM";
    case DE_V2XMsgType_SSM: return "SSM";
    case DE_V2XMsgType_PSM: return "PSM";
    case DE_V2XMsgType_VPM: return "VPM";
    case DE_V2XMsgType_TEST: return "TEST";
    default: return "";
  }
}

enum DF_DeviceSpecificInfo : uint8_t {
  DF_DeviceSpecificInfo_NONE = 0,
  DF_DeviceSpecificInfo_DF_CameraModuleInfo = 1,
  DF_DeviceSpecificInfo_DF_MMWRadarModuleInfo = 2,
  DF_DeviceSpecificInfo_DF_TrafficSignalModuleInfo = 3,
  DF_DeviceSpecificInfo_DF_RSUModuleInfo = 4,
  DF_DeviceSpecificInfo_DF_CloudInfo = 5,
  DF_DeviceSpecificInfo_DF_LidarModuleInfo = 6,
  DF_DeviceSpecificInfo_MIN = DF_DeviceSpecificInfo_NONE,
  DF_DeviceSpecificInfo_MAX = DF_DeviceSpecificInfo_DF_LidarModuleInfo
};

inline const DF_DeviceSpecificInfo (&EnumValuesDF_DeviceSpecificInfo())[7] {
  static const DF_DeviceSpecificInfo values[] = {
    DF_DeviceSpecificInfo_NONE,
    DF_DeviceSpecificInfo_DF_CameraModuleInfo,
    DF_DeviceSpecificInfo_DF_MMWRadarModuleInfo,
    DF_DeviceSpecificInfo_DF_TrafficSignalModuleInfo,
    DF_DeviceSpecificInfo_DF_RSUModuleInfo,
    DF_DeviceSpecificInfo_DF_CloudInfo,
    DF_DeviceSpecificInfo_DF_LidarModuleInfo
  };
  return values;
}

inline const char * const *EnumNamesDF_DeviceSpecificInfo() {
  static const char * const names[8] = {
    "NONE",
    "DF_CameraModuleInfo",
    "DF_MMWRadarModuleInfo",
    "DF_TrafficSignalModuleInfo",
    "DF_RSUModuleInfo",
    "DF_CloudInfo",
    "DF_LidarModuleInfo",
    nullptr
  };
  return names;
}

inline const char *EnumNameDF_DeviceSpecificInfo(DF_DeviceSpecificInfo e) {
  if (::flatbuffers::IsOutRange(e, DF_DeviceSpecificInfo_NONE, DF_DeviceSpecificInfo_DF_LidarModuleInfo)) return "";
  const size_t index = static_cast<size_t>(e);
  return EnumNamesDF_DeviceSpecificInfo()[index];
}

template<typename T> struct DF_DeviceSpecificInfoTraits {
  static const DF_DeviceSpecificInfo enum_value = DF_DeviceSpecificInfo_NONE;
};

template<> struct DF_DeviceSpecificInfoTraits<MECData::DF_CameraModuleInfo> {
  static const DF_DeviceSpecificInfo enum_value = DF_DeviceSpecificInfo_DF_CameraModuleInfo;
};

template<> struct DF_DeviceSpecificInfoTraits<MECData::DF_MMWRadarModuleInfo> {
  static const DF_DeviceSpecificInfo enum_value = DF_DeviceSpecificInfo_DF_MMWRadarModuleInfo;
};

template<> struct DF_DeviceSpecificInfoTraits<MECData::DF_TrafficSignalModuleInfo> {
  static const DF_DeviceSpecificInfo enum_value = DF_DeviceSpecificInfo_DF_TrafficSignalModuleInfo;
};

template<> struct DF_DeviceSpecificInfoTraits<MECData::DF_RSUModuleInfo> {
  static const DF_DeviceSpecificInfo enum_value = DF_DeviceSpecificInfo_DF_RSUModuleInfo;
};

template<> struct DF_DeviceSpecificInfoTraits<MECData::DF_CloudInfo> {
  static const DF_DeviceSpecificInfo enum_value = DF_DeviceSpecificInfo_DF_CloudInfo;
};

template<> struct DF_DeviceSpecificInfoTraits<MECData::DF_LidarModuleInfo> {
  static const DF_DeviceSpecificInfo enum_value = DF_DeviceSpecificInfo_DF_LidarModuleInfo;
};

bool VerifyDF_DeviceSpecificInfo(::flatbuffers::Verifier &verifier, const void *obj, DF_DeviceSpecificInfo type);
bool VerifyDF_DeviceSpecificInfoVector(::flatbuffers::Verifier &verifier, const ::flatbuffers::Vector<::flatbuffers::Offset<void>> *values, const ::flatbuffers::Vector<uint8_t> *types);

struct DF_CamExternalParameters FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_CamExternalParametersBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_FORMAT_INFO = 4,
    VT_VERSION = 6,
    VT_DATA = 8
  };
  const ::flatbuffers::String *format_info() const {
    return GetPointer<const ::flatbuffers::String *>(VT_FORMAT_INFO);
  }
  const ::flatbuffers::String *version() const {
    return GetPointer<const ::flatbuffers::String *>(VT_VERSION);
  }
  const ::flatbuffers::Vector<uint8_t> *data() const {
    return GetPointer<const ::flatbuffers::Vector<uint8_t> *>(VT_DATA);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_FORMAT_INFO) &&
           verifier.VerifyString(format_info()) &&
           VerifyOffset(verifier, VT_VERSION) &&
           verifier.VerifyString(version()) &&
           VerifyOffset(verifier, VT_DATA) &&
           verifier.VerifyVector(data()) &&
           verifier.EndTable();
  }
};

struct DF_CamExternalParametersBuilder {
  typedef DF_CamExternalParameters Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_format_info(::flatbuffers::Offset<::flatbuffers::String> format_info) {
    fbb_.AddOffset(DF_CamExternalParameters::VT_FORMAT_INFO, format_info);
  }
  void add_version(::flatbuffers::Offset<::flatbuffers::String> version) {
    fbb_.AddOffset(DF_CamExternalParameters::VT_VERSION, version);
  }
  void add_data(::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> data) {
    fbb_.AddOffset(DF_CamExternalParameters::VT_DATA, data);
  }
  explicit DF_CamExternalParametersBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_CamExternalParameters> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_CamExternalParameters>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_CamExternalParameters> CreateDF_CamExternalParameters(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::String> format_info = 0,
    ::flatbuffers::Offset<::flatbuffers::String> version = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> data = 0) {
  DF_CamExternalParametersBuilder builder_(_fbb);
  builder_.add_data(data);
  builder_.add_version(version);
  builder_.add_format_info(format_info);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DF_CamExternalParameters> CreateDF_CamExternalParametersDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const char *format_info = nullptr,
    const char *version = nullptr,
    const std::vector<uint8_t> *data = nullptr) {
  auto format_info__ = format_info ? _fbb.CreateString(format_info) : 0;
  auto version__ = version ? _fbb.CreateString(version) : 0;
  auto data__ = data ? _fbb.CreateVector<uint8_t>(*data) : 0;
  return MECData::CreateDF_CamExternalParameters(
      _fbb,
      format_info__,
      version__,
      data__);
}

struct DF_CameraModuleInfo FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_CameraModuleInfoBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_NODE = 4,
    VT_POS = 6,
    VT_DIRECTION = 8,
    VT_DIRECTION8 = 10,
    VT_USER = 12,
    VT_PASSWORD = 14,
    VT_STREAM_ADDRESS = 16,
    VT_EXTERNAL_PARAMETERS = 18,
    VT_PERCEPTION_RADIUS = 20
  };
  const MECData::DF_NodeReferenceID *node() const {
    return GetPointer<const MECData::DF_NodeReferenceID *>(VT_NODE);
  }
  const MECData::DF_Position3D *pos() const {
    return GetPointer<const MECData::DF_Position3D *>(VT_POS);
  }
  uint16_t direction() const {
    return GetField<uint16_t>(VT_DIRECTION, 0);
  }
  MECData::DE_Direction8 direction8() const {
    return static_cast<MECData::DE_Direction8>(GetField<uint8_t>(VT_DIRECTION8, 0));
  }
  const ::flatbuffers::String *user() const {
    return GetPointer<const ::flatbuffers::String *>(VT_USER);
  }
  const ::flatbuffers::String *password() const {
    return GetPointer<const ::flatbuffers::String *>(VT_PASSWORD);
  }
  const ::flatbuffers::String *stream_address() const {
    return GetPointer<const ::flatbuffers::String *>(VT_STREAM_ADDRESS);
  }
  const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_CamExternalParameters>> *external_parameters() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_CamExternalParameters>> *>(VT_EXTERNAL_PARAMETERS);
  }
  uint64_t perception_radius() const {
    return GetField<uint64_t>(VT_PERCEPTION_RADIUS, 0);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_NODE) &&
           verifier.VerifyTable(node()) &&
           VerifyOffset(verifier, VT_POS) &&
           verifier.VerifyTable(pos()) &&
           VerifyField<uint16_t>(verifier, VT_DIRECTION, 2) &&
           VerifyField<uint8_t>(verifier, VT_DIRECTION8, 1) &&
           VerifyOffset(verifier, VT_USER) &&
           verifier.VerifyString(user()) &&
           VerifyOffset(verifier, VT_PASSWORD) &&
           verifier.VerifyString(password()) &&
           VerifyOffset(verifier, VT_STREAM_ADDRESS) &&
           verifier.VerifyString(stream_address()) &&
           VerifyOffset(verifier, VT_EXTERNAL_PARAMETERS) &&
           verifier.VerifyVector(external_parameters()) &&
           verifier.VerifyVectorOfTables(external_parameters()) &&
           VerifyField<uint64_t>(verifier, VT_PERCEPTION_RADIUS, 8) &&
           verifier.EndTable();
  }
};

struct DF_CameraModuleInfoBuilder {
  typedef DF_CameraModuleInfo Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_node(::flatbuffers::Offset<MECData::DF_NodeReferenceID> node) {
    fbb_.AddOffset(DF_CameraModuleInfo::VT_NODE, node);
  }
  void add_pos(::flatbuffers::Offset<MECData::DF_Position3D> pos) {
    fbb_.AddOffset(DF_CameraModuleInfo::VT_POS, pos);
  }
  void add_direction(uint16_t direction) {
    fbb_.AddElement<uint16_t>(DF_CameraModuleInfo::VT_DIRECTION, direction, 0);
  }
  void add_direction8(MECData::DE_Direction8 direction8) {
    fbb_.AddElement<uint8_t>(DF_CameraModuleInfo::VT_DIRECTION8, static_cast<uint8_t>(direction8), 0);
  }
  void add_user(::flatbuffers::Offset<::flatbuffers::String> user) {
    fbb_.AddOffset(DF_CameraModuleInfo::VT_USER, user);
  }
  void add_password(::flatbuffers::Offset<::flatbuffers::String> password) {
    fbb_.AddOffset(DF_CameraModuleInfo::VT_PASSWORD, password);
  }
  void add_stream_address(::flatbuffers::Offset<::flatbuffers::String> stream_address) {
    fbb_.AddOffset(DF_CameraModuleInfo::VT_STREAM_ADDRESS, stream_address);
  }
  void add_external_parameters(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_CamExternalParameters>>> external_parameters) {
    fbb_.AddOffset(DF_CameraModuleInfo::VT_EXTERNAL_PARAMETERS, external_parameters);
  }
  void add_perception_radius(uint64_t perception_radius) {
    fbb_.AddElement<uint64_t>(DF_CameraModuleInfo::VT_PERCEPTION_RADIUS, perception_radius, 0);
  }
  explicit DF_CameraModuleInfoBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_CameraModuleInfo> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_CameraModuleInfo>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_CameraModuleInfo> CreateDF_CameraModuleInfo(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<MECData::DF_NodeReferenceID> node = 0,
    ::flatbuffers::Offset<MECData::DF_Position3D> pos = 0,
    uint16_t direction = 0,
    MECData::DE_Direction8 direction8 = MECData::DE_Direction8_N,
    ::flatbuffers::Offset<::flatbuffers::String> user = 0,
    ::flatbuffers::Offset<::flatbuffers::String> password = 0,
    ::flatbuffers::Offset<::flatbuffers::String> stream_address = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_CamExternalParameters>>> external_parameters = 0,
    uint64_t perception_radius = 0) {
  DF_CameraModuleInfoBuilder builder_(_fbb);
  builder_.add_perception_radius(perception_radius);
  builder_.add_external_parameters(external_parameters);
  builder_.add_stream_address(stream_address);
  builder_.add_password(password);
  builder_.add_user(user);
  builder_.add_pos(pos);
  builder_.add_node(node);
  builder_.add_direction(direction);
  builder_.add_direction8(direction8);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DF_CameraModuleInfo> CreateDF_CameraModuleInfoDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<MECData::DF_NodeReferenceID> node = 0,
    ::flatbuffers::Offset<MECData::DF_Position3D> pos = 0,
    uint16_t direction = 0,
    MECData::DE_Direction8 direction8 = MECData::DE_Direction8_N,
    const char *user = nullptr,
    const char *password = nullptr,
    const char *stream_address = nullptr,
    const std::vector<::flatbuffers::Offset<MECData::DF_CamExternalParameters>> *external_parameters = nullptr,
    uint64_t perception_radius = 0) {
  auto user__ = user ? _fbb.CreateString(user) : 0;
  auto password__ = password ? _fbb.CreateString(password) : 0;
  auto stream_address__ = stream_address ? _fbb.CreateString(stream_address) : 0;
  auto external_parameters__ = external_parameters ? _fbb.CreateVector<::flatbuffers::Offset<MECData::DF_CamExternalParameters>>(*external_parameters) : 0;
  return MECData::CreateDF_CameraModuleInfo(
      _fbb,
      node,
      pos,
      direction,
      direction8,
      user__,
      password__,
      stream_address__,
      external_parameters__,
      perception_radius);
}

struct DF_MMWRadarModuleInfo FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_MMWRadarModuleInfoBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_NODE = 4,
    VT_POS = 6,
    VT_DIRECTION = 8,
    VT_DIRECTION8 = 10,
    VT_PERCEPTION_RADIUS = 12
  };
  const MECData::DF_NodeReferenceID *node() const {
    return GetPointer<const MECData::DF_NodeReferenceID *>(VT_NODE);
  }
  const MECData::DF_Position3D *pos() const {
    return GetPointer<const MECData::DF_Position3D *>(VT_POS);
  }
  uint16_t direction() const {
    return GetField<uint16_t>(VT_DIRECTION, 0);
  }
  MECData::DE_Direction8 direction8() const {
    return static_cast<MECData::DE_Direction8>(GetField<uint8_t>(VT_DIRECTION8, 0));
  }
  uint64_t perception_radius() const {
    return GetField<uint64_t>(VT_PERCEPTION_RADIUS, 0);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_NODE) &&
           verifier.VerifyTable(node()) &&
           VerifyOffset(verifier, VT_POS) &&
           verifier.VerifyTable(pos()) &&
           VerifyField<uint16_t>(verifier, VT_DIRECTION, 2) &&
           VerifyField<uint8_t>(verifier, VT_DIRECTION8, 1) &&
           VerifyField<uint64_t>(verifier, VT_PERCEPTION_RADIUS, 8) &&
           verifier.EndTable();
  }
};

struct DF_MMWRadarModuleInfoBuilder {
  typedef DF_MMWRadarModuleInfo Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_node(::flatbuffers::Offset<MECData::DF_NodeReferenceID> node) {
    fbb_.AddOffset(DF_MMWRadarModuleInfo::VT_NODE, node);
  }
  void add_pos(::flatbuffers::Offset<MECData::DF_Position3D> pos) {
    fbb_.AddOffset(DF_MMWRadarModuleInfo::VT_POS, pos);
  }
  void add_direction(uint16_t direction) {
    fbb_.AddElement<uint16_t>(DF_MMWRadarModuleInfo::VT_DIRECTION, direction, 0);
  }
  void add_direction8(MECData::DE_Direction8 direction8) {
    fbb_.AddElement<uint8_t>(DF_MMWRadarModuleInfo::VT_DIRECTION8, static_cast<uint8_t>(direction8), 0);
  }
  void add_perception_radius(uint64_t perception_radius) {
    fbb_.AddElement<uint64_t>(DF_MMWRadarModuleInfo::VT_PERCEPTION_RADIUS, perception_radius, 0);
  }
  explicit DF_MMWRadarModuleInfoBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_MMWRadarModuleInfo> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_MMWRadarModuleInfo>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_MMWRadarModuleInfo> CreateDF_MMWRadarModuleInfo(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<MECData::DF_NodeReferenceID> node = 0,
    ::flatbuffers::Offset<MECData::DF_Position3D> pos = 0,
    uint16_t direction = 0,
    MECData::DE_Direction8 direction8 = MECData::DE_Direction8_N,
    uint64_t perception_radius = 0) {
  DF_MMWRadarModuleInfoBuilder builder_(_fbb);
  builder_.add_perception_radius(perception_radius);
  builder_.add_pos(pos);
  builder_.add_node(node);
  builder_.add_direction(direction);
  builder_.add_direction8(direction8);
  return builder_.Finish();
}

struct DE_TrafficSignalSupportedCommand FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DE_TrafficSignalSupportedCommandBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_FULL_SIGNAL_SCHEME = 4,
    VT_PHASE_JUMP = 6,
    VT_STEP_CONTROL = 8,
    VT_SCHEME_CONSTRAINTS = 10
  };
  bool full_signal_scheme() const {
    return GetField<uint8_t>(VT_FULL_SIGNAL_SCHEME, 0) != 0;
  }
  bool phase_jump() const {
    return GetField<uint8_t>(VT_PHASE_JUMP, 0) != 0;
  }
  bool step_control() const {
    return GetField<uint8_t>(VT_STEP_CONTROL, 0) != 0;
  }
  bool scheme_constraints() const {
    return GetField<uint8_t>(VT_SCHEME_CONSTRAINTS, 0) != 0;
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<uint8_t>(verifier, VT_FULL_SIGNAL_SCHEME, 1) &&
           VerifyField<uint8_t>(verifier, VT_PHASE_JUMP, 1) &&
           VerifyField<uint8_t>(verifier, VT_STEP_CONTROL, 1) &&
           VerifyField<uint8_t>(verifier, VT_SCHEME_CONSTRAINTS, 1) &&
           verifier.EndTable();
  }
};

struct DE_TrafficSignalSupportedCommandBuilder {
  typedef DE_TrafficSignalSupportedCommand Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_full_signal_scheme(bool full_signal_scheme) {
    fbb_.AddElement<uint8_t>(DE_TrafficSignalSupportedCommand::VT_FULL_SIGNAL_SCHEME, static_cast<uint8_t>(full_signal_scheme), 0);
  }
  void add_phase_jump(bool phase_jump) {
    fbb_.AddElement<uint8_t>(DE_TrafficSignalSupportedCommand::VT_PHASE_JUMP, static_cast<uint8_t>(phase_jump), 0);
  }
  void add_step_control(bool step_control) {
    fbb_.AddElement<uint8_t>(DE_TrafficSignalSupportedCommand::VT_STEP_CONTROL, static_cast<uint8_t>(step_control), 0);
  }
  void add_scheme_constraints(bool scheme_constraints) {
    fbb_.AddElement<uint8_t>(DE_TrafficSignalSupportedCommand::VT_SCHEME_CONSTRAINTS, static_cast<uint8_t>(scheme_constraints), 0);
  }
  explicit DE_TrafficSignalSupportedCommandBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DE_TrafficSignalSupportedCommand> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DE_TrafficSignalSupportedCommand>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DE_TrafficSignalSupportedCommand> CreateDE_TrafficSignalSupportedCommand(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    bool full_signal_scheme = false,
    bool phase_jump = false,
    bool step_control = false,
    bool scheme_constraints = false) {
  DE_TrafficSignalSupportedCommandBuilder builder_(_fbb);
  builder_.add_scheme_constraints(scheme_constraints);
  builder_.add_step_control(step_control);
  builder_.add_phase_jump(phase_jump);
  builder_.add_full_signal_scheme(full_signal_scheme);
  return builder_.Finish();
}

struct DF_TrafficSignalStatusInfo FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_TrafficSignalStatusInfoBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_CURRENT_SCHEDULE_ID = 4,
    VT_CURRENT_DAY_PLAN_ID = 6,
    VT_CURRENT_MODE = 8,
    VT_CURRENT_SCHEME = 10
  };
  uint8_t current_schedule_id() const {
    return GetField<uint8_t>(VT_CURRENT_SCHEDULE_ID, 255);
  }
  uint8_t current_day_plan_id() const {
    return GetField<uint8_t>(VT_CURRENT_DAY_PLAN_ID, 255);
  }
  MECData::DE_SignalControlMode current_mode() const {
    return static_cast<MECData::DE_SignalControlMode>(GetField<uint8_t>(VT_CURRENT_MODE, 0));
  }
  int32_t current_scheme() const {
    return GetField<int32_t>(VT_CURRENT_SCHEME, 0);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<uint8_t>(verifier, VT_CURRENT_SCHEDULE_ID, 1) &&
           VerifyField<uint8_t>(verifier, VT_CURRENT_DAY_PLAN_ID, 1) &&
           VerifyField<uint8_t>(verifier, VT_CURRENT_MODE, 1) &&
           VerifyField<int32_t>(verifier, VT_CURRENT_SCHEME, 4) &&
           verifier.EndTable();
  }
};

struct DF_TrafficSignalStatusInfoBuilder {
  typedef DF_TrafficSignalStatusInfo Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_current_schedule_id(uint8_t current_schedule_id) {
    fbb_.AddElement<uint8_t>(DF_TrafficSignalStatusInfo::VT_CURRENT_SCHEDULE_ID, current_schedule_id, 255);
  }
  void add_current_day_plan_id(uint8_t current_day_plan_id) {
    fbb_.AddElement<uint8_t>(DF_TrafficSignalStatusInfo::VT_CURRENT_DAY_PLAN_ID, current_day_plan_id, 255);
  }
  void add_current_mode(MECData::DE_SignalControlMode current_mode) {
    fbb_.AddElement<uint8_t>(DF_TrafficSignalStatusInfo::VT_CURRENT_MODE, static_cast<uint8_t>(current_mode), 0);
  }
  void add_current_scheme(int32_t current_scheme) {
    fbb_.AddElement<int32_t>(DF_TrafficSignalStatusInfo::VT_CURRENT_SCHEME, current_scheme, 0);
  }
  explicit DF_TrafficSignalStatusInfoBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_TrafficSignalStatusInfo> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_TrafficSignalStatusInfo>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_TrafficSignalStatusInfo> CreateDF_TrafficSignalStatusInfo(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    uint8_t current_schedule_id = 255,
    uint8_t current_day_plan_id = 255,
    MECData::DE_SignalControlMode current_mode = MECData::DE_SignalControlMode_CYCLIC_FIXED,
    int32_t current_scheme = 0) {
  DF_TrafficSignalStatusInfoBuilder builder_(_fbb);
  builder_.add_current_scheme(current_scheme);
  builder_.add_current_mode(current_mode);
  builder_.add_current_day_plan_id(current_day_plan_id);
  builder_.add_current_schedule_id(current_schedule_id);
  return builder_.Finish();
}

struct DF_TrafficSignalModuleInfo FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_TrafficSignalModuleInfoBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_NODE = 4,
    VT_SUPPORTED_INSTRUCTIONS = 6,
    VT_STATUS_DETAILS = 8,
    VT_CONFIG = 10,
    VT_SCHEME_CONSTRAINTS = 12
  };
  const MECData::DF_NodeReferenceID *node() const {
    return GetPointer<const MECData::DF_NodeReferenceID *>(VT_NODE);
  }
  const MECData::DE_TrafficSignalSupportedCommand *supported_instructions() const {
    return GetPointer<const MECData::DE_TrafficSignalSupportedCommand *>(VT_SUPPORTED_INSTRUCTIONS);
  }
  const MECData::DF_TrafficSignalStatusInfo *status_details() const {
    return GetPointer<const MECData::DF_TrafficSignalStatusInfo *>(VT_STATUS_DETAILS);
  }
  const MECData::DF_SignalControllerConfig *config() const {
    return GetPointer<const MECData::DF_SignalControllerConfig *>(VT_CONFIG);
  }
  const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_SignalSchemeConstraint>> *scheme_constraints() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_SignalSchemeConstraint>> *>(VT_SCHEME_CONSTRAINTS);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_NODE) &&
           verifier.VerifyTable(node()) &&
           VerifyOffset(verifier, VT_SUPPORTED_INSTRUCTIONS) &&
           verifier.VerifyTable(supported_instructions()) &&
           VerifyOffset(verifier, VT_STATUS_DETAILS) &&
           verifier.VerifyTable(status_details()) &&
           VerifyOffset(verifier, VT_CONFIG) &&
           verifier.VerifyTable(config()) &&
           VerifyOffset(verifier, VT_SCHEME_CONSTRAINTS) &&
           verifier.VerifyVector(scheme_constraints()) &&
           verifier.VerifyVectorOfTables(scheme_constraints()) &&
           verifier.EndTable();
  }
};

struct DF_TrafficSignalModuleInfoBuilder {
  typedef DF_TrafficSignalModuleInfo Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_node(::flatbuffers::Offset<MECData::DF_NodeReferenceID> node) {
    fbb_.AddOffset(DF_TrafficSignalModuleInfo::VT_NODE, node);
  }
  void add_supported_instructions(::flatbuffers::Offset<MECData::DE_TrafficSignalSupportedCommand> supported_instructions) {
    fbb_.AddOffset(DF_TrafficSignalModuleInfo::VT_SUPPORTED_INSTRUCTIONS, supported_instructions);
  }
  void add_status_details(::flatbuffers::Offset<MECData::DF_TrafficSignalStatusInfo> status_details) {
    fbb_.AddOffset(DF_TrafficSignalModuleInfo::VT_STATUS_DETAILS, status_details);
  }
  void add_config(::flatbuffers::Offset<MECData::DF_SignalControllerConfig> config) {
    fbb_.AddOffset(DF_TrafficSignalModuleInfo::VT_CONFIG, config);
  }
  void add_scheme_constraints(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_SignalSchemeConstraint>>> scheme_constraints) {
    fbb_.AddOffset(DF_TrafficSignalModuleInfo::VT_SCHEME_CONSTRAINTS, scheme_constraints);
  }
  explicit DF_TrafficSignalModuleInfoBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_TrafficSignalModuleInfo> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_TrafficSignalModuleInfo>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_TrafficSignalModuleInfo> CreateDF_TrafficSignalModuleInfo(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<MECData::DF_NodeReferenceID> node = 0,
    ::flatbuffers::Offset<MECData::DE_TrafficSignalSupportedCommand> supported_instructions = 0,
    ::flatbuffers::Offset<MECData::DF_TrafficSignalStatusInfo> status_details = 0,
    ::flatbuffers::Offset<MECData::DF_SignalControllerConfig> config = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_SignalSchemeConstraint>>> scheme_constraints = 0) {
  DF_TrafficSignalModuleInfoBuilder builder_(_fbb);
  builder_.add_scheme_constraints(scheme_constraints);
  builder_.add_config(config);
  builder_.add_status_details(status_details);
  builder_.add_supported_instructions(supported_instructions);
  builder_.add_node(node);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DF_TrafficSignalModuleInfo> CreateDF_TrafficSignalModuleInfoDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<MECData::DF_NodeReferenceID> node = 0,
    ::flatbuffers::Offset<MECData::DE_TrafficSignalSupportedCommand> supported_instructions = 0,
    ::flatbuffers::Offset<MECData::DF_TrafficSignalStatusInfo> status_details = 0,
    ::flatbuffers::Offset<MECData::DF_SignalControllerConfig> config = 0,
    const std::vector<::flatbuffers::Offset<MECData::DF_SignalSchemeConstraint>> *scheme_constraints = nullptr) {
  auto scheme_constraints__ = scheme_constraints ? _fbb.CreateVector<::flatbuffers::Offset<MECData::DF_SignalSchemeConstraint>>(*scheme_constraints) : 0;
  return MECData::CreateDF_TrafficSignalModuleInfo(
      _fbb,
      node,
      supported_instructions,
      status_details,
      config,
      scheme_constraints__);
}

struct DF_V2XMsgConfig FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_V2XMsgConfigBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_MSG_TYPE = 4,
    VT_SEND = 6,
    VT_RECV = 8
  };
  MECData::DE_V2XMsgType msg_type() const {
    return static_cast<MECData::DE_V2XMsgType>(GetField<uint16_t>(VT_MSG_TYPE, 0));
  }
  bool send() const {
    return GetField<uint8_t>(VT_SEND, 1) != 0;
  }
  bool recv() const {
    return GetField<uint8_t>(VT_RECV, 1) != 0;
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<uint16_t>(verifier, VT_MSG_TYPE, 2) &&
           VerifyField<uint8_t>(verifier, VT_SEND, 1) &&
           VerifyField<uint8_t>(verifier, VT_RECV, 1) &&
           verifier.EndTable();
  }
};

struct DF_V2XMsgConfigBuilder {
  typedef DF_V2XMsgConfig Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_msg_type(MECData::DE_V2XMsgType msg_type) {
    fbb_.AddElement<uint16_t>(DF_V2XMsgConfig::VT_MSG_TYPE, static_cast<uint16_t>(msg_type), 0);
  }
  void add_send(bool send) {
    fbb_.AddElement<uint8_t>(DF_V2XMsgConfig::VT_SEND, static_cast<uint8_t>(send), 1);
  }
  void add_recv(bool recv) {
    fbb_.AddElement<uint8_t>(DF_V2XMsgConfig::VT_RECV, static_cast<uint8_t>(recv), 1);
  }
  explicit DF_V2XMsgConfigBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_V2XMsgConfig> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_V2XMsgConfig>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_V2XMsgConfig> CreateDF_V2XMsgConfig(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    MECData::DE_V2XMsgType msg_type = MECData::DE_V2XMsgType_UNKNOWN,
    bool send = true,
    bool recv = true) {
  DF_V2XMsgConfigBuilder builder_(_fbb);
  builder_.add_msg_type(msg_type);
  builder_.add_recv(recv);
  builder_.add_send(send);
  return builder_.Finish();
}

struct DF_RSUBroadcastConfig FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_RSUBroadcastConfigBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_MSG_CONFIGS = 4,
    VT_BSM_UPLOAD = 6,
    VT_STATUS_UPLOAD_INTERVAL = 8
  };
  const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_V2XMsgConfig>> *msg_configs() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_V2XMsgConfig>> *>(VT_MSG_CONFIGS);
  }
  bool bsm_upload() const {
    return GetField<uint8_t>(VT_BSM_UPLOAD, 0) != 0;
  }
  uint8_t status_upload_interval() const {
    return GetField<uint8_t>(VT_STATUS_UPLOAD_INTERVAL, 0);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_MSG_CONFIGS) &&
           verifier.VerifyVector(msg_configs()) &&
           verifier.VerifyVectorOfTables(msg_configs()) &&
           VerifyField<uint8_t>(verifier, VT_BSM_UPLOAD, 1) &&
           VerifyField<uint8_t>(verifier, VT_STATUS_UPLOAD_INTERVAL, 1) &&
           verifier.EndTable();
  }
};

struct DF_RSUBroadcastConfigBuilder {
  typedef DF_RSUBroadcastConfig Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_msg_configs(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_V2XMsgConfig>>> msg_configs) {
    fbb_.AddOffset(DF_RSUBroadcastConfig::VT_MSG_CONFIGS, msg_configs);
  }
  void add_bsm_upload(bool bsm_upload) {
    fbb_.AddElement<uint8_t>(DF_RSUBroadcastConfig::VT_BSM_UPLOAD, static_cast<uint8_t>(bsm_upload), 0);
  }
  void add_status_upload_interval(uint8_t status_upload_interval) {
    fbb_.AddElement<uint8_t>(DF_RSUBroadcastConfig::VT_STATUS_UPLOAD_INTERVAL, status_upload_interval, 0);
  }
  explicit DF_RSUBroadcastConfigBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_RSUBroadcastConfig> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_RSUBroadcastConfig>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_RSUBroadcastConfig> CreateDF_RSUBroadcastConfig(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_V2XMsgConfig>>> msg_configs = 0,
    bool bsm_upload = false,
    uint8_t status_upload_interval = 0) {
  DF_RSUBroadcastConfigBuilder builder_(_fbb);
  builder_.add_msg_configs(msg_configs);
  builder_.add_status_upload_interval(status_upload_interval);
  builder_.add_bsm_upload(bsm_upload);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DF_RSUBroadcastConfig> CreateDF_RSUBroadcastConfigDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const std::vector<::flatbuffers::Offset<MECData::DF_V2XMsgConfig>> *msg_configs = nullptr,
    bool bsm_upload = false,
    uint8_t status_upload_interval = 0) {
  auto msg_configs__ = msg_configs ? _fbb.CreateVector<::flatbuffers::Offset<MECData::DF_V2XMsgConfig>>(*msg_configs) : 0;
  return MECData::CreateDF_RSUBroadcastConfig(
      _fbb,
      msg_configs__,
      bsm_upload,
      status_upload_interval);
}

struct DF_RSUStatus FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_RSUStatusBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_SERVICE_STATUS = 4,
    VT_CELLULAR_STATUS = 6,
    VT_NTP_STATUS = 8,
    VT_PTP_STATUS = 10,
    VT_GNSS_STATUS = 12,
    VT_V2X_STATUS = 14,
    VT_CAN_STATUS = 16,
    VT_CA_STATUS = 18,
    VT_ENV_STATUS = 20
  };
  uint8_t service_status() const {
    return GetField<uint8_t>(VT_SERVICE_STATUS, 0);
  }
  uint8_t cellular_status() const {
    return GetField<uint8_t>(VT_CELLULAR_STATUS, 0);
  }
  uint8_t ntp_status() const {
    return GetField<uint8_t>(VT_NTP_STATUS, 0);
  }
  uint8_t ptp_status() const {
    return GetField<uint8_t>(VT_PTP_STATUS, 0);
  }
  uint8_t gnss_status() const {
    return GetField<uint8_t>(VT_GNSS_STATUS, 0);
  }
  uint8_t v2x_status() const {
    return GetField<uint8_t>(VT_V2X_STATUS, 0);
  }
  uint8_t can_status() const {
    return GetField<uint8_t>(VT_CAN_STATUS, 0);
  }
  uint8_t ca_status() const {
    return GetField<uint8_t>(VT_CA_STATUS, 0);
  }
  uint8_t env_status() const {
    return GetField<uint8_t>(VT_ENV_STATUS, 0);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<uint8_t>(verifier, VT_SERVICE_STATUS, 1) &&
           VerifyField<uint8_t>(verifier, VT_CELLULAR_STATUS, 1) &&
           VerifyField<uint8_t>(verifier, VT_NTP_STATUS, 1) &&
           VerifyField<uint8_t>(verifier, VT_PTP_STATUS, 1) &&
           VerifyField<uint8_t>(verifier, VT_GNSS_STATUS, 1) &&
           VerifyField<uint8_t>(verifier, VT_V2X_STATUS, 1) &&
           VerifyField<uint8_t>(verifier, VT_CAN_STATUS, 1) &&
           VerifyField<uint8_t>(verifier, VT_CA_STATUS, 1) &&
           VerifyField<uint8_t>(verifier, VT_ENV_STATUS, 1) &&
           verifier.EndTable();
  }
};

struct DF_RSUStatusBuilder {
  typedef DF_RSUStatus Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_service_status(uint8_t service_status) {
    fbb_.AddElement<uint8_t>(DF_RSUStatus::VT_SERVICE_STATUS, service_status, 0);
  }
  void add_cellular_status(uint8_t cellular_status) {
    fbb_.AddElement<uint8_t>(DF_RSUStatus::VT_CELLULAR_STATUS, cellular_status, 0);
  }
  void add_ntp_status(uint8_t ntp_status) {
    fbb_.AddElement<uint8_t>(DF_RSUStatus::VT_NTP_STATUS, ntp_status, 0);
  }
  void add_ptp_status(uint8_t ptp_status) {
    fbb_.AddElement<uint8_t>(DF_RSUStatus::VT_PTP_STATUS, ptp_status, 0);
  }
  void add_gnss_status(uint8_t gnss_status) {
    fbb_.AddElement<uint8_t>(DF_RSUStatus::VT_GNSS_STATUS, gnss_status, 0);
  }
  void add_v2x_status(uint8_t v2x_status) {
    fbb_.AddElement<uint8_t>(DF_RSUStatus::VT_V2X_STATUS, v2x_status, 0);
  }
  void add_can_status(uint8_t can_status) {
    fbb_.AddElement<uint8_t>(DF_RSUStatus::VT_CAN_STATUS, can_status, 0);
  }
  void add_ca_status(uint8_t ca_status) {
    fbb_.AddElement<uint8_t>(DF_RSUStatus::VT_CA_STATUS, ca_status, 0);
  }
  void add_env_status(uint8_t env_status) {
    fbb_.AddElement<uint8_t>(DF_RSUStatus::VT_ENV_STATUS, env_status, 0);
  }
  explicit DF_RSUStatusBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_RSUStatus> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_RSUStatus>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_RSUStatus> CreateDF_RSUStatus(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    uint8_t service_status = 0,
    uint8_t cellular_status = 0,
    uint8_t ntp_status = 0,
    uint8_t ptp_status = 0,
    uint8_t gnss_status = 0,
    uint8_t v2x_status = 0,
    uint8_t can_status = 0,
    uint8_t ca_status = 0,
    uint8_t env_status = 0) {
  DF_RSUStatusBuilder builder_(_fbb);
  builder_.add_env_status(env_status);
  builder_.add_ca_status(ca_status);
  builder_.add_can_status(can_status);
  builder_.add_v2x_status(v2x_status);
  builder_.add_gnss_status(gnss_status);
  builder_.add_ptp_status(ptp_status);
  builder_.add_ntp_status(ntp_status);
  builder_.add_cellular_status(cellular_status);
  builder_.add_service_status(service_status);
  return builder_.Finish();
}

struct DF_RSUModuleInfo FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_RSUModuleInfoBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_RSU_ID = 4,
    VT_SUPPORTED_V2X_STANDARDS = 6,
    VT_STATUS = 8,
    VT_BROADCAST_CONFIG = 10
  };
  const ::flatbuffers::Vector<uint8_t> *rsu_id() const {
    return GetPointer<const ::flatbuffers::Vector<uint8_t> *>(VT_RSU_ID);
  }
  const ::flatbuffers::Vector<int8_t> *supported_v2x_standards() const {
    return GetPointer<const ::flatbuffers::Vector<int8_t> *>(VT_SUPPORTED_V2X_STANDARDS);
  }
  const MECData::DF_RSUStatus *status() const {
    return GetPointer<const MECData::DF_RSUStatus *>(VT_STATUS);
  }
  const MECData::DF_RSUBroadcastConfig *broadcast_config() const {
    return GetPointer<const MECData::DF_RSUBroadcastConfig *>(VT_BROADCAST_CONFIG);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_RSU_ID) &&
           verifier.VerifyVector(rsu_id()) &&
           VerifyOffset(verifier, VT_SUPPORTED_V2X_STANDARDS) &&
           verifier.VerifyVector(supported_v2x_standards()) &&
           VerifyOffset(verifier, VT_STATUS) &&
           verifier.VerifyTable(status()) &&
           VerifyOffset(verifier, VT_BROADCAST_CONFIG) &&
           verifier.VerifyTable(broadcast_config()) &&
           verifier.EndTable();
  }
};

struct DF_RSUModuleInfoBuilder {
  typedef DF_RSUModuleInfo Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_rsu_id(::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> rsu_id) {
    fbb_.AddOffset(DF_RSUModuleInfo::VT_RSU_ID, rsu_id);
  }
  void add_supported_v2x_standards(::flatbuffers::Offset<::flatbuffers::Vector<int8_t>> supported_v2x_standards) {
    fbb_.AddOffset(DF_RSUModuleInfo::VT_SUPPORTED_V2X_STANDARDS, supported_v2x_standards);
  }
  void add_status(::flatbuffers::Offset<MECData::DF_RSUStatus> status) {
    fbb_.AddOffset(DF_RSUModuleInfo::VT_STATUS, status);
  }
  void add_broadcast_config(::flatbuffers::Offset<MECData::DF_RSUBroadcastConfig> broadcast_config) {
    fbb_.AddOffset(DF_RSUModuleInfo::VT_BROADCAST_CONFIG, broadcast_config);
  }
  explicit DF_RSUModuleInfoBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_RSUModuleInfo> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_RSUModuleInfo>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_RSUModuleInfo> CreateDF_RSUModuleInfo(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> rsu_id = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<int8_t>> supported_v2x_standards = 0,
    ::flatbuffers::Offset<MECData::DF_RSUStatus> status = 0,
    ::flatbuffers::Offset<MECData::DF_RSUBroadcastConfig> broadcast_config = 0) {
  DF_RSUModuleInfoBuilder builder_(_fbb);
  builder_.add_broadcast_config(broadcast_config);
  builder_.add_status(status);
  builder_.add_supported_v2x_standards(supported_v2x_standards);
  builder_.add_rsu_id(rsu_id);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DF_RSUModuleInfo> CreateDF_RSUModuleInfoDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const std::vector<uint8_t> *rsu_id = nullptr,
    const std::vector<int8_t> *supported_v2x_standards = nullptr,
    ::flatbuffers::Offset<MECData::DF_RSUStatus> status = 0,
    ::flatbuffers::Offset<MECData::DF_RSUBroadcastConfig> broadcast_config = 0) {
  auto rsu_id__ = rsu_id ? _fbb.CreateVector<uint8_t>(*rsu_id) : 0;
  auto supported_v2x_standards__ = supported_v2x_standards ? _fbb.CreateVector<int8_t>(*supported_v2x_standards) : 0;
  return MECData::CreateDF_RSUModuleInfo(
      _fbb,
      rsu_id__,
      supported_v2x_standards__,
      status,
      broadcast_config);
}

struct DF_CloudInfo FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_CloudInfoBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_HOST = 4,
    VT_PORT = 6,
    VT_COMMUNICATION_PROTOCOL = 8
  };
  const ::flatbuffers::String *host() const {
    return GetPointer<const ::flatbuffers::String *>(VT_HOST);
  }
  uint16_t port() const {
    return GetField<uint16_t>(VT_PORT, 0);
  }
  MECData::DE_CommunicationProtocol communication_protocol() const {
    return static_cast<MECData::DE_CommunicationProtocol>(GetField<int8_t>(VT_COMMUNICATION_PROTOCOL, 0));
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_HOST) &&
           verifier.VerifyString(host()) &&
           VerifyField<uint16_t>(verifier, VT_PORT, 2) &&
           VerifyField<int8_t>(verifier, VT_COMMUNICATION_PROTOCOL, 1) &&
           verifier.EndTable();
  }
};

struct DF_CloudInfoBuilder {
  typedef DF_CloudInfo Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_host(::flatbuffers::Offset<::flatbuffers::String> host) {
    fbb_.AddOffset(DF_CloudInfo::VT_HOST, host);
  }
  void add_port(uint16_t port) {
    fbb_.AddElement<uint16_t>(DF_CloudInfo::VT_PORT, port, 0);
  }
  void add_communication_protocol(MECData::DE_CommunicationProtocol communication_protocol) {
    fbb_.AddElement<int8_t>(DF_CloudInfo::VT_COMMUNICATION_PROTOCOL, static_cast<int8_t>(communication_protocol), 0);
  }
  explicit DF_CloudInfoBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_CloudInfo> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_CloudInfo>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_CloudInfo> CreateDF_CloudInfo(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::String> host = 0,
    uint16_t port = 0,
    MECData::DE_CommunicationProtocol communication_protocol = MECData::DE_CommunicationProtocol_MQTT) {
  DF_CloudInfoBuilder builder_(_fbb);
  builder_.add_host(host);
  builder_.add_port(port);
  builder_.add_communication_protocol(communication_protocol);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DF_CloudInfo> CreateDF_CloudInfoDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const char *host = nullptr,
    uint16_t port = 0,
    MECData::DE_CommunicationProtocol communication_protocol = MECData::DE_CommunicationProtocol_MQTT) {
  auto host__ = host ? _fbb.CreateString(host) : 0;
  return MECData::CreateDF_CloudInfo(
      _fbb,
      host__,
      port,
      communication_protocol);
}

struct DF_LidarModuleInfo FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_LidarModuleInfoBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_NODE = 4,
    VT_POS = 6,
    VT_DIRECTION = 8,
    VT_DIRECTION8 = 10,
    VT_PERCEPTION_RADIUS = 12,
    VT_SCAN_FREQUENCY = 14,
    VT_HORIZONAL_FIELD_OF_VIEW = 16,
    VT_HORIZONAL_RESOLUTION = 18,
    VT_VERTICAL_FIELD_OF_VIEW = 20,
    VT_VERTICAL_RESOLUTION = 22
  };
  const MECData::DF_NodeReferenceID *node() const {
    return GetPointer<const MECData::DF_NodeReferenceID *>(VT_NODE);
  }
  const MECData::DF_Position3D *pos() const {
    return GetPointer<const MECData::DF_Position3D *>(VT_POS);
  }
  uint16_t direction() const {
    return GetField<uint16_t>(VT_DIRECTION, 65535);
  }
  MECData::DE_Direction8 direction8() const {
    return static_cast<MECData::DE_Direction8>(GetField<uint8_t>(VT_DIRECTION8, 0));
  }
  uint64_t perception_radius() const {
    return GetField<uint64_t>(VT_PERCEPTION_RADIUS, 0);
  }
  uint32_t scan_frequency() const {
    return GetField<uint32_t>(VT_SCAN_FREQUENCY, 0);
  }
  uint16_t horizonal_field_of_view() const {
    return GetField<uint16_t>(VT_HORIZONAL_FIELD_OF_VIEW, 65535);
  }
  uint32_t horizonal_resolution() const {
    return GetField<uint32_t>(VT_HORIZONAL_RESOLUTION, 4294967295);
  }
  uint16_t vertical_field_of_view() const {
    return GetField<uint16_t>(VT_VERTICAL_FIELD_OF_VIEW, 65535);
  }
  uint32_t vertical_resolution() const {
    return GetField<uint32_t>(VT_VERTICAL_RESOLUTION, 4294967295);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_NODE) &&
           verifier.VerifyTable(node()) &&
           VerifyOffset(verifier, VT_POS) &&
           verifier.VerifyTable(pos()) &&
           VerifyField<uint16_t>(verifier, VT_DIRECTION, 2) &&
           VerifyField<uint8_t>(verifier, VT_DIRECTION8, 1) &&
           VerifyField<uint64_t>(verifier, VT_PERCEPTION_RADIUS, 8) &&
           VerifyField<uint32_t>(verifier, VT_SCAN_FREQUENCY, 4) &&
           VerifyField<uint16_t>(verifier, VT_HORIZONAL_FIELD_OF_VIEW, 2) &&
           VerifyField<uint32_t>(verifier, VT_HORIZONAL_RESOLUTION, 4) &&
           VerifyField<uint16_t>(verifier, VT_VERTICAL_FIELD_OF_VIEW, 2) &&
           VerifyField<uint32_t>(verifier, VT_VERTICAL_RESOLUTION, 4) &&
           verifier.EndTable();
  }
};

struct DF_LidarModuleInfoBuilder {
  typedef DF_LidarModuleInfo Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_node(::flatbuffers::Offset<MECData::DF_NodeReferenceID> node) {
    fbb_.AddOffset(DF_LidarModuleInfo::VT_NODE, node);
  }
  void add_pos(::flatbuffers::Offset<MECData::DF_Position3D> pos) {
    fbb_.AddOffset(DF_LidarModuleInfo::VT_POS, pos);
  }
  void add_direction(uint16_t direction) {
    fbb_.AddElement<uint16_t>(DF_LidarModuleInfo::VT_DIRECTION, direction, 65535);
  }
  void add_direction8(MECData::DE_Direction8 direction8) {
    fbb_.AddElement<uint8_t>(DF_LidarModuleInfo::VT_DIRECTION8, static_cast<uint8_t>(direction8), 0);
  }
  void add_perception_radius(uint64_t perception_radius) {
    fbb_.AddElement<uint64_t>(DF_LidarModuleInfo::VT_PERCEPTION_RADIUS, perception_radius, 0);
  }
  void add_scan_frequency(uint32_t scan_frequency) {
    fbb_.AddElement<uint32_t>(DF_LidarModuleInfo::VT_SCAN_FREQUENCY, scan_frequency, 0);
  }
  void add_horizonal_field_of_view(uint16_t horizonal_field_of_view) {
    fbb_.AddElement<uint16_t>(DF_LidarModuleInfo::VT_HORIZONAL_FIELD_OF_VIEW, horizonal_field_of_view, 65535);
  }
  void add_horizonal_resolution(uint32_t horizonal_resolution) {
    fbb_.AddElement<uint32_t>(DF_LidarModuleInfo::VT_HORIZONAL_RESOLUTION, horizonal_resolution, 4294967295);
  }
  void add_vertical_field_of_view(uint16_t vertical_field_of_view) {
    fbb_.AddElement<uint16_t>(DF_LidarModuleInfo::VT_VERTICAL_FIELD_OF_VIEW, vertical_field_of_view, 65535);
  }
  void add_vertical_resolution(uint32_t vertical_resolution) {
    fbb_.AddElement<uint32_t>(DF_LidarModuleInfo::VT_VERTICAL_RESOLUTION, vertical_resolution, 4294967295);
  }
  explicit DF_LidarModuleInfoBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_LidarModuleInfo> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_LidarModuleInfo>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_LidarModuleInfo> CreateDF_LidarModuleInfo(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<MECData::DF_NodeReferenceID> node = 0,
    ::flatbuffers::Offset<MECData::DF_Position3D> pos = 0,
    uint16_t direction = 65535,
    MECData::DE_Direction8 direction8 = MECData::DE_Direction8_N,
    uint64_t perception_radius = 0,
    uint32_t scan_frequency = 0,
    uint16_t horizonal_field_of_view = 65535,
    uint32_t horizonal_resolution = 4294967295,
    uint16_t vertical_field_of_view = 65535,
    uint32_t vertical_resolution = 4294967295) {
  DF_LidarModuleInfoBuilder builder_(_fbb);
  builder_.add_perception_radius(perception_radius);
  builder_.add_vertical_resolution(vertical_resolution);
  builder_.add_horizonal_resolution(horizonal_resolution);
  builder_.add_scan_frequency(scan_frequency);
  builder_.add_pos(pos);
  builder_.add_node(node);
  builder_.add_vertical_field_of_view(vertical_field_of_view);
  builder_.add_horizonal_field_of_view(horizonal_field_of_view);
  builder_.add_direction(direction);
  builder_.add_direction8(direction8);
  return builder_.Finish();
}

inline bool VerifyDF_DeviceSpecificInfo(::flatbuffers::Verifier &verifier, const void *obj, DF_DeviceSpecificInfo type) {
  switch (type) {
    case DF_DeviceSpecificInfo_NONE: {
      return true;
    }
    case DF_DeviceSpecificInfo_DF_CameraModuleInfo: {
      auto ptr = reinterpret_cast<const MECData::DF_CameraModuleInfo *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case DF_DeviceSpecificInfo_DF_MMWRadarModuleInfo: {
      auto ptr = reinterpret_cast<const MECData::DF_MMWRadarModuleInfo *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case DF_DeviceSpecificInfo_DF_TrafficSignalModuleInfo: {
      auto ptr = reinterpret_cast<const MECData::DF_TrafficSignalModuleInfo *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case DF_DeviceSpecificInfo_DF_RSUModuleInfo: {
      auto ptr = reinterpret_cast<const MECData::DF_RSUModuleInfo *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case DF_DeviceSpecificInfo_DF_CloudInfo: {
      auto ptr = reinterpret_cast<const MECData::DF_CloudInfo *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case DF_DeviceSpecificInfo_DF_LidarModuleInfo: {
      auto ptr = reinterpret_cast<const MECData::DF_LidarModuleInfo *>(obj);
      return verifier.VerifyTable(ptr);
    }
    default: return true;
  }
}

inline bool VerifyDF_DeviceSpecificInfoVector(::flatbuffers::Verifier &verifier, const ::flatbuffers::Vector<::flatbuffers::Offset<void>> *values, const ::flatbuffers::Vector<uint8_t> *types) {
  if (!values || !types) return !values && !types;
  if (values->size() != types->size()) return false;
  for (::flatbuffers::uoffset_t i = 0; i < values->size(); ++i) {
    if (!VerifyDF_DeviceSpecificInfo(
        verifier,  values->Get(i), types->GetEnum<DF_DeviceSpecificInfo>(i))) {
      return false;
    }
  }
  return true;
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_DEVICESPECIFICINFO_MECDATA_H_
