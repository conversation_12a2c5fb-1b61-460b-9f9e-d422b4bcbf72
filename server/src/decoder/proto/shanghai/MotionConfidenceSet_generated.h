// automatically generated by the FlatBuffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_MOTIONCONFIDENCESET_MECDATA_H_
#define FLATBUFFERS_GENERATED_MOTIONCONFIDENCESET_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

#include "HeadingConfidence_generated.h"
#include "SpeedConfidence_generated.h"
#include "SteeringWheelAngleConfidence_generated.h"

namespace MECData {

struct DF_MotionConfidenceSet;
struct DF_MotionConfidenceSetBuilder;

struct DF_MotionConfidenceSet FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_MotionConfidenceSetBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_SPEEDCFD = 4,
    VT_HEADINGCFD = 6,
    VT_STEERCFD = 8
  };
  MECData::DE_SpeedConfidence speedCfd() const {
    return static_cast<MECData::DE_SpeedConfidence>(GetField<int8_t>(VT_SPEEDCFD, 0));
  }
  MECData::DE_HeadingConfidence headingCfd() const {
    return static_cast<MECData::DE_HeadingConfidence>(GetField<int8_t>(VT_HEADINGCFD, 0));
  }
  MECData::DE_SteeringWheelAngleConfidence steerCfd() const {
    return static_cast<MECData::DE_SteeringWheelAngleConfidence>(GetField<int8_t>(VT_STEERCFD, 0));
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<int8_t>(verifier, VT_SPEEDCFD, 1) &&
           VerifyField<int8_t>(verifier, VT_HEADINGCFD, 1) &&
           VerifyField<int8_t>(verifier, VT_STEERCFD, 1) &&
           verifier.EndTable();
  }
};

struct DF_MotionConfidenceSetBuilder {
  typedef DF_MotionConfidenceSet Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_speedCfd(MECData::DE_SpeedConfidence speedCfd) {
    fbb_.AddElement<int8_t>(DF_MotionConfidenceSet::VT_SPEEDCFD, static_cast<int8_t>(speedCfd), 0);
  }
  void add_headingCfd(MECData::DE_HeadingConfidence headingCfd) {
    fbb_.AddElement<int8_t>(DF_MotionConfidenceSet::VT_HEADINGCFD, static_cast<int8_t>(headingCfd), 0);
  }
  void add_steerCfd(MECData::DE_SteeringWheelAngleConfidence steerCfd) {
    fbb_.AddElement<int8_t>(DF_MotionConfidenceSet::VT_STEERCFD, static_cast<int8_t>(steerCfd), 0);
  }
  explicit DF_MotionConfidenceSetBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_MotionConfidenceSet> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_MotionConfidenceSet>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_MotionConfidenceSet> CreateDF_MotionConfidenceSet(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    MECData::DE_SpeedConfidence speedCfd = MECData::DE_SpeedConfidence_unavailable,
    MECData::DE_HeadingConfidence headingCfd = MECData::DE_HeadingConfidence_unavailable,
    MECData::DE_SteeringWheelAngleConfidence steerCfd = MECData::DE_SteeringWheelAngleConfidence_unavailable) {
  DF_MotionConfidenceSetBuilder builder_(_fbb);
  builder_.add_steerCfd(steerCfd);
  builder_.add_headingCfd(headingCfd);
  builder_.add_speedCfd(speedCfd);
  return builder_.Finish();
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_MOTIONCONFIDENCESET_MECDATA_H_
