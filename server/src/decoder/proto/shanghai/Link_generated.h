// automatically generated by the FlatBuffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_LINK_MECDATA_H_
#define FLATBUFFERS_GENERATED_LINK_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

#include "Lane_generated.h"
#include "Movement_generated.h"
#include "NodeReferenceID_generated.h"
#include "RegulatorySpeedLimit_generated.h"
#include "RoadPoint_generated.h"

namespace MECData {

struct DF_Link;
struct DF_LinkBuilder;

struct DF_Link FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_LinkBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_NAME = 4,
    VT_UPSTREAMNODEID = 6,
    VT_SPEEDLIMITS = 8,
    VT_LANEWIDTH = 10,
    VT_POINTS = 12,
    VT_MOVEMENTS = 14,
    VT_LANES = 16
  };
  const ::flatbuffers::String *name() const {
    return GetPointer<const ::flatbuffers::String *>(VT_NAME);
  }
  const MECData::DF_NodeReferenceID *upstreamNodeId() const {
    return GetPointer<const MECData::DF_NodeReferenceID *>(VT_UPSTREAMNODEID);
  }
  const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_RegulatorySpeedLimit>> *speedLimits() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_RegulatorySpeedLimit>> *>(VT_SPEEDLIMITS);
  }
  uint16_t laneWidth() const {
    return GetField<uint16_t>(VT_LANEWIDTH, 0);
  }
  const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_RoadPoint>> *points() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_RoadPoint>> *>(VT_POINTS);
  }
  const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_Movement>> *movements() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_Movement>> *>(VT_MOVEMENTS);
  }
  const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_Lane>> *lanes() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_Lane>> *>(VT_LANES);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_NAME) &&
           verifier.VerifyString(name()) &&
           VerifyOffsetRequired(verifier, VT_UPSTREAMNODEID) &&
           verifier.VerifyTable(upstreamNodeId()) &&
           VerifyOffset(verifier, VT_SPEEDLIMITS) &&
           verifier.VerifyVector(speedLimits()) &&
           verifier.VerifyVectorOfTables(speedLimits()) &&
           VerifyField<uint16_t>(verifier, VT_LANEWIDTH, 2) &&
           VerifyOffset(verifier, VT_POINTS) &&
           verifier.VerifyVector(points()) &&
           verifier.VerifyVectorOfTables(points()) &&
           VerifyOffset(verifier, VT_MOVEMENTS) &&
           verifier.VerifyVector(movements()) &&
           verifier.VerifyVectorOfTables(movements()) &&
           VerifyOffsetRequired(verifier, VT_LANES) &&
           verifier.VerifyVector(lanes()) &&
           verifier.VerifyVectorOfTables(lanes()) &&
           verifier.EndTable();
  }
};

struct DF_LinkBuilder {
  typedef DF_Link Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_name(::flatbuffers::Offset<::flatbuffers::String> name) {
    fbb_.AddOffset(DF_Link::VT_NAME, name);
  }
  void add_upstreamNodeId(::flatbuffers::Offset<MECData::DF_NodeReferenceID> upstreamNodeId) {
    fbb_.AddOffset(DF_Link::VT_UPSTREAMNODEID, upstreamNodeId);
  }
  void add_speedLimits(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_RegulatorySpeedLimit>>> speedLimits) {
    fbb_.AddOffset(DF_Link::VT_SPEEDLIMITS, speedLimits);
  }
  void add_laneWidth(uint16_t laneWidth) {
    fbb_.AddElement<uint16_t>(DF_Link::VT_LANEWIDTH, laneWidth, 0);
  }
  void add_points(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_RoadPoint>>> points) {
    fbb_.AddOffset(DF_Link::VT_POINTS, points);
  }
  void add_movements(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_Movement>>> movements) {
    fbb_.AddOffset(DF_Link::VT_MOVEMENTS, movements);
  }
  void add_lanes(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_Lane>>> lanes) {
    fbb_.AddOffset(DF_Link::VT_LANES, lanes);
  }
  explicit DF_LinkBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_Link> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_Link>(end);
    fbb_.Required(o, DF_Link::VT_UPSTREAMNODEID);
    fbb_.Required(o, DF_Link::VT_LANES);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_Link> CreateDF_Link(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::String> name = 0,
    ::flatbuffers::Offset<MECData::DF_NodeReferenceID> upstreamNodeId = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_RegulatorySpeedLimit>>> speedLimits = 0,
    uint16_t laneWidth = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_RoadPoint>>> points = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_Movement>>> movements = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_Lane>>> lanes = 0) {
  DF_LinkBuilder builder_(_fbb);
  builder_.add_lanes(lanes);
  builder_.add_movements(movements);
  builder_.add_points(points);
  builder_.add_speedLimits(speedLimits);
  builder_.add_upstreamNodeId(upstreamNodeId);
  builder_.add_name(name);
  builder_.add_laneWidth(laneWidth);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DF_Link> CreateDF_LinkDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const char *name = nullptr,
    ::flatbuffers::Offset<MECData::DF_NodeReferenceID> upstreamNodeId = 0,
    const std::vector<::flatbuffers::Offset<MECData::DF_RegulatorySpeedLimit>> *speedLimits = nullptr,
    uint16_t laneWidth = 0,
    const std::vector<::flatbuffers::Offset<MECData::DF_RoadPoint>> *points = nullptr,
    const std::vector<::flatbuffers::Offset<MECData::DF_Movement>> *movements = nullptr,
    const std::vector<::flatbuffers::Offset<MECData::DF_Lane>> *lanes = nullptr) {
  auto name__ = name ? _fbb.CreateString(name) : 0;
  auto speedLimits__ = speedLimits ? _fbb.CreateVector<::flatbuffers::Offset<MECData::DF_RegulatorySpeedLimit>>(*speedLimits) : 0;
  auto points__ = points ? _fbb.CreateVector<::flatbuffers::Offset<MECData::DF_RoadPoint>>(*points) : 0;
  auto movements__ = movements ? _fbb.CreateVector<::flatbuffers::Offset<MECData::DF_Movement>>(*movements) : 0;
  auto lanes__ = lanes ? _fbb.CreateVector<::flatbuffers::Offset<MECData::DF_Lane>>(*lanes) : 0;
  return MECData::CreateDF_Link(
      _fbb,
      name__,
      upstreamNodeId,
      speedLimits__,
      laneWidth,
      points__,
      movements__,
      lanes__);
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_LINK_MECDATA_H_
