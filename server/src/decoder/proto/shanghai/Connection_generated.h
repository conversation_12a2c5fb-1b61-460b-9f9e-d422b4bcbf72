// automatically generated by the <PERSON><PERSON>uffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_CONNECTION_MECDATA_H_
#define FLATBUFFERS_GENERATED_CONNECTION_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

#include "ConnectingLane_generated.h"
#include "NodeReferenceID_generated.h"

namespace MECData {

struct DF_Connection;
struct DF_ConnectionBuilder;

struct DF_Connection FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_ConnectionBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_REMOTEINTERSECTION = 4,
    VT_CONNECTINGLANE = 6,
    VT_PHASEID = 8
  };
  const MECData::DF_NodeReferenceID *remoteIntersection() const {
    return GetPointer<const MECData::DF_NodeReferenceID *>(VT_REMOTEINTERSECTION);
  }
  const MECData::DF_ConnectingLane *connectingLane() const {
    return GetPointer<const MECData::DF_ConnectingLane *>(VT_CONNECTINGLANE);
  }
  uint8_t phaseid() const {
    return GetField<uint8_t>(VT_PHASEID, 0);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffsetRequired(verifier, VT_REMOTEINTERSECTION) &&
           verifier.VerifyTable(remoteIntersection()) &&
           VerifyOffset(verifier, VT_CONNECTINGLANE) &&
           verifier.VerifyTable(connectingLane()) &&
           VerifyField<uint8_t>(verifier, VT_PHASEID, 1) &&
           verifier.EndTable();
  }
};

struct DF_ConnectionBuilder {
  typedef DF_Connection Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_remoteIntersection(::flatbuffers::Offset<MECData::DF_NodeReferenceID> remoteIntersection) {
    fbb_.AddOffset(DF_Connection::VT_REMOTEINTERSECTION, remoteIntersection);
  }
  void add_connectingLane(::flatbuffers::Offset<MECData::DF_ConnectingLane> connectingLane) {
    fbb_.AddOffset(DF_Connection::VT_CONNECTINGLANE, connectingLane);
  }
  void add_phaseid(uint8_t phaseid) {
    fbb_.AddElement<uint8_t>(DF_Connection::VT_PHASEID, phaseid, 0);
  }
  explicit DF_ConnectionBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_Connection> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_Connection>(end);
    fbb_.Required(o, DF_Connection::VT_REMOTEINTERSECTION);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_Connection> CreateDF_Connection(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<MECData::DF_NodeReferenceID> remoteIntersection = 0,
    ::flatbuffers::Offset<MECData::DF_ConnectingLane> connectingLane = 0,
    uint8_t phaseid = 0) {
  DF_ConnectionBuilder builder_(_fbb);
  builder_.add_connectingLane(connectingLane);
  builder_.add_remoteIntersection(remoteIntersection);
  builder_.add_phaseid(phaseid);
  return builder_.Finish();
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_CONNECTION_MECDATA_H_
