// automatically generated by the FlatBuffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_PACKETFLOWRECORD_MECDATA_H_
#define FLATBUFFERS_GENERATED_PACKETFLOWRECORD_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

namespace MECData {

struct DE_TimepointCount;
struct DE_TimepointCountBuilder;

struct DF_PacketFlowCount;
struct DF_PacketFlowCountBuilder;

struct DF_PacketFlowRecord;
struct DF_PacketFlowRecordBuilder;

struct DE_TimepointCount FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DE_TimepointCountBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_GEN_TIME = 4,
    VT_COUNT = 6
  };
  int64_t gen_time() const {
    return GetField<int64_t>(VT_GEN_TIME, 0);
  }
  uint32_t count() const {
    return GetField<uint32_t>(VT_COUNT, 0);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<int64_t>(verifier, VT_GEN_TIME, 8) &&
           VerifyField<uint32_t>(verifier, VT_COUNT, 4) &&
           verifier.EndTable();
  }
};

struct DE_TimepointCountBuilder {
  typedef DE_TimepointCount Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_gen_time(int64_t gen_time) {
    fbb_.AddElement<int64_t>(DE_TimepointCount::VT_GEN_TIME, gen_time, 0);
  }
  void add_count(uint32_t count) {
    fbb_.AddElement<uint32_t>(DE_TimepointCount::VT_COUNT, count, 0);
  }
  explicit DE_TimepointCountBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DE_TimepointCount> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DE_TimepointCount>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DE_TimepointCount> CreateDE_TimepointCount(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    int64_t gen_time = 0,
    uint32_t count = 0) {
  DE_TimepointCountBuilder builder_(_fbb);
  builder_.add_gen_time(gen_time);
  builder_.add_count(count);
  return builder_.Finish();
}

struct DF_PacketFlowCount FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_PacketFlowCountBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_DATA_TYPE = 4,
    VT_RESERVED = 6,
    VT_SRC_MODULE_ID = 8,
    VT_DST_MODULE_ID = 10,
    VT_COUNTS = 12,
    VT_DESCRIPTION = 14
  };
  uint16_t data_type() const {
    return GetField<uint16_t>(VT_DATA_TYPE, 0);
  }
  bool reserved() const {
    return GetField<uint8_t>(VT_RESERVED, 0) != 0;
  }
  uint16_t src_module_id() const {
    return GetField<uint16_t>(VT_SRC_MODULE_ID, 65535);
  }
  uint16_t dst_module_id() const {
    return GetField<uint16_t>(VT_DST_MODULE_ID, 65535);
  }
  const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DE_TimepointCount>> *counts() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DE_TimepointCount>> *>(VT_COUNTS);
  }
  const ::flatbuffers::String *description() const {
    return GetPointer<const ::flatbuffers::String *>(VT_DESCRIPTION);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<uint16_t>(verifier, VT_DATA_TYPE, 2) &&
           VerifyField<uint8_t>(verifier, VT_RESERVED, 1) &&
           VerifyField<uint16_t>(verifier, VT_SRC_MODULE_ID, 2) &&
           VerifyField<uint16_t>(verifier, VT_DST_MODULE_ID, 2) &&
           VerifyOffset(verifier, VT_COUNTS) &&
           verifier.VerifyVector(counts()) &&
           verifier.VerifyVectorOfTables(counts()) &&
           VerifyOffset(verifier, VT_DESCRIPTION) &&
           verifier.VerifyString(description()) &&
           verifier.EndTable();
  }
};

struct DF_PacketFlowCountBuilder {
  typedef DF_PacketFlowCount Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_data_type(uint16_t data_type) {
    fbb_.AddElement<uint16_t>(DF_PacketFlowCount::VT_DATA_TYPE, data_type, 0);
  }
  void add_reserved(bool reserved) {
    fbb_.AddElement<uint8_t>(DF_PacketFlowCount::VT_RESERVED, static_cast<uint8_t>(reserved), 0);
  }
  void add_src_module_id(uint16_t src_module_id) {
    fbb_.AddElement<uint16_t>(DF_PacketFlowCount::VT_SRC_MODULE_ID, src_module_id, 65535);
  }
  void add_dst_module_id(uint16_t dst_module_id) {
    fbb_.AddElement<uint16_t>(DF_PacketFlowCount::VT_DST_MODULE_ID, dst_module_id, 65535);
  }
  void add_counts(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DE_TimepointCount>>> counts) {
    fbb_.AddOffset(DF_PacketFlowCount::VT_COUNTS, counts);
  }
  void add_description(::flatbuffers::Offset<::flatbuffers::String> description) {
    fbb_.AddOffset(DF_PacketFlowCount::VT_DESCRIPTION, description);
  }
  explicit DF_PacketFlowCountBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_PacketFlowCount> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_PacketFlowCount>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_PacketFlowCount> CreateDF_PacketFlowCount(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    uint16_t data_type = 0,
    bool reserved = false,
    uint16_t src_module_id = 65535,
    uint16_t dst_module_id = 65535,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DE_TimepointCount>>> counts = 0,
    ::flatbuffers::Offset<::flatbuffers::String> description = 0) {
  DF_PacketFlowCountBuilder builder_(_fbb);
  builder_.add_description(description);
  builder_.add_counts(counts);
  builder_.add_dst_module_id(dst_module_id);
  builder_.add_src_module_id(src_module_id);
  builder_.add_data_type(data_type);
  builder_.add_reserved(reserved);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DF_PacketFlowCount> CreateDF_PacketFlowCountDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    uint16_t data_type = 0,
    bool reserved = false,
    uint16_t src_module_id = 65535,
    uint16_t dst_module_id = 65535,
    const std::vector<::flatbuffers::Offset<MECData::DE_TimepointCount>> *counts = nullptr,
    const char *description = nullptr) {
  auto counts__ = counts ? _fbb.CreateVector<::flatbuffers::Offset<MECData::DE_TimepointCount>>(*counts) : 0;
  auto description__ = description ? _fbb.CreateString(description) : 0;
  return MECData::CreateDF_PacketFlowCount(
      _fbb,
      data_type,
      reserved,
      src_module_id,
      dst_module_id,
      counts__,
      description__);
}

struct DF_PacketFlowRecord FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_PacketFlowRecordBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_FLOW_COUNTS = 4
  };
  const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_PacketFlowCount>> *flow_counts() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_PacketFlowCount>> *>(VT_FLOW_COUNTS);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_FLOW_COUNTS) &&
           verifier.VerifyVector(flow_counts()) &&
           verifier.VerifyVectorOfTables(flow_counts()) &&
           verifier.EndTable();
  }
};

struct DF_PacketFlowRecordBuilder {
  typedef DF_PacketFlowRecord Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_flow_counts(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_PacketFlowCount>>> flow_counts) {
    fbb_.AddOffset(DF_PacketFlowRecord::VT_FLOW_COUNTS, flow_counts);
  }
  explicit DF_PacketFlowRecordBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_PacketFlowRecord> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_PacketFlowRecord>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_PacketFlowRecord> CreateDF_PacketFlowRecord(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_PacketFlowCount>>> flow_counts = 0) {
  DF_PacketFlowRecordBuilder builder_(_fbb);
  builder_.add_flow_counts(flow_counts);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DF_PacketFlowRecord> CreateDF_PacketFlowRecordDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const std::vector<::flatbuffers::Offset<MECData::DF_PacketFlowCount>> *flow_counts = nullptr) {
  auto flow_counts__ = flow_counts ? _fbb.CreateVector<::flatbuffers::Offset<MECData::DF_PacketFlowCount>>(*flow_counts) : 0;
  return MECData::CreateDF_PacketFlowRecord(
      _fbb,
      flow_counts__);
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_PACKETFLOWRECORD_MECDATA_H_
