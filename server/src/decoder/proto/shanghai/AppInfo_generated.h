// automatically generated by the <PERSON>Buffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_APPINFO_MECDATA_H_
#define FLATBUFFERS_GENERATED_APPINFO_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

#include "AppConfig_generated.h"
#include "DeploySpecificInfo_generated.h"
#include "DeviceInfo_generated.h"
#include "DeviceSpecificInfo_generated.h"
#include "Image_generated.h"
#include "ModuleIOConfig_generated.h"
#include "ModuleMiscellaneous_generated.h"
#include "ModulePerformance_generated.h"
#include "OperationTags_generated.h"
#include "ZStub_generated.h"

namespace MECData {

struct DF_InstanceUptime;
struct DF_InstanceUptimeBuilder;

struct DF_ResourceUsage;
struct DF_ResourceUsageBuilder;

struct DF_InstanceRuntimeAbnormal;
struct DF_InstanceRuntimeAbnormalBuilder;

struct DF_AppInstanceInfo;
struct DF_AppInstanceInfoBuilder;

struct MSG_AppInfo;
struct MSG_AppInfoBuilder;

struct DF_InstanceUptime FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_InstanceUptimeBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_LAST_START_TIME = 4,
    VT_CONTINUOUS_UPTIME = 6
  };
  int64_t last_start_time() const {
    return GetField<int64_t>(VT_LAST_START_TIME, 0);
  }
  int64_t continuous_uptime() const {
    return GetField<int64_t>(VT_CONTINUOUS_UPTIME, 0);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<int64_t>(verifier, VT_LAST_START_TIME, 8) &&
           VerifyField<int64_t>(verifier, VT_CONTINUOUS_UPTIME, 8) &&
           verifier.EndTable();
  }
};

struct DF_InstanceUptimeBuilder {
  typedef DF_InstanceUptime Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_last_start_time(int64_t last_start_time) {
    fbb_.AddElement<int64_t>(DF_InstanceUptime::VT_LAST_START_TIME, last_start_time, 0);
  }
  void add_continuous_uptime(int64_t continuous_uptime) {
    fbb_.AddElement<int64_t>(DF_InstanceUptime::VT_CONTINUOUS_UPTIME, continuous_uptime, 0);
  }
  explicit DF_InstanceUptimeBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_InstanceUptime> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_InstanceUptime>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_InstanceUptime> CreateDF_InstanceUptime(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    int64_t last_start_time = 0,
    int64_t continuous_uptime = 0) {
  DF_InstanceUptimeBuilder builder_(_fbb);
  builder_.add_continuous_uptime(continuous_uptime);
  builder_.add_last_start_time(last_start_time);
  return builder_.Finish();
}

struct DF_ResourceUsage FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_ResourceUsageBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_CPU = 4,
    VT_MEMORY = 6,
    VT_GPU = 8,
    VT_STORAGE = 10
  };
  uint16_t cpu() const {
    return GetField<uint16_t>(VT_CPU, 65535);
  }
  uint16_t memory() const {
    return GetField<uint16_t>(VT_MEMORY, 65535);
  }
  uint16_t gpu() const {
    return GetField<uint16_t>(VT_GPU, 65535);
  }
  uint16_t storage() const {
    return GetField<uint16_t>(VT_STORAGE, 65535);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<uint16_t>(verifier, VT_CPU, 2) &&
           VerifyField<uint16_t>(verifier, VT_MEMORY, 2) &&
           VerifyField<uint16_t>(verifier, VT_GPU, 2) &&
           VerifyField<uint16_t>(verifier, VT_STORAGE, 2) &&
           verifier.EndTable();
  }
};

struct DF_ResourceUsageBuilder {
  typedef DF_ResourceUsage Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_cpu(uint16_t cpu) {
    fbb_.AddElement<uint16_t>(DF_ResourceUsage::VT_CPU, cpu, 65535);
  }
  void add_memory(uint16_t memory) {
    fbb_.AddElement<uint16_t>(DF_ResourceUsage::VT_MEMORY, memory, 65535);
  }
  void add_gpu(uint16_t gpu) {
    fbb_.AddElement<uint16_t>(DF_ResourceUsage::VT_GPU, gpu, 65535);
  }
  void add_storage(uint16_t storage) {
    fbb_.AddElement<uint16_t>(DF_ResourceUsage::VT_STORAGE, storage, 65535);
  }
  explicit DF_ResourceUsageBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_ResourceUsage> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_ResourceUsage>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_ResourceUsage> CreateDF_ResourceUsage(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    uint16_t cpu = 65535,
    uint16_t memory = 65535,
    uint16_t gpu = 65535,
    uint16_t storage = 65535) {
  DF_ResourceUsageBuilder builder_(_fbb);
  builder_.add_storage(storage);
  builder_.add_gpu(gpu);
  builder_.add_memory(memory);
  builder_.add_cpu(cpu);
  return builder_.Finish();
}

struct DF_InstanceRuntimeAbnormal FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_InstanceRuntimeAbnormalBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_CODE = 4,
    VT_DESC = 6
  };
  int32_t code() const {
    return GetField<int32_t>(VT_CODE, 0);
  }
  const ::flatbuffers::String *desc() const {
    return GetPointer<const ::flatbuffers::String *>(VT_DESC);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<int32_t>(verifier, VT_CODE, 4) &&
           VerifyOffset(verifier, VT_DESC) &&
           verifier.VerifyString(desc()) &&
           verifier.EndTable();
  }
};

struct DF_InstanceRuntimeAbnormalBuilder {
  typedef DF_InstanceRuntimeAbnormal Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_code(int32_t code) {
    fbb_.AddElement<int32_t>(DF_InstanceRuntimeAbnormal::VT_CODE, code, 0);
  }
  void add_desc(::flatbuffers::Offset<::flatbuffers::String> desc) {
    fbb_.AddOffset(DF_InstanceRuntimeAbnormal::VT_DESC, desc);
  }
  explicit DF_InstanceRuntimeAbnormalBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_InstanceRuntimeAbnormal> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_InstanceRuntimeAbnormal>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_InstanceRuntimeAbnormal> CreateDF_InstanceRuntimeAbnormal(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    int32_t code = 0,
    ::flatbuffers::Offset<::flatbuffers::String> desc = 0) {
  DF_InstanceRuntimeAbnormalBuilder builder_(_fbb);
  builder_.add_desc(desc);
  builder_.add_code(code);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DF_InstanceRuntimeAbnormal> CreateDF_InstanceRuntimeAbnormalDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    int32_t code = 0,
    const char *desc = nullptr) {
  auto desc__ = desc ? _fbb.CreateString(desc) : 0;
  return MECData::CreateDF_InstanceRuntimeAbnormal(
      _fbb,
      code,
      desc__);
}

struct DF_AppInstanceInfo FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_AppInstanceInfoBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_ID = 4,
    VT_NAME = 6,
    VT_STATUS = 8,
    VT_UPTIME = 10,
    VT_RESOURCES = 12,
    VT_PERFORMANCE_INDEXES = 14,
    VT_DEVICE_INFO = 16,
    VT_CONFIG = 18,
    VT_RUNTIME_ABNORMAL = 20
  };
  uint16_t id() const {
    return GetField<uint16_t>(VT_ID, 0);
  }
  const ::flatbuffers::String *name() const {
    return GetPointer<const ::flatbuffers::String *>(VT_NAME);
  }
  MECData::DE_ModuleStatus status() const {
    return static_cast<MECData::DE_ModuleStatus>(GetField<uint8_t>(VT_STATUS, 0));
  }
  const MECData::DF_InstanceUptime *uptime() const {
    return GetPointer<const MECData::DF_InstanceUptime *>(VT_UPTIME);
  }
  const MECData::DF_ResourceUsage *resources() const {
    return GetPointer<const MECData::DF_ResourceUsage *>(VT_RESOURCES);
  }
  const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_ModulePerformance>> *performance_indexes() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_ModulePerformance>> *>(VT_PERFORMANCE_INDEXES);
  }
  const MECData::DF_DeviceInfo *device_info() const {
    return GetPointer<const MECData::DF_DeviceInfo *>(VT_DEVICE_INFO);
  }
  const MECData::DF_AppInstanceConfig *config() const {
    return GetPointer<const MECData::DF_AppInstanceConfig *>(VT_CONFIG);
  }
  const MECData::DF_InstanceRuntimeAbnormal *runtime_abnormal() const {
    return GetPointer<const MECData::DF_InstanceRuntimeAbnormal *>(VT_RUNTIME_ABNORMAL);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<uint16_t>(verifier, VT_ID, 2) &&
           VerifyOffset(verifier, VT_NAME) &&
           verifier.VerifyString(name()) &&
           VerifyField<uint8_t>(verifier, VT_STATUS, 1) &&
           VerifyOffset(verifier, VT_UPTIME) &&
           verifier.VerifyTable(uptime()) &&
           VerifyOffset(verifier, VT_RESOURCES) &&
           verifier.VerifyTable(resources()) &&
           VerifyOffset(verifier, VT_PERFORMANCE_INDEXES) &&
           verifier.VerifyVector(performance_indexes()) &&
           verifier.VerifyVectorOfTables(performance_indexes()) &&
           VerifyOffset(verifier, VT_DEVICE_INFO) &&
           verifier.VerifyTable(device_info()) &&
           VerifyOffset(verifier, VT_CONFIG) &&
           verifier.VerifyTable(config()) &&
           VerifyOffset(verifier, VT_RUNTIME_ABNORMAL) &&
           verifier.VerifyTable(runtime_abnormal()) &&
           verifier.EndTable();
  }
};

struct DF_AppInstanceInfoBuilder {
  typedef DF_AppInstanceInfo Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_id(uint16_t id) {
    fbb_.AddElement<uint16_t>(DF_AppInstanceInfo::VT_ID, id, 0);
  }
  void add_name(::flatbuffers::Offset<::flatbuffers::String> name) {
    fbb_.AddOffset(DF_AppInstanceInfo::VT_NAME, name);
  }
  void add_status(MECData::DE_ModuleStatus status) {
    fbb_.AddElement<uint8_t>(DF_AppInstanceInfo::VT_STATUS, static_cast<uint8_t>(status), 0);
  }
  void add_uptime(::flatbuffers::Offset<MECData::DF_InstanceUptime> uptime) {
    fbb_.AddOffset(DF_AppInstanceInfo::VT_UPTIME, uptime);
  }
  void add_resources(::flatbuffers::Offset<MECData::DF_ResourceUsage> resources) {
    fbb_.AddOffset(DF_AppInstanceInfo::VT_RESOURCES, resources);
  }
  void add_performance_indexes(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_ModulePerformance>>> performance_indexes) {
    fbb_.AddOffset(DF_AppInstanceInfo::VT_PERFORMANCE_INDEXES, performance_indexes);
  }
  void add_device_info(::flatbuffers::Offset<MECData::DF_DeviceInfo> device_info) {
    fbb_.AddOffset(DF_AppInstanceInfo::VT_DEVICE_INFO, device_info);
  }
  void add_config(::flatbuffers::Offset<MECData::DF_AppInstanceConfig> config) {
    fbb_.AddOffset(DF_AppInstanceInfo::VT_CONFIG, config);
  }
  void add_runtime_abnormal(::flatbuffers::Offset<MECData::DF_InstanceRuntimeAbnormal> runtime_abnormal) {
    fbb_.AddOffset(DF_AppInstanceInfo::VT_RUNTIME_ABNORMAL, runtime_abnormal);
  }
  explicit DF_AppInstanceInfoBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_AppInstanceInfo> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_AppInstanceInfo>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_AppInstanceInfo> CreateDF_AppInstanceInfo(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    uint16_t id = 0,
    ::flatbuffers::Offset<::flatbuffers::String> name = 0,
    MECData::DE_ModuleStatus status = MECData::DE_ModuleStatus_UNINITIALIZED,
    ::flatbuffers::Offset<MECData::DF_InstanceUptime> uptime = 0,
    ::flatbuffers::Offset<MECData::DF_ResourceUsage> resources = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_ModulePerformance>>> performance_indexes = 0,
    ::flatbuffers::Offset<MECData::DF_DeviceInfo> device_info = 0,
    ::flatbuffers::Offset<MECData::DF_AppInstanceConfig> config = 0,
    ::flatbuffers::Offset<MECData::DF_InstanceRuntimeAbnormal> runtime_abnormal = 0) {
  DF_AppInstanceInfoBuilder builder_(_fbb);
  builder_.add_runtime_abnormal(runtime_abnormal);
  builder_.add_config(config);
  builder_.add_device_info(device_info);
  builder_.add_performance_indexes(performance_indexes);
  builder_.add_resources(resources);
  builder_.add_uptime(uptime);
  builder_.add_name(name);
  builder_.add_id(id);
  builder_.add_status(status);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DF_AppInstanceInfo> CreateDF_AppInstanceInfoDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    uint16_t id = 0,
    const char *name = nullptr,
    MECData::DE_ModuleStatus status = MECData::DE_ModuleStatus_UNINITIALIZED,
    ::flatbuffers::Offset<MECData::DF_InstanceUptime> uptime = 0,
    ::flatbuffers::Offset<MECData::DF_ResourceUsage> resources = 0,
    const std::vector<::flatbuffers::Offset<MECData::DF_ModulePerformance>> *performance_indexes = nullptr,
    ::flatbuffers::Offset<MECData::DF_DeviceInfo> device_info = 0,
    ::flatbuffers::Offset<MECData::DF_AppInstanceConfig> config = 0,
    ::flatbuffers::Offset<MECData::DF_InstanceRuntimeAbnormal> runtime_abnormal = 0) {
  auto name__ = name ? _fbb.CreateString(name) : 0;
  auto performance_indexes__ = performance_indexes ? _fbb.CreateVector<::flatbuffers::Offset<MECData::DF_ModulePerformance>>(*performance_indexes) : 0;
  return MECData::CreateDF_AppInstanceInfo(
      _fbb,
      id,
      name__,
      status,
      uptime,
      resources,
      performance_indexes__,
      device_info,
      config,
      runtime_abnormal);
}

struct MSG_AppInfo FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef MSG_AppInfoBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_PACKAGE_ID = 4,
    VT_NAME = 6,
    VT_DESCRIPTION = 8,
    VT_VERSION = 10,
    VT_BUILD_DATE = 12,
    VT_GPU_DRIVER_DEPENDENCY = 14,
    VT_MECDATA_VERSION = 16,
    VT_CATEGORY = 18,
    VT_INSTANCES = 20,
    VT_CONFIG = 22,
    VT_MSG_ID = 24,
    VT_ICON = 26,
    VT_RESOURCES = 28
  };
  const ::flatbuffers::String *package_id() const {
    return GetPointer<const ::flatbuffers::String *>(VT_PACKAGE_ID);
  }
  const ::flatbuffers::String *name() const {
    return GetPointer<const ::flatbuffers::String *>(VT_NAME);
  }
  const ::flatbuffers::String *description() const {
    return GetPointer<const ::flatbuffers::String *>(VT_DESCRIPTION);
  }
  const ::flatbuffers::String *version() const {
    return GetPointer<const ::flatbuffers::String *>(VT_VERSION);
  }
  const ::flatbuffers::String *build_date() const {
    return GetPointer<const ::flatbuffers::String *>(VT_BUILD_DATE);
  }
  const ::flatbuffers::String *gpu_driver_dependency() const {
    return GetPointer<const ::flatbuffers::String *>(VT_GPU_DRIVER_DEPENDENCY);
  }
  const ::flatbuffers::String *mecdata_version() const {
    return GetPointer<const ::flatbuffers::String *>(VT_MECDATA_VERSION);
  }
  MECData::DE_ModuleCategory category() const {
    return static_cast<MECData::DE_ModuleCategory>(GetField<uint8_t>(VT_CATEGORY, 1));
  }
  const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_AppInstanceInfo>> *instances() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_AppInstanceInfo>> *>(VT_INSTANCES);
  }
  const MECData::DF_AppPackageConfig *config() const {
    return GetPointer<const MECData::DF_AppPackageConfig *>(VT_CONFIG);
  }
  int64_t msg_id() const {
    return GetField<int64_t>(VT_MSG_ID, 0);
  }
  const MECData::DF_Image *icon() const {
    return GetPointer<const MECData::DF_Image *>(VT_ICON);
  }
  const MECData::DF_ResourceUsage *resources() const {
    return GetPointer<const MECData::DF_ResourceUsage *>(VT_RESOURCES);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffsetRequired(verifier, VT_PACKAGE_ID) &&
           verifier.VerifyString(package_id()) &&
           VerifyOffsetRequired(verifier, VT_NAME) &&
           verifier.VerifyString(name()) &&
           VerifyOffset(verifier, VT_DESCRIPTION) &&
           verifier.VerifyString(description()) &&
           VerifyOffset(verifier, VT_VERSION) &&
           verifier.VerifyString(version()) &&
           VerifyOffset(verifier, VT_BUILD_DATE) &&
           verifier.VerifyString(build_date()) &&
           VerifyOffset(verifier, VT_GPU_DRIVER_DEPENDENCY) &&
           verifier.VerifyString(gpu_driver_dependency()) &&
           VerifyOffset(verifier, VT_MECDATA_VERSION) &&
           verifier.VerifyString(mecdata_version()) &&
           VerifyField<uint8_t>(verifier, VT_CATEGORY, 1) &&
           VerifyOffset(verifier, VT_INSTANCES) &&
           verifier.VerifyVector(instances()) &&
           verifier.VerifyVectorOfTables(instances()) &&
           VerifyOffset(verifier, VT_CONFIG) &&
           verifier.VerifyTable(config()) &&
           VerifyField<int64_t>(verifier, VT_MSG_ID, 8) &&
           VerifyOffset(verifier, VT_ICON) &&
           verifier.VerifyTable(icon()) &&
           VerifyOffset(verifier, VT_RESOURCES) &&
           verifier.VerifyTable(resources()) &&
           verifier.EndTable();
  }
};

struct MSG_AppInfoBuilder {
  typedef MSG_AppInfo Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_package_id(::flatbuffers::Offset<::flatbuffers::String> package_id) {
    fbb_.AddOffset(MSG_AppInfo::VT_PACKAGE_ID, package_id);
  }
  void add_name(::flatbuffers::Offset<::flatbuffers::String> name) {
    fbb_.AddOffset(MSG_AppInfo::VT_NAME, name);
  }
  void add_description(::flatbuffers::Offset<::flatbuffers::String> description) {
    fbb_.AddOffset(MSG_AppInfo::VT_DESCRIPTION, description);
  }
  void add_version(::flatbuffers::Offset<::flatbuffers::String> version) {
    fbb_.AddOffset(MSG_AppInfo::VT_VERSION, version);
  }
  void add_build_date(::flatbuffers::Offset<::flatbuffers::String> build_date) {
    fbb_.AddOffset(MSG_AppInfo::VT_BUILD_DATE, build_date);
  }
  void add_gpu_driver_dependency(::flatbuffers::Offset<::flatbuffers::String> gpu_driver_dependency) {
    fbb_.AddOffset(MSG_AppInfo::VT_GPU_DRIVER_DEPENDENCY, gpu_driver_dependency);
  }
  void add_mecdata_version(::flatbuffers::Offset<::flatbuffers::String> mecdata_version) {
    fbb_.AddOffset(MSG_AppInfo::VT_MECDATA_VERSION, mecdata_version);
  }
  void add_category(MECData::DE_ModuleCategory category) {
    fbb_.AddElement<uint8_t>(MSG_AppInfo::VT_CATEGORY, static_cast<uint8_t>(category), 1);
  }
  void add_instances(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_AppInstanceInfo>>> instances) {
    fbb_.AddOffset(MSG_AppInfo::VT_INSTANCES, instances);
  }
  void add_config(::flatbuffers::Offset<MECData::DF_AppPackageConfig> config) {
    fbb_.AddOffset(MSG_AppInfo::VT_CONFIG, config);
  }
  void add_msg_id(int64_t msg_id) {
    fbb_.AddElement<int64_t>(MSG_AppInfo::VT_MSG_ID, msg_id, 0);
  }
  void add_icon(::flatbuffers::Offset<MECData::DF_Image> icon) {
    fbb_.AddOffset(MSG_AppInfo::VT_ICON, icon);
  }
  void add_resources(::flatbuffers::Offset<MECData::DF_ResourceUsage> resources) {
    fbb_.AddOffset(MSG_AppInfo::VT_RESOURCES, resources);
  }
  explicit MSG_AppInfoBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<MSG_AppInfo> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<MSG_AppInfo>(end);
    fbb_.Required(o, MSG_AppInfo::VT_PACKAGE_ID);
    fbb_.Required(o, MSG_AppInfo::VT_NAME);
    return o;
  }
};

inline ::flatbuffers::Offset<MSG_AppInfo> CreateMSG_AppInfo(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::String> package_id = 0,
    ::flatbuffers::Offset<::flatbuffers::String> name = 0,
    ::flatbuffers::Offset<::flatbuffers::String> description = 0,
    ::flatbuffers::Offset<::flatbuffers::String> version = 0,
    ::flatbuffers::Offset<::flatbuffers::String> build_date = 0,
    ::flatbuffers::Offset<::flatbuffers::String> gpu_driver_dependency = 0,
    ::flatbuffers::Offset<::flatbuffers::String> mecdata_version = 0,
    MECData::DE_ModuleCategory category = MECData::DE_ModuleCategory_APPLICATION_ALGORITHM,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_AppInstanceInfo>>> instances = 0,
    ::flatbuffers::Offset<MECData::DF_AppPackageConfig> config = 0,
    int64_t msg_id = 0,
    ::flatbuffers::Offset<MECData::DF_Image> icon = 0,
    ::flatbuffers::Offset<MECData::DF_ResourceUsage> resources = 0) {
  MSG_AppInfoBuilder builder_(_fbb);
  builder_.add_msg_id(msg_id);
  builder_.add_resources(resources);
  builder_.add_icon(icon);
  builder_.add_config(config);
  builder_.add_instances(instances);
  builder_.add_mecdata_version(mecdata_version);
  builder_.add_gpu_driver_dependency(gpu_driver_dependency);
  builder_.add_build_date(build_date);
  builder_.add_version(version);
  builder_.add_description(description);
  builder_.add_name(name);
  builder_.add_package_id(package_id);
  builder_.add_category(category);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<MSG_AppInfo> CreateMSG_AppInfoDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const char *package_id = nullptr,
    const char *name = nullptr,
    const char *description = nullptr,
    const char *version = nullptr,
    const char *build_date = nullptr,
    const char *gpu_driver_dependency = nullptr,
    const char *mecdata_version = nullptr,
    MECData::DE_ModuleCategory category = MECData::DE_ModuleCategory_APPLICATION_ALGORITHM,
    const std::vector<::flatbuffers::Offset<MECData::DF_AppInstanceInfo>> *instances = nullptr,
    ::flatbuffers::Offset<MECData::DF_AppPackageConfig> config = 0,
    int64_t msg_id = 0,
    ::flatbuffers::Offset<MECData::DF_Image> icon = 0,
    ::flatbuffers::Offset<MECData::DF_ResourceUsage> resources = 0) {
  auto package_id__ = package_id ? _fbb.CreateString(package_id) : 0;
  auto name__ = name ? _fbb.CreateString(name) : 0;
  auto description__ = description ? _fbb.CreateString(description) : 0;
  auto version__ = version ? _fbb.CreateString(version) : 0;
  auto build_date__ = build_date ? _fbb.CreateString(build_date) : 0;
  auto gpu_driver_dependency__ = gpu_driver_dependency ? _fbb.CreateString(gpu_driver_dependency) : 0;
  auto mecdata_version__ = mecdata_version ? _fbb.CreateString(mecdata_version) : 0;
  auto instances__ = instances ? _fbb.CreateVector<::flatbuffers::Offset<MECData::DF_AppInstanceInfo>>(*instances) : 0;
  return MECData::CreateMSG_AppInfo(
      _fbb,
      package_id__,
      name__,
      description__,
      version__,
      build_date__,
      gpu_driver_dependency__,
      mecdata_version__,
      category,
      instances__,
      config,
      msg_id,
      icon,
      resources);
}

inline const MECData::MSG_AppInfo *GetMSG_AppInfo(const void *buf) {
  return ::flatbuffers::GetRoot<MECData::MSG_AppInfo>(buf);
}

inline const MECData::MSG_AppInfo *GetSizePrefixedMSG_AppInfo(const void *buf) {
  return ::flatbuffers::GetSizePrefixedRoot<MECData::MSG_AppInfo>(buf);
}

inline bool VerifyMSG_AppInfoBuffer(
    ::flatbuffers::Verifier &verifier) {
  return verifier.VerifyBuffer<MECData::MSG_AppInfo>(nullptr);
}

inline bool VerifySizePrefixedMSG_AppInfoBuffer(
    ::flatbuffers::Verifier &verifier) {
  return verifier.VerifySizePrefixedBuffer<MECData::MSG_AppInfo>(nullptr);
}

inline void FinishMSG_AppInfoBuffer(
    ::flatbuffers::FlatBufferBuilder &fbb,
    ::flatbuffers::Offset<MECData::MSG_AppInfo> root) {
  fbb.Finish(root);
}

inline void FinishSizePrefixedMSG_AppInfoBuffer(
    ::flatbuffers::FlatBufferBuilder &fbb,
    ::flatbuffers::Offset<MECData::MSG_AppInfo> root) {
  fbb.FinishSizePrefixed(root);
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_APPINFO_MECDATA_H_
