// automatically generated by the <PERSON><PERSON>uffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_NOWWEATHER_MECDATA_H_
#define FLATBUFFERS_GENERATED_NOWWEATHER_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

#include "Position3D_generated.h"

namespace MECData {

struct MSG_NowWeather;
struct MSG_NowWeatherBuilder;

struct MSG_NowWeather FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef MSG_NowWeatherBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_DIVISIONCODE = 4,
    VT_TIMEZONE = 6,
    VT_APP_ID = 8,
    VT_SUBDEVICE_ID = 10,
    VT_POS = 12,
    VT_UPDATETIME = 14,
    VT_DESCRIPTION = 16,
    VT_WEATHERCODE = 18,
    VT_TEMPERATURE = 20,
    VT_APPARENTTEMPERATURE = 22,
    VT_PRESSURE = 24,
    VT_HUMIDITY = 26,
    VT_VISIBILITY = 28,
    VT_WINDDIRECTION = 30,
    VT_WINDSPEED = 32,
    VT_WINDSCALE = 34,
    VT_CLOUDS = 36,
    VT_DEWPOINT = 38,
    VT_ROADSURTEMP = 40,
    VT_FREEZINGTEMP = 42,
    VT_WATERFILMTHICKNESS = 44,
    VT_SNOWTHICKNESS = 46,
    VT_ICETHICKNESS = 48,
    VT_SALINITY = 50,
    VT_SURFACEWETSLIPCOEFFICIENT = 52,
    VT_ROADCONDITIONS = 54,
    VT_MSG_ID = 56
  };
  uint32_t divisionCode() const {
    return GetField<uint32_t>(VT_DIVISIONCODE, 4294967295);
  }
  int16_t timezone() const {
    return GetField<int16_t>(VT_TIMEZONE, 32767);
  }
  uint16_t app_id() const {
    return GetField<uint16_t>(VT_APP_ID, 0);
  }
  uint8_t subdevice_id() const {
    return GetField<uint8_t>(VT_SUBDEVICE_ID, 0);
  }
  const MECData::DF_Position3D *pos() const {
    return GetPointer<const MECData::DF_Position3D *>(VT_POS);
  }
  uint32_t updateTime() const {
    return GetField<uint32_t>(VT_UPDATETIME, 4294967295);
  }
  const ::flatbuffers::String *description() const {
    return GetPointer<const ::flatbuffers::String *>(VT_DESCRIPTION);
  }
  uint8_t weatherCode() const {
    return GetField<uint8_t>(VT_WEATHERCODE, 255);
  }
  int16_t temperature() const {
    return GetField<int16_t>(VT_TEMPERATURE, 0);
  }
  int16_t apparentTemperature() const {
    return GetField<int16_t>(VT_APPARENTTEMPERATURE, 0);
  }
  uint16_t pressure() const {
    return GetField<uint16_t>(VT_PRESSURE, 0);
  }
  uint8_t humidity() const {
    return GetField<uint8_t>(VT_HUMIDITY, 0);
  }
  uint16_t visibility() const {
    return GetField<uint16_t>(VT_VISIBILITY, 0);
  }
  uint16_t windDirection() const {
    return GetField<uint16_t>(VT_WINDDIRECTION, 0);
  }
  uint16_t windSpeed() const {
    return GetField<uint16_t>(VT_WINDSPEED, 0);
  }
  uint8_t windScale() const {
    return GetField<uint8_t>(VT_WINDSCALE, 0);
  }
  uint8_t clouds() const {
    return GetField<uint8_t>(VT_CLOUDS, 0);
  }
  int16_t dewPoint() const {
    return GetField<int16_t>(VT_DEWPOINT, 0);
  }
  int16_t roadsurtemp() const {
    return GetField<int16_t>(VT_ROADSURTEMP, 0);
  }
  int16_t freezingtemp() const {
    return GetField<int16_t>(VT_FREEZINGTEMP, 0);
  }
  uint8_t waterfilmthickness() const {
    return GetField<uint8_t>(VT_WATERFILMTHICKNESS, 0);
  }
  uint8_t snowthickness() const {
    return GetField<uint8_t>(VT_SNOWTHICKNESS, 0);
  }
  uint8_t icethickness() const {
    return GetField<uint8_t>(VT_ICETHICKNESS, 0);
  }
  uint8_t salinity() const {
    return GetField<uint8_t>(VT_SALINITY, 0);
  }
  uint8_t surfacewetslipcoefficient() const {
    return GetField<uint8_t>(VT_SURFACEWETSLIPCOEFFICIENT, 0);
  }
  uint8_t roadconditions() const {
    return GetField<uint8_t>(VT_ROADCONDITIONS, 0);
  }
  int64_t msg_id() const {
    return GetField<int64_t>(VT_MSG_ID, 0);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<uint32_t>(verifier, VT_DIVISIONCODE, 4) &&
           VerifyField<int16_t>(verifier, VT_TIMEZONE, 2) &&
           VerifyField<uint16_t>(verifier, VT_APP_ID, 2) &&
           VerifyField<uint8_t>(verifier, VT_SUBDEVICE_ID, 1) &&
           VerifyOffsetRequired(verifier, VT_POS) &&
           verifier.VerifyTable(pos()) &&
           VerifyField<uint32_t>(verifier, VT_UPDATETIME, 4) &&
           VerifyOffset(verifier, VT_DESCRIPTION) &&
           verifier.VerifyString(description()) &&
           VerifyField<uint8_t>(verifier, VT_WEATHERCODE, 1) &&
           VerifyField<int16_t>(verifier, VT_TEMPERATURE, 2) &&
           VerifyField<int16_t>(verifier, VT_APPARENTTEMPERATURE, 2) &&
           VerifyField<uint16_t>(verifier, VT_PRESSURE, 2) &&
           VerifyField<uint8_t>(verifier, VT_HUMIDITY, 1) &&
           VerifyField<uint16_t>(verifier, VT_VISIBILITY, 2) &&
           VerifyField<uint16_t>(verifier, VT_WINDDIRECTION, 2) &&
           VerifyField<uint16_t>(verifier, VT_WINDSPEED, 2) &&
           VerifyField<uint8_t>(verifier, VT_WINDSCALE, 1) &&
           VerifyField<uint8_t>(verifier, VT_CLOUDS, 1) &&
           VerifyField<int16_t>(verifier, VT_DEWPOINT, 2) &&
           VerifyField<int16_t>(verifier, VT_ROADSURTEMP, 2) &&
           VerifyField<int16_t>(verifier, VT_FREEZINGTEMP, 2) &&
           VerifyField<uint8_t>(verifier, VT_WATERFILMTHICKNESS, 1) &&
           VerifyField<uint8_t>(verifier, VT_SNOWTHICKNESS, 1) &&
           VerifyField<uint8_t>(verifier, VT_ICETHICKNESS, 1) &&
           VerifyField<uint8_t>(verifier, VT_SALINITY, 1) &&
           VerifyField<uint8_t>(verifier, VT_SURFACEWETSLIPCOEFFICIENT, 1) &&
           VerifyField<uint8_t>(verifier, VT_ROADCONDITIONS, 1) &&
           VerifyField<int64_t>(verifier, VT_MSG_ID, 8) &&
           verifier.EndTable();
  }
};

struct MSG_NowWeatherBuilder {
  typedef MSG_NowWeather Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_divisionCode(uint32_t divisionCode) {
    fbb_.AddElement<uint32_t>(MSG_NowWeather::VT_DIVISIONCODE, divisionCode, 4294967295);
  }
  void add_timezone(int16_t timezone) {
    fbb_.AddElement<int16_t>(MSG_NowWeather::VT_TIMEZONE, timezone, 32767);
  }
  void add_app_id(uint16_t app_id) {
    fbb_.AddElement<uint16_t>(MSG_NowWeather::VT_APP_ID, app_id, 0);
  }
  void add_subdevice_id(uint8_t subdevice_id) {
    fbb_.AddElement<uint8_t>(MSG_NowWeather::VT_SUBDEVICE_ID, subdevice_id, 0);
  }
  void add_pos(::flatbuffers::Offset<MECData::DF_Position3D> pos) {
    fbb_.AddOffset(MSG_NowWeather::VT_POS, pos);
  }
  void add_updateTime(uint32_t updateTime) {
    fbb_.AddElement<uint32_t>(MSG_NowWeather::VT_UPDATETIME, updateTime, 4294967295);
  }
  void add_description(::flatbuffers::Offset<::flatbuffers::String> description) {
    fbb_.AddOffset(MSG_NowWeather::VT_DESCRIPTION, description);
  }
  void add_weatherCode(uint8_t weatherCode) {
    fbb_.AddElement<uint8_t>(MSG_NowWeather::VT_WEATHERCODE, weatherCode, 255);
  }
  void add_temperature(int16_t temperature) {
    fbb_.AddElement<int16_t>(MSG_NowWeather::VT_TEMPERATURE, temperature, 0);
  }
  void add_apparentTemperature(int16_t apparentTemperature) {
    fbb_.AddElement<int16_t>(MSG_NowWeather::VT_APPARENTTEMPERATURE, apparentTemperature, 0);
  }
  void add_pressure(uint16_t pressure) {
    fbb_.AddElement<uint16_t>(MSG_NowWeather::VT_PRESSURE, pressure, 0);
  }
  void add_humidity(uint8_t humidity) {
    fbb_.AddElement<uint8_t>(MSG_NowWeather::VT_HUMIDITY, humidity, 0);
  }
  void add_visibility(uint16_t visibility) {
    fbb_.AddElement<uint16_t>(MSG_NowWeather::VT_VISIBILITY, visibility, 0);
  }
  void add_windDirection(uint16_t windDirection) {
    fbb_.AddElement<uint16_t>(MSG_NowWeather::VT_WINDDIRECTION, windDirection, 0);
  }
  void add_windSpeed(uint16_t windSpeed) {
    fbb_.AddElement<uint16_t>(MSG_NowWeather::VT_WINDSPEED, windSpeed, 0);
  }
  void add_windScale(uint8_t windScale) {
    fbb_.AddElement<uint8_t>(MSG_NowWeather::VT_WINDSCALE, windScale, 0);
  }
  void add_clouds(uint8_t clouds) {
    fbb_.AddElement<uint8_t>(MSG_NowWeather::VT_CLOUDS, clouds, 0);
  }
  void add_dewPoint(int16_t dewPoint) {
    fbb_.AddElement<int16_t>(MSG_NowWeather::VT_DEWPOINT, dewPoint, 0);
  }
  void add_roadsurtemp(int16_t roadsurtemp) {
    fbb_.AddElement<int16_t>(MSG_NowWeather::VT_ROADSURTEMP, roadsurtemp, 0);
  }
  void add_freezingtemp(int16_t freezingtemp) {
    fbb_.AddElement<int16_t>(MSG_NowWeather::VT_FREEZINGTEMP, freezingtemp, 0);
  }
  void add_waterfilmthickness(uint8_t waterfilmthickness) {
    fbb_.AddElement<uint8_t>(MSG_NowWeather::VT_WATERFILMTHICKNESS, waterfilmthickness, 0);
  }
  void add_snowthickness(uint8_t snowthickness) {
    fbb_.AddElement<uint8_t>(MSG_NowWeather::VT_SNOWTHICKNESS, snowthickness, 0);
  }
  void add_icethickness(uint8_t icethickness) {
    fbb_.AddElement<uint8_t>(MSG_NowWeather::VT_ICETHICKNESS, icethickness, 0);
  }
  void add_salinity(uint8_t salinity) {
    fbb_.AddElement<uint8_t>(MSG_NowWeather::VT_SALINITY, salinity, 0);
  }
  void add_surfacewetslipcoefficient(uint8_t surfacewetslipcoefficient) {
    fbb_.AddElement<uint8_t>(MSG_NowWeather::VT_SURFACEWETSLIPCOEFFICIENT, surfacewetslipcoefficient, 0);
  }
  void add_roadconditions(uint8_t roadconditions) {
    fbb_.AddElement<uint8_t>(MSG_NowWeather::VT_ROADCONDITIONS, roadconditions, 0);
  }
  void add_msg_id(int64_t msg_id) {
    fbb_.AddElement<int64_t>(MSG_NowWeather::VT_MSG_ID, msg_id, 0);
  }
  explicit MSG_NowWeatherBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<MSG_NowWeather> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<MSG_NowWeather>(end);
    fbb_.Required(o, MSG_NowWeather::VT_POS);
    return o;
  }
};

inline ::flatbuffers::Offset<MSG_NowWeather> CreateMSG_NowWeather(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    uint32_t divisionCode = 4294967295,
    int16_t timezone = 32767,
    uint16_t app_id = 0,
    uint8_t subdevice_id = 0,
    ::flatbuffers::Offset<MECData::DF_Position3D> pos = 0,
    uint32_t updateTime = 4294967295,
    ::flatbuffers::Offset<::flatbuffers::String> description = 0,
    uint8_t weatherCode = 255,
    int16_t temperature = 0,
    int16_t apparentTemperature = 0,
    uint16_t pressure = 0,
    uint8_t humidity = 0,
    uint16_t visibility = 0,
    uint16_t windDirection = 0,
    uint16_t windSpeed = 0,
    uint8_t windScale = 0,
    uint8_t clouds = 0,
    int16_t dewPoint = 0,
    int16_t roadsurtemp = 0,
    int16_t freezingtemp = 0,
    uint8_t waterfilmthickness = 0,
    uint8_t snowthickness = 0,
    uint8_t icethickness = 0,
    uint8_t salinity = 0,
    uint8_t surfacewetslipcoefficient = 0,
    uint8_t roadconditions = 0,
    int64_t msg_id = 0) {
  MSG_NowWeatherBuilder builder_(_fbb);
  builder_.add_msg_id(msg_id);
  builder_.add_description(description);
  builder_.add_updateTime(updateTime);
  builder_.add_pos(pos);
  builder_.add_divisionCode(divisionCode);
  builder_.add_freezingtemp(freezingtemp);
  builder_.add_roadsurtemp(roadsurtemp);
  builder_.add_dewPoint(dewPoint);
  builder_.add_windSpeed(windSpeed);
  builder_.add_windDirection(windDirection);
  builder_.add_visibility(visibility);
  builder_.add_pressure(pressure);
  builder_.add_apparentTemperature(apparentTemperature);
  builder_.add_temperature(temperature);
  builder_.add_app_id(app_id);
  builder_.add_timezone(timezone);
  builder_.add_roadconditions(roadconditions);
  builder_.add_surfacewetslipcoefficient(surfacewetslipcoefficient);
  builder_.add_salinity(salinity);
  builder_.add_icethickness(icethickness);
  builder_.add_snowthickness(snowthickness);
  builder_.add_waterfilmthickness(waterfilmthickness);
  builder_.add_clouds(clouds);
  builder_.add_windScale(windScale);
  builder_.add_humidity(humidity);
  builder_.add_weatherCode(weatherCode);
  builder_.add_subdevice_id(subdevice_id);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<MSG_NowWeather> CreateMSG_NowWeatherDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    uint32_t divisionCode = 4294967295,
    int16_t timezone = 32767,
    uint16_t app_id = 0,
    uint8_t subdevice_id = 0,
    ::flatbuffers::Offset<MECData::DF_Position3D> pos = 0,
    uint32_t updateTime = 4294967295,
    const char *description = nullptr,
    uint8_t weatherCode = 255,
    int16_t temperature = 0,
    int16_t apparentTemperature = 0,
    uint16_t pressure = 0,
    uint8_t humidity = 0,
    uint16_t visibility = 0,
    uint16_t windDirection = 0,
    uint16_t windSpeed = 0,
    uint8_t windScale = 0,
    uint8_t clouds = 0,
    int16_t dewPoint = 0,
    int16_t roadsurtemp = 0,
    int16_t freezingtemp = 0,
    uint8_t waterfilmthickness = 0,
    uint8_t snowthickness = 0,
    uint8_t icethickness = 0,
    uint8_t salinity = 0,
    uint8_t surfacewetslipcoefficient = 0,
    uint8_t roadconditions = 0,
    int64_t msg_id = 0) {
  auto description__ = description ? _fbb.CreateString(description) : 0;
  return MECData::CreateMSG_NowWeather(
      _fbb,
      divisionCode,
      timezone,
      app_id,
      subdevice_id,
      pos,
      updateTime,
      description__,
      weatherCode,
      temperature,
      apparentTemperature,
      pressure,
      humidity,
      visibility,
      windDirection,
      windSpeed,
      windScale,
      clouds,
      dewPoint,
      roadsurtemp,
      freezingtemp,
      waterfilmthickness,
      snowthickness,
      icethickness,
      salinity,
      surfacewetslipcoefficient,
      roadconditions,
      msg_id);
}

inline const MECData::MSG_NowWeather *GetMSG_NowWeather(const void *buf) {
  return ::flatbuffers::GetRoot<MECData::MSG_NowWeather>(buf);
}

inline const MECData::MSG_NowWeather *GetSizePrefixedMSG_NowWeather(const void *buf) {
  return ::flatbuffers::GetSizePrefixedRoot<MECData::MSG_NowWeather>(buf);
}

inline bool VerifyMSG_NowWeatherBuffer(
    ::flatbuffers::Verifier &verifier) {
  return verifier.VerifyBuffer<MECData::MSG_NowWeather>(nullptr);
}

inline bool VerifySizePrefixedMSG_NowWeatherBuffer(
    ::flatbuffers::Verifier &verifier) {
  return verifier.VerifySizePrefixedBuffer<MECData::MSG_NowWeather>(nullptr);
}

inline void FinishMSG_NowWeatherBuffer(
    ::flatbuffers::FlatBufferBuilder &fbb,
    ::flatbuffers::Offset<MECData::MSG_NowWeather> root) {
  fbb.Finish(root);
}

inline void FinishSizePrefixedMSG_NowWeatherBuffer(
    ::flatbuffers::FlatBufferBuilder &fbb,
    ::flatbuffers::Offset<MECData::MSG_NowWeather> root) {
  fbb.FinishSizePrefixed(root);
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_NOWWEATHER_MECDATA_H_
