// automatically generated by the <PERSON><PERSON>uffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_PHASECHARACTERISTICS_MECDATA_H_
#define FLATBUFFERS_GENERATED_PHASECHARACTERISTICS_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

#include "Direction8_generated.h"
#include "Maneuver_generated.h"

namespace MECData {

struct DF_PhaseMapElement;
struct DF_PhaseMapElementBuilder;

struct DF_PhaseCharacteristics;
struct DF_PhaseCharacteristicsBuilder;

enum DE_PhaseType : uint8_t {
  DE_PhaseType_UNKNOWN = 0,
  DE_PhaseType_MOTOR = 1,
  DE_PhaseType_NON_MOTOR = 2,
  DE_PhaseType_PEDESTRIAN = 3,
  DE_PhaseType_LANE = 4,
  DE_PhaseType_MIN = DE_PhaseType_UNKNOWN,
  DE_PhaseType_MAX = DE_PhaseType_LANE
};

inline const DE_PhaseType (&EnumValuesDE_PhaseType())[5] {
  static const DE_PhaseType values[] = {
    DE_PhaseType_UNKNOWN,
    DE_PhaseType_MOTOR,
    DE_PhaseType_NON_MOTOR,
    DE_PhaseType_PEDESTRIAN,
    DE_PhaseType_LANE
  };
  return values;
}

inline const char * const *EnumNamesDE_PhaseType() {
  static const char * const names[6] = {
    "UNKNOWN",
    "MOTOR",
    "NON_MOTOR",
    "PEDESTRIAN",
    "LANE",
    nullptr
  };
  return names;
}

inline const char *EnumNameDE_PhaseType(DE_PhaseType e) {
  if (::flatbuffers::IsOutRange(e, DE_PhaseType_UNKNOWN, DE_PhaseType_LANE)) return "";
  const size_t index = static_cast<size_t>(e);
  return EnumNamesDE_PhaseType()[index];
}

struct DF_PhaseMapElement FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_PhaseMapElementBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_MOVEMENT_EXT_ID = 4,
    VT_MANEUVER = 6,
    VT_DIRECTION = 8
  };
  const ::flatbuffers::String *movement_ext_id() const {
    return GetPointer<const ::flatbuffers::String *>(VT_MOVEMENT_EXT_ID);
  }
  MECData::DE_Maneuver maneuver() const {
    return static_cast<MECData::DE_Maneuver>(GetField<uint8_t>(VT_MANEUVER, 0));
  }
  MECData::DE_Direction8 direction() const {
    return static_cast<MECData::DE_Direction8>(GetField<uint8_t>(VT_DIRECTION, 0));
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_MOVEMENT_EXT_ID) &&
           verifier.VerifyString(movement_ext_id()) &&
           VerifyField<uint8_t>(verifier, VT_MANEUVER, 1) &&
           VerifyField<uint8_t>(verifier, VT_DIRECTION, 1) &&
           verifier.EndTable();
  }
};

struct DF_PhaseMapElementBuilder {
  typedef DF_PhaseMapElement Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_movement_ext_id(::flatbuffers::Offset<::flatbuffers::String> movement_ext_id) {
    fbb_.AddOffset(DF_PhaseMapElement::VT_MOVEMENT_EXT_ID, movement_ext_id);
  }
  void add_maneuver(MECData::DE_Maneuver maneuver) {
    fbb_.AddElement<uint8_t>(DF_PhaseMapElement::VT_MANEUVER, static_cast<uint8_t>(maneuver), 0);
  }
  void add_direction(MECData::DE_Direction8 direction) {
    fbb_.AddElement<uint8_t>(DF_PhaseMapElement::VT_DIRECTION, static_cast<uint8_t>(direction), 0);
  }
  explicit DF_PhaseMapElementBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_PhaseMapElement> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_PhaseMapElement>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_PhaseMapElement> CreateDF_PhaseMapElement(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::String> movement_ext_id = 0,
    MECData::DE_Maneuver maneuver = MECData::DE_Maneuver_maneuverStraight,
    MECData::DE_Direction8 direction = MECData::DE_Direction8_N) {
  DF_PhaseMapElementBuilder builder_(_fbb);
  builder_.add_movement_ext_id(movement_ext_id);
  builder_.add_direction(direction);
  builder_.add_maneuver(maneuver);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DF_PhaseMapElement> CreateDF_PhaseMapElementDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const char *movement_ext_id = nullptr,
    MECData::DE_Maneuver maneuver = MECData::DE_Maneuver_maneuverStraight,
    MECData::DE_Direction8 direction = MECData::DE_Direction8_N) {
  auto movement_ext_id__ = movement_ext_id ? _fbb.CreateString(movement_ext_id) : 0;
  return MECData::CreateDF_PhaseMapElement(
      _fbb,
      movement_ext_id__,
      maneuver,
      direction);
}

struct DF_PhaseCharacteristics FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_PhaseCharacteristicsBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_PHASE_ID = 4,
    VT_PHASE_TYPE = 6,
    VT_MAP_ELEMENTS = 8
  };
  uint8_t phase_id() const {
    return GetField<uint8_t>(VT_PHASE_ID, 0);
  }
  MECData::DE_PhaseType phase_type() const {
    return static_cast<MECData::DE_PhaseType>(GetField<uint8_t>(VT_PHASE_TYPE, 0));
  }
  const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_PhaseMapElement>> *map_elements() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_PhaseMapElement>> *>(VT_MAP_ELEMENTS);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<uint8_t>(verifier, VT_PHASE_ID, 1) &&
           VerifyField<uint8_t>(verifier, VT_PHASE_TYPE, 1) &&
           VerifyOffset(verifier, VT_MAP_ELEMENTS) &&
           verifier.VerifyVector(map_elements()) &&
           verifier.VerifyVectorOfTables(map_elements()) &&
           verifier.EndTable();
  }
};

struct DF_PhaseCharacteristicsBuilder {
  typedef DF_PhaseCharacteristics Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_phase_id(uint8_t phase_id) {
    fbb_.AddElement<uint8_t>(DF_PhaseCharacteristics::VT_PHASE_ID, phase_id, 0);
  }
  void add_phase_type(MECData::DE_PhaseType phase_type) {
    fbb_.AddElement<uint8_t>(DF_PhaseCharacteristics::VT_PHASE_TYPE, static_cast<uint8_t>(phase_type), 0);
  }
  void add_map_elements(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_PhaseMapElement>>> map_elements) {
    fbb_.AddOffset(DF_PhaseCharacteristics::VT_MAP_ELEMENTS, map_elements);
  }
  explicit DF_PhaseCharacteristicsBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_PhaseCharacteristics> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_PhaseCharacteristics>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_PhaseCharacteristics> CreateDF_PhaseCharacteristics(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    uint8_t phase_id = 0,
    MECData::DE_PhaseType phase_type = MECData::DE_PhaseType_UNKNOWN,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_PhaseMapElement>>> map_elements = 0) {
  DF_PhaseCharacteristicsBuilder builder_(_fbb);
  builder_.add_map_elements(map_elements);
  builder_.add_phase_type(phase_type);
  builder_.add_phase_id(phase_id);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DF_PhaseCharacteristics> CreateDF_PhaseCharacteristicsDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    uint8_t phase_id = 0,
    MECData::DE_PhaseType phase_type = MECData::DE_PhaseType_UNKNOWN,
    const std::vector<::flatbuffers::Offset<MECData::DF_PhaseMapElement>> *map_elements = nullptr) {
  auto map_elements__ = map_elements ? _fbb.CreateVector<::flatbuffers::Offset<MECData::DF_PhaseMapElement>>(*map_elements) : 0;
  return MECData::CreateDF_PhaseCharacteristics(
      _fbb,
      phase_id,
      phase_type,
      map_elements__);
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_PHASECHARACTERISTICS_MECDATA_H_
