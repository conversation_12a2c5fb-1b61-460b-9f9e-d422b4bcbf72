// automatically generated by the <PERSON>Buffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_POSITIONCONFIDENCESET_MECDATA_H_
#define FLATBUFFERS_GENERATED_POSITIONCONFIDENCESET_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

#include "ElevationConfidence_generated.h"
#include "PositionConfidence_generated.h"

namespace MECData {

struct DF_PositionConfidenceSet;
struct DF_PositionConfidenceSetBuilder;

struct DF_PositionConfidenceSet FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_PositionConfidenceSetBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_POS = 4,
    VT_ELEVATION = 6
  };
  MECData::DE_PositionConfidence pos() const {
    return static_cast<MECData::DE_PositionConfidence>(GetField<int8_t>(VT_POS, 0));
  }
  MECData::DE_ElevationConfidence elevation() const {
    return static_cast<MECData::DE_ElevationConfidence>(GetField<int8_t>(VT_ELEVATION, 0));
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<int8_t>(verifier, VT_POS, 1) &&
           VerifyField<int8_t>(verifier, VT_ELEVATION, 1) &&
           verifier.EndTable();
  }
};

struct DF_PositionConfidenceSetBuilder {
  typedef DF_PositionConfidenceSet Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_pos(MECData::DE_PositionConfidence pos) {
    fbb_.AddElement<int8_t>(DF_PositionConfidenceSet::VT_POS, static_cast<int8_t>(pos), 0);
  }
  void add_elevation(MECData::DE_ElevationConfidence elevation) {
    fbb_.AddElement<int8_t>(DF_PositionConfidenceSet::VT_ELEVATION, static_cast<int8_t>(elevation), 0);
  }
  explicit DF_PositionConfidenceSetBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_PositionConfidenceSet> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_PositionConfidenceSet>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_PositionConfidenceSet> CreateDF_PositionConfidenceSet(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    MECData::DE_PositionConfidence pos = MECData::DE_PositionConfidence_unavailable,
    MECData::DE_ElevationConfidence elevation = MECData::DE_ElevationConfidence_unavailable) {
  DF_PositionConfidenceSetBuilder builder_(_fbb);
  builder_.add_elevation(elevation);
  builder_.add_pos(pos);
  return builder_.Finish();
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_POSITIONCONFIDENCESET_MECDATA_H_
