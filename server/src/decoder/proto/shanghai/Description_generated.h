// automatically generated by the FlatBuffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_DESCRIPTION_MECDATA_H_
#define FLATBUFFERS_GENERATED_DESCRIPTION_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

#include "TextGB2312_generated.h"
#include "TextString_generated.h"

namespace MECData {

enum DF_Description : uint8_t {
  DF_Description_NONE = 0,
  DF_Description_DF_TextString = 1,
  DF_Description_DF_TextGB2312 = 2,
  DF_Description_MIN = DF_Description_NONE,
  DF_Description_MAX = DF_Description_DF_TextGB2312
};

inline const DF_Description (&EnumValuesDF_Description())[3] {
  static const DF_Description values[] = {
    DF_Description_NONE,
    DF_Description_DF_TextString,
    DF_Description_DF_TextGB2312
  };
  return values;
}

inline const char * const *EnumNamesDF_Description() {
  static const char * const names[4] = {
    "NONE",
    "DF_TextString",
    "DF_TextGB2312",
    nullptr
  };
  return names;
}

inline const char *EnumNameDF_Description(DF_Description e) {
  if (::flatbuffers::IsOutRange(e, DF_Description_NONE, DF_Description_DF_TextGB2312)) return "";
  const size_t index = static_cast<size_t>(e);
  return EnumNamesDF_Description()[index];
}

template<typename T> struct DF_DescriptionTraits {
  static const DF_Description enum_value = DF_Description_NONE;
};

template<> struct DF_DescriptionTraits<MECData::DF_TextString> {
  static const DF_Description enum_value = DF_Description_DF_TextString;
};

template<> struct DF_DescriptionTraits<MECData::DF_TextGB2312> {
  static const DF_Description enum_value = DF_Description_DF_TextGB2312;
};

bool VerifyDF_Description(::flatbuffers::Verifier &verifier, const void *obj, DF_Description type);
bool VerifyDF_DescriptionVector(::flatbuffers::Verifier &verifier, const ::flatbuffers::Vector<::flatbuffers::Offset<void>> *values, const ::flatbuffers::Vector<uint8_t> *types);

inline bool VerifyDF_Description(::flatbuffers::Verifier &verifier, const void *obj, DF_Description type) {
  switch (type) {
    case DF_Description_NONE: {
      return true;
    }
    case DF_Description_DF_TextString: {
      auto ptr = reinterpret_cast<const MECData::DF_TextString *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case DF_Description_DF_TextGB2312: {
      auto ptr = reinterpret_cast<const MECData::DF_TextGB2312 *>(obj);
      return verifier.VerifyTable(ptr);
    }
    default: return true;
  }
}

inline bool VerifyDF_DescriptionVector(::flatbuffers::Verifier &verifier, const ::flatbuffers::Vector<::flatbuffers::Offset<void>> *values, const ::flatbuffers::Vector<uint8_t> *types) {
  if (!values || !types) return !values && !types;
  if (values->size() != types->size()) return false;
  for (::flatbuffers::uoffset_t i = 0; i < values->size(); ++i) {
    if (!VerifyDF_Description(
        verifier,  values->Get(i), types->GetEnum<DF_Description>(i))) {
      return false;
    }
  }
  return true;
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_DESCRIPTION_MECDATA_H_
