// automatically generated by the FlatBuffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_MANEUVER_MECDATA_H_
#define FLATBUFFERS_GENERATED_MANEUVER_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

namespace MECData {

enum DE_Maneuver : uint8_t {
  DE_Maneuver_maneuverStraight = 0,
  DE_Maneuver_maneuverLeftTurn = 1,
  DE_Maneuver_maneuverRightTurn = 2,
  DE_Maneuver_maneuverUTurn = 3,
  DE_Maneuver_MIN = DE_Maneuver_maneuverStraight,
  DE_Maneuver_MAX = DE_Maneuver_maneuverUTurn
};

inline const DE_Maneuver (&EnumValuesDE_Maneuver())[4] {
  static const DE_Maneuver values[] = {
    DE_Maneuver_maneuverStraight,
    DE_Maneuver_maneuverLeftTurn,
    DE_Maneuver_maneuverRightTurn,
    DE_Maneuver_maneuverUTurn
  };
  return values;
}

inline const char * const *EnumNamesDE_Maneuver() {
  static const char * const names[5] = {
    "maneuverStraight",
    "maneuverLeftTurn",
    "maneuverRightTurn",
    "maneuverUTurn",
    nullptr
  };
  return names;
}

inline const char *EnumNameDE_Maneuver(DE_Maneuver e) {
  if (::flatbuffers::IsOutRange(e, DE_Maneuver_maneuverStraight, DE_Maneuver_maneuverUTurn)) return "";
  const size_t index = static_cast<size_t>(e);
  return EnumNamesDE_Maneuver()[index];
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_MANEUVER_MECDATA_H_
