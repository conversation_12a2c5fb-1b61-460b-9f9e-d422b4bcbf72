// automatically generated by the <PERSON>Buffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_NODEREFERENCEID_MECDATA_H_
#define FLATBUFFERS_GENERATED_NODEREFERENCEID_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

namespace MECData {

struct DF_NodeReferenceID;
struct DF_NodeReferenceIDBuilder;

struct DF_NodeReferenceID FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_NodeReferenceIDBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_REGION = 4,
    VT_ID = 6
  };
  uint16_t region() const {
    return GetField<uint16_t>(VT_REGION, 0);
  }
  uint16_t id() const {
    return GetField<uint16_t>(VT_ID, 65535);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<uint16_t>(verifier, VT_REGION, 2) &&
           VerifyField<uint16_t>(verifier, VT_ID, 2) &&
           verifier.EndTable();
  }
};

struct DF_NodeReferenceIDBuilder {
  typedef DF_NodeReferenceID Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_region(uint16_t region) {
    fbb_.AddElement<uint16_t>(DF_NodeReferenceID::VT_REGION, region, 0);
  }
  void add_id(uint16_t id) {
    fbb_.AddElement<uint16_t>(DF_NodeReferenceID::VT_ID, id, 65535);
  }
  explicit DF_NodeReferenceIDBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_NodeReferenceID> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_NodeReferenceID>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_NodeReferenceID> CreateDF_NodeReferenceID(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    uint16_t region = 0,
    uint16_t id = 65535) {
  DF_NodeReferenceIDBuilder builder_(_fbb);
  builder_.add_id(id);
  builder_.add_region(region);
  return builder_.Finish();
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_NODEREFERENCEID_MECDATA_H_
