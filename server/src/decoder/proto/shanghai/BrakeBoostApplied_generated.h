// automatically generated by the FlatBuffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_BRAKEBOOSTAPPLIED_MECDATA_H_
#define FLATBUFFERS_GENERATED_BRAKEBOOSTAPPLIED_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

namespace MECData {

enum DE_BrakeBoostApplied : int8_t {
  DE_BrakeBoostApplied_unavailable = 0,
  DE_BrakeBoostApplied_off = 1,
  DE_BrakeBoostApplied_on = 2,
  DE_BrakeBoostApplied_MIN = DE_BrakeBoostApplied_unavailable,
  DE_BrakeBoostApplied_MAX = DE_BrakeBoostApplied_on
};

inline const DE_BrakeBoostApplied (&EnumValuesDE_BrakeBoostApplied())[3] {
  static const DE_BrakeBoostApplied values[] = {
    DE_BrakeBoostApplied_unavailable,
    DE_BrakeBoostApplied_off,
    DE_BrakeBoostApplied_on
  };
  return values;
}

inline const char * const *EnumNamesDE_BrakeBoostApplied() {
  static const char * const names[4] = {
    "unavailable",
    "off",
    "on",
    nullptr
  };
  return names;
}

inline const char *EnumNameDE_BrakeBoostApplied(DE_BrakeBoostApplied e) {
  if (::flatbuffers::IsOutRange(e, DE_BrakeBoostApplied_unavailable, DE_BrakeBoostApplied_on)) return "";
  const size_t index = static_cast<size_t>(e);
  return EnumNamesDE_BrakeBoostApplied()[index];
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_BRAKEBOOSTAPPLIED_MECDATA_H_
