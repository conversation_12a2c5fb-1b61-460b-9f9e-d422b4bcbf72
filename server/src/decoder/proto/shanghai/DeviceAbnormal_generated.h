// automatically generated by the <PERSON>Buffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_DEVICEABNORMAL_MECDATA_H_
#define FLATBUFFERS_GENERATED_DEVICEABNORMAL_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

namespace MECData {

struct DF_DeviceAbnormal;
struct DF_DeviceAbnormalBuilder;

enum DE_DeviceState : uint8_t {
  DE_DeviceState_OK = 0,
  DE_DeviceState_OFF = 1,
  DE_DeviceState_ABNORMAL = 2,
  DE_DeviceState_MIN = DE_DeviceState_OK,
  DE_DeviceState_MAX = DE_DeviceState_ABNORMAL
};

inline const DE_DeviceState (&EnumValuesDE_DeviceState())[3] {
  static const DE_DeviceState values[] = {
    DE_DeviceState_OK,
    DE_DeviceState_OFF,
    DE_DeviceState_ABNORMAL
  };
  return values;
}

inline const char * const *EnumNamesDE_DeviceState() {
  static const char * const names[4] = {
    "OK",
    "OFF",
    "ABNORMAL",
    nullptr
  };
  return names;
}

inline const char *EnumNameDE_DeviceState(DE_DeviceState e) {
  if (::flatbuffers::IsOutRange(e, DE_DeviceState_OK, DE_DeviceState_ABNORMAL)) return "";
  const size_t index = static_cast<size_t>(e);
  return EnumNamesDE_DeviceState()[index];
}

enum DE_DeviceAbnormalType : uint8_t {
  DE_DeviceAbnormalType_DEV_STATE_OK = 0,
  DE_DeviceAbnormalType_DEV_AB_POWER = 1,
  DE_DeviceAbnormalType_DEV_AB_NETWORK = 2,
  DE_DeviceAbnormalType_DEV_AB_ENV = 3,
  DE_DeviceAbnormalType_DEV_AB_FUNC = 4,
  DE_DeviceAbnormalType_DEV_AB_INTERR = 5,
  DE_DeviceAbnormalType_DEV_AB_UNKNOWN = 255,
  DE_DeviceAbnormalType_MIN = DE_DeviceAbnormalType_DEV_STATE_OK,
  DE_DeviceAbnormalType_MAX = DE_DeviceAbnormalType_DEV_AB_UNKNOWN
};

inline const DE_DeviceAbnormalType (&EnumValuesDE_DeviceAbnormalType())[7] {
  static const DE_DeviceAbnormalType values[] = {
    DE_DeviceAbnormalType_DEV_STATE_OK,
    DE_DeviceAbnormalType_DEV_AB_POWER,
    DE_DeviceAbnormalType_DEV_AB_NETWORK,
    DE_DeviceAbnormalType_DEV_AB_ENV,
    DE_DeviceAbnormalType_DEV_AB_FUNC,
    DE_DeviceAbnormalType_DEV_AB_INTERR,
    DE_DeviceAbnormalType_DEV_AB_UNKNOWN
  };
  return values;
}

inline const char *EnumNameDE_DeviceAbnormalType(DE_DeviceAbnormalType e) {
  switch (e) {
    case DE_DeviceAbnormalType_DEV_STATE_OK: return "DEV_STATE_OK";
    case DE_DeviceAbnormalType_DEV_AB_POWER: return "DEV_AB_POWER";
    case DE_DeviceAbnormalType_DEV_AB_NETWORK: return "DEV_AB_NETWORK";
    case DE_DeviceAbnormalType_DEV_AB_ENV: return "DEV_AB_ENV";
    case DE_DeviceAbnormalType_DEV_AB_FUNC: return "DEV_AB_FUNC";
    case DE_DeviceAbnormalType_DEV_AB_INTERR: return "DEV_AB_INTERR";
    case DE_DeviceAbnormalType_DEV_AB_UNKNOWN: return "DEV_AB_UNKNOWN";
    default: return "";
  }
}

struct DF_DeviceAbnormal FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_DeviceAbnormalBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_STATE = 4,
    VT_ABNORMALTYPE = 6,
    VT_DESCRIPTION = 8
  };
  MECData::DE_DeviceState state() const {
    return static_cast<MECData::DE_DeviceState>(GetField<uint8_t>(VT_STATE, 0));
  }
  MECData::DE_DeviceAbnormalType abnormalType() const {
    return static_cast<MECData::DE_DeviceAbnormalType>(GetField<uint8_t>(VT_ABNORMALTYPE, 0));
  }
  const ::flatbuffers::String *description() const {
    return GetPointer<const ::flatbuffers::String *>(VT_DESCRIPTION);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<uint8_t>(verifier, VT_STATE, 1) &&
           VerifyField<uint8_t>(verifier, VT_ABNORMALTYPE, 1) &&
           VerifyOffset(verifier, VT_DESCRIPTION) &&
           verifier.VerifyString(description()) &&
           verifier.EndTable();
  }
};

struct DF_DeviceAbnormalBuilder {
  typedef DF_DeviceAbnormal Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_state(MECData::DE_DeviceState state) {
    fbb_.AddElement<uint8_t>(DF_DeviceAbnormal::VT_STATE, static_cast<uint8_t>(state), 0);
  }
  void add_abnormalType(MECData::DE_DeviceAbnormalType abnormalType) {
    fbb_.AddElement<uint8_t>(DF_DeviceAbnormal::VT_ABNORMALTYPE, static_cast<uint8_t>(abnormalType), 0);
  }
  void add_description(::flatbuffers::Offset<::flatbuffers::String> description) {
    fbb_.AddOffset(DF_DeviceAbnormal::VT_DESCRIPTION, description);
  }
  explicit DF_DeviceAbnormalBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_DeviceAbnormal> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_DeviceAbnormal>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_DeviceAbnormal> CreateDF_DeviceAbnormal(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    MECData::DE_DeviceState state = MECData::DE_DeviceState_OK,
    MECData::DE_DeviceAbnormalType abnormalType = MECData::DE_DeviceAbnormalType_DEV_STATE_OK,
    ::flatbuffers::Offset<::flatbuffers::String> description = 0) {
  DF_DeviceAbnormalBuilder builder_(_fbb);
  builder_.add_description(description);
  builder_.add_abnormalType(abnormalType);
  builder_.add_state(state);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DF_DeviceAbnormal> CreateDF_DeviceAbnormalDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    MECData::DE_DeviceState state = MECData::DE_DeviceState_OK,
    MECData::DE_DeviceAbnormalType abnormalType = MECData::DE_DeviceAbnormalType_DEV_STATE_OK,
    const char *description = nullptr) {
  auto description__ = description ? _fbb.CreateString(description) : 0;
  return MECData::CreateDF_DeviceAbnormal(
      _fbb,
      state,
      abnormalType,
      description__);
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_DEVICEABNORMAL_MECDATA_H_
