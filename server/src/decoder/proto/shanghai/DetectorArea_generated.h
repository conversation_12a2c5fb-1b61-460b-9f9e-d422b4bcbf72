// automatically generated by the <PERSON><PERSON>uffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_DETECTORAREA_MECDATA_H_
#define FLATBUFFERS_GENERATED_DETECTORAREA_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

namespace MECData {

struct DF_DetectorArea;
struct DF_DetectorAreaBuilder;

struct DF_DetectorArea FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_DetectorAreaBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_ID = 4,
    VT_SET_TIME = 6,
    VT_LANE_EXT_ID = 8,
    VT_START_LAT = 10,
    VT_START_LON = 12,
    VT_END_LAT = 14,
    VT_END_LON = 16
  };
  int32_t id() const {
    return GetField<int32_t>(VT_ID, 0);
  }
  uint64_t set_time() const {
    return GetField<uint64_t>(VT_SET_TIME, 0);
  }
  const ::flatbuffers::String *lane_ext_id() const {
    return GetPointer<const ::flatbuffers::String *>(VT_LANE_EXT_ID);
  }
  int32_t start_lat() const {
    return GetField<int32_t>(VT_START_LAT, 0);
  }
  int32_t start_lon() const {
    return GetField<int32_t>(VT_START_LON, 0);
  }
  int32_t end_lat() const {
    return GetField<int32_t>(VT_END_LAT, 0);
  }
  int32_t end_lon() const {
    return GetField<int32_t>(VT_END_LON, 0);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<int32_t>(verifier, VT_ID, 4) &&
           VerifyField<uint64_t>(verifier, VT_SET_TIME, 8) &&
           VerifyOffset(verifier, VT_LANE_EXT_ID) &&
           verifier.VerifyString(lane_ext_id()) &&
           VerifyField<int32_t>(verifier, VT_START_LAT, 4) &&
           VerifyField<int32_t>(verifier, VT_START_LON, 4) &&
           VerifyField<int32_t>(verifier, VT_END_LAT, 4) &&
           VerifyField<int32_t>(verifier, VT_END_LON, 4) &&
           verifier.EndTable();
  }
};

struct DF_DetectorAreaBuilder {
  typedef DF_DetectorArea Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_id(int32_t id) {
    fbb_.AddElement<int32_t>(DF_DetectorArea::VT_ID, id, 0);
  }
  void add_set_time(uint64_t set_time) {
    fbb_.AddElement<uint64_t>(DF_DetectorArea::VT_SET_TIME, set_time, 0);
  }
  void add_lane_ext_id(::flatbuffers::Offset<::flatbuffers::String> lane_ext_id) {
    fbb_.AddOffset(DF_DetectorArea::VT_LANE_EXT_ID, lane_ext_id);
  }
  void add_start_lat(int32_t start_lat) {
    fbb_.AddElement<int32_t>(DF_DetectorArea::VT_START_LAT, start_lat, 0);
  }
  void add_start_lon(int32_t start_lon) {
    fbb_.AddElement<int32_t>(DF_DetectorArea::VT_START_LON, start_lon, 0);
  }
  void add_end_lat(int32_t end_lat) {
    fbb_.AddElement<int32_t>(DF_DetectorArea::VT_END_LAT, end_lat, 0);
  }
  void add_end_lon(int32_t end_lon) {
    fbb_.AddElement<int32_t>(DF_DetectorArea::VT_END_LON, end_lon, 0);
  }
  explicit DF_DetectorAreaBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_DetectorArea> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_DetectorArea>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_DetectorArea> CreateDF_DetectorArea(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    int32_t id = 0,
    uint64_t set_time = 0,
    ::flatbuffers::Offset<::flatbuffers::String> lane_ext_id = 0,
    int32_t start_lat = 0,
    int32_t start_lon = 0,
    int32_t end_lat = 0,
    int32_t end_lon = 0) {
  DF_DetectorAreaBuilder builder_(_fbb);
  builder_.add_set_time(set_time);
  builder_.add_end_lon(end_lon);
  builder_.add_end_lat(end_lat);
  builder_.add_start_lon(start_lon);
  builder_.add_start_lat(start_lat);
  builder_.add_lane_ext_id(lane_ext_id);
  builder_.add_id(id);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DF_DetectorArea> CreateDF_DetectorAreaDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    int32_t id = 0,
    uint64_t set_time = 0,
    const char *lane_ext_id = nullptr,
    int32_t start_lat = 0,
    int32_t start_lon = 0,
    int32_t end_lat = 0,
    int32_t end_lon = 0) {
  auto lane_ext_id__ = lane_ext_id ? _fbb.CreateString(lane_ext_id) : 0;
  return MECData::CreateDF_DetectorArea(
      _fbb,
      id,
      set_time,
      lane_ext_id__,
      start_lat,
      start_lon,
      end_lat,
      end_lon);
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_DETECTORAREA_MECDATA_H_
