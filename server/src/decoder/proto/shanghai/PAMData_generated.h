// automatically generated by the FlatBuffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_PAMDATA_MECDATA_H_
#define FLATBUFFERS_GENERATED_PAMDATA_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

#include "Position3D_generated.h"
#include "PositionOffsetLLV_generated.h"
#include "RoadPoint_generated.h"

namespace MECData {

struct DF_ParkingLotInfo;
struct DF_ParkingLotInfoBuilder;

struct DF_PAMMovement;
struct DF_PAMMovementBuilder;

struct DF_ParkingSlotPosition;
struct DF_ParkingSlotPositionBuilder;

struct DF_ParkingSlot;
struct DF_ParkingSlotBuilder;

struct DF_PAMDrive;
struct DF_PAMDriveBuilder;

struct DF_PAMNode;
struct DF_PAMNodeBuilder;

struct DF_ParkingGuide;
struct DF_ParkingGuideBuilder;

struct MSG_PAMData;
struct MSG_PAMDataBuilder;

enum DE_AVPType : uint8_t {
  DE_AVPType_p0 = 0,
  DE_AVPType_p1 = 1,
  DE_AVPType_p2 = 2,
  DE_AVPType_p3 = 3,
  DE_AVPType_p4 = 4,
  DE_AVPType_p5 = 5,
  DE_AVPType_MIN = DE_AVPType_p0,
  DE_AVPType_MAX = DE_AVPType_p5
};

inline const DE_AVPType (&EnumValuesDE_AVPType())[6] {
  static const DE_AVPType values[] = {
    DE_AVPType_p0,
    DE_AVPType_p1,
    DE_AVPType_p2,
    DE_AVPType_p3,
    DE_AVPType_p4,
    DE_AVPType_p5
  };
  return values;
}

inline const char * const *EnumNamesDE_AVPType() {
  static const char * const names[7] = {
    "p0",
    "p1",
    "p2",
    "p3",
    "p4",
    "p5",
    nullptr
  };
  return names;
}

inline const char *EnumNameDE_AVPType(DE_AVPType e) {
  if (::flatbuffers::IsOutRange(e, DE_AVPType_p0, DE_AVPType_p5)) return "";
  const size_t index = static_cast<size_t>(e);
  return EnumNamesDE_AVPType()[index];
}

enum DE_SlotStatus : uint8_t {
  DE_SlotStatus_UNKNOWN = 0,
  DE_SlotStatus_AVAILABLE = 1,
  DE_SlotStatus_OCCUPIED = 2,
  DE_SlotStatus_RESERVED = 3,
  DE_SlotStatus_MIN = DE_SlotStatus_UNKNOWN,
  DE_SlotStatus_MAX = DE_SlotStatus_RESERVED
};

inline const DE_SlotStatus (&EnumValuesDE_SlotStatus())[4] {
  static const DE_SlotStatus values[] = {
    DE_SlotStatus_UNKNOWN,
    DE_SlotStatus_AVAILABLE,
    DE_SlotStatus_OCCUPIED,
    DE_SlotStatus_RESERVED
  };
  return values;
}

inline const char * const *EnumNamesDE_SlotStatus() {
  static const char * const names[5] = {
    "UNKNOWN",
    "AVAILABLE",
    "OCCUPIED",
    "RESERVED",
    nullptr
  };
  return names;
}

inline const char *EnumNameDE_SlotStatus(DE_SlotStatus e) {
  if (::flatbuffers::IsOutRange(e, DE_SlotStatus_UNKNOWN, DE_SlotStatus_RESERVED)) return "";
  const size_t index = static_cast<size_t>(e);
  return EnumNamesDE_SlotStatus()[index];
}

enum DE_ParkingSpaceTheta : uint8_t {
  DE_ParkingSpaceTheta_UNKNOWN = 0,
  DE_ParkingSpaceTheta_VERTICAL = 1,
  DE_ParkingSpaceTheta_SIDE = 2,
  DE_ParkingSpaceTheta_OBLIQUE = 3,
  DE_ParkingSpaceTheta_MIN = DE_ParkingSpaceTheta_UNKNOWN,
  DE_ParkingSpaceTheta_MAX = DE_ParkingSpaceTheta_OBLIQUE
};

inline const DE_ParkingSpaceTheta (&EnumValuesDE_ParkingSpaceTheta())[4] {
  static const DE_ParkingSpaceTheta values[] = {
    DE_ParkingSpaceTheta_UNKNOWN,
    DE_ParkingSpaceTheta_VERTICAL,
    DE_ParkingSpaceTheta_SIDE,
    DE_ParkingSpaceTheta_OBLIQUE
  };
  return values;
}

inline const char * const *EnumNamesDE_ParkingSpaceTheta() {
  static const char * const names[5] = {
    "UNKNOWN",
    "VERTICAL",
    "SIDE",
    "OBLIQUE",
    nullptr
  };
  return names;
}

inline const char *EnumNameDE_ParkingSpaceTheta(DE_ParkingSpaceTheta e) {
  if (::flatbuffers::IsOutRange(e, DE_ParkingSpaceTheta_UNKNOWN, DE_ParkingSpaceTheta_OBLIQUE)) return "";
  const size_t index = static_cast<size_t>(e);
  return EnumNamesDE_ParkingSpaceTheta()[index];
}

enum DE_ParkingLock : uint8_t {
  DE_ParkingLock_UNKNOWN = 0,
  DE_ParkingLock_NOLOCK = 1,
  DE_ParkingLock_LOCKED = 2,
  DE_ParkingLock_UNLOCKED = 3,
  DE_ParkingLock_MIN = DE_ParkingLock_UNKNOWN,
  DE_ParkingLock_MAX = DE_ParkingLock_UNLOCKED
};

inline const DE_ParkingLock (&EnumValuesDE_ParkingLock())[4] {
  static const DE_ParkingLock values[] = {
    DE_ParkingLock_UNKNOWN,
    DE_ParkingLock_NOLOCK,
    DE_ParkingLock_LOCKED,
    DE_ParkingLock_UNLOCKED
  };
  return values;
}

inline const char * const *EnumNamesDE_ParkingLock() {
  static const char * const names[5] = {
    "UNKNOWN",
    "NOLOCK",
    "LOCKED",
    "UNLOCKED",
    nullptr
  };
  return names;
}

inline const char *EnumNameDE_ParkingLock(DE_ParkingLock e) {
  if (::flatbuffers::IsOutRange(e, DE_ParkingLock_UNKNOWN, DE_ParkingLock_UNLOCKED)) return "";
  const size_t index = static_cast<size_t>(e);
  return EnumNamesDE_ParkingLock()[index];
}

struct DF_ParkingLotInfo FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_ParkingLotInfoBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_ID = 4,
    VT_NAME = 6,
    VT_NUMBER = 8,
    VT_BUILDING_LAYER_NUM = 10,
    VT_AVP_TYPE = 12
  };
  uint16_t id() const {
    return GetField<uint16_t>(VT_ID, 0);
  }
  const ::flatbuffers::String *name() const {
    return GetPointer<const ::flatbuffers::String *>(VT_NAME);
  }
  uint16_t number() const {
    return GetField<uint16_t>(VT_NUMBER, 0);
  }
  uint8_t building_layer_num() const {
    return GetField<uint8_t>(VT_BUILDING_LAYER_NUM, 0);
  }
  MECData::DE_AVPType avp_type() const {
    return static_cast<MECData::DE_AVPType>(GetField<uint8_t>(VT_AVP_TYPE, 0));
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<uint16_t>(verifier, VT_ID, 2) &&
           VerifyOffset(verifier, VT_NAME) &&
           verifier.VerifyString(name()) &&
           VerifyField<uint16_t>(verifier, VT_NUMBER, 2) &&
           VerifyField<uint8_t>(verifier, VT_BUILDING_LAYER_NUM, 1) &&
           VerifyField<uint8_t>(verifier, VT_AVP_TYPE, 1) &&
           verifier.EndTable();
  }
};

struct DF_ParkingLotInfoBuilder {
  typedef DF_ParkingLotInfo Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_id(uint16_t id) {
    fbb_.AddElement<uint16_t>(DF_ParkingLotInfo::VT_ID, id, 0);
  }
  void add_name(::flatbuffers::Offset<::flatbuffers::String> name) {
    fbb_.AddOffset(DF_ParkingLotInfo::VT_NAME, name);
  }
  void add_number(uint16_t number) {
    fbb_.AddElement<uint16_t>(DF_ParkingLotInfo::VT_NUMBER, number, 0);
  }
  void add_building_layer_num(uint8_t building_layer_num) {
    fbb_.AddElement<uint8_t>(DF_ParkingLotInfo::VT_BUILDING_LAYER_NUM, building_layer_num, 0);
  }
  void add_avp_type(MECData::DE_AVPType avp_type) {
    fbb_.AddElement<uint8_t>(DF_ParkingLotInfo::VT_AVP_TYPE, static_cast<uint8_t>(avp_type), 0);
  }
  explicit DF_ParkingLotInfoBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_ParkingLotInfo> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_ParkingLotInfo>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_ParkingLotInfo> CreateDF_ParkingLotInfo(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    uint16_t id = 0,
    ::flatbuffers::Offset<::flatbuffers::String> name = 0,
    uint16_t number = 0,
    uint8_t building_layer_num = 0,
    MECData::DE_AVPType avp_type = MECData::DE_AVPType_p0) {
  DF_ParkingLotInfoBuilder builder_(_fbb);
  builder_.add_name(name);
  builder_.add_number(number);
  builder_.add_id(id);
  builder_.add_avp_type(avp_type);
  builder_.add_building_layer_num(building_layer_num);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DF_ParkingLotInfo> CreateDF_ParkingLotInfoDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    uint16_t id = 0,
    const char *name = nullptr,
    uint16_t number = 0,
    uint8_t building_layer_num = 0,
    MECData::DE_AVPType avp_type = MECData::DE_AVPType_p0) {
  auto name__ = name ? _fbb.CreateString(name) : 0;
  return MECData::CreateDF_ParkingLotInfo(
      _fbb,
      id,
      name__,
      number,
      building_layer_num,
      avp_type);
}

struct DF_PAMMovement FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_PAMMovementBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_NODES = 4
  };
  const ::flatbuffers::Vector<uint16_t> *nodes() const {
    return GetPointer<const ::flatbuffers::Vector<uint16_t> *>(VT_NODES);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_NODES) &&
           verifier.VerifyVector(nodes()) &&
           verifier.EndTable();
  }
};

struct DF_PAMMovementBuilder {
  typedef DF_PAMMovement Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_nodes(::flatbuffers::Offset<::flatbuffers::Vector<uint16_t>> nodes) {
    fbb_.AddOffset(DF_PAMMovement::VT_NODES, nodes);
  }
  explicit DF_PAMMovementBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_PAMMovement> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_PAMMovement>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_PAMMovement> CreateDF_PAMMovement(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::Vector<uint16_t>> nodes = 0) {
  DF_PAMMovementBuilder builder_(_fbb);
  builder_.add_nodes(nodes);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DF_PAMMovement> CreateDF_PAMMovementDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const std::vector<uint16_t> *nodes = nullptr) {
  auto nodes__ = nodes ? _fbb.CreateVector<uint16_t>(*nodes) : 0;
  return MECData::CreateDF_PAMMovement(
      _fbb,
      nodes__);
}

struct DF_ParkingSlotPosition FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_ParkingSlotPositionBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_TOP_LEFT = 4,
    VT_TOP_RIGHT = 6,
    VT_BOTTOM_LEFT = 8
  };
  const MECData::DF_PositionOffsetLLV *top_left() const {
    return GetPointer<const MECData::DF_PositionOffsetLLV *>(VT_TOP_LEFT);
  }
  const MECData::DF_PositionOffsetLLV *top_right() const {
    return GetPointer<const MECData::DF_PositionOffsetLLV *>(VT_TOP_RIGHT);
  }
  const MECData::DF_PositionOffsetLLV *bottom_left() const {
    return GetPointer<const MECData::DF_PositionOffsetLLV *>(VT_BOTTOM_LEFT);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_TOP_LEFT) &&
           verifier.VerifyTable(top_left()) &&
           VerifyOffset(verifier, VT_TOP_RIGHT) &&
           verifier.VerifyTable(top_right()) &&
           VerifyOffset(verifier, VT_BOTTOM_LEFT) &&
           verifier.VerifyTable(bottom_left()) &&
           verifier.EndTable();
  }
};

struct DF_ParkingSlotPositionBuilder {
  typedef DF_ParkingSlotPosition Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_top_left(::flatbuffers::Offset<MECData::DF_PositionOffsetLLV> top_left) {
    fbb_.AddOffset(DF_ParkingSlotPosition::VT_TOP_LEFT, top_left);
  }
  void add_top_right(::flatbuffers::Offset<MECData::DF_PositionOffsetLLV> top_right) {
    fbb_.AddOffset(DF_ParkingSlotPosition::VT_TOP_RIGHT, top_right);
  }
  void add_bottom_left(::flatbuffers::Offset<MECData::DF_PositionOffsetLLV> bottom_left) {
    fbb_.AddOffset(DF_ParkingSlotPosition::VT_BOTTOM_LEFT, bottom_left);
  }
  explicit DF_ParkingSlotPositionBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_ParkingSlotPosition> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_ParkingSlotPosition>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_ParkingSlotPosition> CreateDF_ParkingSlotPosition(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<MECData::DF_PositionOffsetLLV> top_left = 0,
    ::flatbuffers::Offset<MECData::DF_PositionOffsetLLV> top_right = 0,
    ::flatbuffers::Offset<MECData::DF_PositionOffsetLLV> bottom_left = 0) {
  DF_ParkingSlotPositionBuilder builder_(_fbb);
  builder_.add_bottom_left(bottom_left);
  builder_.add_top_right(top_right);
  builder_.add_top_left(top_left);
  return builder_.Finish();
}

struct DF_ParkingSlot FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_ParkingSlotBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_SLOT_ID = 4,
    VT_POSITION = 6,
    VT_SIGN = 8,
    VT_PARKING_TYPE = 10,
    VT_STATUS = 12,
    VT_PARKING_SPACE_THETA = 14,
    VT_PARKING_LOCK = 16
  };
  uint16_t slot_id() const {
    return GetField<uint16_t>(VT_SLOT_ID, 0);
  }
  const MECData::DF_ParkingSlotPosition *position() const {
    return GetPointer<const MECData::DF_ParkingSlotPosition *>(VT_POSITION);
  }
  const ::flatbuffers::String *sign() const {
    return GetPointer<const ::flatbuffers::String *>(VT_SIGN);
  }
  uint16_t parking_type() const {
    return GetField<uint16_t>(VT_PARKING_TYPE, 0);
  }
  MECData::DE_SlotStatus status() const {
    return static_cast<MECData::DE_SlotStatus>(GetField<uint8_t>(VT_STATUS, 0));
  }
  MECData::DE_ParkingSpaceTheta parking_space_theta() const {
    return static_cast<MECData::DE_ParkingSpaceTheta>(GetField<uint8_t>(VT_PARKING_SPACE_THETA, 0));
  }
  MECData::DE_ParkingLock parking_lock() const {
    return static_cast<MECData::DE_ParkingLock>(GetField<uint8_t>(VT_PARKING_LOCK, 0));
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<uint16_t>(verifier, VT_SLOT_ID, 2) &&
           VerifyOffset(verifier, VT_POSITION) &&
           verifier.VerifyTable(position()) &&
           VerifyOffset(verifier, VT_SIGN) &&
           verifier.VerifyString(sign()) &&
           VerifyField<uint16_t>(verifier, VT_PARKING_TYPE, 2) &&
           VerifyField<uint8_t>(verifier, VT_STATUS, 1) &&
           VerifyField<uint8_t>(verifier, VT_PARKING_SPACE_THETA, 1) &&
           VerifyField<uint8_t>(verifier, VT_PARKING_LOCK, 1) &&
           verifier.EndTable();
  }
};

struct DF_ParkingSlotBuilder {
  typedef DF_ParkingSlot Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_slot_id(uint16_t slot_id) {
    fbb_.AddElement<uint16_t>(DF_ParkingSlot::VT_SLOT_ID, slot_id, 0);
  }
  void add_position(::flatbuffers::Offset<MECData::DF_ParkingSlotPosition> position) {
    fbb_.AddOffset(DF_ParkingSlot::VT_POSITION, position);
  }
  void add_sign(::flatbuffers::Offset<::flatbuffers::String> sign) {
    fbb_.AddOffset(DF_ParkingSlot::VT_SIGN, sign);
  }
  void add_parking_type(uint16_t parking_type) {
    fbb_.AddElement<uint16_t>(DF_ParkingSlot::VT_PARKING_TYPE, parking_type, 0);
  }
  void add_status(MECData::DE_SlotStatus status) {
    fbb_.AddElement<uint8_t>(DF_ParkingSlot::VT_STATUS, static_cast<uint8_t>(status), 0);
  }
  void add_parking_space_theta(MECData::DE_ParkingSpaceTheta parking_space_theta) {
    fbb_.AddElement<uint8_t>(DF_ParkingSlot::VT_PARKING_SPACE_THETA, static_cast<uint8_t>(parking_space_theta), 0);
  }
  void add_parking_lock(MECData::DE_ParkingLock parking_lock) {
    fbb_.AddElement<uint8_t>(DF_ParkingSlot::VT_PARKING_LOCK, static_cast<uint8_t>(parking_lock), 0);
  }
  explicit DF_ParkingSlotBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_ParkingSlot> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_ParkingSlot>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_ParkingSlot> CreateDF_ParkingSlot(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    uint16_t slot_id = 0,
    ::flatbuffers::Offset<MECData::DF_ParkingSlotPosition> position = 0,
    ::flatbuffers::Offset<::flatbuffers::String> sign = 0,
    uint16_t parking_type = 0,
    MECData::DE_SlotStatus status = MECData::DE_SlotStatus_UNKNOWN,
    MECData::DE_ParkingSpaceTheta parking_space_theta = MECData::DE_ParkingSpaceTheta_UNKNOWN,
    MECData::DE_ParkingLock parking_lock = MECData::DE_ParkingLock_UNKNOWN) {
  DF_ParkingSlotBuilder builder_(_fbb);
  builder_.add_sign(sign);
  builder_.add_position(position);
  builder_.add_parking_type(parking_type);
  builder_.add_slot_id(slot_id);
  builder_.add_parking_lock(parking_lock);
  builder_.add_parking_space_theta(parking_space_theta);
  builder_.add_status(status);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DF_ParkingSlot> CreateDF_ParkingSlotDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    uint16_t slot_id = 0,
    ::flatbuffers::Offset<MECData::DF_ParkingSlotPosition> position = 0,
    const char *sign = nullptr,
    uint16_t parking_type = 0,
    MECData::DE_SlotStatus status = MECData::DE_SlotStatus_UNKNOWN,
    MECData::DE_ParkingSpaceTheta parking_space_theta = MECData::DE_ParkingSpaceTheta_UNKNOWN,
    MECData::DE_ParkingLock parking_lock = MECData::DE_ParkingLock_UNKNOWN) {
  auto sign__ = sign ? _fbb.CreateString(sign) : 0;
  return MECData::CreateDF_ParkingSlot(
      _fbb,
      slot_id,
      position,
      sign__,
      parking_type,
      status,
      parking_space_theta,
      parking_lock);
}

struct DF_PAMDrive FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_PAMDriveBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_UPSTREAM_PAMNODE_ID = 4,
    VT_DRIVE_ID = 6,
    VT_TWO_WAY_SEPERATION = 8,
    VT_SPEED_LIMIT = 10,
    VT_HEIGHT_RESTRICTION = 12,
    VT_DRIVE_WIDTH = 14,
    VT_POINTS = 16,
    VT_MOVEMENTS = 18,
    VT_PARKING_SLOTS = 20
  };
  uint16_t upstream_pamnode_id() const {
    return GetField<uint16_t>(VT_UPSTREAM_PAMNODE_ID, 0);
  }
  uint8_t drive_id() const {
    return GetField<uint8_t>(VT_DRIVE_ID, 0);
  }
  bool two_way_seperation() const {
    return GetField<uint8_t>(VT_TWO_WAY_SEPERATION, 0) != 0;
  }
  uint16_t speed_limit() const {
    return GetField<uint16_t>(VT_SPEED_LIMIT, 0);
  }
  uint8_t height_restriction() const {
    return GetField<uint8_t>(VT_HEIGHT_RESTRICTION, 0);
  }
  uint8_t drive_width() const {
    return GetField<uint8_t>(VT_DRIVE_WIDTH, 0);
  }
  const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_RoadPoint>> *points() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_RoadPoint>> *>(VT_POINTS);
  }
  const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_PAMMovement>> *movements() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_PAMMovement>> *>(VT_MOVEMENTS);
  }
  const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_ParkingSlot>> *parking_slots() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_ParkingSlot>> *>(VT_PARKING_SLOTS);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<uint16_t>(verifier, VT_UPSTREAM_PAMNODE_ID, 2) &&
           VerifyField<uint8_t>(verifier, VT_DRIVE_ID, 1) &&
           VerifyField<uint8_t>(verifier, VT_TWO_WAY_SEPERATION, 1) &&
           VerifyField<uint16_t>(verifier, VT_SPEED_LIMIT, 2) &&
           VerifyField<uint8_t>(verifier, VT_HEIGHT_RESTRICTION, 1) &&
           VerifyField<uint8_t>(verifier, VT_DRIVE_WIDTH, 1) &&
           VerifyOffset(verifier, VT_POINTS) &&
           verifier.VerifyVector(points()) &&
           verifier.VerifyVectorOfTables(points()) &&
           VerifyOffset(verifier, VT_MOVEMENTS) &&
           verifier.VerifyVector(movements()) &&
           verifier.VerifyVectorOfTables(movements()) &&
           VerifyOffset(verifier, VT_PARKING_SLOTS) &&
           verifier.VerifyVector(parking_slots()) &&
           verifier.VerifyVectorOfTables(parking_slots()) &&
           verifier.EndTable();
  }
};

struct DF_PAMDriveBuilder {
  typedef DF_PAMDrive Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_upstream_pamnode_id(uint16_t upstream_pamnode_id) {
    fbb_.AddElement<uint16_t>(DF_PAMDrive::VT_UPSTREAM_PAMNODE_ID, upstream_pamnode_id, 0);
  }
  void add_drive_id(uint8_t drive_id) {
    fbb_.AddElement<uint8_t>(DF_PAMDrive::VT_DRIVE_ID, drive_id, 0);
  }
  void add_two_way_seperation(bool two_way_seperation) {
    fbb_.AddElement<uint8_t>(DF_PAMDrive::VT_TWO_WAY_SEPERATION, static_cast<uint8_t>(two_way_seperation), 0);
  }
  void add_speed_limit(uint16_t speed_limit) {
    fbb_.AddElement<uint16_t>(DF_PAMDrive::VT_SPEED_LIMIT, speed_limit, 0);
  }
  void add_height_restriction(uint8_t height_restriction) {
    fbb_.AddElement<uint8_t>(DF_PAMDrive::VT_HEIGHT_RESTRICTION, height_restriction, 0);
  }
  void add_drive_width(uint8_t drive_width) {
    fbb_.AddElement<uint8_t>(DF_PAMDrive::VT_DRIVE_WIDTH, drive_width, 0);
  }
  void add_points(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_RoadPoint>>> points) {
    fbb_.AddOffset(DF_PAMDrive::VT_POINTS, points);
  }
  void add_movements(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_PAMMovement>>> movements) {
    fbb_.AddOffset(DF_PAMDrive::VT_MOVEMENTS, movements);
  }
  void add_parking_slots(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_ParkingSlot>>> parking_slots) {
    fbb_.AddOffset(DF_PAMDrive::VT_PARKING_SLOTS, parking_slots);
  }
  explicit DF_PAMDriveBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_PAMDrive> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_PAMDrive>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_PAMDrive> CreateDF_PAMDrive(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    uint16_t upstream_pamnode_id = 0,
    uint8_t drive_id = 0,
    bool two_way_seperation = false,
    uint16_t speed_limit = 0,
    uint8_t height_restriction = 0,
    uint8_t drive_width = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_RoadPoint>>> points = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_PAMMovement>>> movements = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_ParkingSlot>>> parking_slots = 0) {
  DF_PAMDriveBuilder builder_(_fbb);
  builder_.add_parking_slots(parking_slots);
  builder_.add_movements(movements);
  builder_.add_points(points);
  builder_.add_speed_limit(speed_limit);
  builder_.add_upstream_pamnode_id(upstream_pamnode_id);
  builder_.add_drive_width(drive_width);
  builder_.add_height_restriction(height_restriction);
  builder_.add_two_way_seperation(two_way_seperation);
  builder_.add_drive_id(drive_id);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DF_PAMDrive> CreateDF_PAMDriveDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    uint16_t upstream_pamnode_id = 0,
    uint8_t drive_id = 0,
    bool two_way_seperation = false,
    uint16_t speed_limit = 0,
    uint8_t height_restriction = 0,
    uint8_t drive_width = 0,
    const std::vector<::flatbuffers::Offset<MECData::DF_RoadPoint>> *points = nullptr,
    const std::vector<::flatbuffers::Offset<MECData::DF_PAMMovement>> *movements = nullptr,
    const std::vector<::flatbuffers::Offset<MECData::DF_ParkingSlot>> *parking_slots = nullptr) {
  auto points__ = points ? _fbb.CreateVector<::flatbuffers::Offset<MECData::DF_RoadPoint>>(*points) : 0;
  auto movements__ = movements ? _fbb.CreateVector<::flatbuffers::Offset<MECData::DF_PAMMovement>>(*movements) : 0;
  auto parking_slots__ = parking_slots ? _fbb.CreateVector<::flatbuffers::Offset<MECData::DF_ParkingSlot>>(*parking_slots) : 0;
  return MECData::CreateDF_PAMDrive(
      _fbb,
      upstream_pamnode_id,
      drive_id,
      two_way_seperation,
      speed_limit,
      height_restriction,
      drive_width,
      points__,
      movements__,
      parking_slots__);
}

struct DF_PAMNode FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_PAMNodeBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_ID = 4,
    VT_REF_POS = 6,
    VT_FLOOR = 8,
    VT_ATTRIBUTES = 10,
    VT_IN_DRIVES = 12
  };
  uint16_t id() const {
    return GetField<uint16_t>(VT_ID, 0);
  }
  const MECData::DF_Position3D *ref_pos() const {
    return GetPointer<const MECData::DF_Position3D *>(VT_REF_POS);
  }
  int8_t floor() const {
    return GetField<int8_t>(VT_FLOOR, 0);
  }
  uint8_t attributes() const {
    return GetField<uint8_t>(VT_ATTRIBUTES, 0);
  }
  const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_PAMDrive>> *in_drives() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_PAMDrive>> *>(VT_IN_DRIVES);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<uint16_t>(verifier, VT_ID, 2) &&
           VerifyOffset(verifier, VT_REF_POS) &&
           verifier.VerifyTable(ref_pos()) &&
           VerifyField<int8_t>(verifier, VT_FLOOR, 1) &&
           VerifyField<uint8_t>(verifier, VT_ATTRIBUTES, 1) &&
           VerifyOffset(verifier, VT_IN_DRIVES) &&
           verifier.VerifyVector(in_drives()) &&
           verifier.VerifyVectorOfTables(in_drives()) &&
           verifier.EndTable();
  }
};

struct DF_PAMNodeBuilder {
  typedef DF_PAMNode Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_id(uint16_t id) {
    fbb_.AddElement<uint16_t>(DF_PAMNode::VT_ID, id, 0);
  }
  void add_ref_pos(::flatbuffers::Offset<MECData::DF_Position3D> ref_pos) {
    fbb_.AddOffset(DF_PAMNode::VT_REF_POS, ref_pos);
  }
  void add_floor(int8_t floor) {
    fbb_.AddElement<int8_t>(DF_PAMNode::VT_FLOOR, floor, 0);
  }
  void add_attributes(uint8_t attributes) {
    fbb_.AddElement<uint8_t>(DF_PAMNode::VT_ATTRIBUTES, attributes, 0);
  }
  void add_in_drives(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_PAMDrive>>> in_drives) {
    fbb_.AddOffset(DF_PAMNode::VT_IN_DRIVES, in_drives);
  }
  explicit DF_PAMNodeBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_PAMNode> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_PAMNode>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_PAMNode> CreateDF_PAMNode(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    uint16_t id = 0,
    ::flatbuffers::Offset<MECData::DF_Position3D> ref_pos = 0,
    int8_t floor = 0,
    uint8_t attributes = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_PAMDrive>>> in_drives = 0) {
  DF_PAMNodeBuilder builder_(_fbb);
  builder_.add_in_drives(in_drives);
  builder_.add_ref_pos(ref_pos);
  builder_.add_id(id);
  builder_.add_attributes(attributes);
  builder_.add_floor(floor);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DF_PAMNode> CreateDF_PAMNodeDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    uint16_t id = 0,
    ::flatbuffers::Offset<MECData::DF_Position3D> ref_pos = 0,
    int8_t floor = 0,
    uint8_t attributes = 0,
    const std::vector<::flatbuffers::Offset<MECData::DF_PAMDrive>> *in_drives = nullptr) {
  auto in_drives__ = in_drives ? _fbb.CreateVector<::flatbuffers::Offset<MECData::DF_PAMDrive>>(*in_drives) : 0;
  return MECData::CreateDF_PAMNode(
      _fbb,
      id,
      ref_pos,
      floor,
      attributes,
      in_drives__);
}

struct DF_ParkingGuide FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_ParkingGuideBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_VEH_ID = 4,
    VT_DRIVE_PATH = 6,
    VT_TARGET_PARKING_SLOT = 8
  };
  const ::flatbuffers::Vector<uint8_t> *veh_id() const {
    return GetPointer<const ::flatbuffers::Vector<uint8_t> *>(VT_VEH_ID);
  }
  const ::flatbuffers::Vector<uint16_t> *drive_path() const {
    return GetPointer<const ::flatbuffers::Vector<uint16_t> *>(VT_DRIVE_PATH);
  }
  uint16_t target_parking_slot() const {
    return GetField<uint16_t>(VT_TARGET_PARKING_SLOT, 0);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_VEH_ID) &&
           verifier.VerifyVector(veh_id()) &&
           VerifyOffset(verifier, VT_DRIVE_PATH) &&
           verifier.VerifyVector(drive_path()) &&
           VerifyField<uint16_t>(verifier, VT_TARGET_PARKING_SLOT, 2) &&
           verifier.EndTable();
  }
};

struct DF_ParkingGuideBuilder {
  typedef DF_ParkingGuide Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_veh_id(::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> veh_id) {
    fbb_.AddOffset(DF_ParkingGuide::VT_VEH_ID, veh_id);
  }
  void add_drive_path(::flatbuffers::Offset<::flatbuffers::Vector<uint16_t>> drive_path) {
    fbb_.AddOffset(DF_ParkingGuide::VT_DRIVE_PATH, drive_path);
  }
  void add_target_parking_slot(uint16_t target_parking_slot) {
    fbb_.AddElement<uint16_t>(DF_ParkingGuide::VT_TARGET_PARKING_SLOT, target_parking_slot, 0);
  }
  explicit DF_ParkingGuideBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_ParkingGuide> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_ParkingGuide>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_ParkingGuide> CreateDF_ParkingGuide(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::Vector<uint8_t>> veh_id = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<uint16_t>> drive_path = 0,
    uint16_t target_parking_slot = 0) {
  DF_ParkingGuideBuilder builder_(_fbb);
  builder_.add_drive_path(drive_path);
  builder_.add_veh_id(veh_id);
  builder_.add_target_parking_slot(target_parking_slot);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DF_ParkingGuide> CreateDF_ParkingGuideDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const std::vector<uint8_t> *veh_id = nullptr,
    const std::vector<uint16_t> *drive_path = nullptr,
    uint16_t target_parking_slot = 0) {
  auto veh_id__ = veh_id ? _fbb.CreateVector<uint8_t>(*veh_id) : 0;
  auto drive_path__ = drive_path ? _fbb.CreateVector<uint16_t>(*drive_path) : 0;
  return MECData::CreateDF_ParkingGuide(
      _fbb,
      veh_id__,
      drive_path__,
      target_parking_slot);
}

struct MSG_PAMData FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef MSG_PAMDataBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_MOY = 4,
    VT_PARKING_LOT_INFO = 6,
    VT_PAM_NODES = 8,
    VT_PARKING_AREA_GUIDANCE = 10
  };
  uint32_t moy() const {
    return GetField<uint32_t>(VT_MOY, 0);
  }
  const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_ParkingLotInfo>> *parking_lot_info() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_ParkingLotInfo>> *>(VT_PARKING_LOT_INFO);
  }
  const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_PAMNode>> *pam_nodes() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_PAMNode>> *>(VT_PAM_NODES);
  }
  const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_ParkingGuide>> *parking_area_guidance() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_ParkingGuide>> *>(VT_PARKING_AREA_GUIDANCE);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<uint32_t>(verifier, VT_MOY, 4) &&
           VerifyOffset(verifier, VT_PARKING_LOT_INFO) &&
           verifier.VerifyVector(parking_lot_info()) &&
           verifier.VerifyVectorOfTables(parking_lot_info()) &&
           VerifyOffset(verifier, VT_PAM_NODES) &&
           verifier.VerifyVector(pam_nodes()) &&
           verifier.VerifyVectorOfTables(pam_nodes()) &&
           VerifyOffset(verifier, VT_PARKING_AREA_GUIDANCE) &&
           verifier.VerifyVector(parking_area_guidance()) &&
           verifier.VerifyVectorOfTables(parking_area_guidance()) &&
           verifier.EndTable();
  }
};

struct MSG_PAMDataBuilder {
  typedef MSG_PAMData Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_moy(uint32_t moy) {
    fbb_.AddElement<uint32_t>(MSG_PAMData::VT_MOY, moy, 0);
  }
  void add_parking_lot_info(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_ParkingLotInfo>>> parking_lot_info) {
    fbb_.AddOffset(MSG_PAMData::VT_PARKING_LOT_INFO, parking_lot_info);
  }
  void add_pam_nodes(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_PAMNode>>> pam_nodes) {
    fbb_.AddOffset(MSG_PAMData::VT_PAM_NODES, pam_nodes);
  }
  void add_parking_area_guidance(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_ParkingGuide>>> parking_area_guidance) {
    fbb_.AddOffset(MSG_PAMData::VT_PARKING_AREA_GUIDANCE, parking_area_guidance);
  }
  explicit MSG_PAMDataBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<MSG_PAMData> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<MSG_PAMData>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<MSG_PAMData> CreateMSG_PAMData(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    uint32_t moy = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_ParkingLotInfo>>> parking_lot_info = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_PAMNode>>> pam_nodes = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_ParkingGuide>>> parking_area_guidance = 0) {
  MSG_PAMDataBuilder builder_(_fbb);
  builder_.add_parking_area_guidance(parking_area_guidance);
  builder_.add_pam_nodes(pam_nodes);
  builder_.add_parking_lot_info(parking_lot_info);
  builder_.add_moy(moy);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<MSG_PAMData> CreateMSG_PAMDataDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    uint32_t moy = 0,
    const std::vector<::flatbuffers::Offset<MECData::DF_ParkingLotInfo>> *parking_lot_info = nullptr,
    const std::vector<::flatbuffers::Offset<MECData::DF_PAMNode>> *pam_nodes = nullptr,
    const std::vector<::flatbuffers::Offset<MECData::DF_ParkingGuide>> *parking_area_guidance = nullptr) {
  auto parking_lot_info__ = parking_lot_info ? _fbb.CreateVector<::flatbuffers::Offset<MECData::DF_ParkingLotInfo>>(*parking_lot_info) : 0;
  auto pam_nodes__ = pam_nodes ? _fbb.CreateVector<::flatbuffers::Offset<MECData::DF_PAMNode>>(*pam_nodes) : 0;
  auto parking_area_guidance__ = parking_area_guidance ? _fbb.CreateVector<::flatbuffers::Offset<MECData::DF_ParkingGuide>>(*parking_area_guidance) : 0;
  return MECData::CreateMSG_PAMData(
      _fbb,
      moy,
      parking_lot_info__,
      pam_nodes__,
      parking_area_guidance__);
}

inline const MECData::MSG_PAMData *GetMSG_PAMData(const void *buf) {
  return ::flatbuffers::GetRoot<MECData::MSG_PAMData>(buf);
}

inline const MECData::MSG_PAMData *GetSizePrefixedMSG_PAMData(const void *buf) {
  return ::flatbuffers::GetSizePrefixedRoot<MECData::MSG_PAMData>(buf);
}

inline bool VerifyMSG_PAMDataBuffer(
    ::flatbuffers::Verifier &verifier) {
  return verifier.VerifyBuffer<MECData::MSG_PAMData>(nullptr);
}

inline bool VerifySizePrefixedMSG_PAMDataBuffer(
    ::flatbuffers::Verifier &verifier) {
  return verifier.VerifySizePrefixedBuffer<MECData::MSG_PAMData>(nullptr);
}

inline void FinishMSG_PAMDataBuffer(
    ::flatbuffers::FlatBufferBuilder &fbb,
    ::flatbuffers::Offset<MECData::MSG_PAMData> root) {
  fbb.Finish(root);
}

inline void FinishSizePrefixedMSG_PAMDataBuffer(
    ::flatbuffers::FlatBufferBuilder &fbb,
    ::flatbuffers::Offset<MECData::MSG_PAMData> root) {
  fbb.FinishSizePrefixed(root);
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_PAMDATA_MECDATA_H_
