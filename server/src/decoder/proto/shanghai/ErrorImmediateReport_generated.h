// automatically generated by the FlatBuffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_ERRORIMMEDIATEREPORT_MECDATA_H_
#define FLATBUFFERS_GENERATED_ERRORIMMEDIATEREPORT_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

#include "ErrorLevel_generated.h"

namespace MECData {

struct MSG_ErrorImmediateReport;
struct MSG_ErrorImmediateReportBuilder;

struct MSG_ErrorImmediateReport FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef MSG_ErrorImmediateReportBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_MODULE_ID = 4,
    VT_TIME = 6,
    VT_NATIVE_ERROR_ID = 8,
    VT_LEVEL = 10,
    VT_DESCRIPTION = 12,
    VT_MSG_ID = 14
  };
  uint16_t module_id() const {
    return GetField<uint16_t>(VT_MODULE_ID, 0);
  }
  int64_t time() const {
    return GetField<int64_t>(VT_TIME, 0);
  }
  int16_t native_error_id() const {
    return GetField<int16_t>(VT_NATIVE_ERROR_ID, 0);
  }
  MECData::DE_ErrorLevel level() const {
    return static_cast<MECData::DE_ErrorLevel>(GetField<int8_t>(VT_LEVEL, 0));
  }
  const ::flatbuffers::String *description() const {
    return GetPointer<const ::flatbuffers::String *>(VT_DESCRIPTION);
  }
  int64_t msg_id() const {
    return GetField<int64_t>(VT_MSG_ID, 0);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<uint16_t>(verifier, VT_MODULE_ID, 2) &&
           VerifyField<int64_t>(verifier, VT_TIME, 8) &&
           VerifyField<int16_t>(verifier, VT_NATIVE_ERROR_ID, 2) &&
           VerifyField<int8_t>(verifier, VT_LEVEL, 1) &&
           VerifyOffset(verifier, VT_DESCRIPTION) &&
           verifier.VerifyString(description()) &&
           VerifyField<int64_t>(verifier, VT_MSG_ID, 8) &&
           verifier.EndTable();
  }
};

struct MSG_ErrorImmediateReportBuilder {
  typedef MSG_ErrorImmediateReport Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_module_id(uint16_t module_id) {
    fbb_.AddElement<uint16_t>(MSG_ErrorImmediateReport::VT_MODULE_ID, module_id, 0);
  }
  void add_time(int64_t time) {
    fbb_.AddElement<int64_t>(MSG_ErrorImmediateReport::VT_TIME, time, 0);
  }
  void add_native_error_id(int16_t native_error_id) {
    fbb_.AddElement<int16_t>(MSG_ErrorImmediateReport::VT_NATIVE_ERROR_ID, native_error_id, 0);
  }
  void add_level(MECData::DE_ErrorLevel level) {
    fbb_.AddElement<int8_t>(MSG_ErrorImmediateReport::VT_LEVEL, static_cast<int8_t>(level), 0);
  }
  void add_description(::flatbuffers::Offset<::flatbuffers::String> description) {
    fbb_.AddOffset(MSG_ErrorImmediateReport::VT_DESCRIPTION, description);
  }
  void add_msg_id(int64_t msg_id) {
    fbb_.AddElement<int64_t>(MSG_ErrorImmediateReport::VT_MSG_ID, msg_id, 0);
  }
  explicit MSG_ErrorImmediateReportBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<MSG_ErrorImmediateReport> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<MSG_ErrorImmediateReport>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<MSG_ErrorImmediateReport> CreateMSG_ErrorImmediateReport(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    uint16_t module_id = 0,
    int64_t time = 0,
    int16_t native_error_id = 0,
    MECData::DE_ErrorLevel level = MECData::DE_ErrorLevel_DEBUG,
    ::flatbuffers::Offset<::flatbuffers::String> description = 0,
    int64_t msg_id = 0) {
  MSG_ErrorImmediateReportBuilder builder_(_fbb);
  builder_.add_msg_id(msg_id);
  builder_.add_time(time);
  builder_.add_description(description);
  builder_.add_native_error_id(native_error_id);
  builder_.add_module_id(module_id);
  builder_.add_level(level);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<MSG_ErrorImmediateReport> CreateMSG_ErrorImmediateReportDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    uint16_t module_id = 0,
    int64_t time = 0,
    int16_t native_error_id = 0,
    MECData::DE_ErrorLevel level = MECData::DE_ErrorLevel_DEBUG,
    const char *description = nullptr,
    int64_t msg_id = 0) {
  auto description__ = description ? _fbb.CreateString(description) : 0;
  return MECData::CreateMSG_ErrorImmediateReport(
      _fbb,
      module_id,
      time,
      native_error_id,
      level,
      description__,
      msg_id);
}

inline const MECData::MSG_ErrorImmediateReport *GetMSG_ErrorImmediateReport(const void *buf) {
  return ::flatbuffers::GetRoot<MECData::MSG_ErrorImmediateReport>(buf);
}

inline const MECData::MSG_ErrorImmediateReport *GetSizePrefixedMSG_ErrorImmediateReport(const void *buf) {
  return ::flatbuffers::GetSizePrefixedRoot<MECData::MSG_ErrorImmediateReport>(buf);
}

inline bool VerifyMSG_ErrorImmediateReportBuffer(
    ::flatbuffers::Verifier &verifier) {
  return verifier.VerifyBuffer<MECData::MSG_ErrorImmediateReport>(nullptr);
}

inline bool VerifySizePrefixedMSG_ErrorImmediateReportBuffer(
    ::flatbuffers::Verifier &verifier) {
  return verifier.VerifySizePrefixedBuffer<MECData::MSG_ErrorImmediateReport>(nullptr);
}

inline void FinishMSG_ErrorImmediateReportBuffer(
    ::flatbuffers::FlatBufferBuilder &fbb,
    ::flatbuffers::Offset<MECData::MSG_ErrorImmediateReport> root) {
  fbb.Finish(root);
}

inline void FinishSizePrefixedMSG_ErrorImmediateReportBuffer(
    ::flatbuffers::FlatBufferBuilder &fbb,
    ::flatbuffers::Offset<MECData::MSG_ErrorImmediateReport> root) {
  fbb.FinishSizePrefixed(root);
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_ERRORIMMEDIATEREPORT_MECDATA_H_
