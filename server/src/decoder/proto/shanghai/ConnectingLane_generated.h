// automatically generated by the <PERSON><PERSON>uffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_CONNECTINGLANE_MECDATA_H_
#define FLATBUFFERS_GENERATED_CONNECTINGLANE_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

#include "AllowedManeuvers_generated.h"

namespace MECData {

struct DF_ConnectingLane;
struct DF_ConnectingLaneBuilder;

struct DF_ConnectingLane FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_ConnectingLaneBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_LANE = 4,
    VT_MANEUVER = 6
  };
  uint8_t lane() const {
    return GetField<uint8_t>(VT_LANE, 255);
  }
  const MECData::DE_AllowedManeuvers *maneuver() const {
    return GetPointer<const MECData::DE_AllowedManeuvers *>(VT_MANEUVER);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<uint8_t>(verifier, VT_LANE, 1) &&
           VerifyOffset(verifier, VT_MANEUVER) &&
           verifier.VerifyTable(maneuver()) &&
           verifier.EndTable();
  }
};

struct DF_ConnectingLaneBuilder {
  typedef DF_ConnectingLane Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_lane(uint8_t lane) {
    fbb_.AddElement<uint8_t>(DF_ConnectingLane::VT_LANE, lane, 255);
  }
  void add_maneuver(::flatbuffers::Offset<MECData::DE_AllowedManeuvers> maneuver) {
    fbb_.AddOffset(DF_ConnectingLane::VT_MANEUVER, maneuver);
  }
  explicit DF_ConnectingLaneBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_ConnectingLane> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_ConnectingLane>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_ConnectingLane> CreateDF_ConnectingLane(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    uint8_t lane = 255,
    ::flatbuffers::Offset<MECData::DE_AllowedManeuvers> maneuver = 0) {
  DF_ConnectingLaneBuilder builder_(_fbb);
  builder_.add_maneuver(maneuver);
  builder_.add_lane(lane);
  return builder_.Finish();
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_CONNECTINGLANE_MECDATA_H_
