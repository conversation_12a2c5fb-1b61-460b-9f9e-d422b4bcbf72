// automatically generated by the FlatBuffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_ANGULARVCONFIDENCE_MECDATA_H_
#define FLATBUFFERS_GENERATED_ANGULARVCONFIDENCE_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

namespace MECData {

enum DE_AngularVConfidence : int8_t {
  DE_AngularVConfidence_unavailable = 0,
  DE_AngularVConfidence_prec100deg = 1,
  DE_AngularVConfidence_prec10deg = 2,
  DE_AngularVConfidence_prec5deg = 3,
  DE_AngularVConfidence_prec1deg = 4,
  DE_AngularVConfidence_prec01deg = 5,
  DE_AngularVConfidence_prec005deg = 6,
  DE_AngularVConfidence_prec001deg = 7,
  DE_AngularVConfidence_MIN = DE_AngularVConfidence_unavailable,
  DE_AngularVConfidence_MAX = DE_AngularVConfidence_prec001deg
};

inline const DE_AngularVConfidence (&EnumValuesDE_AngularVConfidence())[8] {
  static const DE_AngularVConfidence values[] = {
    DE_AngularVConfidence_unavailable,
    DE_AngularVConfidence_prec100deg,
    DE_AngularVConfidence_prec10deg,
    DE_AngularVConfidence_prec5deg,
    DE_AngularVConfidence_prec1deg,
    DE_AngularVConfidence_prec01deg,
    DE_AngularVConfidence_prec005deg,
    DE_AngularVConfidence_prec001deg
  };
  return values;
}

inline const char * const *EnumNamesDE_AngularVConfidence() {
  static const char * const names[9] = {
    "unavailable",
    "prec100deg",
    "prec10deg",
    "prec5deg",
    "prec1deg",
    "prec01deg",
    "prec005deg",
    "prec001deg",
    nullptr
  };
  return names;
}

inline const char *EnumNameDE_AngularVConfidence(DE_AngularVConfidence e) {
  if (::flatbuffers::IsOutRange(e, DE_AngularVConfidence_unavailable, DE_AngularVConfidence_prec001deg)) return "";
  const size_t index = static_cast<size_t>(e);
  return EnumNamesDE_AngularVConfidence()[index];
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_ANGULARVCONFIDENCE_MECDATA_H_
