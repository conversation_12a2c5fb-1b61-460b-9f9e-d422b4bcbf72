// automatically generated by the <PERSON>Buffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_INTERSECTIONSTATE_MECDATA_H_
#define FLATBUFFERS_GENERATED_INTERSECTIONSTATE_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

#include "IntersectionStatusObject_generated.h"
#include "NodeReferenceID_generated.h"
#include "Phase_generated.h"
#include "TimeConfidence_generated.h"

namespace MECData {

struct DF_IntersectionState;
struct DF_IntersectionStateBuilder;

struct DF_IntersectionState FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_IntersectionStateBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_INTERSECTIONID = 4,
    VT_STATUS = 6,
    VT_MOY = 8,
    VT_TIMESTAMP = 10,
    VT_TIMECONFIDENCE = 12,
    VT_PHASES = 14
  };
  const MECData::DF_NodeReferenceID *intersectionId() const {
    return GetPointer<const MECData::DF_NodeReferenceID *>(VT_INTERSECTIONID);
  }
  const MECData::DE_IntersectionStatusObject *status() const {
    return GetPointer<const MECData::DE_IntersectionStatusObject *>(VT_STATUS);
  }
  uint32_t moy() const {
    return GetField<uint32_t>(VT_MOY, 0);
  }
  uint16_t timeStamp() const {
    return GetField<uint16_t>(VT_TIMESTAMP, 0);
  }
  MECData::DE_TimeConfidence timeConfidence() const {
    return static_cast<MECData::DE_TimeConfidence>(GetField<int8_t>(VT_TIMECONFIDENCE, 0));
  }
  const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_Phase>> *phases() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_Phase>> *>(VT_PHASES);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffsetRequired(verifier, VT_INTERSECTIONID) &&
           verifier.VerifyTable(intersectionId()) &&
           VerifyOffsetRequired(verifier, VT_STATUS) &&
           verifier.VerifyTable(status()) &&
           VerifyField<uint32_t>(verifier, VT_MOY, 4) &&
           VerifyField<uint16_t>(verifier, VT_TIMESTAMP, 2) &&
           VerifyField<int8_t>(verifier, VT_TIMECONFIDENCE, 1) &&
           VerifyOffsetRequired(verifier, VT_PHASES) &&
           verifier.VerifyVector(phases()) &&
           verifier.VerifyVectorOfTables(phases()) &&
           verifier.EndTable();
  }
};

struct DF_IntersectionStateBuilder {
  typedef DF_IntersectionState Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_intersectionId(::flatbuffers::Offset<MECData::DF_NodeReferenceID> intersectionId) {
    fbb_.AddOffset(DF_IntersectionState::VT_INTERSECTIONID, intersectionId);
  }
  void add_status(::flatbuffers::Offset<MECData::DE_IntersectionStatusObject> status) {
    fbb_.AddOffset(DF_IntersectionState::VT_STATUS, status);
  }
  void add_moy(uint32_t moy) {
    fbb_.AddElement<uint32_t>(DF_IntersectionState::VT_MOY, moy, 0);
  }
  void add_timeStamp(uint16_t timeStamp) {
    fbb_.AddElement<uint16_t>(DF_IntersectionState::VT_TIMESTAMP, timeStamp, 0);
  }
  void add_timeConfidence(MECData::DE_TimeConfidence timeConfidence) {
    fbb_.AddElement<int8_t>(DF_IntersectionState::VT_TIMECONFIDENCE, static_cast<int8_t>(timeConfidence), 0);
  }
  void add_phases(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_Phase>>> phases) {
    fbb_.AddOffset(DF_IntersectionState::VT_PHASES, phases);
  }
  explicit DF_IntersectionStateBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_IntersectionState> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_IntersectionState>(end);
    fbb_.Required(o, DF_IntersectionState::VT_INTERSECTIONID);
    fbb_.Required(o, DF_IntersectionState::VT_STATUS);
    fbb_.Required(o, DF_IntersectionState::VT_PHASES);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_IntersectionState> CreateDF_IntersectionState(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<MECData::DF_NodeReferenceID> intersectionId = 0,
    ::flatbuffers::Offset<MECData::DE_IntersectionStatusObject> status = 0,
    uint32_t moy = 0,
    uint16_t timeStamp = 0,
    MECData::DE_TimeConfidence timeConfidence = MECData::DE_TimeConfidence_unavailable,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_Phase>>> phases = 0) {
  DF_IntersectionStateBuilder builder_(_fbb);
  builder_.add_phases(phases);
  builder_.add_moy(moy);
  builder_.add_status(status);
  builder_.add_intersectionId(intersectionId);
  builder_.add_timeStamp(timeStamp);
  builder_.add_timeConfidence(timeConfidence);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DF_IntersectionState> CreateDF_IntersectionStateDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<MECData::DF_NodeReferenceID> intersectionId = 0,
    ::flatbuffers::Offset<MECData::DE_IntersectionStatusObject> status = 0,
    uint32_t moy = 0,
    uint16_t timeStamp = 0,
    MECData::DE_TimeConfidence timeConfidence = MECData::DE_TimeConfidence_unavailable,
    const std::vector<::flatbuffers::Offset<MECData::DF_Phase>> *phases = nullptr) {
  auto phases__ = phases ? _fbb.CreateVector<::flatbuffers::Offset<MECData::DF_Phase>>(*phases) : 0;
  return MECData::CreateDF_IntersectionState(
      _fbb,
      intersectionId,
      status,
      moy,
      timeStamp,
      timeConfidence,
      phases__);
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_INTERSECTIONSTATE_MECDATA_H_
