// automatically generated by the <PERSON>Buffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_PARKINGREQUEST_MECDATA_H_
#define FLATBUFFERS_GENERATED_PARKINGREQUEST_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

namespace MECData {

struct DE_ParkingRequest;
struct DE_ParkingRequestBuilder;

struct DE_ParkingRequest FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DE_ParkingRequestBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_PARKINGREQUEST = 4
  };
  int16_t parkingRequest() const {
    return GetField<int16_t>(VT_PARKINGREQUEST, 0);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<int16_t>(verifier, VT_PARKINGREQUEST, 2) &&
           verifier.EndTable();
  }
};

struct DE_ParkingRequestBuilder {
  typedef DE_ParkingRequest Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_parkingRequest(int16_t parkingRequest) {
    fbb_.AddElement<int16_t>(DE_ParkingRequest::VT_PARKINGREQUEST, parkingRequest, 0);
  }
  explicit DE_ParkingRequestBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DE_ParkingRequest> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DE_ParkingRequest>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DE_ParkingRequest> CreateDE_ParkingRequest(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    int16_t parkingRequest = 0) {
  DE_ParkingRequestBuilder builder_(_fbb);
  builder_.add_parkingRequest(parkingRequest);
  return builder_.Finish();
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_PARKINGREQUEST_MECDATA_H_
