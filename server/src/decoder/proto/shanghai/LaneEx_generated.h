// automatically generated by the <PERSON><PERSON>uffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_LANEEX_MECDATA_H_
#define FLATBUFFERS_GENERATED_LANEEX_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

#include "AllowedManeuvers_generated.h"
#include "ConnectionEx_generated.h"
#include "LaneAttributes_generated.h"
#include "RegulatorySpeedLimit_generated.h"
#include "RoadMark_generated.h"
#include "RoadPoint_generated.h"
#include "STPoint_generated.h"

namespace MECData {

struct DF_LaneEx;
struct DF_LaneExBuilder;

struct DF_LaneEx FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_LaneExBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_LANEREFID = 4,
    VT_LANEWIDTH = 6,
    VT_LANEATTRIBUTES = 8,
    VT_MANEUVERS = 10,
    VT_CONNECTSTO_EX = 12,
    VT_SPEEDLIMITS = 14,
    VT_STPOINTS = 16,
    VT_EXT_ID = 18,
    VT_ROAD_MARKS = 20,
    VT_OFFSET_POINTS = 22
  };
  int8_t laneRefID() const {
    return GetField<int8_t>(VT_LANEREFID, 0);
  }
  uint16_t laneWidth() const {
    return GetField<uint16_t>(VT_LANEWIDTH, 0);
  }
  const MECData::DF_LaneAttributes *laneAttributes() const {
    return GetPointer<const MECData::DF_LaneAttributes *>(VT_LANEATTRIBUTES);
  }
  const MECData::DE_AllowedManeuvers *maneuvers() const {
    return GetPointer<const MECData::DE_AllowedManeuvers *>(VT_MANEUVERS);
  }
  const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_ConnectionEx>> *connectsTo_ex() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_ConnectionEx>> *>(VT_CONNECTSTO_EX);
  }
  const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_RegulatorySpeedLimit>> *speedLimits() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_RegulatorySpeedLimit>> *>(VT_SPEEDLIMITS);
  }
  const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_STPoint>> *stpoints() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_STPoint>> *>(VT_STPOINTS);
  }
  const ::flatbuffers::String *ext_id() const {
    return GetPointer<const ::flatbuffers::String *>(VT_EXT_ID);
  }
  const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_RoadMark>> *road_marks() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_RoadMark>> *>(VT_ROAD_MARKS);
  }
  const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_RoadPoint>> *offset_points() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_RoadPoint>> *>(VT_OFFSET_POINTS);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<int8_t>(verifier, VT_LANEREFID, 1) &&
           VerifyField<uint16_t>(verifier, VT_LANEWIDTH, 2) &&
           VerifyOffset(verifier, VT_LANEATTRIBUTES) &&
           verifier.VerifyTable(laneAttributes()) &&
           VerifyOffset(verifier, VT_MANEUVERS) &&
           verifier.VerifyTable(maneuvers()) &&
           VerifyOffset(verifier, VT_CONNECTSTO_EX) &&
           verifier.VerifyVector(connectsTo_ex()) &&
           verifier.VerifyVectorOfTables(connectsTo_ex()) &&
           VerifyOffset(verifier, VT_SPEEDLIMITS) &&
           verifier.VerifyVector(speedLimits()) &&
           verifier.VerifyVectorOfTables(speedLimits()) &&
           VerifyOffset(verifier, VT_STPOINTS) &&
           verifier.VerifyVector(stpoints()) &&
           verifier.VerifyVectorOfTables(stpoints()) &&
           VerifyOffset(verifier, VT_EXT_ID) &&
           verifier.VerifyString(ext_id()) &&
           VerifyOffset(verifier, VT_ROAD_MARKS) &&
           verifier.VerifyVector(road_marks()) &&
           verifier.VerifyVectorOfTables(road_marks()) &&
           VerifyOffset(verifier, VT_OFFSET_POINTS) &&
           verifier.VerifyVector(offset_points()) &&
           verifier.VerifyVectorOfTables(offset_points()) &&
           verifier.EndTable();
  }
};

struct DF_LaneExBuilder {
  typedef DF_LaneEx Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_laneRefID(int8_t laneRefID) {
    fbb_.AddElement<int8_t>(DF_LaneEx::VT_LANEREFID, laneRefID, 0);
  }
  void add_laneWidth(uint16_t laneWidth) {
    fbb_.AddElement<uint16_t>(DF_LaneEx::VT_LANEWIDTH, laneWidth, 0);
  }
  void add_laneAttributes(::flatbuffers::Offset<MECData::DF_LaneAttributes> laneAttributes) {
    fbb_.AddOffset(DF_LaneEx::VT_LANEATTRIBUTES, laneAttributes);
  }
  void add_maneuvers(::flatbuffers::Offset<MECData::DE_AllowedManeuvers> maneuvers) {
    fbb_.AddOffset(DF_LaneEx::VT_MANEUVERS, maneuvers);
  }
  void add_connectsTo_ex(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_ConnectionEx>>> connectsTo_ex) {
    fbb_.AddOffset(DF_LaneEx::VT_CONNECTSTO_EX, connectsTo_ex);
  }
  void add_speedLimits(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_RegulatorySpeedLimit>>> speedLimits) {
    fbb_.AddOffset(DF_LaneEx::VT_SPEEDLIMITS, speedLimits);
  }
  void add_stpoints(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_STPoint>>> stpoints) {
    fbb_.AddOffset(DF_LaneEx::VT_STPOINTS, stpoints);
  }
  void add_ext_id(::flatbuffers::Offset<::flatbuffers::String> ext_id) {
    fbb_.AddOffset(DF_LaneEx::VT_EXT_ID, ext_id);
  }
  void add_road_marks(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_RoadMark>>> road_marks) {
    fbb_.AddOffset(DF_LaneEx::VT_ROAD_MARKS, road_marks);
  }
  void add_offset_points(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_RoadPoint>>> offset_points) {
    fbb_.AddOffset(DF_LaneEx::VT_OFFSET_POINTS, offset_points);
  }
  explicit DF_LaneExBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_LaneEx> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_LaneEx>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_LaneEx> CreateDF_LaneEx(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    int8_t laneRefID = 0,
    uint16_t laneWidth = 0,
    ::flatbuffers::Offset<MECData::DF_LaneAttributes> laneAttributes = 0,
    ::flatbuffers::Offset<MECData::DE_AllowedManeuvers> maneuvers = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_ConnectionEx>>> connectsTo_ex = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_RegulatorySpeedLimit>>> speedLimits = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_STPoint>>> stpoints = 0,
    ::flatbuffers::Offset<::flatbuffers::String> ext_id = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_RoadMark>>> road_marks = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_RoadPoint>>> offset_points = 0) {
  DF_LaneExBuilder builder_(_fbb);
  builder_.add_offset_points(offset_points);
  builder_.add_road_marks(road_marks);
  builder_.add_ext_id(ext_id);
  builder_.add_stpoints(stpoints);
  builder_.add_speedLimits(speedLimits);
  builder_.add_connectsTo_ex(connectsTo_ex);
  builder_.add_maneuvers(maneuvers);
  builder_.add_laneAttributes(laneAttributes);
  builder_.add_laneWidth(laneWidth);
  builder_.add_laneRefID(laneRefID);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DF_LaneEx> CreateDF_LaneExDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    int8_t laneRefID = 0,
    uint16_t laneWidth = 0,
    ::flatbuffers::Offset<MECData::DF_LaneAttributes> laneAttributes = 0,
    ::flatbuffers::Offset<MECData::DE_AllowedManeuvers> maneuvers = 0,
    const std::vector<::flatbuffers::Offset<MECData::DF_ConnectionEx>> *connectsTo_ex = nullptr,
    const std::vector<::flatbuffers::Offset<MECData::DF_RegulatorySpeedLimit>> *speedLimits = nullptr,
    const std::vector<::flatbuffers::Offset<MECData::DF_STPoint>> *stpoints = nullptr,
    const char *ext_id = nullptr,
    const std::vector<::flatbuffers::Offset<MECData::DF_RoadMark>> *road_marks = nullptr,
    const std::vector<::flatbuffers::Offset<MECData::DF_RoadPoint>> *offset_points = nullptr) {
  auto connectsTo_ex__ = connectsTo_ex ? _fbb.CreateVector<::flatbuffers::Offset<MECData::DF_ConnectionEx>>(*connectsTo_ex) : 0;
  auto speedLimits__ = speedLimits ? _fbb.CreateVector<::flatbuffers::Offset<MECData::DF_RegulatorySpeedLimit>>(*speedLimits) : 0;
  auto stpoints__ = stpoints ? _fbb.CreateVector<::flatbuffers::Offset<MECData::DF_STPoint>>(*stpoints) : 0;
  auto ext_id__ = ext_id ? _fbb.CreateString(ext_id) : 0;
  auto road_marks__ = road_marks ? _fbb.CreateVector<::flatbuffers::Offset<MECData::DF_RoadMark>>(*road_marks) : 0;
  auto offset_points__ = offset_points ? _fbb.CreateVector<::flatbuffers::Offset<MECData::DF_RoadPoint>>(*offset_points) : 0;
  return MECData::CreateDF_LaneEx(
      _fbb,
      laneRefID,
      laneWidth,
      laneAttributes,
      maneuvers,
      connectsTo_ex__,
      speedLimits__,
      stpoints__,
      ext_id__,
      road_marks__,
      offset_points__);
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_LANEEX_MECDATA_H_
