// automatically generated by the <PERSON><PERSON>uffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_HOURLYFORECAST_MECDATA_H_
#define FLATBUFFERS_GENERATED_HOURLYFORECAST_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

namespace MECData {

struct DF_HourlyForecast;
struct DF_HourlyForecastBuilder;

struct DF_HourlyForecast FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_HourlyForecastBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_TIME = 4,
    VT_DESCRIPTION = 6,
    VT_WEATHERCODE = 8,
    VT_TEMPERATURE = 10,
    VT_HUMIDITY = 12,
    VT_WINDDIRECTION = 14,
    VT_WINDSPEED = 16,
    VT_WINDSCALE = 18,
    VT_MSG_ID = 20
  };
  uint32_t time() const {
    return GetField<uint32_t>(VT_TIME, 4294967295);
  }
  const ::flatbuffers::String *description() const {
    return GetPointer<const ::flatbuffers::String *>(VT_DESCRIPTION);
  }
  uint8_t weatherCode() const {
    return GetField<uint8_t>(VT_WEATHERCODE, 255);
  }
  int16_t temperature() const {
    return GetField<int16_t>(VT_TEMPERATURE, 0);
  }
  uint8_t humidity() const {
    return GetField<uint8_t>(VT_HUMIDITY, 0);
  }
  uint16_t windDirection() const {
    return GetField<uint16_t>(VT_WINDDIRECTION, 0);
  }
  uint16_t windSpeed() const {
    return GetField<uint16_t>(VT_WINDSPEED, 0);
  }
  uint8_t windScale() const {
    return GetField<uint8_t>(VT_WINDSCALE, 0);
  }
  int64_t msg_id() const {
    return GetField<int64_t>(VT_MSG_ID, 0);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<uint32_t>(verifier, VT_TIME, 4) &&
           VerifyOffset(verifier, VT_DESCRIPTION) &&
           verifier.VerifyString(description()) &&
           VerifyField<uint8_t>(verifier, VT_WEATHERCODE, 1) &&
           VerifyField<int16_t>(verifier, VT_TEMPERATURE, 2) &&
           VerifyField<uint8_t>(verifier, VT_HUMIDITY, 1) &&
           VerifyField<uint16_t>(verifier, VT_WINDDIRECTION, 2) &&
           VerifyField<uint16_t>(verifier, VT_WINDSPEED, 2) &&
           VerifyField<uint8_t>(verifier, VT_WINDSCALE, 1) &&
           VerifyField<int64_t>(verifier, VT_MSG_ID, 8) &&
           verifier.EndTable();
  }
};

struct DF_HourlyForecastBuilder {
  typedef DF_HourlyForecast Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_time(uint32_t time) {
    fbb_.AddElement<uint32_t>(DF_HourlyForecast::VT_TIME, time, 4294967295);
  }
  void add_description(::flatbuffers::Offset<::flatbuffers::String> description) {
    fbb_.AddOffset(DF_HourlyForecast::VT_DESCRIPTION, description);
  }
  void add_weatherCode(uint8_t weatherCode) {
    fbb_.AddElement<uint8_t>(DF_HourlyForecast::VT_WEATHERCODE, weatherCode, 255);
  }
  void add_temperature(int16_t temperature) {
    fbb_.AddElement<int16_t>(DF_HourlyForecast::VT_TEMPERATURE, temperature, 0);
  }
  void add_humidity(uint8_t humidity) {
    fbb_.AddElement<uint8_t>(DF_HourlyForecast::VT_HUMIDITY, humidity, 0);
  }
  void add_windDirection(uint16_t windDirection) {
    fbb_.AddElement<uint16_t>(DF_HourlyForecast::VT_WINDDIRECTION, windDirection, 0);
  }
  void add_windSpeed(uint16_t windSpeed) {
    fbb_.AddElement<uint16_t>(DF_HourlyForecast::VT_WINDSPEED, windSpeed, 0);
  }
  void add_windScale(uint8_t windScale) {
    fbb_.AddElement<uint8_t>(DF_HourlyForecast::VT_WINDSCALE, windScale, 0);
  }
  void add_msg_id(int64_t msg_id) {
    fbb_.AddElement<int64_t>(DF_HourlyForecast::VT_MSG_ID, msg_id, 0);
  }
  explicit DF_HourlyForecastBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_HourlyForecast> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_HourlyForecast>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_HourlyForecast> CreateDF_HourlyForecast(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    uint32_t time = 4294967295,
    ::flatbuffers::Offset<::flatbuffers::String> description = 0,
    uint8_t weatherCode = 255,
    int16_t temperature = 0,
    uint8_t humidity = 0,
    uint16_t windDirection = 0,
    uint16_t windSpeed = 0,
    uint8_t windScale = 0,
    int64_t msg_id = 0) {
  DF_HourlyForecastBuilder builder_(_fbb);
  builder_.add_msg_id(msg_id);
  builder_.add_description(description);
  builder_.add_time(time);
  builder_.add_windSpeed(windSpeed);
  builder_.add_windDirection(windDirection);
  builder_.add_temperature(temperature);
  builder_.add_windScale(windScale);
  builder_.add_humidity(humidity);
  builder_.add_weatherCode(weatherCode);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DF_HourlyForecast> CreateDF_HourlyForecastDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    uint32_t time = 4294967295,
    const char *description = nullptr,
    uint8_t weatherCode = 255,
    int16_t temperature = 0,
    uint8_t humidity = 0,
    uint16_t windDirection = 0,
    uint16_t windSpeed = 0,
    uint8_t windScale = 0,
    int64_t msg_id = 0) {
  auto description__ = description ? _fbb.CreateString(description) : 0;
  return MECData::CreateDF_HourlyForecast(
      _fbb,
      time,
      description__,
      weatherCode,
      temperature,
      humidity,
      windDirection,
      windSpeed,
      windScale,
      msg_id);
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_HOURLYFORECAST_MECDATA_H_
