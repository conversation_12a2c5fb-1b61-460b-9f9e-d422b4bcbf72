// automatically generated by the FlatBuffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_APPOTA_MECDATA_H_
#define FLATBUFFERS_GENERATED_APPOTA_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

#include "AppConfig_generated.h"
#include "DeploySpecificInfo_generated.h"
#include "DeviceSpecificInfo_generated.h"
#include "ModuleIOConfig_generated.h"
#include "ModuleMiscellaneous_generated.h"
#include "OperationTags_generated.h"
#include "ZStub_generated.h"

namespace MECData {

struct DF_AddInstance;
struct DF_AddInstanceBuilder;

struct DF_InstanceOTA;
struct DF_InstanceOTABuilder;

struct DF_AppPackageOTA;
struct DF_AppPackageOTABuilder;

struct MSG_AppOTA;
struct MSG_AppOTABuilder;

struct DF_AddInstance FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_AddInstanceBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_ID = 4,
    VT_PACKAGE_ID = 6,
    VT_CONFIG = 8
  };
  uint16_t id() const {
    return GetField<uint16_t>(VT_ID, 0);
  }
  const ::flatbuffers::String *package_id() const {
    return GetPointer<const ::flatbuffers::String *>(VT_PACKAGE_ID);
  }
  const MECData::DF_AppInstanceConfig *config() const {
    return GetPointer<const MECData::DF_AppInstanceConfig *>(VT_CONFIG);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<uint16_t>(verifier, VT_ID, 2) &&
           VerifyOffsetRequired(verifier, VT_PACKAGE_ID) &&
           verifier.VerifyString(package_id()) &&
           VerifyOffset(verifier, VT_CONFIG) &&
           verifier.VerifyTable(config()) &&
           verifier.EndTable();
  }
};

struct DF_AddInstanceBuilder {
  typedef DF_AddInstance Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_id(uint16_t id) {
    fbb_.AddElement<uint16_t>(DF_AddInstance::VT_ID, id, 0);
  }
  void add_package_id(::flatbuffers::Offset<::flatbuffers::String> package_id) {
    fbb_.AddOffset(DF_AddInstance::VT_PACKAGE_ID, package_id);
  }
  void add_config(::flatbuffers::Offset<MECData::DF_AppInstanceConfig> config) {
    fbb_.AddOffset(DF_AddInstance::VT_CONFIG, config);
  }
  explicit DF_AddInstanceBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_AddInstance> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_AddInstance>(end);
    fbb_.Required(o, DF_AddInstance::VT_PACKAGE_ID);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_AddInstance> CreateDF_AddInstance(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    uint16_t id = 0,
    ::flatbuffers::Offset<::flatbuffers::String> package_id = 0,
    ::flatbuffers::Offset<MECData::DF_AppInstanceConfig> config = 0) {
  DF_AddInstanceBuilder builder_(_fbb);
  builder_.add_config(config);
  builder_.add_package_id(package_id);
  builder_.add_id(id);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DF_AddInstance> CreateDF_AddInstanceDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    uint16_t id = 0,
    const char *package_id = nullptr,
    ::flatbuffers::Offset<MECData::DF_AppInstanceConfig> config = 0) {
  auto package_id__ = package_id ? _fbb.CreateString(package_id) : 0;
  return MECData::CreateDF_AddInstance(
      _fbb,
      id,
      package_id__,
      config);
}

struct DF_InstanceOTA FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_InstanceOTABuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_ADD = 4,
    VT_CONF = 6,
    VT_START = 8,
    VT_STOP = 10,
    VT_RESTART = 12,
    VT_DEL = 14
  };
  const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_AddInstance>> *add() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_AddInstance>> *>(VT_ADD);
  }
  const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_AppInstanceConfig>> *conf() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_AppInstanceConfig>> *>(VT_CONF);
  }
  const ::flatbuffers::Vector<uint16_t> *start() const {
    return GetPointer<const ::flatbuffers::Vector<uint16_t> *>(VT_START);
  }
  const ::flatbuffers::Vector<uint16_t> *stop() const {
    return GetPointer<const ::flatbuffers::Vector<uint16_t> *>(VT_STOP);
  }
  const ::flatbuffers::Vector<uint16_t> *restart() const {
    return GetPointer<const ::flatbuffers::Vector<uint16_t> *>(VT_RESTART);
  }
  const ::flatbuffers::Vector<uint16_t> *del() const {
    return GetPointer<const ::flatbuffers::Vector<uint16_t> *>(VT_DEL);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_ADD) &&
           verifier.VerifyVector(add()) &&
           verifier.VerifyVectorOfTables(add()) &&
           VerifyOffset(verifier, VT_CONF) &&
           verifier.VerifyVector(conf()) &&
           verifier.VerifyVectorOfTables(conf()) &&
           VerifyOffset(verifier, VT_START) &&
           verifier.VerifyVector(start()) &&
           VerifyOffset(verifier, VT_STOP) &&
           verifier.VerifyVector(stop()) &&
           VerifyOffset(verifier, VT_RESTART) &&
           verifier.VerifyVector(restart()) &&
           VerifyOffset(verifier, VT_DEL) &&
           verifier.VerifyVector(del()) &&
           verifier.EndTable();
  }
};

struct DF_InstanceOTABuilder {
  typedef DF_InstanceOTA Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_add(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_AddInstance>>> add) {
    fbb_.AddOffset(DF_InstanceOTA::VT_ADD, add);
  }
  void add_conf(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_AppInstanceConfig>>> conf) {
    fbb_.AddOffset(DF_InstanceOTA::VT_CONF, conf);
  }
  void add_start(::flatbuffers::Offset<::flatbuffers::Vector<uint16_t>> start) {
    fbb_.AddOffset(DF_InstanceOTA::VT_START, start);
  }
  void add_stop(::flatbuffers::Offset<::flatbuffers::Vector<uint16_t>> stop) {
    fbb_.AddOffset(DF_InstanceOTA::VT_STOP, stop);
  }
  void add_restart(::flatbuffers::Offset<::flatbuffers::Vector<uint16_t>> restart) {
    fbb_.AddOffset(DF_InstanceOTA::VT_RESTART, restart);
  }
  void add_del(::flatbuffers::Offset<::flatbuffers::Vector<uint16_t>> del) {
    fbb_.AddOffset(DF_InstanceOTA::VT_DEL, del);
  }
  explicit DF_InstanceOTABuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_InstanceOTA> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_InstanceOTA>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_InstanceOTA> CreateDF_InstanceOTA(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_AddInstance>>> add = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_AppInstanceConfig>>> conf = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<uint16_t>> start = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<uint16_t>> stop = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<uint16_t>> restart = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<uint16_t>> del = 0) {
  DF_InstanceOTABuilder builder_(_fbb);
  builder_.add_del(del);
  builder_.add_restart(restart);
  builder_.add_stop(stop);
  builder_.add_start(start);
  builder_.add_conf(conf);
  builder_.add_add(add);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DF_InstanceOTA> CreateDF_InstanceOTADirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const std::vector<::flatbuffers::Offset<MECData::DF_AddInstance>> *add = nullptr,
    const std::vector<::flatbuffers::Offset<MECData::DF_AppInstanceConfig>> *conf = nullptr,
    const std::vector<uint16_t> *start = nullptr,
    const std::vector<uint16_t> *stop = nullptr,
    const std::vector<uint16_t> *restart = nullptr,
    const std::vector<uint16_t> *del = nullptr) {
  auto add__ = add ? _fbb.CreateVector<::flatbuffers::Offset<MECData::DF_AddInstance>>(*add) : 0;
  auto conf__ = conf ? _fbb.CreateVector<::flatbuffers::Offset<MECData::DF_AppInstanceConfig>>(*conf) : 0;
  auto start__ = start ? _fbb.CreateVector<uint16_t>(*start) : 0;
  auto stop__ = stop ? _fbb.CreateVector<uint16_t>(*stop) : 0;
  auto restart__ = restart ? _fbb.CreateVector<uint16_t>(*restart) : 0;
  auto del__ = del ? _fbb.CreateVector<uint16_t>(*del) : 0;
  return MECData::CreateDF_InstanceOTA(
      _fbb,
      add__,
      conf__,
      start__,
      stop__,
      restart__,
      del__);
}

struct DF_AppPackageOTA FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_AppPackageOTABuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_PACKAGE_ID = 4,
    VT_NAME = 6,
    VT_CATEGORY = 8,
    VT_VERSION = 10,
    VT_BUILD_DATE = 12,
    VT_CONFIG = 14,
    VT_REMOVE = 16,
    VT_SHA256SUM = 18
  };
  const ::flatbuffers::String *package_id() const {
    return GetPointer<const ::flatbuffers::String *>(VT_PACKAGE_ID);
  }
  const ::flatbuffers::String *name() const {
    return GetPointer<const ::flatbuffers::String *>(VT_NAME);
  }
  MECData::DE_ModuleCategory category() const {
    return static_cast<MECData::DE_ModuleCategory>(GetField<uint8_t>(VT_CATEGORY, 1));
  }
  const ::flatbuffers::String *version() const {
    return GetPointer<const ::flatbuffers::String *>(VT_VERSION);
  }
  const ::flatbuffers::String *build_date() const {
    return GetPointer<const ::flatbuffers::String *>(VT_BUILD_DATE);
  }
  const MECData::DF_AppPackageConfig *config() const {
    return GetPointer<const MECData::DF_AppPackageConfig *>(VT_CONFIG);
  }
  bool remove() const {
    return GetField<uint8_t>(VT_REMOVE, 0) != 0;
  }
  const ::flatbuffers::String *sha256sum() const {
    return GetPointer<const ::flatbuffers::String *>(VT_SHA256SUM);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffsetRequired(verifier, VT_PACKAGE_ID) &&
           verifier.VerifyString(package_id()) &&
           VerifyOffsetRequired(verifier, VT_NAME) &&
           verifier.VerifyString(name()) &&
           VerifyField<uint8_t>(verifier, VT_CATEGORY, 1) &&
           VerifyOffset(verifier, VT_VERSION) &&
           verifier.VerifyString(version()) &&
           VerifyOffset(verifier, VT_BUILD_DATE) &&
           verifier.VerifyString(build_date()) &&
           VerifyOffset(verifier, VT_CONFIG) &&
           verifier.VerifyTable(config()) &&
           VerifyField<uint8_t>(verifier, VT_REMOVE, 1) &&
           VerifyOffset(verifier, VT_SHA256SUM) &&
           verifier.VerifyString(sha256sum()) &&
           verifier.EndTable();
  }
};

struct DF_AppPackageOTABuilder {
  typedef DF_AppPackageOTA Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_package_id(::flatbuffers::Offset<::flatbuffers::String> package_id) {
    fbb_.AddOffset(DF_AppPackageOTA::VT_PACKAGE_ID, package_id);
  }
  void add_name(::flatbuffers::Offset<::flatbuffers::String> name) {
    fbb_.AddOffset(DF_AppPackageOTA::VT_NAME, name);
  }
  void add_category(MECData::DE_ModuleCategory category) {
    fbb_.AddElement<uint8_t>(DF_AppPackageOTA::VT_CATEGORY, static_cast<uint8_t>(category), 1);
  }
  void add_version(::flatbuffers::Offset<::flatbuffers::String> version) {
    fbb_.AddOffset(DF_AppPackageOTA::VT_VERSION, version);
  }
  void add_build_date(::flatbuffers::Offset<::flatbuffers::String> build_date) {
    fbb_.AddOffset(DF_AppPackageOTA::VT_BUILD_DATE, build_date);
  }
  void add_config(::flatbuffers::Offset<MECData::DF_AppPackageConfig> config) {
    fbb_.AddOffset(DF_AppPackageOTA::VT_CONFIG, config);
  }
  void add_remove(bool remove) {
    fbb_.AddElement<uint8_t>(DF_AppPackageOTA::VT_REMOVE, static_cast<uint8_t>(remove), 0);
  }
  void add_sha256sum(::flatbuffers::Offset<::flatbuffers::String> sha256sum) {
    fbb_.AddOffset(DF_AppPackageOTA::VT_SHA256SUM, sha256sum);
  }
  explicit DF_AppPackageOTABuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_AppPackageOTA> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_AppPackageOTA>(end);
    fbb_.Required(o, DF_AppPackageOTA::VT_PACKAGE_ID);
    fbb_.Required(o, DF_AppPackageOTA::VT_NAME);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_AppPackageOTA> CreateDF_AppPackageOTA(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::String> package_id = 0,
    ::flatbuffers::Offset<::flatbuffers::String> name = 0,
    MECData::DE_ModuleCategory category = MECData::DE_ModuleCategory_APPLICATION_ALGORITHM,
    ::flatbuffers::Offset<::flatbuffers::String> version = 0,
    ::flatbuffers::Offset<::flatbuffers::String> build_date = 0,
    ::flatbuffers::Offset<MECData::DF_AppPackageConfig> config = 0,
    bool remove = false,
    ::flatbuffers::Offset<::flatbuffers::String> sha256sum = 0) {
  DF_AppPackageOTABuilder builder_(_fbb);
  builder_.add_sha256sum(sha256sum);
  builder_.add_config(config);
  builder_.add_build_date(build_date);
  builder_.add_version(version);
  builder_.add_name(name);
  builder_.add_package_id(package_id);
  builder_.add_remove(remove);
  builder_.add_category(category);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DF_AppPackageOTA> CreateDF_AppPackageOTADirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const char *package_id = nullptr,
    const char *name = nullptr,
    MECData::DE_ModuleCategory category = MECData::DE_ModuleCategory_APPLICATION_ALGORITHM,
    const char *version = nullptr,
    const char *build_date = nullptr,
    ::flatbuffers::Offset<MECData::DF_AppPackageConfig> config = 0,
    bool remove = false,
    const char *sha256sum = nullptr) {
  auto package_id__ = package_id ? _fbb.CreateString(package_id) : 0;
  auto name__ = name ? _fbb.CreateString(name) : 0;
  auto version__ = version ? _fbb.CreateString(version) : 0;
  auto build_date__ = build_date ? _fbb.CreateString(build_date) : 0;
  auto sha256sum__ = sha256sum ? _fbb.CreateString(sha256sum) : 0;
  return MECData::CreateDF_AppPackageOTA(
      _fbb,
      package_id__,
      name__,
      category,
      version__,
      build_date__,
      config,
      remove,
      sha256sum__);
}

struct MSG_AppOTA FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef MSG_AppOTABuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_PACKAGES = 4,
    VT_INSTANCES = 6
  };
  const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_AppPackageOTA>> *packages() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_AppPackageOTA>> *>(VT_PACKAGES);
  }
  const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_InstanceOTA>> *instances() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_InstanceOTA>> *>(VT_INSTANCES);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_PACKAGES) &&
           verifier.VerifyVector(packages()) &&
           verifier.VerifyVectorOfTables(packages()) &&
           VerifyOffset(verifier, VT_INSTANCES) &&
           verifier.VerifyVector(instances()) &&
           verifier.VerifyVectorOfTables(instances()) &&
           verifier.EndTable();
  }
};

struct MSG_AppOTABuilder {
  typedef MSG_AppOTA Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_packages(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_AppPackageOTA>>> packages) {
    fbb_.AddOffset(MSG_AppOTA::VT_PACKAGES, packages);
  }
  void add_instances(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_InstanceOTA>>> instances) {
    fbb_.AddOffset(MSG_AppOTA::VT_INSTANCES, instances);
  }
  explicit MSG_AppOTABuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<MSG_AppOTA> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<MSG_AppOTA>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<MSG_AppOTA> CreateMSG_AppOTA(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_AppPackageOTA>>> packages = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_InstanceOTA>>> instances = 0) {
  MSG_AppOTABuilder builder_(_fbb);
  builder_.add_instances(instances);
  builder_.add_packages(packages);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<MSG_AppOTA> CreateMSG_AppOTADirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const std::vector<::flatbuffers::Offset<MECData::DF_AppPackageOTA>> *packages = nullptr,
    const std::vector<::flatbuffers::Offset<MECData::DF_InstanceOTA>> *instances = nullptr) {
  auto packages__ = packages ? _fbb.CreateVector<::flatbuffers::Offset<MECData::DF_AppPackageOTA>>(*packages) : 0;
  auto instances__ = instances ? _fbb.CreateVector<::flatbuffers::Offset<MECData::DF_InstanceOTA>>(*instances) : 0;
  return MECData::CreateMSG_AppOTA(
      _fbb,
      packages__,
      instances__);
}

inline const MECData::MSG_AppOTA *GetMSG_AppOTA(const void *buf) {
  return ::flatbuffers::GetRoot<MECData::MSG_AppOTA>(buf);
}

inline const MECData::MSG_AppOTA *GetSizePrefixedMSG_AppOTA(const void *buf) {
  return ::flatbuffers::GetSizePrefixedRoot<MECData::MSG_AppOTA>(buf);
}

inline bool VerifyMSG_AppOTABuffer(
    ::flatbuffers::Verifier &verifier) {
  return verifier.VerifyBuffer<MECData::MSG_AppOTA>(nullptr);
}

inline bool VerifySizePrefixedMSG_AppOTABuffer(
    ::flatbuffers::Verifier &verifier) {
  return verifier.VerifySizePrefixedBuffer<MECData::MSG_AppOTA>(nullptr);
}

inline void FinishMSG_AppOTABuffer(
    ::flatbuffers::FlatBufferBuilder &fbb,
    ::flatbuffers::Offset<MECData::MSG_AppOTA> root) {
  fbb.Finish(root);
}

inline void FinishSizePrefixedMSG_AppOTABuffer(
    ::flatbuffers::FlatBufferBuilder &fbb,
    ::flatbuffers::Offset<MECData::MSG_AppOTA> root) {
  fbb.FinishSizePrefixed(root);
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_APPOTA_MECDATA_H_
