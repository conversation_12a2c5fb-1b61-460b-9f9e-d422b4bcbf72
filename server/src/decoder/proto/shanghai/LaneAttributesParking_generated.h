// automatically generated by the <PERSON>Buffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_LANEATTRIBUTESPARKING_MECDATA_H_
#define FLATBUFFERS_GENERATED_LANEATTRIBUTESPARKING_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

namespace MECData {

struct DF_LaneAttributesParking;
struct DF_LaneAttributesParkingBuilder;

struct DF_LaneAttributesParking FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_LaneAttributesParkingBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_PARKING = 4
  };
  int8_t parking() const {
    return GetField<int8_t>(VT_PARKING, 0);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<int8_t>(verifier, VT_PARKING, 1) &&
           verifier.EndTable();
  }
};

struct DF_LaneAttributesParkingBuilder {
  typedef DF_LaneAttributesParking Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_parking(int8_t parking) {
    fbb_.AddElement<int8_t>(DF_LaneAttributesParking::VT_PARKING, parking, 0);
  }
  explicit DF_LaneAttributesParkingBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_LaneAttributesParking> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_LaneAttributesParking>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_LaneAttributesParking> CreateDF_LaneAttributesParking(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    int8_t parking = 0) {
  DF_LaneAttributesParkingBuilder builder_(_fbb);
  builder_.add_parking(parking);
  return builder_.Finish();
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_LANEATTRIBUTESPARKING_MECDATA_H_
