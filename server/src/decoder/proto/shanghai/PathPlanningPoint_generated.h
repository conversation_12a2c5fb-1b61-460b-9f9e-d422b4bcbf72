// automatically generated by the <PERSON>Buffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_PATHPLANNINGPOINT_MECDATA_H_
#define FLATBUFFERS_GENERATED_PATHPLANNINGPOINT_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

#include "AccSet4WayConfidence_generated.h"
#include "AccelerationSet4Way_generated.h"
#include "HeadingConfidence_generated.h"
#include "PositionConfidenceSet_generated.h"
#include "PositionOffsetLLV_generated.h"
#include "ReferenceLink_generated.h"
#include "SpeedConfidence_generated.h"

namespace MECData {

struct DF_PathPlanningPoint;
struct DF_PathPlanningPointBuilder;

struct DF_PathPlanningPoint FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_PathPlanningPointBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_POSINMAP = 4,
    VT_POS = 6,
    VT_POSACCURACY = 8,
    VT_SPEED = 10,
    VT_SPEEDCFD = 12,
    VT_HEADING = 14,
    VT_HEADINGCFD = 16,
    VT_ACCELSET = 18,
    VT_ACC4WAYCONFIDENCE = 20,
    VT_ESTIMATEDTIME = 22,
    VT_TIMECONFIDENCE = 24
  };
  const MECData::DF_ReferenceLink *posInMap() const {
    return GetPointer<const MECData::DF_ReferenceLink *>(VT_POSINMAP);
  }
  const MECData::DF_PositionOffsetLLV *pos() const {
    return GetPointer<const MECData::DF_PositionOffsetLLV *>(VT_POS);
  }
  const MECData::DF_PositionConfidenceSet *posAccuracy() const {
    return GetPointer<const MECData::DF_PositionConfidenceSet *>(VT_POSACCURACY);
  }
  uint16_t speed() const {
    return GetField<uint16_t>(VT_SPEED, 0);
  }
  MECData::DE_SpeedConfidence speedCfd() const {
    return static_cast<MECData::DE_SpeedConfidence>(GetField<int8_t>(VT_SPEEDCFD, 0));
  }
  uint16_t heading() const {
    return GetField<uint16_t>(VT_HEADING, 0);
  }
  MECData::DE_HeadingConfidence headingCfd() const {
    return static_cast<MECData::DE_HeadingConfidence>(GetField<int8_t>(VT_HEADINGCFD, 0));
  }
  const MECData::DF_AccelerationSet4Way *accelSet() const {
    return GetPointer<const MECData::DF_AccelerationSet4Way *>(VT_ACCELSET);
  }
  const MECData::DF_AccSet4WayConfidence *acc4WayConfidence() const {
    return GetPointer<const MECData::DF_AccSet4WayConfidence *>(VT_ACC4WAYCONFIDENCE);
  }
  uint16_t estimatedTime() const {
    return GetField<uint16_t>(VT_ESTIMATEDTIME, 0);
  }
  uint8_t timeConfidence() const {
    return GetField<uint8_t>(VT_TIMECONFIDENCE, 0);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_POSINMAP) &&
           verifier.VerifyTable(posInMap()) &&
           VerifyOffsetRequired(verifier, VT_POS) &&
           verifier.VerifyTable(pos()) &&
           VerifyOffset(verifier, VT_POSACCURACY) &&
           verifier.VerifyTable(posAccuracy()) &&
           VerifyField<uint16_t>(verifier, VT_SPEED, 2) &&
           VerifyField<int8_t>(verifier, VT_SPEEDCFD, 1) &&
           VerifyField<uint16_t>(verifier, VT_HEADING, 2) &&
           VerifyField<int8_t>(verifier, VT_HEADINGCFD, 1) &&
           VerifyOffset(verifier, VT_ACCELSET) &&
           verifier.VerifyTable(accelSet()) &&
           VerifyOffset(verifier, VT_ACC4WAYCONFIDENCE) &&
           verifier.VerifyTable(acc4WayConfidence()) &&
           VerifyField<uint16_t>(verifier, VT_ESTIMATEDTIME, 2) &&
           VerifyField<uint8_t>(verifier, VT_TIMECONFIDENCE, 1) &&
           verifier.EndTable();
  }
};

struct DF_PathPlanningPointBuilder {
  typedef DF_PathPlanningPoint Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_posInMap(::flatbuffers::Offset<MECData::DF_ReferenceLink> posInMap) {
    fbb_.AddOffset(DF_PathPlanningPoint::VT_POSINMAP, posInMap);
  }
  void add_pos(::flatbuffers::Offset<MECData::DF_PositionOffsetLLV> pos) {
    fbb_.AddOffset(DF_PathPlanningPoint::VT_POS, pos);
  }
  void add_posAccuracy(::flatbuffers::Offset<MECData::DF_PositionConfidenceSet> posAccuracy) {
    fbb_.AddOffset(DF_PathPlanningPoint::VT_POSACCURACY, posAccuracy);
  }
  void add_speed(uint16_t speed) {
    fbb_.AddElement<uint16_t>(DF_PathPlanningPoint::VT_SPEED, speed, 0);
  }
  void add_speedCfd(MECData::DE_SpeedConfidence speedCfd) {
    fbb_.AddElement<int8_t>(DF_PathPlanningPoint::VT_SPEEDCFD, static_cast<int8_t>(speedCfd), 0);
  }
  void add_heading(uint16_t heading) {
    fbb_.AddElement<uint16_t>(DF_PathPlanningPoint::VT_HEADING, heading, 0);
  }
  void add_headingCfd(MECData::DE_HeadingConfidence headingCfd) {
    fbb_.AddElement<int8_t>(DF_PathPlanningPoint::VT_HEADINGCFD, static_cast<int8_t>(headingCfd), 0);
  }
  void add_accelSet(::flatbuffers::Offset<MECData::DF_AccelerationSet4Way> accelSet) {
    fbb_.AddOffset(DF_PathPlanningPoint::VT_ACCELSET, accelSet);
  }
  void add_acc4WayConfidence(::flatbuffers::Offset<MECData::DF_AccSet4WayConfidence> acc4WayConfidence) {
    fbb_.AddOffset(DF_PathPlanningPoint::VT_ACC4WAYCONFIDENCE, acc4WayConfidence);
  }
  void add_estimatedTime(uint16_t estimatedTime) {
    fbb_.AddElement<uint16_t>(DF_PathPlanningPoint::VT_ESTIMATEDTIME, estimatedTime, 0);
  }
  void add_timeConfidence(uint8_t timeConfidence) {
    fbb_.AddElement<uint8_t>(DF_PathPlanningPoint::VT_TIMECONFIDENCE, timeConfidence, 0);
  }
  explicit DF_PathPlanningPointBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_PathPlanningPoint> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_PathPlanningPoint>(end);
    fbb_.Required(o, DF_PathPlanningPoint::VT_POS);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_PathPlanningPoint> CreateDF_PathPlanningPoint(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<MECData::DF_ReferenceLink> posInMap = 0,
    ::flatbuffers::Offset<MECData::DF_PositionOffsetLLV> pos = 0,
    ::flatbuffers::Offset<MECData::DF_PositionConfidenceSet> posAccuracy = 0,
    uint16_t speed = 0,
    MECData::DE_SpeedConfidence speedCfd = MECData::DE_SpeedConfidence_unavailable,
    uint16_t heading = 0,
    MECData::DE_HeadingConfidence headingCfd = MECData::DE_HeadingConfidence_unavailable,
    ::flatbuffers::Offset<MECData::DF_AccelerationSet4Way> accelSet = 0,
    ::flatbuffers::Offset<MECData::DF_AccSet4WayConfidence> acc4WayConfidence = 0,
    uint16_t estimatedTime = 0,
    uint8_t timeConfidence = 0) {
  DF_PathPlanningPointBuilder builder_(_fbb);
  builder_.add_acc4WayConfidence(acc4WayConfidence);
  builder_.add_accelSet(accelSet);
  builder_.add_posAccuracy(posAccuracy);
  builder_.add_pos(pos);
  builder_.add_posInMap(posInMap);
  builder_.add_estimatedTime(estimatedTime);
  builder_.add_heading(heading);
  builder_.add_speed(speed);
  builder_.add_timeConfidence(timeConfidence);
  builder_.add_headingCfd(headingCfd);
  builder_.add_speedCfd(speedCfd);
  return builder_.Finish();
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_PATHPLANNINGPOINT_MECDATA_H_
