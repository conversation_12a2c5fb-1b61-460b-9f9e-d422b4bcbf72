// automatically generated by the FlatBuffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_BRAKEPEDALSTATUS_MECDATA_H_
#define FLATBUFFERS_GENERATED_BRAKEPEDALSTATUS_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

namespace MECData {

enum DE_BrakePedalStatus : int8_t {
  DE_BrakePedalStatus_unavailable = 0,
  DE_BrakePedalStatus_off = 1,
  DE_BrakePedalStatus_on = 2,
  DE_BrakePedalStatus_MIN = DE_BrakePedalStatus_unavailable,
  DE_BrakePedalStatus_MAX = DE_BrakePedalStatus_on
};

inline const DE_BrakePedalStatus (&EnumValuesDE_BrakePedalStatus())[3] {
  static const DE_BrakePedalStatus values[] = {
    DE_BrakePedalStatus_unavailable,
    DE_BrakePedalStatus_off,
    DE_BrakePedalStatus_on
  };
  return values;
}

inline const char * const *EnumNamesDE_BrakePedalStatus() {
  static const char * const names[4] = {
    "unavailable",
    "off",
    "on",
    nullptr
  };
  return names;
}

inline const char *EnumNameDE_BrakePedalStatus(DE_BrakePedalStatus e) {
  if (::flatbuffers::IsOutRange(e, DE_BrakePedalStatus_unavailable, DE_BrakePedalStatus_on)) return "";
  const size_t index = static_cast<size_t>(e);
  return EnumNamesDE_BrakePedalStatus()[index];
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_BRAKEPEDALSTATUS_MECDATA_H_
