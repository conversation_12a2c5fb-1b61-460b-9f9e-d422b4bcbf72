// automatically generated by the FlatBuffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_NODE_MECDATA_H_
#define FLATBUFFERS_GENERATED_NODE_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

#include "Link_generated.h"
#include "LinkEx_generated.h"
#include "NodeReferenceID_generated.h"
#include "PedCrossing_generated.h"
#include "Position3D_generated.h"
#include "ProhibitedZone_generated.h"

namespace MECData {

struct DF_Node;
struct DF_NodeBuilder;

struct DF_Node FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_NodeBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_NAME = 4,
    VT_ID = 6,
    VT_REFPOS = 8,
    VT_INLINKS = 10,
    VT_INLINKS_EX = 12,
    VT_PROHIBITEDZONE = 14,
    VT_SIGNAL_CONTROLLED = 16,
    VT_PED_CROSSINGS = 18
  };
  const ::flatbuffers::String *name() const {
    return GetPointer<const ::flatbuffers::String *>(VT_NAME);
  }
  const MECData::DF_NodeReferenceID *id() const {
    return GetPointer<const MECData::DF_NodeReferenceID *>(VT_ID);
  }
  const MECData::DF_Position3D *refPos() const {
    return GetPointer<const MECData::DF_Position3D *>(VT_REFPOS);
  }
  const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_Link>> *inLinks() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_Link>> *>(VT_INLINKS);
  }
  const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_LinkEx>> *inLinks_ex() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_LinkEx>> *>(VT_INLINKS_EX);
  }
  const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_ProhibitedZone>> *prohibitedzone() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_ProhibitedZone>> *>(VT_PROHIBITEDZONE);
  }
  bool signal_controlled() const {
    return GetField<uint8_t>(VT_SIGNAL_CONTROLLED, 0) != 0;
  }
  const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_PedCrossing>> *ped_crossings() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_PedCrossing>> *>(VT_PED_CROSSINGS);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_NAME) &&
           verifier.VerifyString(name()) &&
           VerifyOffsetRequired(verifier, VT_ID) &&
           verifier.VerifyTable(id()) &&
           VerifyOffsetRequired(verifier, VT_REFPOS) &&
           verifier.VerifyTable(refPos()) &&
           VerifyOffset(verifier, VT_INLINKS) &&
           verifier.VerifyVector(inLinks()) &&
           verifier.VerifyVectorOfTables(inLinks()) &&
           VerifyOffset(verifier, VT_INLINKS_EX) &&
           verifier.VerifyVector(inLinks_ex()) &&
           verifier.VerifyVectorOfTables(inLinks_ex()) &&
           VerifyOffset(verifier, VT_PROHIBITEDZONE) &&
           verifier.VerifyVector(prohibitedzone()) &&
           verifier.VerifyVectorOfTables(prohibitedzone()) &&
           VerifyField<uint8_t>(verifier, VT_SIGNAL_CONTROLLED, 1) &&
           VerifyOffset(verifier, VT_PED_CROSSINGS) &&
           verifier.VerifyVector(ped_crossings()) &&
           verifier.VerifyVectorOfTables(ped_crossings()) &&
           verifier.EndTable();
  }
};

struct DF_NodeBuilder {
  typedef DF_Node Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_name(::flatbuffers::Offset<::flatbuffers::String> name) {
    fbb_.AddOffset(DF_Node::VT_NAME, name);
  }
  void add_id(::flatbuffers::Offset<MECData::DF_NodeReferenceID> id) {
    fbb_.AddOffset(DF_Node::VT_ID, id);
  }
  void add_refPos(::flatbuffers::Offset<MECData::DF_Position3D> refPos) {
    fbb_.AddOffset(DF_Node::VT_REFPOS, refPos);
  }
  void add_inLinks(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_Link>>> inLinks) {
    fbb_.AddOffset(DF_Node::VT_INLINKS, inLinks);
  }
  void add_inLinks_ex(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_LinkEx>>> inLinks_ex) {
    fbb_.AddOffset(DF_Node::VT_INLINKS_EX, inLinks_ex);
  }
  void add_prohibitedzone(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_ProhibitedZone>>> prohibitedzone) {
    fbb_.AddOffset(DF_Node::VT_PROHIBITEDZONE, prohibitedzone);
  }
  void add_signal_controlled(bool signal_controlled) {
    fbb_.AddElement<uint8_t>(DF_Node::VT_SIGNAL_CONTROLLED, static_cast<uint8_t>(signal_controlled), 0);
  }
  void add_ped_crossings(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_PedCrossing>>> ped_crossings) {
    fbb_.AddOffset(DF_Node::VT_PED_CROSSINGS, ped_crossings);
  }
  explicit DF_NodeBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_Node> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_Node>(end);
    fbb_.Required(o, DF_Node::VT_ID);
    fbb_.Required(o, DF_Node::VT_REFPOS);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_Node> CreateDF_Node(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::String> name = 0,
    ::flatbuffers::Offset<MECData::DF_NodeReferenceID> id = 0,
    ::flatbuffers::Offset<MECData::DF_Position3D> refPos = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_Link>>> inLinks = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_LinkEx>>> inLinks_ex = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_ProhibitedZone>>> prohibitedzone = 0,
    bool signal_controlled = false,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_PedCrossing>>> ped_crossings = 0) {
  DF_NodeBuilder builder_(_fbb);
  builder_.add_ped_crossings(ped_crossings);
  builder_.add_prohibitedzone(prohibitedzone);
  builder_.add_inLinks_ex(inLinks_ex);
  builder_.add_inLinks(inLinks);
  builder_.add_refPos(refPos);
  builder_.add_id(id);
  builder_.add_name(name);
  builder_.add_signal_controlled(signal_controlled);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DF_Node> CreateDF_NodeDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const char *name = nullptr,
    ::flatbuffers::Offset<MECData::DF_NodeReferenceID> id = 0,
    ::flatbuffers::Offset<MECData::DF_Position3D> refPos = 0,
    const std::vector<::flatbuffers::Offset<MECData::DF_Link>> *inLinks = nullptr,
    const std::vector<::flatbuffers::Offset<MECData::DF_LinkEx>> *inLinks_ex = nullptr,
    const std::vector<::flatbuffers::Offset<MECData::DF_ProhibitedZone>> *prohibitedzone = nullptr,
    bool signal_controlled = false,
    const std::vector<::flatbuffers::Offset<MECData::DF_PedCrossing>> *ped_crossings = nullptr) {
  auto name__ = name ? _fbb.CreateString(name) : 0;
  auto inLinks__ = inLinks ? _fbb.CreateVector<::flatbuffers::Offset<MECData::DF_Link>>(*inLinks) : 0;
  auto inLinks_ex__ = inLinks_ex ? _fbb.CreateVector<::flatbuffers::Offset<MECData::DF_LinkEx>>(*inLinks_ex) : 0;
  auto prohibitedzone__ = prohibitedzone ? _fbb.CreateVector<::flatbuffers::Offset<MECData::DF_ProhibitedZone>>(*prohibitedzone) : 0;
  auto ped_crossings__ = ped_crossings ? _fbb.CreateVector<::flatbuffers::Offset<MECData::DF_PedCrossing>>(*ped_crossings) : 0;
  return MECData::CreateDF_Node(
      _fbb,
      name__,
      id,
      refPos,
      inLinks__,
      inLinks_ex__,
      prohibitedzone__,
      signal_controlled,
      ped_crossings__);
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_NODE_MECDATA_H_
