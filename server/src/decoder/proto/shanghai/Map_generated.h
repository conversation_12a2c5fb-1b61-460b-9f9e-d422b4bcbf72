// automatically generated by the <PERSON>Buffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_MAP_MECDATA_H_
#define FLATBUFFERS_GENERATED_MAP_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

#include "Node_generated.h"

namespace MECData {

struct MSG_Map;
struct MSG_MapBuilder;

struct MSG_Map FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef MSG_MapBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_TIMESTAMP = 4,
    VT_NODES = 6,
    VT_MSG_ID = 8
  };
  uint32_t timeStamp() const {
    return GetField<uint32_t>(VT_TIMESTAMP, 255);
  }
  const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_Node>> *nodes() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_Node>> *>(VT_NODES);
  }
  int64_t msg_id() const {
    return GetField<int64_t>(VT_MSG_ID, 0);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<uint32_t>(verifier, VT_TIMESTAMP, 4) &&
           VerifyOffsetRequired(verifier, VT_NODES) &&
           verifier.VerifyVector(nodes()) &&
           verifier.VerifyVectorOfTables(nodes()) &&
           VerifyField<int64_t>(verifier, VT_MSG_ID, 8) &&
           verifier.EndTable();
  }
};

struct MSG_MapBuilder {
  typedef MSG_Map Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_timeStamp(uint32_t timeStamp) {
    fbb_.AddElement<uint32_t>(MSG_Map::VT_TIMESTAMP, timeStamp, 255);
  }
  void add_nodes(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_Node>>> nodes) {
    fbb_.AddOffset(MSG_Map::VT_NODES, nodes);
  }
  void add_msg_id(int64_t msg_id) {
    fbb_.AddElement<int64_t>(MSG_Map::VT_MSG_ID, msg_id, 0);
  }
  explicit MSG_MapBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<MSG_Map> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<MSG_Map>(end);
    fbb_.Required(o, MSG_Map::VT_NODES);
    return o;
  }
};

inline ::flatbuffers::Offset<MSG_Map> CreateMSG_Map(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    uint32_t timeStamp = 255,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_Node>>> nodes = 0,
    int64_t msg_id = 0) {
  MSG_MapBuilder builder_(_fbb);
  builder_.add_msg_id(msg_id);
  builder_.add_nodes(nodes);
  builder_.add_timeStamp(timeStamp);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<MSG_Map> CreateMSG_MapDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    uint32_t timeStamp = 255,
    const std::vector<::flatbuffers::Offset<MECData::DF_Node>> *nodes = nullptr,
    int64_t msg_id = 0) {
  auto nodes__ = nodes ? _fbb.CreateVector<::flatbuffers::Offset<MECData::DF_Node>>(*nodes) : 0;
  return MECData::CreateMSG_Map(
      _fbb,
      timeStamp,
      nodes__,
      msg_id);
}

inline const MECData::MSG_Map *GetMSG_Map(const void *buf) {
  return ::flatbuffers::GetRoot<MECData::MSG_Map>(buf);
}

inline const MECData::MSG_Map *GetSizePrefixedMSG_Map(const void *buf) {
  return ::flatbuffers::GetSizePrefixedRoot<MECData::MSG_Map>(buf);
}

inline bool VerifyMSG_MapBuffer(
    ::flatbuffers::Verifier &verifier) {
  return verifier.VerifyBuffer<MECData::MSG_Map>(nullptr);
}

inline bool VerifySizePrefixedMSG_MapBuffer(
    ::flatbuffers::Verifier &verifier) {
  return verifier.VerifySizePrefixedBuffer<MECData::MSG_Map>(nullptr);
}

inline void FinishMSG_MapBuffer(
    ::flatbuffers::FlatBufferBuilder &fbb,
    ::flatbuffers::Offset<MECData::MSG_Map> root) {
  fbb.Finish(root);
}

inline void FinishSizePrefixedMSG_MapBuffer(
    ::flatbuffers::FlatBufferBuilder &fbb,
    ::flatbuffers::Offset<MECData::MSG_Map> root) {
  fbb.FinishSizePrefixed(root);
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_MAP_MECDATA_H_
