// automatically generated by the <PERSON><PERSON>uffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_IARDATA_MECDATA_H_
#define FLATBUFFERS_GENERATED_IARDATA_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

#include "DriveBehavior_generated.h"
#include "DriveRequest_generated.h"
#include "PathPlanningPoint_generated.h"

namespace MECData {

struct DF_IARData;
struct DF_IARDataBuilder;

struct DF_IARData FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_IARDataBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_CURRENTPOS = 4,
    VT_PATHPLANNING = 6,
    VT_CURRENTBEHAVIOR = 8,
    VT_REQS = 10
  };
  const MECData::DF_PathPlanningPoint *currentPos() const {
    return GetPointer<const MECData::DF_PathPlanningPoint *>(VT_CURRENTPOS);
  }
  const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_PathPlanningPoint>> *pathPlanning() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_PathPlanningPoint>> *>(VT_PATHPLANNING);
  }
  const MECData::DE_DriveBehavior *currentBehavior() const {
    return GetPointer<const MECData::DE_DriveBehavior *>(VT_CURRENTBEHAVIOR);
  }
  const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_DriveRequest>> *reqs() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_DriveRequest>> *>(VT_REQS);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_CURRENTPOS) &&
           verifier.VerifyTable(currentPos()) &&
           VerifyOffset(verifier, VT_PATHPLANNING) &&
           verifier.VerifyVector(pathPlanning()) &&
           verifier.VerifyVectorOfTables(pathPlanning()) &&
           VerifyOffset(verifier, VT_CURRENTBEHAVIOR) &&
           verifier.VerifyTable(currentBehavior()) &&
           VerifyOffset(verifier, VT_REQS) &&
           verifier.VerifyVector(reqs()) &&
           verifier.VerifyVectorOfTables(reqs()) &&
           verifier.EndTable();
  }
};

struct DF_IARDataBuilder {
  typedef DF_IARData Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_currentPos(::flatbuffers::Offset<MECData::DF_PathPlanningPoint> currentPos) {
    fbb_.AddOffset(DF_IARData::VT_CURRENTPOS, currentPos);
  }
  void add_pathPlanning(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_PathPlanningPoint>>> pathPlanning) {
    fbb_.AddOffset(DF_IARData::VT_PATHPLANNING, pathPlanning);
  }
  void add_currentBehavior(::flatbuffers::Offset<MECData::DE_DriveBehavior> currentBehavior) {
    fbb_.AddOffset(DF_IARData::VT_CURRENTBEHAVIOR, currentBehavior);
  }
  void add_reqs(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_DriveRequest>>> reqs) {
    fbb_.AddOffset(DF_IARData::VT_REQS, reqs);
  }
  explicit DF_IARDataBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_IARData> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_IARData>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_IARData> CreateDF_IARData(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<MECData::DF_PathPlanningPoint> currentPos = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_PathPlanningPoint>>> pathPlanning = 0,
    ::flatbuffers::Offset<MECData::DE_DriveBehavior> currentBehavior = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<MECData::DF_DriveRequest>>> reqs = 0) {
  DF_IARDataBuilder builder_(_fbb);
  builder_.add_reqs(reqs);
  builder_.add_currentBehavior(currentBehavior);
  builder_.add_pathPlanning(pathPlanning);
  builder_.add_currentPos(currentPos);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DF_IARData> CreateDF_IARDataDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<MECData::DF_PathPlanningPoint> currentPos = 0,
    const std::vector<::flatbuffers::Offset<MECData::DF_PathPlanningPoint>> *pathPlanning = nullptr,
    ::flatbuffers::Offset<MECData::DE_DriveBehavior> currentBehavior = 0,
    const std::vector<::flatbuffers::Offset<MECData::DF_DriveRequest>> *reqs = nullptr) {
  auto pathPlanning__ = pathPlanning ? _fbb.CreateVector<::flatbuffers::Offset<MECData::DF_PathPlanningPoint>>(*pathPlanning) : 0;
  auto reqs__ = reqs ? _fbb.CreateVector<::flatbuffers::Offset<MECData::DF_DriveRequest>>(*reqs) : 0;
  return MECData::CreateDF_IARData(
      _fbb,
      currentPos,
      pathPlanning__,
      currentBehavior,
      reqs__);
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_IARDATA_MECDATA_H_
