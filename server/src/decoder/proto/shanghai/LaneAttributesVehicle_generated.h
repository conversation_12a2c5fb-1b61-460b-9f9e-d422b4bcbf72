// automatically generated by the <PERSON>Buffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_LANEATTRIBUTESVEHICLE_MECDATA_H_
#define FLATBUFFERS_GENERATED_LANEATTRIBUTESVEHICLE_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

namespace MECData {

struct DF_LaneAttributesVehicle;
struct DF_LaneAttributesVehicleBuilder;

struct DF_LaneAttributesVehicle FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DF_LaneAttributesVehicleBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_VEHICLE = 4
  };
  int16_t vehicle() const {
    return GetField<int16_t>(VT_VEHICLE, 0);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<int16_t>(verifier, VT_VEHICLE, 2) &&
           verifier.EndTable();
  }
};

struct DF_LaneAttributesVehicleBuilder {
  typedef DF_LaneAttributesVehicle Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_vehicle(int16_t vehicle) {
    fbb_.AddElement<int16_t>(DF_LaneAttributesVehicle::VT_VEHICLE, vehicle, 0);
  }
  explicit DF_LaneAttributesVehicleBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DF_LaneAttributesVehicle> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DF_LaneAttributesVehicle>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DF_LaneAttributesVehicle> CreateDF_LaneAttributesVehicle(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    int16_t vehicle = 0) {
  DF_LaneAttributesVehicleBuilder builder_(_fbb);
  builder_.add_vehicle(vehicle);
  return builder_.Finish();
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_LANEATTRIBUTESVEHICLE_MECDATA_H_
