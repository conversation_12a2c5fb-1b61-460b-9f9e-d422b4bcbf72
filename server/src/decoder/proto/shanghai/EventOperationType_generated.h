// automatically generated by the <PERSON><PERSON>uffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_EVENTOPERATIONTYPE_MECDATA_H_
#define FLATBUFFERS_GENERATED_EVENTOPERATIONTYPE_MECDATA_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 24 &&
              FLATBUFFERS_VERSION_MINOR == 3 &&
              FLATBUFFERS_VERSION_REVISION == 25,
             "Non-compatible flatbuffers version included");

namespace MECData {

enum DE_EventOperationType : uint8_t {
  DE_EventOperationType_AUTO_FORWARD = 0,
  DE_EventOperationType_MANUAL_FORWARD = 1,
  DE_EventOperationType_UNKNOWN = 2,
  DE_EventOperationType_MIN = DE_EventOperationType_AUTO_FORWARD,
  DE_EventOperationType_MAX = DE_EventOperationType_UNKNOWN
};

inline const DE_EventOperationType (&EnumValuesDE_EventOperationType())[3] {
  static const DE_EventOperationType values[] = {
    DE_EventOperationType_AUTO_FORWARD,
    DE_EventOperationType_MANUAL_FORWARD,
    DE_EventOperationType_UNKNOWN
  };
  return values;
}

inline const char * const *EnumNamesDE_EventOperationType() {
  static const char * const names[4] = {
    "AUTO_FORWARD",
    "MANUAL_FORWARD",
    "UNKNOWN",
    nullptr
  };
  return names;
}

inline const char *EnumNameDE_EventOperationType(DE_EventOperationType e) {
  if (::flatbuffers::IsOutRange(e, DE_EventOperationType_AUTO_FORWARD, DE_EventOperationType_UNKNOWN)) return "";
  const size_t index = static_cast<size_t>(e);
  return EnumNamesDE_EventOperationType()[index];
}

}  // namespace MECData

#endif  // FLATBUFFERS_GENERATED_EVENTOPERATIONTYPE_MECDATA_H_
