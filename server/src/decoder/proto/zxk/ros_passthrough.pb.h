// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ros_passthrough.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_ros_5fpassthrough_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_ros_5fpassthrough_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3021000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3021006 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_ros_5fpassthrough_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_ros_5fpassthrough_2eproto {
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_ros_5fpassthrough_2eproto;
namespace allride {
namespace proto {
namespace infra {
namespace rsuconnect {
class RosPassThrough;
struct RosPassThroughDefaultTypeInternal;
extern RosPassThroughDefaultTypeInternal _RosPassThrough_default_instance_;
}  // namespace rsuconnect
}  // namespace infra
}  // namespace proto
}  // namespace allride
PROTOBUF_NAMESPACE_OPEN
template<> ::allride::proto::infra::rsuconnect::RosPassThrough* Arena::CreateMaybeMessage<::allride::proto::infra::rsuconnect::RosPassThrough>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace allride {
namespace proto {
namespace infra {
namespace rsuconnect {

// ===================================================================

class RosPassThrough final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:allride.proto.infra.rsuconnect.RosPassThrough) */ {
 public:
  inline RosPassThrough() : RosPassThrough(nullptr) {}
  ~RosPassThrough() override;
  explicit PROTOBUF_CONSTEXPR RosPassThrough(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  RosPassThrough(const RosPassThrough& from);
  RosPassThrough(RosPassThrough&& from) noexcept
    : RosPassThrough() {
    *this = ::std::move(from);
  }

  inline RosPassThrough& operator=(const RosPassThrough& from) {
    CopyFrom(from);
    return *this;
  }
  inline RosPassThrough& operator=(RosPassThrough&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const RosPassThrough& default_instance() {
    return *internal_default_instance();
  }
  static inline const RosPassThrough* internal_default_instance() {
    return reinterpret_cast<const RosPassThrough*>(
               &_RosPassThrough_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(RosPassThrough& a, RosPassThrough& b) {
    a.Swap(&b);
  }
  inline void Swap(RosPassThrough* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(RosPassThrough* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  RosPassThrough* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<RosPassThrough>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const RosPassThrough& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const RosPassThrough& from) {
    RosPassThrough::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(RosPassThrough* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "allride.proto.infra.rsuconnect.RosPassThrough";
  }
  protected:
  explicit RosPassThrough(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kDataFieldNumber = 3,
    kSourceIdFieldNumber = 4,
    kRosTopicNameFieldNumber = 5,
    kSourceTypeFieldNumber = 6,
    kSeqFieldNumber = 1,
    kTimeFieldNumber = 2,
  };
  // bytes data = 3;
  void clear_data();
  const std::string& data() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_data(ArgT0&& arg0, ArgT... args);
  std::string* mutable_data();
  PROTOBUF_NODISCARD std::string* release_data();
  void set_allocated_data(std::string* data);
  private:
  const std::string& _internal_data() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_data(const std::string& value);
  std::string* _internal_mutable_data();
  public:

  // string source_id = 4;
  void clear_source_id();
  const std::string& source_id() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_source_id(ArgT0&& arg0, ArgT... args);
  std::string* mutable_source_id();
  PROTOBUF_NODISCARD std::string* release_source_id();
  void set_allocated_source_id(std::string* source_id);
  private:
  const std::string& _internal_source_id() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_source_id(const std::string& value);
  std::string* _internal_mutable_source_id();
  public:

  // string ros_topic_name = 5;
  void clear_ros_topic_name();
  const std::string& ros_topic_name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_ros_topic_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_ros_topic_name();
  PROTOBUF_NODISCARD std::string* release_ros_topic_name();
  void set_allocated_ros_topic_name(std::string* ros_topic_name);
  private:
  const std::string& _internal_ros_topic_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_ros_topic_name(const std::string& value);
  std::string* _internal_mutable_ros_topic_name();
  public:

  // string source_type = 6;
  void clear_source_type();
  const std::string& source_type() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_source_type(ArgT0&& arg0, ArgT... args);
  std::string* mutable_source_type();
  PROTOBUF_NODISCARD std::string* release_source_type();
  void set_allocated_source_type(std::string* source_type);
  private:
  const std::string& _internal_source_type() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_source_type(const std::string& value);
  std::string* _internal_mutable_source_type();
  public:

  // int64 seq = 1;
  void clear_seq();
  int64_t seq() const;
  void set_seq(int64_t value);
  private:
  int64_t _internal_seq() const;
  void _internal_set_seq(int64_t value);
  public:

  // int64 time = 2;
  void clear_time();
  int64_t time() const;
  void set_time(int64_t value);
  private:
  int64_t _internal_time() const;
  void _internal_set_time(int64_t value);
  public:

  // @@protoc_insertion_point(class_scope:allride.proto.infra.rsuconnect.RosPassThrough)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr data_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr source_id_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr ros_topic_name_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr source_type_;
    int64_t seq_;
    int64_t time_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_ros_5fpassthrough_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// RosPassThrough

// int64 seq = 1;
inline void RosPassThrough::clear_seq() {
  _impl_.seq_ = int64_t{0};
}
inline int64_t RosPassThrough::_internal_seq() const {
  return _impl_.seq_;
}
inline int64_t RosPassThrough::seq() const {
  // @@protoc_insertion_point(field_get:allride.proto.infra.rsuconnect.RosPassThrough.seq)
  return _internal_seq();
}
inline void RosPassThrough::_internal_set_seq(int64_t value) {
  
  _impl_.seq_ = value;
}
inline void RosPassThrough::set_seq(int64_t value) {
  _internal_set_seq(value);
  // @@protoc_insertion_point(field_set:allride.proto.infra.rsuconnect.RosPassThrough.seq)
}

// int64 time = 2;
inline void RosPassThrough::clear_time() {
  _impl_.time_ = int64_t{0};
}
inline int64_t RosPassThrough::_internal_time() const {
  return _impl_.time_;
}
inline int64_t RosPassThrough::time() const {
  // @@protoc_insertion_point(field_get:allride.proto.infra.rsuconnect.RosPassThrough.time)
  return _internal_time();
}
inline void RosPassThrough::_internal_set_time(int64_t value) {
  
  _impl_.time_ = value;
}
inline void RosPassThrough::set_time(int64_t value) {
  _internal_set_time(value);
  // @@protoc_insertion_point(field_set:allride.proto.infra.rsuconnect.RosPassThrough.time)
}

// bytes data = 3;
inline void RosPassThrough::clear_data() {
  _impl_.data_.ClearToEmpty();
}
inline const std::string& RosPassThrough::data() const {
  // @@protoc_insertion_point(field_get:allride.proto.infra.rsuconnect.RosPassThrough.data)
  return _internal_data();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void RosPassThrough::set_data(ArgT0&& arg0, ArgT... args) {
 
 _impl_.data_.SetBytes(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:allride.proto.infra.rsuconnect.RosPassThrough.data)
}
inline std::string* RosPassThrough::mutable_data() {
  std::string* _s = _internal_mutable_data();
  // @@protoc_insertion_point(field_mutable:allride.proto.infra.rsuconnect.RosPassThrough.data)
  return _s;
}
inline const std::string& RosPassThrough::_internal_data() const {
  return _impl_.data_.Get();
}
inline void RosPassThrough::_internal_set_data(const std::string& value) {
  
  _impl_.data_.Set(value, GetArenaForAllocation());
}
inline std::string* RosPassThrough::_internal_mutable_data() {
  
  return _impl_.data_.Mutable(GetArenaForAllocation());
}
inline std::string* RosPassThrough::release_data() {
  // @@protoc_insertion_point(field_release:allride.proto.infra.rsuconnect.RosPassThrough.data)
  return _impl_.data_.Release();
}
inline void RosPassThrough::set_allocated_data(std::string* data) {
  if (data != nullptr) {
    
  } else {
    
  }
  _impl_.data_.SetAllocated(data, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.data_.IsDefault()) {
    _impl_.data_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:allride.proto.infra.rsuconnect.RosPassThrough.data)
}

// string source_id = 4;
inline void RosPassThrough::clear_source_id() {
  _impl_.source_id_.ClearToEmpty();
}
inline const std::string& RosPassThrough::source_id() const {
  // @@protoc_insertion_point(field_get:allride.proto.infra.rsuconnect.RosPassThrough.source_id)
  return _internal_source_id();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void RosPassThrough::set_source_id(ArgT0&& arg0, ArgT... args) {
 
 _impl_.source_id_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:allride.proto.infra.rsuconnect.RosPassThrough.source_id)
}
inline std::string* RosPassThrough::mutable_source_id() {
  std::string* _s = _internal_mutable_source_id();
  // @@protoc_insertion_point(field_mutable:allride.proto.infra.rsuconnect.RosPassThrough.source_id)
  return _s;
}
inline const std::string& RosPassThrough::_internal_source_id() const {
  return _impl_.source_id_.Get();
}
inline void RosPassThrough::_internal_set_source_id(const std::string& value) {
  
  _impl_.source_id_.Set(value, GetArenaForAllocation());
}
inline std::string* RosPassThrough::_internal_mutable_source_id() {
  
  return _impl_.source_id_.Mutable(GetArenaForAllocation());
}
inline std::string* RosPassThrough::release_source_id() {
  // @@protoc_insertion_point(field_release:allride.proto.infra.rsuconnect.RosPassThrough.source_id)
  return _impl_.source_id_.Release();
}
inline void RosPassThrough::set_allocated_source_id(std::string* source_id) {
  if (source_id != nullptr) {
    
  } else {
    
  }
  _impl_.source_id_.SetAllocated(source_id, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.source_id_.IsDefault()) {
    _impl_.source_id_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:allride.proto.infra.rsuconnect.RosPassThrough.source_id)
}

// string ros_topic_name = 5;
inline void RosPassThrough::clear_ros_topic_name() {
  _impl_.ros_topic_name_.ClearToEmpty();
}
inline const std::string& RosPassThrough::ros_topic_name() const {
  // @@protoc_insertion_point(field_get:allride.proto.infra.rsuconnect.RosPassThrough.ros_topic_name)
  return _internal_ros_topic_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void RosPassThrough::set_ros_topic_name(ArgT0&& arg0, ArgT... args) {
 
 _impl_.ros_topic_name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:allride.proto.infra.rsuconnect.RosPassThrough.ros_topic_name)
}
inline std::string* RosPassThrough::mutable_ros_topic_name() {
  std::string* _s = _internal_mutable_ros_topic_name();
  // @@protoc_insertion_point(field_mutable:allride.proto.infra.rsuconnect.RosPassThrough.ros_topic_name)
  return _s;
}
inline const std::string& RosPassThrough::_internal_ros_topic_name() const {
  return _impl_.ros_topic_name_.Get();
}
inline void RosPassThrough::_internal_set_ros_topic_name(const std::string& value) {
  
  _impl_.ros_topic_name_.Set(value, GetArenaForAllocation());
}
inline std::string* RosPassThrough::_internal_mutable_ros_topic_name() {
  
  return _impl_.ros_topic_name_.Mutable(GetArenaForAllocation());
}
inline std::string* RosPassThrough::release_ros_topic_name() {
  // @@protoc_insertion_point(field_release:allride.proto.infra.rsuconnect.RosPassThrough.ros_topic_name)
  return _impl_.ros_topic_name_.Release();
}
inline void RosPassThrough::set_allocated_ros_topic_name(std::string* ros_topic_name) {
  if (ros_topic_name != nullptr) {
    
  } else {
    
  }
  _impl_.ros_topic_name_.SetAllocated(ros_topic_name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.ros_topic_name_.IsDefault()) {
    _impl_.ros_topic_name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:allride.proto.infra.rsuconnect.RosPassThrough.ros_topic_name)
}

// string source_type = 6;
inline void RosPassThrough::clear_source_type() {
  _impl_.source_type_.ClearToEmpty();
}
inline const std::string& RosPassThrough::source_type() const {
  // @@protoc_insertion_point(field_get:allride.proto.infra.rsuconnect.RosPassThrough.source_type)
  return _internal_source_type();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void RosPassThrough::set_source_type(ArgT0&& arg0, ArgT... args) {
 
 _impl_.source_type_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:allride.proto.infra.rsuconnect.RosPassThrough.source_type)
}
inline std::string* RosPassThrough::mutable_source_type() {
  std::string* _s = _internal_mutable_source_type();
  // @@protoc_insertion_point(field_mutable:allride.proto.infra.rsuconnect.RosPassThrough.source_type)
  return _s;
}
inline const std::string& RosPassThrough::_internal_source_type() const {
  return _impl_.source_type_.Get();
}
inline void RosPassThrough::_internal_set_source_type(const std::string& value) {
  
  _impl_.source_type_.Set(value, GetArenaForAllocation());
}
inline std::string* RosPassThrough::_internal_mutable_source_type() {
  
  return _impl_.source_type_.Mutable(GetArenaForAllocation());
}
inline std::string* RosPassThrough::release_source_type() {
  // @@protoc_insertion_point(field_release:allride.proto.infra.rsuconnect.RosPassThrough.source_type)
  return _impl_.source_type_.Release();
}
inline void RosPassThrough::set_allocated_source_type(std::string* source_type) {
  if (source_type != nullptr) {
    
  } else {
    
  }
  _impl_.source_type_.SetAllocated(source_type, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.source_type_.IsDefault()) {
    _impl_.source_type_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:allride.proto.infra.rsuconnect.RosPassThrough.source_type)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__

// @@protoc_insertion_point(namespace_scope)

}  // namespace rsuconnect
}  // namespace infra
}  // namespace proto
}  // namespace allride

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_ros_5fpassthrough_2eproto
