#include "rcu2cloud_objs.h"
#include "protocal_endian.h"



CRCU2Cloud_Objs::CRCU2Cloud_Objs()
{
}

CRCU2Cloud_Objs::~CRCU2Cloud_Objs()
{
}

bool CRCU2Cloud_Objs::ParseProtocal(BYTE *buffer,int length)
{
    DWORD offset=0;
    m_stData.channelId = GetHostUnsignedChar(buffer,offset);
    for(int i =0 ;i < 8 ;i ++)
    {
        m_stData.rcuId[i] = GetHostUnsignedChar(buffer,offset);
    }
    m_stData.deviceType = GetHostUnsignedChar(buffer,offset);
    for(int i =0 ;i < 11 ;i ++)
    {
        m_stData.deivceId[i] = GetHostUnsignedChar(buffer,offset);
    }
    m_stData.timestampOfDevOut = GetHostlonglong(buffer,offset);
    m_stData.timestampOfDetIn = GetHostlonglong(buffer,offset);
    m_stData.timestampOfDetOut = GetHostlonglong(buffer,offset);
    m_stData.gnssType = GetHostUnsignedChar(buffer,offset);
    m_stData.objectiveNum = GetHostUnsignedShort(buffer,offset);
    for(int i =0;i<m_stData.objectiveNum;i++)
    {
        TObjs stObjs;
        ParseObjs(buffer,offset,stObjs);
        m_stData.objective.push_back(stObjs);
        // std::cout << "offset = +++++++++++++++++++++" << offset << std::endl;
    }
    return true;
}

bool CRCU2Cloud_Objs::ParseObjs(BYTE* buffer,DWORD& offset,TObjs& objs)
{
    for(int i =0 ;i < 16 ;i ++)
    {
        objs.uuid[i] = GetHostUnsignedChar(buffer,offset);
    }
    objs.objId = GetHostUnsignedShort(buffer,offset);
    objs.type = GetHostUnsignedChar(buffer,offset);
    objs.status = GetHostUnsignedChar(buffer,offset);
    objs.len = GetHostUnsignedShort(buffer,offset);
    // std::cout << "objs.len " <<objs.len<< std::endl;
    objs.width = GetHostUnsignedShort(buffer,offset);
    // std::cout << "objs.width " <<objs.width<< std::endl;
    objs.height = GetHostUnsignedShort(buffer,offset);
    // std::cout << "objs.height " <<objs.height<< std::endl;
    objs.longitude = GetValue<float,DWORD>(GetHostUnsignedInt(buffer,offset),1e-7,-1800000000);
    objs.latitude = GetValue<float,DWORD>(GetHostUnsignedInt(buffer,offset),1e-7,-900000000);
    // std::cout << "long = " << objs.longitude << " lat = " << objs.latitude << std::endl;
    objs.locEast = GetValue<int,DWORD>(GetHostUnsignedInt(buffer,offset),1,-2000000);
    objs.locNorth = GetValue<int,DWORD>(GetHostUnsignedInt(buffer,offset),1,-2000000);
    objs.posConfidence = GetHostUnsignedChar(buffer,offset);
    objs.elevation = GetValue<int,DWORD>(GetHostUnsignedInt(buffer,offset),1,-5000);
    // std::cout << "elevation = " << objs.elevation << std::endl;
    objs.elevConfidence = GetHostUnsignedChar(buffer,offset);
    objs.speed = GetValue<float,WORD>(GetHostUnsignedShort(buffer,offset),0.01,0);
    // std::cout << "speed = " << objs.speed << std::endl;
    objs.speedConfidence = GetHostUnsignedChar(buffer,offset);

    objs.speedEast = GetValue<float,WORD>(GetHostUnsignedShort(buffer,offset),1,-30000);
    // std::cout << "speedEast = " << objs.speedEast << std::endl;
    objs.speedEastConfidence = GetHostUnsignedChar(buffer,offset);
    objs.speedNorth = GetValue<float,WORD>(GetHostUnsignedShort(buffer,offset),1,-30000);
    // std::cout << "speedNorth = " << objs.speedNorth << std::endl;
    objs.speedNorthCOnfidence = GetHostUnsignedChar(buffer,offset);

    objs.heading = GetValue<float,DWORD>(GetHostUnsignedInt(buffer,offset),1e-4,0);
    // std::cout << "heading = " << objs.heading << std::endl;
    objs.headConfidence = GetHostUnsignedChar(buffer,offset);
    objs.acclVert = GetValue<short,WORD>(GetHostUnsignedShort(buffer,offset),0.01,-30000);
    objs.accelVertConfidence = GetHostUnsignedChar(buffer,offset);

    objs.trackedTimes = GetHostUnsignedInt(buffer,offset);
    objs.histLocNum = GetHostUnsignedShort(buffer,offset);
    for(int i=0;i<objs.histLocNum;i++)
    {
        TLocation location;
        ParseLocation(buffer,offset,location);
        objs.histLocs.push_back(location);
    }
    objs.predLocNum = GetHostUnsignedShort(buffer,offset);
    for(int i=0;i<objs.predLocNum;i++)
    {
        TLocation location;
        ParseLocation(buffer,offset,location);
        objs.predLoc.push_back(location);
    }
    objs.laneId = GetHostUnsignedChar(buffer,offset);
    objs.filterInfoType = GetHostUnsignedChar(buffer,offset);
    if(objs.filterInfoType == 1)
    {
        
    }
    objs.lenplateNo = GetHostUnsignedChar(buffer,offset);
    for(int i=0;i<objs.lenplateNo;i++)
    {
        objs.plateNo.push_back(buffer[offset++]);
    }
    objs.plateType = GetHostUnsignedChar(buffer,offset);
    // std::cout << "plateType = " << objs.plateType << std::endl;
    objs.plateColor = GetHostUnsignedChar(buffer,offset);
    objs.objColor = GetHostUnsignedChar(buffer,offset);

    return true;

}

bool CRCU2Cloud_Objs::ParseLocation(BYTE *buffer, DWORD &offset, TLocation &location)
{
    location.longitude = GetValue<double,DWORD>(GetHostUnsignedInt(buffer,offset),1e-7,-1800000000);
    location.latitude = GetValue<double,DWORD>(GetHostUnsignedInt(buffer,offset),1e-7,-900000000);
    location.posConfidence = GetHostUnsignedChar(buffer,offset);
    location.speed = GetHostUnsignedShort(buffer,offset);
    location.speedConfidence = GetHostUnsignedChar(buffer,offset);
    location.heading = GetHostUnsignedInt(buffer,offset);
    location.headConfidence = GetHostUnsignedChar(buffer,offset);
    return true;
}


TRcu2cloudObjs CRCU2Cloud_Objs::GetData()
{
    return m_stData;
}