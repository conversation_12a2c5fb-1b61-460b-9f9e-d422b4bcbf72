#include "protocal_base.h"
#include <chrono>
// #include "protocal_map.hpp"
#include <string.h>
#include <math.h>
#include <iostream>
using namespace cvis_bcl;

ProtocalBase::ProtocalBase()
{

}

ProtocalBase::~ProtocalBase()
{

}


Header ProtocalBase::GetHeader()
{
    return header_;
}

TIMESTAMP ProtocalBase::GetTimestamp()
{
    auto now = std::chrono::system_clock::now();
    auto milli = std::chrono::duration_cast<std::chrono::milliseconds>(now.time_since_epoch()).count();
    return milli;
}

HeaderData ProtocalBase::MakeHeader(std::string msg_type,DWORD data_length)
{
    // HeaderData data;
    // data.length = data_length;
    // data.control =0;
    // data.msg_type = datatype_reflect_map[msg_type];
    // data.timestamp = GetTimestamp();
    // std::cout<<"make header timestamp"<<data.timestamp<<std::endl;
    // data.version = SUPPORT_VERSION;
    // return data;
}

bool ProtocalBase::ParseProtocal(BYTE* buffer,int length)
{
    return true;
}

void ProtocalBase::Free()
{
    if(buffer_!=nullptr)
    {
        free(buffer_);
        buffer_ = nullptr;
    }
}

void ProtocalBase::ConvertString2Byte(std::string str,BYTE* byte,int byte_length)
{
    if(str.length()<byte_length)
    {
        memcpy(byte,str.data(),str.length());
    }
    else
    {
        memcpy(byte,str.data(),byte_length);
    }
}

DWORD ProtocalBase::MakeDWORD(double value,double scale,int offset)
{
    return (DWORD)round((value/scale)-offset);
}

WORD ProtocalBase::MakeWORD(float value,float scale,int offset)
{
    return (WORD)round((value/scale)-offset);
}

DWORD ProtocalBase::MakeLongitude(double value)
{
    return MakeDWORD(value,1e-7,-1800000001);
}

DWORD ProtocalBase::MakeLatitude(double value)
{
    return MakeDWORD(value,1e-7,-900000001);
}

DWORD ProtocalBase::MakeElevation(double value)
{
    return MakeDWORD(value,0.1,-100001);
}

DWORD ProtocalBase::MakeHeading(double value)
{
    return MakeDWORD(value,1e-4,-1);
}

BYTE ProtocalBase::MakeBYTE(float value,float scale,int offset)
{
    return (BYTE)round((value/scale)-offset);
}

double ProtocalBase::GetDouble(DWORD proto_value,double scale,int offset)
{
    return (proto_value+offset)*scale;
}

float ProtocalBase::GetFloat(DWORD proto_value,float scale,int offset)
{
    return (proto_value+offset)*scale;
}

double ProtocalBase::GetLongitude(DWORD proto_value)
{
    return GetDouble(proto_value,1e-7,-1800000001);
}

double ProtocalBase::GetLatitude(DWORD proto_value)
{
    return GetDouble(proto_value,1e-7,-900000001);
}

double ProtocalBase::GetElevation(DWORD proto_value)
{
    return GetDouble(proto_value,0.1,-100001);
}

double ProtocalBase::GetHeading(DWORD proto_value)
{
    return GetDouble(proto_value,1e-4,-1);
}