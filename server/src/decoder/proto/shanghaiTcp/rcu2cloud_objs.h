#ifndef _RCU2CLOUD_OBJS_H_
#define _RCU2CLOUD_OBJS_H_

#include "protocal_base.h"

using namespace cvis_bcl;

class CRCU2Cloud_Objs: public ProtocalBase
{
    public:
    CRCU2Cloud_Objs();
    ~CRCU2Cloud_Objs();

    virtual bool ParseProtocal(BYTE *buffer,int length);
    
    bool ParseObjs(BYTE* buffer,DWORD& offset,TObjs& objs);
    bool ParseLocation(BYTE* buffer,DWORD& offset,TLocation& location);

    TRcu2cloudObjs GetData();
    
    private:
    TRcu2cloudObjs m_stData;
};


#endif