/*---------------------------------------------------------------------
/* Copyright (c) 2023 tsari. All rights reserved. */
/**
* @Author: hlf
* @brief: 协议基础工具类，解析报文头
 *
 * @version: 版本编号  修订日期  修订者 修订内容 （最新的在最前面）
 * --------------------------------------------------------------------
*/

#pragma once
#include "protcal_datatype.h"
#include "protocal_endian.h"

namespace cvis_bcl
{
#pragma pack(1)
    // struct HeaderData
    // {
    //     BYTE start_flag;
    //     DWORD length;
    //     BYTE msg_type;
    //     BYTE version;
    //     TIMESTAMP timestamp;
    //     BYTE control;
    // };
    struct HeaderData
    {
        DWORD start_flag:8;
        // BYTE start_flag;
        DWORD length:24;
        BYTE msg_type;
        BYTE version;
        TIMESTAMP timestamp;
        BYTE control;
    };
    class Header:public ProtocalEndian
    {
    public:
        Header();
        virtual ~Header();
        bool ParseHeader(BYTE* buffer,unsigned int& offset,int length);
        void MakeHeader(BYTE* buffer,DWORD& offset,HeaderData data);
        DWORD GetLength();
        BYTE GetMsgType();
        BYTE GetVersion();
        TIMESTAMP GetTimestamp();
        BYTE GetControl();
        void SetLength(DWORD length);
        void SetMsgType(BYTE msg_type);
        void SetVersion(BYTE version);
        void SetTimestamp(TIMESTAMP timestamp);
        void SetControl(BYTE control);
    private:
        HeaderData data_;

    };
}