#ifndef _PROTOCAL_STRUCT_SHANGHAI_HPP_
#define _PROTOCAL_STRUCT_SHANGHAI_HPP_

#include "protcal_datatype.h"
#include "vector"

#pragma pack(1)


struct TLocation
{
    DWORD longitude = 0;
    DWORD latitude = 0;
    BYTE posConfidence = 0;
    WORD speed = 0;
    BYTE speedConfidence = 0;
    DWORD heading = 0;
    BYTE headConfidence = 0;
    int GetLength()
    {
        return 17;
    }
};

struct TFilterInfo
{
    WORD dimension = 0;
    std::vector<WORD> VarN_Index;
    //????
};



struct TObjs{
    BYTE uuid[16];
    WORD objId = 0;
    BYTE type = 0;
    BYTE status = 0;
    WORD len = 0;
    WORD width = 0;
    WORD height = 0;
    float longitude = 0;
    float latitude = 0;
    DWORD locEast = 0;
    DWORD locNorth = 0;
    BYTE posConfidence = 0;
    DWORD elevation = 0;
    BYTE elevConfidence = 0;
    float speed = 0;
    BYTE speedConfidence = 0;

    float speedEast = 0;
    BYTE speedEastConfidence = 0;
    float speedNorth = 0;
    BYTE speedNorthCOnfidence = 0;

    float heading = 0;
    BYTE headConfidence = 0;
    WORD acclVert = 0;
    BYTE accelVertConfidence = 0;
    DWORD trackedTimes = 0;
    WORD histLocNum = 0;
    std::vector<TLocation> histLocs;
    WORD predLocNum = 0;
    std::vector<TLocation> predLoc;
    BYTE laneId = 0;
    BYTE filterInfoType = 0;
    TFilterInfo filterInfo;
    BYTE lenplateNo = 0;
    std::string plateNo;
    BYTE plateType = 0;
    BYTE plateColor = 0;
    BYTE objColor = 0;

    int GetLength()
    {

    }
};

struct TRcu2cloudObjs{
    BYTE channelId = 0;
    BYTE rcuId[8];
    BYTE deviceType =0;
    BYTE deivceId[11];
    TIMESTAMP timestampOfDevOut = 0;
    TIMESTAMP timestampOfDetIn = 0;
    TIMESTAMP timestampOfDetOut = 0;
    BYTE gnssType = 0;
    WORD objectiveNum = 0;
    std::vector<TObjs> objective;
    int GetLength()
    {
        int length = 48;
        for (int i = 0; i < objective.size(); i++)
        {
            length += objective[i].GetLength();
        }
        return length;
    }
};



#endif