/*---------------------------------------------------------------------
/* Copyright (c) 2023 tsari. All rights reserved. */
/**
* @Author: hlf
* @brief: 协议基础工具类，输入的buffer一定为大端字节序，完成
 *        1.主机大小端检测  2.将网络字节序转到主机字节序解析 3解析报头
 *
 * @version: 版本编号  修订日期  修订者 修订内容 （最新的在最前面）
 * --------------------------------------------------------------------
*/

#pragma once
#include "protcal_datatype.h"
namespace cvis_bcl
{
    enum ByteOrder{CVIS_BIG_ENDIAN=0,CVIS_LITTLE_ENDIAN};

    class ProtocalEndian
    {
    public:
        ProtocalEndian();
        virtual ~ProtocalEndian();
        ByteOrder GetByteOrder();
        BYTE GetBit2Byte(BYTE* buffer,DWORD& offset);
        BYTE GetHostUnsignedChar(BYTE* buffer,DWORD& offset);

        WORD GetHostUnsignedShort(BYTE* buffer,DWORD& offset);

        DWORD GetHostUnsignedInt(BYTE* buffer,DWORD& offset);

        TIMESTAMP GetHostlonglong(BYTE* buffer,DWORD& offset);
        
        void MakeBitByte(BYTE* buffer,DWORD& offset,BYTE value);

        void MakeUnsignedChar(BYTE* buffer,DWORD& offset,BYTE value);

        void MakeUnsignedShort(BYTE* buffer,DWORD& offset,WORD value);

        void MakeUnsignedInt(BYTE* buffer,DWORD& offset,DWORD value);

        void MakeUsignedLongLong(BYTE* buffer,DWORD& offset,uint64_t value);
        
    private:
        ByteOrder byte_order_;   

    };
}