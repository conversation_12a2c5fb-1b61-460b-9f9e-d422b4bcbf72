/*---------------------------------------------------------------------
/* Copyright (c) 2023 tsari. All rights reserved. */
/**
* @Author: hlf
* @brief: 协议基类，
 *        
 * @version: 版本编号  修订日期  修订者 修订内容 （最新的在最前面）
 * --------------------------------------------------------------------
*/

#pragma once
#include <string>
#include "protocal_endian.h"
#include "protocal_header.h"
// #include "protocal_version.h"
#include "protocal_struct_shanghai.hpp"
namespace cvis_bcl
{
    class ProtocalBase:public ProtocalEndian
    {
        public:
        ProtocalBase();
        virtual ~ProtocalBase();
        virtual bool ParseProtocal(BYTE* buffer,int length);
        TIMESTAMP GetTimestamp();
        Header GetHeader();
        HeaderData MakeHeader(std::string msg_type,DWORD data_length);
        void ConvertString2Byte(std::string str,BYTE* byte,int byte_length);
        DWORD MakeDWORD(double value,double scale,int offset);
        WORD MakeWORD(float value,float scale,int offset);
        BYTE MakeBYTE(float value,float scale,int offset);
        DWORD MakeLongitude(double value);
        DWORD MakeLatitude(double value);
        DWORD MakeElevation(double value);
        DWORD MakeHeading(double value);
        template <typename T1,typename T2>
        T1 GetValue(T2 proto_value,float scale,int offset){ return (T1)(proto_value+offset)*scale;};
;
        double GetDouble(DWORD proto_value,double scale,int offset);
        float GetFloat(DWORD proto_value,float scale,int offset);
        double GetLongitude(DWORD proto_value);
        double GetLatitude(DWORD proto_value);
        double GetElevation(DWORD proto_value);
        double GetHeading(DWORD proto_value);
        void Free();
        protected:
        Header header_;
        BYTE* buffer_;
        std::string msg_type;
    };
}