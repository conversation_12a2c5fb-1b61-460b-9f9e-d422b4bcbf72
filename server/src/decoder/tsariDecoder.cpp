#include "tsariDecoder.h"
#include "json/json.h"
#include "logger.hpp"

CTsariDecoder::CTsariDecoder()
{

}

CTsariDecoder::~CTsariDecoder()
{
}

bool CTsariDecoder::Init(std::string strRoadID)
{
    m_strRoadId = strRoadID;
    return true;
}

bool CTsariDecoder::DecodeData(u64& llCapTime,std::string strTargets,std::string *strResult)
{
    Json::Value root;
    Json::CharReaderBuilder reader;
    std::string errs;
    std::istringstream s(strTargets);
    if (!Json::parseFromStream(reader, s, &root, &errs)) {
        ERROR("Failed to parse JSON");
        return false;
    }

    if(!root.isObject())
    {
        std::cout << "json is not type object!" << std::endl;
        ERROR("json is not type object"); 
        return false;
    }

    if(!root.isMember("timestamp") || !root["timestamp"].isUInt64())
    {
        ERROR("can't found timestamp or timestamp type is not int64");
        return false;
    }

    if(!root.isMember("data_object") || !root["data_object"].isArray())
    {
        ERROR("can't found data_object or data_object type is not array");
        return false;
    }

    Json::Value data_objectArray = root["data_object"];
    for(const auto& object:data_objectArray)
    {
        if(!object.isObject())
        {
            ERROR("json array have item which is not object");
            return false;
        }

        if(!object.isMember("id") || !object["id"].isString())
        {
            ERROR("can't found id in array or id type is not string");
            return false;
        }

        if(!object.isMember("type") || !object["type"].isInt())
        {
            ERROR("can't found type in array or type type is not int");
            return false;
        }

        if(!object.isMember("center_longitude") || !object["center_longitude"].isDouble())
        {
            ERROR("can't found center_longitude in array or center_longitude type is not uint64");
            return false;
        }

        if(!object.isMember("center_latitude") || !object["center_latitude"].isDouble())
        {
            ERROR("can't found center_latitude in array or center_latitude type is not uint64");
            return false;
        }

        if(!object.isMember("heading") || !object["heading"].isDouble())
        {
            ERROR("can't found heading in array or heading type is not double");
            return false;
        }

        if(!object.isMember("speed") || !object["speed"].isDouble())
        {
            ERROR("can't found speed in array or speed type is not double");
            return false;
        }
    }

    *strResult = strTargets;
    return true;
}