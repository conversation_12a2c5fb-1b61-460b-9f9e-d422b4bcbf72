#include "shanghaiDecoder.h"
#include "RoadsideSafetyMessage_generated.h"
#include "flatbuffers/flatbuffers.h"

#include "json/json.h"

CShanghaiDecoder::CShanghaiDecoder()
{

}

CShanghaiDecoder::~CShanghaiDecoder()
{

}


bool CShanghaiDecoder::Init(std::string strRoadId)
{
    m_strRoadId = strRoadId;
    return true;
}

/*ros_passthrough解析原始 data使用esurfing_group_detectred*/
bool CShanghaiDecoder::DecodeData(u64& llCapTime,std::string strTargets, std::string *strResult)
{
    Json::Value root;
    const MECData::MSG_RoadsideSafetyMessage * road_data = MECData::GetMSG_RoadsideSafetyMessage(strTargets.data());
    root["id"] = road_data->id();
    const MECData::DF_Position3D*  pos = road_data->refPos();
    root["ref_longitude"] = pos->lon();
    root["ref_latitude"] = pos->lat();
    root["ref_elevation"] = pos->ele();

    for(int i=0;i<road_data->participants()->size();i++)
    {
        Json::Value obj;
        const MECData::DF_ParticipantData* participant = road_data->participants()->Get(i);
        obj["ptcType"] = participant->ptcType();
        obj["ptcId"] = participant->ptcId();
        obj["source"] = participant->source();
        obj["secMark"] = participant->secMark();
        Json::Value pos;
        pos["offsetLL_type"] = participant->pos()->offsetLL_type();
        pos["offsetLL"]["lon"] = participant->pos()->offsetLL_as_DF_PositionLLmD64b()->lon();
        pos["offsetLL"]["lat"] = participant->pos()->offsetLL_as_DF_PositionLLmD64b()->lat();
        obj["pos"] = pos;
        obj["posConfidence"]["pos"] = participant->posConfidence()->pos();
        obj["posConfidence"]["elevation"] = participant->posConfidence()->elevation();
        obj["transmission"] = participant->transmission();
        obj["speed"] = participant->speed();
        obj["heading"] = participant->heading();
        obj["angle"] = participant->angle();
        obj["size"]["width"] = participant->size()->width();
        obj["size"]["length"] =participant->size()->length();
        obj["size"]["height"] = participant->size()->height();
        obj["vehicleClass"]["classification"] = participant->vehicleClass()->classification();
        obj["vehicleClass"]["fuelType"] = participant->vehicleClass()->fuelType();
        obj["moy"] = participant->moy();
        obj["nodeId"]["region"] = participant->nodeId()->region();
        obj["nodeId"]["id"] = participant->nodeId()->id();
        root["participants"].append(obj);
        
    }
    for(int i=0;i<road_data->time_records()->records()->size();i++)
    {
       const MECData::DE_ArrivalTimeStamp* time = road_data->time_records()->records()->Get(i);
       Json::Value time_record;
       time_record["moduleId"] = time->module_id();
       time_record["timestamp"] = time->timestamp();
       time_record["packageId"] = time->package_id();
       root["arrive_time"].append(time_record);
    }
    Json::StreamWriterBuilder write_builder;
    write_builder["indentation"] = "";
    *strResult = Json::writeString(write_builder, root);
    return true;
}
