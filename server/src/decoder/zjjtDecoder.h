#ifndef _ZJJT_DECODER_H_
#define _ZJJT_DECODER_H_

#include "decoderBase.h"
#include "logger.hpp"


#define chengGu "WJ_01"   // 成谷科技
#define wanJi "WJ_01"     // 万集
#define haiKang "HLZL_01" // 海康智联
#define baiDu01 "BD_01"   // 百度（百度因技术原因，从两个MEC发数据，整个路段的数据需要做拼接）
#define baiDu02 "BD_02"

class CZjjtDecoder : public CDecoderBase
{
public:
    CZjjtDecoder();
    ~CZjjtDecoder();
    bool Init(std::string strRoadId) override;
    bool DecodeData(u64& llCapTime,std::string strTargets, std::string *strResult) override;
};

#endif