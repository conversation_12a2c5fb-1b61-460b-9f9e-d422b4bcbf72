#ifndef _SHANGHAI_TCP_DECODER_H_
#define _SHANGHAI_TCP_DECODER_H_

#include "decoderBase.h"
// #include "rcu2cloud_objs.h"
struct TRcu2cloudObjs;

// 团表目标类型
enum ObjType
{
    obj_type_pedestrians = 0,              // 行人
    obj_type_bicycle = 1,                  // 自行车
    obj_type_passenger_cars = 2,           // 乘用车
    obj_type_motorcycles = 3,              // 摩托车
    obj_type_special_purpose_vehicles = 4, // 特殊用车辆
    obj_type_public_buses = 5,             // 公交车
    obj_type_have_rail_cars = 6,           // 有轨道车
    obj_type_trucks = 7,                   // 卡车
    obj_type_tricycles = 8,                // 三轮车
    obj_type_traffic_signal_lights = 9,    // 交通信号灯
    obj_type_traffic_Signs = 10,           // 交通标识
    obj_type_animals = 15,                 // 动物
    obj_type_roadblocks = 60,              // 路障
    obj_type_traffic_Cone = 61,            // 交通锥
    obj_type_other_types = 254,            // 其他类型
    obj_type_not_obtained = 255            // 未获取
};

// 清研院目标类型
enum TsariObjType
{
    tsari_obj_type_unknow = 0,      // 未知
    tsari_obj_type_vehicle = 1,     // 机动车
    tsari_obj_type_person = 2,      // 人
    tsari_obj_type_non_vehicle = 3, // 非机动车
    tsari_obj_type_obstacle = 4     // 障碍物
};

class CShanghaiTcpDecoder:public CDecoderBase
{
public:
    CShanghaiTcpDecoder(/* args */);
    ~CShanghaiTcpDecoder();
    bool Init(std::string strRoadID) override;
    bool DecodeData(u64& llCapTime,std::string strTargets, std::string *strResult) override;

protected:
    bool parseToJson(TRcu2cloudObjs &stDetectedObjects, std::string *strResult);    // 标准协议格式
    bool parseToTsariJson(u64& llCapTime,TRcu2cloudObjs &stDetectedObjects, std::string *strResult);   // 清研院后处理格式
    int tfType(int type);  // 用于转换目标类型
private:    
    /* data */
};





#endif