#include "shanghaiTcpDecoder.h"
#include "iostream"
#include "rcu2cloud_objs.h"
#include "json/json.h"


CShanghaiTcpDecoder::CShanghaiTcpDecoder(/* args */)
{
}

CShanghaiTcpDecoder::~CShanghaiTcpDecoder()
{
}

bool CShanghaiTcpDecoder::Init(std::string strRoadID)
{
    m_strRoadId = strRoadID;
    return true;
}

bool CShanghaiTcpDecoder::DecodeData(u64& llCapTime,std::string strTargets, std::string *strResult)
{

    // for(int i = 0 ;i < strTargets.length() ;i++)
    // {
    //     printf("%02x ",(unsigned char)(strTargets.data()[i]));
    // }
    // printf("\n");

    TRcu2cloudObjs rcu2Cloud_objsData;
    CRCU2Cloud_Objs rcu2Cloud_objs;
    DWORD length=0;
    rcu2Cloud_objs.ParseProtocal((unsigned char*)strTargets.data(),length);
    rcu2Cloud_objsData = rcu2Cloud_objs.GetData();

    // parseToJson(rcu2Cloud_objsData,strResult);
    parseToTsariJson(llCapTime,rcu2Cloud_objsData,strResult);
    return true;
}

bool CShanghaiTcpDecoder::parseToJson(TRcu2cloudObjs &stDetectedObjects, std::string *strResult)
{
    Json::Value msgData;
    msgData["channelId"] = stDetectedObjects.channelId;
    // msgData["rcuId"] = std::string(reinterpret_cast<char*>(stDetectedObjects.rcuId));
    msgData["deviceType"] = stDetectedObjects.deviceType;
    // msgData["deviceId"] = std::string(reinterpret_cast<char*>(stDetectedObjects.deivceId));
    msgData["timestampOfDevOut"] = Json::Int64(stDetectedObjects.timestampOfDevOut);
    msgData["timestampOfDetIn"] = Json::Int64(stDetectedObjects.timestampOfDetIn);
    msgData["timestampOfDetOut"] = Json::Int64(stDetectedObjects.timestampOfDetOut);
    msgData["gnssType"] = stDetectedObjects.gnssType;
    msgData["objectiveNum"] = stDetectedObjects.objectiveNum;

    Json::Value arrayObjects(Json::arrayValue);
    for(auto it : stDetectedObjects.objective)
    {
        Json::Value Objs;
        // Objs["uuid"] =std::string(reinterpret_cast<char*>(it.uuid));
        Objs["objId"] = it.objId;
        Objs["type"] = it.type;
        Objs["status"] = it.status;
        Objs["len"] = it.len;
        Objs["width"] = it.width;
        Objs["height"] = it.height;
        Objs["longitude"] = it.longitude;
        Objs["latitude"] = it.latitude;
        Objs["locEast"] = it.locEast;
        Objs["locNorth"] = it.locNorth;
        Objs["posConfidence"] = it.posConfidence;
        Objs["elevation"] = it.elevation;
        Objs["elevConfidence"] = it.elevConfidence;
        Objs["speed"] = it.speed;
        Objs["speedConfidence"] = it.speedConfidence;
        Objs["speedEast"] = it.speedEast;
        Objs["speedEastConfidence"] = it.speedEastConfidence;
        Objs["speedNorth"] = it.speedNorth;
        Objs["speedNorthConfidence"] = it.speedNorthCOnfidence;
        Objs["heading"] = it.heading;
        Objs["headConfidence"] = it.headConfidence;
        Objs["accelVert"] = it.acclVert;
        Objs["accelVertConfidence"] = it.accelVertConfidence;
        Objs["trackedTimes"] = it.trackedTimes;
        Objs["histLocNum"] = it.histLocNum;
        Objs["predLocNum"] = it.predLocNum;
        Objs["laneId"] = it.laneId;
        Objs["filterInfoType"] = it.filterInfoType;
        Objs["lenplateNo"] = it.lenplateNo;
        // Objs["plateNo"] = it.plateNo;
        Objs["plateType"] = it.plateType;
        Objs["plateColor"] = it.plateColor;
        Objs["objColor"] = it.objColor;

        arrayObjects.append(Objs);
        
    }
    msgData["objective"] = arrayObjects;
    
// 将 jsonArray 转换为字符串输出
    Json::StreamWriterBuilder builder;
    builder["indentation"] = "";
    *strResult = Json::writeString(builder, msgData);


}

bool CShanghaiTcpDecoder::parseToTsariJson(u64& llCapTime,TRcu2cloudObjs &stDetectedObjects, std::string *strResult)
{
    Json::Value msgData;
    msgData["timestamp"] = Json::Int64(stDetectedObjects.timestampOfDevOut);    
    msgData["timestamp_capture"] =Json::Int64(stDetectedObjects.timestampOfDetIn);
    msgData["timestamp_predicted_out"] =Json::Int64(stDetectedObjects.timestampOfDetOut);
    msgData["timestamp_receive"] = Json::Int64(llCapTime);
    Json::Value arrayObjects(Json::arrayValue);
    for (auto it : stDetectedObjects.objective)
    {
        Json::Value Objs;
        Objs["id"] = std::to_string(it.objId);
        Objs["uid"] = "1";
        Objs["type"] = tfType(it.type);
        Objs["raw_type"] = std::to_string(it.type);
        Objs["center_longitude"] = it.longitude;
        Objs["center_latitude"] = it.latitude;
        Objs["center_elevation"] = it.elevation;
        Objs["center_x"] = it.longitude;
        Objs["center_y"] = it.latitude;
        Objs["center_z"] = it.elevation;
        Objs["center_gk_x"] = 0;
        Objs["center_gk_y"] = 0;
        Objs["length"] = it.len / 100;
        Objs["width"] = it.width / 100;
        Objs["height"] = it.height / 100;
        Objs["speed"] = it.speed;
        Objs["heading"] = it.heading;
        arrayObjects.append(Objs);
    }
    msgData["data_object"] = arrayObjects;

    // 将 jsonArray 转换为字符串输出
    Json::StreamWriterBuilder builder;
    builder["indentation"] = "";
    *strResult = Json::writeString(builder, msgData);
}

int CShanghaiTcpDecoder::tfType(int type)  // 用于转换目标类型
{
    switch (type)
    {
    case obj_type_passenger_cars:
    case obj_type_special_purpose_vehicles:
    case obj_type_public_buses:
    case obj_type_have_rail_cars:
    case obj_type_trucks:
    case 11:
    case 12:
        return tsari_obj_type_vehicle;
    case obj_type_pedestrians:
        return tsari_obj_type_person;
    case obj_type_bicycle:
    case obj_type_motorcycles:
    case obj_type_tricycles:
        return tsari_obj_type_non_vehicle;
    case obj_type_roadblocks:
    case obj_type_traffic_Cone:
    case 62:
        return tsari_obj_type_obstacle;
    default:
        return tsari_obj_type_unknow;
    }
}