#ifndef _WANJI_STRUCT_H_
#define _WANJI_STRUCT_H_

#include <string>
#include <vector>

namespace wanji
{

    typedef struct tagVector3f
    {
        float x = 0;
        float y = 0;
        float z = 0;
    } TVector3f;

    typedef struct tagVector2d
    {
        double x = 0;
        double y = 0;
    } TVector2d;

    typedef struct tagVector2f
    {
        float x = 0;
        float y = 0;
    } TVector2f;

    typedef struct tagVector3d
    {
        double x = 0;
        double y = 0;
        double z = 0;
    } TVector3d;

    

    typedef struct tagVector3i
    {
        int64_t x = 0;
        int64_t y = 0;
        int64_t z = 0;
    } TVector3i;

    typedef struct tagPolyline
    {
        std::vector<TVector3d> points;
    } TPolyline;

    typedef struct tagPolygon
    {
        std::vector<TVector2f> points;
    } TPolygon;

    typedef struct tagPoints
    {
        std::vector<TVector3d> points;
    } TPoints;

    typedef struct tagMatrix2d
    {
        double e00 = 0, e01 = 0, e10 = 0, e11 = 0;
    } TMatrix2d;

    typedef struct tagMatrix2f
    {
        float e00 = 0, e01 = 0, e10 = 0, e11 = 0;
    } TMatrix2f;

    typedef struct tagMatrix3d
    {
        double e00 = 0, e01 = 0, e02 = 0, e10 = 0, e11 = 0, e12 = 0, e20 = 0, e21 = 0, e22 = 0;
    } TMatrix3d;

    typedef struct tagMatrix3f
    {
        float e00 = 0, e01 = 0, e02 = 0, e10 = 0, e11 = 0, e12 = 0, e20 = 0, e21 = 0, e22 = 0;
    } TMatrix3f;

    typedef struct tagQuaterniond
    {
        double w = 0, x = 0, y = 0, z = 0;
    } TQuaterniond;

    typedef struct tagQuaternionf
    {
        float w = 0, x = 0, y = 0, z = 0;
    } TQuaternionf;

    typedef struct tagTransformation3d
    {
        TQuaterniond rotation;
        TVector3d translation;
    } TTransformation3d;

    typedef struct tagTransformation3f
    {
        TQuaternionf rotation;
        TVector3f translation;
    } TTransformation3f;

    typedef struct tagRosPassThrough
    {
        int64_t seq = 0; // increasing number for receiver to check data loss
        int64_t time = 0;
        std::vector<uint8_t> data; // In C++, 'bytes' is typically represented as a vector of uint8_t
        std::string source_id;
        std::string ros_topic_name;
        std::string source_type;
    } TRosPassThrough;

    enum class ObjectType : int32_t
    {
        UNKNOWN = 0,
        CAR,
        PEDESTRIAN,
        CYCLIST,
        TRUCK,
        VAN,
        BUS,
        STATIC,
        STATIC_EDGE,
        CONE,
        TROLLEY,
        ROBOT,
        GATE
    };

    typedef struct tagColor
    {
        int32_t r = 0;
        int32_t g = 0;
        int32_t b = 0;
        float a = 0;
    } TColor;

    typedef struct tagVelocity
    {
        float heading = 0;
        float speed = 0;
        float acceleration = 0;
    } TVelocity;

    typedef struct tagWayPoint
    {
        int64_t time_meas = 0;
        TVector3f position;
        TVelocity velocity;
    } TWayPoint;

    typedef struct tagWayPoints
    {
        std::vector<TWayPoint> waypoints;
        float probability = 0;
    } TWayPoints;

    typedef struct tagLicenePlate
    { // 注意：这里可能是LicensePlate的拼写错误
        int32_t color = 0;
        float color_confidence = 0;
        std::string number;
        float number_confidence = 0;
    } TLicenePlate;

    typedef struct tagDetectedObject
    {
        std::string overlap_name; // 使用std::vector<uint8_t>表示bytes
        uint64_t uuid = 0;        // 注意：fixed64在C++中通常对应uint64_t，但具体取决于你的系统架构
        int32_t type = 0;
        float confidence = 0;
        TVector3f position;
        TVector3f shape;
        TPolygon hull;
        float orientation = 0;
        TVelocity velocity;
        bool is_static = 0;
        TColor color;
        std::vector<float> feature;
        std::vector<TWayPoints> trajectories;
        std::vector<std::string> str_array; // 使用std::vector<std::vector<uint8_t>>表示bytes的数组
        std::vector<int32_t> int_array;
        int64_t parking_time = 0;
        TLicenePlate plate;
        int32_t obj_color = 0; // 注意：这个字段可能与Color字段重复，具体取决于你的设计意图
    } TDetectedObject;

    typedef struct tagDetectedObjects
    {
        int64_t time_meas = 0; // 注意：sfixed64在C++中可能需要特殊处理，但这里我们简单使用int64_t
        int64_t time_pub = 0;
        std::vector<uint8_t> group_name;
        std::vector<uint8_t> group_info;
        std::vector<TDetectedObject> objects;
    } TDetectedObjects;

    
};

#endif