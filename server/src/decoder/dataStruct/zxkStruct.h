#ifndef _ZXK_STRUCT_H_
#define _ZXK_STRUCT_H_

#include <string>
#include <vector>

namespace zxk
{

    // RosPassThrough message
    typedef struct tagRosPassThrough
    {
        int64_t seq = 0;
        int64_t time = 0;
        std::vector<uint8_t> data;
        std::string source_id;
        std::string ros_topic_name;
        std::string source_type; // cloud, vehicle, rsu
    }TRosPassThrough;

    // Vector3f message
    typedef struct tagVector3f
    {
        float x = 0;
        float y = 0;
        float z = 0;
    } TVector3f;

    // Vector2f message
    typedef struct tagVector2f
    {
        float x = 0;
        float y = 0;
    } TVector2f;

    // Polygon message
    typedef struct tagPolygon
    {
        std::vector<TVector2f> points;
    } TPolygon;

    // Velocity message
    typedef struct tagVelocity
    {
        float heading = 0;
        float speed = 0;
        float acceleration = 0;
    } TVelocity;

    // Color message
    typedef struct tagColor
    {
        int32_t r = 0;
        int32_t g = 0;
        int32_t b = 0;
        float a = 0;
    } TColor;

    // WayPoint message
    typedef struct tagWayPoint
    {
        int64_t time_meas = 0;  // in ms
        TVector3f position; // in map frame, in meter
        TVelocity velocity; // in map frame
    } TWayPoint;

    // WayPoints message
    typedef struct tagWayPoints
    {
        std::vector<TWayPoint> waypoints;
        float probability = 0;
    } TWayPoints;

    // Quaternionf message
    typedef struct tagQuaternionf
    {
        float x = 0;
        float y = 0;
        float z = 0;
        float w = 0;
    } TQuaternionf;

    // LidarPose message
    typedef struct tagLidarPose
    {
        std::string name;
        TVector3f position;
        TQuaternionf orientation;
    } TLidarPose;

    // LicenePlate message
    typedef struct tagLicenePlate
    {
        int32_t color = 0;
        float color_confidence = 0;
        std::string number;
        float number_confidence = 0;
    } TLicenePlate;

    // DetectedObject message
    typedef struct tagDetectedObject
    {
        std::string overlap_name;
        int64_t uuid = 0;
        int32_t type = 0;
        float confidence = 0;
        TVector3f position;
        TVector3f shape;
        TPolygon hull;
        float orientation = 0;
        TVelocity velocity;
        bool is_static = 0;
        TColor color;
        std::vector<float> feature;
        std::vector<TWayPoints> trajectories;
        std::vector<std::string> str_array;
        std::vector<int32_t> int_array;
        int64_t parking_time = 0;
        TLicenePlate plate;
        int32_t obj_color = 0;
        enum TrafficEvent
        {
            NORMAL = 0,
            OVERSPEED = 1,
            SLOWSPEED = 2,
            CONTRAFLOW = 3,
            ILLEGAL_PARKING = 4,
            VRU_WARNING = 5,
            Throwing_object = 6,
            Traffic_event = 7,
            Construction_area = 8
        } event;
    } TDetectedObject;

    // DetectedObjects message
    typedef struct tagDetectedObjects
    {
        int64_t time_meas = 0;
        int64_t time_pub = 0;
        std::vector<uint8_t> group_name;
        std::vector<uint8_t> group_info;
        std::vector<TDetectedObject> objects;
        std::vector<TLidarPose> lidar_poses;
    } TDetectedObjects;
} // namespace zxk

#endif