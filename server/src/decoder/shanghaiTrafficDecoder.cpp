#include "shanghaiTrafficDecoder.h"
#include "TrafficFlow_generated.h"
#include "flatbuffers/flatbuffers.h"

#include "json/json.h"

CShanghaiTrafficDecoder::CShanghaiTrafficDecoder()
{

}

CShanghaiTrafficDecoder::~CShanghaiTrafficDecoder()
{

}


bool CShanghaiTrafficDecoder::Init(std::string strRoadId)
{
    m_strRoadId = strRoadId;
    return true;
}

/*ros_passthrough解析原始 data使用esurfing_group_detectred*/
bool CShanghaiTrafficDecoder::DecodeData(u64& llCapTime,std::string strTargets, std::string *strResult)
{
    // std::cout<<"start decoder"<<std::endl;
    Json::Value root;
    const MECData::MSG_TrafficFlow * traffic_data = MECData::GetMSG_TrafficFlow(strTargets.data());
    root["node"] = traffic_data->node();
    // std::cout<<"start decoder1"<<std::endl;
    root["gen_time"] = traffic_data->gen_time();
    // root["stat_type"]["interval"] = traffic_data->stat_type_as_DE_TrafficFlowStatByInterval()->interval();
    // root["stat_type"]["sequence"] = traffic_data->stat_type_as_DE_TrafficFlowStatBySignalCycle()->sequence();
    // std::cout<<"start decoder2"<<std::endl;
    for(int i=0;i<traffic_data->stats()->size();i++)
    {
        const MECData::DF_TrafficFlowStat* traffic_stat = traffic_data->stats()->Get(i);
        Json::Value stat;
        // stat["map_element"]["detector_area_stat"]["detector_area_id"] = traffic_stat->map_element_as_DE_DetectorAreaStatInfo()->detector_area_id();
        // stat["map_element"]["lane_stat"]["ext_id"] = traffic_stat->map_element_as_DE_LaneStatInfo()->ext_id();
        // stat["map_element"]["lane_stat"]["direction"] = traffic_stat->map_element_as_DE_LinkStatInfo()->direction();
        // stat["map_element"]["section_stat"]["ext_id"] = traffic_stat->map_element_as_DE_SectionStatInfo()->ext_id();
        // stat["map_element"]["link_stat"]["ext_id"] = traffic_stat->map_element_as_DE_LinkStatInfo()->ext_id();
        // stat["map_element"]["link_stat"]["direction"] = traffic_stat->map_element_as_DE_LinkStatInfo()->direction();
        // stat["map_element"]["connecting_lane_stat"]["ext_id"] = traffic_stat->map_element_as_DE_ConnectingLaneStatInfo()->ext_id();
        // stat["map_element"]["connecting_stat"]["ext_id"] = traffic_stat->map_element_as_DE_ConnectionStatInfo()->ext_id();
        // stat["map_element"]["movement_stat"]["ext_id"] = traffic_stat->map_element_as_DE_MovementStatInfo()->ext_id();
        // stat["map_element"]["movement_stat"]["from_direction"] =traffic_stat->map_element_as_DE_MovementStatInfo()->from_direction();
        // stat["map_element"]["movement_stat"]["maneuver"] = traffic_stat->map_element_as_DE_MovementStatInfo()->maneuver();
        // stat["map_element"]["node_stat"]["id"] = traffic_stat->map_element_as_DE_NodeStatInfo()->id();
        
        stat["ptc_type"] = traffic_stat->ptc_type();
        stat["veh_type"] = traffic_stat->veh_type();
        stat["volume"] = traffic_stat->volume();
        stat["speed_point"] =traffic_stat->speed_point();
        stat["speed_area"] = traffic_stat->speed_area();
        stat["density"] = traffic_stat->density();
        stat["delay"] =traffic_stat->delay();
        stat["queue_length"]  = traffic_stat->queue_length();
        stat["congestion"] = traffic_stat->congestion();
        stat["occupation"] = traffic_stat->occupation();
        stat["time_headway"] = traffic_stat->time_headway();
        stat["space_headway"] = traffic_stat->space_headway();
        stat["travel_time"] = traffic_stat->travel_time();
        stat["green_utilization"] = traffic_stat->green_utilization();
        stat["saturation"] = traffic_stat->saturation();
        stat["stops"] = traffic_stat->stops();
        stat["queued_vehicles"] = traffic_stat->queued_vehicles();
        stat["time_occupation"] = traffic_stat->time_occupation();
        root["stats"].append(stat);
        
    }
    Json::StreamWriterBuilder write_builder;
    write_builder["indentation"] = "";
    *strResult = Json::writeString(write_builder, root);
    return true;
}
