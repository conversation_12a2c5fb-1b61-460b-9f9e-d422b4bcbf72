#include "zjjtDecoder.h"
#include "json/json.h"

CZjjtDecoder::CZjjtDecoder()
{
}
CZjjtDecoder::~CZjjtDecoder()
{
}
bool CZjjtDecoder::Init(std::string strRoadId)
{
    m_strRoadId = strRoadId;
    return true;
}
bool CZjjtDecoder::DecodeData(u64& llCapTime,std::string strTargets, std::string *strResult)
{
    //判断deviceId和Road是否一致
    
    Json::Value root;
    Json::Reader reader;

    bool parsingRet = reader.parse(strTargets,root);

    if (parsingRet) {
        std::string deviceId = root["deviceId"].asString();
        if (deviceId != m_strRoadId) {
            *strResult = "";
            return true;
        }
        *strResult  = strTargets;
    }else {
        ERROR("decode json failed!");
        return false;
    }
    return true;
}