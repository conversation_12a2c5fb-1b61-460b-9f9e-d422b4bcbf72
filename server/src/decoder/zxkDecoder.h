#ifndef _ZXK_DECODER_H_
#define _ZXK_DECODER_H_

#ifdef SUPPORT_ZXK
#include "decoderBase.h"
#include "dataStruct/zxkStruct.h"



class CZxkDecoder :public CDecoderBase {
    public:
    CZxkDecoder();
    ~CZxkDecoder();
    bool Init(std::string strRoadId) override;
    bool DecodeData(u64& llCapTime,std::string strTargets,std::string *strResult) override;
    
    protected:
    bool parseToJson(zxk::TRosPassThrough &stRosPassThrough,zxk::TDetectedObjects &stDetectedObject,std::string *strResult);
};
#endif
#endif