cmake_minimum_required(VERSION 3.0.2)

aux_source_directory(. DIR_SRC)

if(FACTORY_WANJI)
    aux_source_directory(./proto/wanji PROTO_SRC)
    set(COMPILE_OPTION -DSUPPORT_WANJI) 
    message("-- Support Factory [ WanJi ]")
endif()

if(FACTORY_ZXK)
    aux_source_directory(./proto/zxk PROTO_SRC)
    set(COMPILE_OPTION -DSUPPORT_ZXK) 
    message("-- Support Factory [ ZXK ]")
endif()

if(FACTORY_SHANGHAIFENGXIAN)
    aux_source_directory(./proto/shanghai PROTO_SRC)
    include_directories(./proto/shanghai)
    set(COMPILE_OPTION -DSUPPORT_SHFX) 
    message("-- Support Factory [ ShangHai Fengxian ]")
endif()

if(FACTORY_SHANGHAIFENGXIAN_TCP)
    aux_source_directory(./proto/shanghaiTcp PROTO_SRC)
    include_directories(./proto/shanghaiTcp)
    set(COMPILE_OPTION -DSUPPORT_SHFX_TCP) 
    message("-- Support Factory [ ShangHai Fengxian tcp ]")
endif()

aux_source_directory(./dataStruct STRUCT_SRC)
add_library(decoder SHARED ${DIR_SRC} ${PROTO_SRC} ${STRUCT_SRC})


target_compile_options(decoder PUBLIC ${COMPILE_OPTION})  

target_link_libraries(decoder jsoncpp)

add_custom_command(TARGET decoder POST_BUILD
 COMMAND
 mv libdecoder.so libdecoder.so.${PROJECT_VERSION}
 COMMAND
 ln -s libdecoder.so.${PROJECT_VERSION} libdecoder.so
 WORKING_DIRECTORY ${LIBRARY_OUTPUT_PATH})
