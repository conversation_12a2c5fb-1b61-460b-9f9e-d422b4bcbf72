#ifndef _SHANGHAI_TRAFFIC_DECODER_H_
#define _SHANGHAI_TRAFFIC_DECODER_H_
#include "decoderBase.h"
#include "logger.hpp"

class CShanghaiTrafficDecoder :public CDecoderBase {
    public:
    CShanghaiTrafficDecoder();
    ~CShanghaiTrafficDecoder();
    virtual bool Init(std::string strRoadId) override;
    virtual bool DecodeData(u64& llCapTime,std::string strTargets,std::string *strResult) override;
};
#endif