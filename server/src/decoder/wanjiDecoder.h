#ifndef _WANJI_DECODER_H_
#define _WANJI_DECODER_H_

#ifdef SUPPORT_WANJI
#include "decoderBase.h"
#include "dataStruct/wanjiStruct.h"


class CWanjiDecoder :public CDecoderBase{

public:
    CWanjiDecoder();
    ~CWanjiDecoder();
    bool Init(std::string strRoadID) override;
    bool DecodeData(u64& llCapTime,std::string strTargets,std::string *strResult) override;

protected:
    bool parseToJson(wanji::TRosPassThrough &stRosPassThrough,wanji::TDetectedObjects &stDetectedObject,std::string *strResult);


};
#endif
#endif