#ifdef SUPPORT_WANJI
#include "wanjiDecoder.h"
#include "proto/wanji/group_detected.pb.h"
#include "proto/wanji/geo.pb.h"
#include "proto/wanji/ros_passthrough.pb.h"
#include "json/json.h"


CWanjiDecoder::CWanjiDecoder()
{

}

CWanjiDecoder::~CWanjiDecoder()
{

}

bool CWanjiDecoder::Init(std::string strRoadID)
{
    m_strRoadId = strRoadID;
    return true;
}

bool CWanjiDecoder::DecodeData(u64& llCapTime,std::string strTargets, std::string *strResult)
{
    esurfing::proto::infra::rsuconnect::RosPassThrough protoMsg;
    bool ret = protoMsg.ParseFromString(strTargets);
    if (!ret)
    {
        printf("protoMsg.ParseFromString(strTargets) failed\n");
        return false;
    }

    //赋值
    wanji::TRosPassThrough stRosPassThrough;
    stRosPassThrough.seq = protoMsg.seq();
    stRosPassThrough.time = protoMsg.time();
    stRosPassThrough.source_id = protoMsg.source_id();
    stRosPassThrough.ros_topic_name = protoMsg.ros_topic_name();
    stRosPassThrough.source_type = protoMsg.source_type();

    esurfing::proto::perc::DetectedObjects protoMsgData;
    ret = protoMsgData.ParseFromString(protoMsg.data());
     if (!ret ) 
    {
        printf("protoMsgData.ParseFromString(protoMsg.data()) failed \n");
        return false;
    }

    //赋值
    wanji::TDetectedObjects stDetectedObjects;
    stDetectedObjects.time_meas = protoMsgData.time_meas();
    stDetectedObjects.time_pub = protoMsgData.time_pub();
    for(auto it:protoMsgData.group_name())
    {
        stDetectedObjects.group_name.push_back(it);
    }
    for(auto it:protoMsgData.group_info())
    {
        stDetectedObjects.group_info.push_back(it);
    }
    for(auto it:protoMsgData.objects())
    {
        wanji::TDetectedObject stDetectedObj;
        stDetectedObj.overlap_name = it.overlap_name();
        stDetectedObj.uuid = it.uuid();
        stDetectedObj.type = it.type();
        stDetectedObj.confidence = it.confidence();
        stDetectedObj.position.x = it.position().x();
        stDetectedObj.position.y = it.position().y();
        stDetectedObj.position.z = it.position().z();
        stDetectedObj.shape.x = it.shape().x();
        stDetectedObj.shape.y = it.shape().y();
        stDetectedObj.shape.z = it.shape().z();
        for (auto iter : it.hull().points())
        {
            wanji::TVector2f stVector2f;
            stVector2f.x = iter.x();
            stVector2f.y = iter.y();
            stDetectedObj.hull.points.push_back(stVector2f);
        }
        stDetectedObj.orientation = it.orientation();
        stDetectedObj.velocity.acceleration = it.velocity().acceleration();
        stDetectedObj.velocity.heading = it.velocity().heading();
        stDetectedObj.velocity.speed = it.velocity().speed();
        stDetectedObj.is_static = it.is_static();
        stDetectedObj.color.a = it.color().a();
        stDetectedObj.color.b = it.color().b();
        stDetectedObj.color.g = it.color().g();
        stDetectedObj.color.r = it.color().r();
        for(auto iter:it.feature())
        {
            stDetectedObj.feature.push_back(iter);
        }
        for(auto iter:it.trajectories())
        {
            wanji::TWayPoints stWayPoints;
            for(auto wayPoints : iter.waypoints())
            {
                wanji::TWayPoint stWayPoint;
                stWayPoint.time_meas = wayPoints.time_meas();
                stWayPoint.position.x = wayPoints.position().x();
                stWayPoint.position.y = wayPoints.position().y();
                stWayPoint.position.z = wayPoints.position().z();
                stWayPoint.velocity.acceleration = wayPoints.velocity().acceleration();
                stWayPoint.velocity.heading = wayPoints.velocity().heading();
                stWayPoint.velocity.speed = wayPoints.velocity().speed();
                stWayPoints.waypoints.push_back(stWayPoint);
            }
            stWayPoints.probability = iter.probability();
            stDetectedObj.trajectories.push_back(stWayPoints);
        }
        for(auto iter:it.str_array())
        {
            stDetectedObj.str_array.push_back(iter);
        }
        for(auto iter:it.int_array())
        {
            stDetectedObj.int_array.push_back(iter);
        }
        stDetectedObj.parking_time = it.parking_time();
        stDetectedObj.plate.color = it.plate().color();
        stDetectedObj.plate.color_confidence = it.plate().color_confidence();
        stDetectedObj.plate.number = it.plate().number();
        stDetectedObj.plate.number_confidence = it.plate().number_confidence();
        stDetectedObj.obj_color = it.obj_color();
        // stDetectedObj.event=static_cast<zxk::TDetectedObject::TrafficEvent>(static_cast<int>(it.event()));
        
        stDetectedObjects.objects.push_back(stDetectedObj);
    }
    ret = parseToJson(stRosPassThrough,stDetectedObjects,strResult);
    if (!ret)
    {
        printf("decode failed!\n");
        return false;
    }

    return true;

}

bool CWanjiDecoder::parseToJson(wanji::TRosPassThrough &stRosPassThrough, wanji::TDetectedObjects &stDetectedObjects, std::string *strResult)
{
    Json::Value RosPassThrough;
    RosPassThrough["seq"] = Json::Int64(stRosPassThrough.seq);
    RosPassThrough["time"] = Json::Int64(stRosPassThrough.time);
    RosPassThrough["source_id"] = stRosPassThrough.source_id;
    RosPassThrough["ros_topic_name"] = stRosPassThrough.ros_topic_name;
    RosPassThrough["source_type"] = stRosPassThrough.source_type;

    Json::Value DetectedObject;
    DetectedObject["time_meas"] = Json::Int64(stDetectedObjects.time_meas);
    DetectedObject["time_pub"] = Json::Int64(stDetectedObjects.time_pub);
    std::string strGroupName(stDetectedObjects.group_name.begin(), stDetectedObjects.group_name.end());
    std::string strGroupInfo(stDetectedObjects.group_info.begin(), stDetectedObjects.group_info.end());
    DetectedObject["group_name"] = strGroupName;
    DetectedObject["group_info"] = strGroupInfo;

    Json::Value arrayObjects(Json::arrayValue);
    for (auto it : stDetectedObjects.objects)
    {
        Json::Value object;
        object["overlap_name"] = it.overlap_name;
        object["uuid"] = Json::Int64(it.uuid);
        object["type"] = Json::Int64(it.type);
        object["confidence"] = it.confidence;
        object["position"]["x"] = it.position.x;
        object["position"]["y"] = it.position.y;
        object["position"]["z"] = it.position.z;
        object["shape"]["x"] = it.shape.x;
        object["shape"]["y"] = it.shape.y;
        object["shape"]["z"] = it.shape.z;

        Json::Value points(Json::arrayValue);
        for (auto iter : it.hull.points)
        {
            Json::Value vector2f;
            vector2f["x"] = iter.x;
            vector2f["y"] = iter.y;
            points.append(vector2f);
        }
        object["hull"]["points"] = points;

        object["orientation"] = it.orientation;
        object["velocity"]["heading"] = it.velocity.heading;
        object["velocity"]["speed"] = it.velocity.speed;
        object["velocity"]["acceleration"] = it.velocity.acceleration;
        object["is_static"] = it.is_static;
        object["color"]["a"] = it.color.a;
        object["color"]["r"] = it.color.r;
        object["color"]["g"] = it.color.g;
        object["color"]["b"] = it.color.b;

        Json::Value featureArray(Json::arrayValue);
        for (auto iter : it.feature)
        {
            featureArray.append(iter);
        }
        object["feature"] = featureArray;

        Json::Value trajectoriesArray(Json::arrayValue);
        for (auto iter : it.trajectories)
        {
            Json::Value waypoint;
            waypoint["probability"] = iter.probability;

            Json::Value waypoints(Json::arrayValue);
            for (auto waypointIt : iter.waypoints)
            {
                Json::Value pointJson;
                pointJson["time_meas"] = Json::Int64(waypointIt.time_meas);
                pointJson["position"]["x"] = waypointIt.position.x;
                pointJson["position"]["y"] = waypointIt.position.y;
                pointJson["position"]["z"] = waypointIt.position.z;
                pointJson["velocity"]["heading"] = waypointIt.velocity.heading;
                pointJson["velocity"]["speed"] = waypointIt.velocity.speed;
                pointJson["velocity"]["acceleration"] = waypointIt.velocity.acceleration;
                waypoints.append(pointJson);
            }
            waypoint["waypoints"] = waypoints;

            trajectoriesArray.append(waypoint);
        }
        object["trajectories"] = trajectoriesArray;

        Json::Value str_array(Json::arrayValue);
        for (auto iter : it.str_array)
        {
            str_array.append(iter);
        }
        object["str_array"] = str_array;

        Json::Value int_array(Json::arrayValue);
        for (auto iter : it.int_array)
        {
            int_array.append(iter);
        }
        object["int_array"] = int_array;

        object["parking_time"] = Json::Int64(it.parking_time);
        object["plate"]["color"] = it.plate.color;
        object["plate"]["color_confidence"] = it.plate.color_confidence;
        object["plate"]["number"] = it.plate.number;
        object["plate"]["number_confidence"] = it.plate.number_confidence;
        object["obj_color"] = it.obj_color;
        // object["event"] = static_cast<int>(it.event);

        arrayObjects.append(object);
    }
    DetectedObject["objects"] = arrayObjects;
    
    RosPassThrough["data"] = DetectedObject;

    // 将 jsonArray 转换为字符串输出
    Json::StreamWriterBuilder builder;
    builder["indentation"] = "";
    *strResult = Json::writeString(builder, RosPassThrough);

    return true;
}
#endif