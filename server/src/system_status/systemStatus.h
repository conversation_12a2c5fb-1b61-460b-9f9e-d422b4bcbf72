#ifndef _SYSTEM_STATUS_H_
#define _SYSTEM_STATUS_H_

#include "moduleBase.h"
#include "message.h"
#include "struct.h"

extern std::string gStrStorePath;
volatile extern int gPort;

class CSystemStatus : public CModuleBase
{
    public:
    CSystemStatus(CMediator *pMediator);
    ~CSystemStatus();

    void Init() override;
    void Start() override;
    void Stop() override;
    void Pause() override;

    bool MsgFilter(u32 msgType);
    void HandleMsg(u32 msgLevel,u32 deviceId,u32 msgType,std::shared_ptr<void> spData);

    private:
    void sendStatus();
    void sendSystemInfo();
    void sendNetConfig();
    void sendStoreConfig();
    void sendPortConfig();
    void hanleSetStorePath(u32 msgLevel,u32 deviceId,std::shared_ptr<void> spData);
    void hanleSystemCmd(u32 msgLevel,u32 deviceId,std::shared_ptr<void> spData);

    private:
    TSystemStatus::enumStatus m_dwSystemStatus;
};


#endif