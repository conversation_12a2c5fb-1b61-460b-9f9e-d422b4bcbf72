#include "systemStatus.h"
#include "commonFunction.h"
#include "struct.h"
#include <cstdio>
#include <cstdlib> 

CSystemStatus::CSystemStatus(CMediator *pMediator)
    :CModuleBase(pMediator)
{
    m_dwSystemStatus =TSystemStatus::STOP;
}

CSystemStatus::~CSystemStatus()
{
}

void CSystemStatus::Init()
{
}

void CSystemStatus::Start()
{
    m_dwSystemStatus = TSystemStatus::STATR;
    sendStatus();
}

void CSystemStatus::Stop()
{
    m_dwSystemStatus = TSystemStatus::STOP;
    sendStatus();
}

void CSystemStatus::Pause()
{
    m_dwSystemStatus = TSystemStatus::PAUSE;
    sendStatus();
}

bool CSystemStatus::MsgFilter(u32 msgType)
{
    bool ret = true;
    if (msgType == SYSTEM_INIT ||
        msgType == SYSTEM_START ||
        msgType == SYSTEM_STOP ||
        msgType == SYSTEM_PAUSE ||
        msgType == SYSTEM_CLIENT_CONNECTED ||
        msgType == SYSTEM_INFO_REQ ||
        msgType == SYSTEM_CMD_REQ ||
        msgType == SYSTEM_STORE_SET_REQ) 
    {
        ret = false;
    }
    return ret;
}

void CSystemStatus::HandleMsg(u32 msgLevel,u32 deviceId,u32 msgType, std::shared_ptr<void> spData)
{
    switch (msgType)
    {
    case SYSTEM_INIT:
        Init();
        break;
    case SYSTEM_START:
        Start();
        break;
    case SYSTEM_STOP:
        Stop();
        break;
    case SYSTEM_PAUSE:
        Pause();
        break;
    case SYSTEM_CLIENT_CONNECTED:
        usleep(50 * 1000);
        sendStatus();
        usleep(50 * 1000);
        sendNetConfig();
        usleep(50 * 1000);
        sendStoreConfig();
        usleep(50 * 1000);
        sendPortConfig();
        break;
    case SYSTEM_INFO_REQ:
        sendSystemInfo();
        break;
    case SYSTEM_CMD_REQ:
        hanleSystemCmd(msgLevel,deviceId,spData);
        break;
    case SYSTEM_STORE_SET_REQ:
        hanleSetStorePath(msgLevel,deviceId,spData);
        break;
    default:
        break;
    } 
}

void CSystemStatus::sendStatus()
{
    std::shared_ptr<TSystemStatus> spData = std::make_shared<TSystemStatus>();

    spData->llTimstamp = commonFunc::GetMsTime();
    spData->emStatus = m_dwSystemStatus;

    Notify(MSG_LEVEL_ALL_DEVICE,NO_DEVICE,SYSTEM_UPDATE_STATUS,std::static_pointer_cast<void>(spData));
}

void CSystemStatus::sendSystemInfo()
{
    std::shared_ptr<TSystemInfo> spData = std::make_shared<TSystemInfo>();
     
    spData->fCpuCoreNum = commonFunc::getCPUCores();
    spData->fCpuUsed = roundf(commonFunc::getCPUUsage() * 100) / 100;
    spData->fCpuTemp =roundf(commonFunc::getTemperature() * 100) / 100;
    spData->fMemoryTotal = roundf(commonFunc::getMemoryTotal() * 100) / 100;
    spData->fMemoryUsed = roundf(commonFunc::getMemoryUsage() * 100) / 100;
    spData->fDiskUsed = roundf(commonFunc::getDiskUsage(gStrStorePath.c_str()) * 100) / 100;

    // std::cout << "CPU 核心数：" << spData->fCpuCoreNum << std::endl;
    // std::cout << "CPU 使用率：" << spData->fCpuUsed << "%" << std::endl;
    // std::cout << "CPU 温度：" << spData->fCpuTemp << "°C" << std::endl;
    // std::cout << "内存总容量：" << spData->fMemoryTotal << " MB" << std::endl;
    // std::cout << "内存使用率：" << spData->fMemoryUsed << "%" << std::endl;
    // std::cout << "磁盘总容量：" << commonFunc::getDiskTotal("/") << " MB"
    //           << std::endl; // 获取根目录磁盘总容量
    // std::cout << "磁盘使用率：" << spData->fDiskUsed << "%"
    //           << std::endl; // 获取根目录磁盘使用率

     Notify(MSG_LEVEL_ALL_DEVICE,NO_DEVICE,SYSTEM_INFO_RES,std::static_pointer_cast<void>(spData));
}

void CSystemStatus::sendNetConfig()
{
    //复用ntp服务
}

void CSystemStatus::sendStoreConfig()
{
    std::shared_ptr<TStoreInfo> spData = std::make_shared<TStoreInfo>();
    spData->strStorePath = gStrStorePath;
    Notify(MSG_LEVEL_ALL_DEVICE,NO_DEVICE,SYSTEM_STORE_SET_RES,std::static_pointer_cast<void>(spData));
}

void CSystemStatus::sendPortConfig()
{
    std::shared_ptr<TSystemControl> spData = std::make_shared<TSystemControl>();
    spData->dwPort = gPort;
    spData->type = TSystemControl::NOCMD;
    Notify(MSG_LEVEL_ALL_DEVICE,NO_DEVICE,SYSTEM_CMD_RES,std::static_pointer_cast<void>(spData));

}
void CSystemStatus::hanleSetStorePath(u32 msgLevel,u32 deviceId,std::shared_ptr<void> spData)
{
    std::shared_ptr<TStoreInfo> spStoreInfo = std::static_pointer_cast<TStoreInfo>(spData);
    if(spStoreInfo->strStorePath != "")
    {
        gStrStorePath = spStoreInfo->strStorePath;
        if (gStrStorePath.back() != '/')
        {
            gStrStorePath += "/";
        }
    }
    Notify(MSG_LEVEL_ALL_DEVICE,NO_DEVICE,SYSTEM_UPDATE_STORE_PATH,nullptr);
    sendStoreConfig();
}

void CSystemStatus::hanleSystemCmd(u32 msgLevel,u32 deviceId,std::shared_ptr<void> spData)
{
    std::shared_ptr<TSystemControl> spSysCmd = std::static_pointer_cast<TSystemControl>(spData);
    switch (spSysCmd->type)
    {
    case TSystemControl::REBOOT:
        system("sudo reboot");
        break;
    case TSystemControl::RECONNECTSERVER:
        /* code */
        break;
    case TSystemControl::RESTARTSERVER:
        system("sudo systemctl restart data_capture_server.service");
        break;
    default:
        break;
    }
}
