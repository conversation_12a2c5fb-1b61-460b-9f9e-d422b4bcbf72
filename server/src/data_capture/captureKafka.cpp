#include "captureKafka.h"
#include "commonFunction.h"
#include <iostream>
#include "data_capture_manager.h"

CCaptureKafka::CCaptureKafka(CDataCaptureManager& manager)
    :CCaptureBase(manager)
{
    m_pConfig = nullptr;
    m_pConsumer = nullptr;
    m_pDispatcher = nullptr;
    m_dwMsgCnt = 0;
}

CCaptureKafka::~CCaptureKafka()
{
    if(m_pConfig)
    {
        delete m_pConfig;
        m_pConfig = nullptr;
    }

    if(m_pConsumer)
    {
        delete m_pConsumer;
        m_pConsumer = nullptr;
    }

    if(m_pDispatcher)
    {
        delete m_pDispatcher;
        m_pDispatcher = nullptr;
    }
}


void CCaptureKafka::Start()
{
    CCaptureBase::Start();
    try
    {
        if (nullptr ==m_pConfig )
        {
            // 设置 Kafka 配置
            m_pConfig = new cppkafka::Configuration();
            m_pConfig->set(m_vecConfig);
        }

        if (nullptr == m_pConsumer)
        {
            // 创建 Consumer 对象并订阅主题
            m_pConsumer = new cppkafka::Consumer(*m_pConfig);
            m_pConsumer->subscribe(m_vecTopic);

        }

        if (nullptr == m_pDispatcher)
        {
            // 创建 ConsumerDispatcher 对象
            m_pDispatcher = new cppkafka::ConsumerDispatcher(*m_pConsumer);
        }
        
        //开始
        m_runThread = std::thread(&CCaptureKafka::captureRun,this);
        
    }
    catch (const std::exception &e)
    {
        std::cerr << e.what() << '\n';
    }

}

void CCaptureKafka::captureRun()
{
    if(!m_bIsRunning)
    {
        return;
    }
    try
    {
        auto cb = [this](const cppkafka::Message msg)
        {
            this->onMessageReceived(msg);
        };
        // 设置回调函数
        m_pDispatcher->run(cb);
    }
    catch (const std::exception &e)
    {
        std::cerr << e.what() << '\n';
    }
}

void CCaptureKafka::Stop()
{
    CCaptureBase::Stop();
    if (m_pDispatcher != nullptr)
    {
        m_pDispatcher->stop();
    }
    if (m_runThread.joinable())
    {
        m_runThread.join();
    }

    if (m_pDispatcher != nullptr)
    {
        m_pDispatcher->stop();
        delete m_pDispatcher;
        m_pDispatcher = nullptr;
    }

    if (m_pConsumer != nullptr)
    {
         delete m_pConsumer; 
        m_pConsumer = nullptr;
    }
    std::cout << "kafka stop " << std::endl;
}

void CCaptureKafka::SetConfig(TConnectArgs stArgs)
{
   
    cppkafka::ConfigurationOption address("metadata.broker.list",stArgs.strAddr.c_str());
    cppkafka::ConfigurationOption group("group.id", "my-group");

    m_vecConfig.push_back(address);
    m_vecConfig.push_back(group);

     
    m_vecTopic.push_back(stArgs.strTopic);

    m_strCrossId = stArgs.strRoadId;
}

void CCaptureKafka::SetCallBack(const std::function<void(TRawData stCapData)> &pCb)
{
    if(pCb != NULL)
    {
        m_pCb = pCb;
    }
}

void CCaptureKafka::statusSendThread()
{
    int dwCapCnt = 0;//用来采集状态记数
    u32 recvDataCnt = 0;
    u64 llLastTime = commonFunc::GetMsTime();
    while(m_bIsRunning)
    {
        u64 llNowTime = commonFunc::GetMsTime();
        u64 llDiff = llNowTime - llLastTime;
        if (llDiff >= 60 * 1000)
        {
            m_fFreq = static_cast<float>(m_dwMsgCnt) / 60;
            m_mutex.lock();
            recvDataCnt = m_dwMsgCnt;
            m_dwMsgCnt = 0;
            m_mutex.unlock();
            llLastTime = llNowTime;
        }
        if (m_bIsCaping == false)
        {
            dwCapCnt++;
            if(dwCapCnt <=3)    //三次之后未收到数据才更新
            {
                continue;
            }
        }
        else
        {
            dwCapCnt = 0;
        }
        m_manager.updateStatus(m_strCrossId, m_bIsCaping, m_fFreq,recvDataCnt);
        m_bIsCaping = false;

        sleep(1);
    }
}

void CCaptureKafka::onMessageReceived(const cppkafka::Message &msg)
{
    m_bIsCaping = true;
    switch (gFactoryType)
    {
    case FACTORY_NONE:
    case FACTORY_ZXK:
        onMessageReceivedByUniversal(msg);
        break;
    case FACTORY_ZJJT:
        onMessageReceivedByZJJT(msg);
    default:
        break;
    }
}

void CCaptureKafka::onMessageReceivedByUniversal(const cppkafka::Message &msg) // 通用
{
    m_mutex.lock();
    m_dwMsgCnt++;
    m_mutex.unlock();
    if (m_pCb)
    {
        TRawData stCapData;
        stCapData.llRecvTime = commonFunc::GetMsTime();
        stCapData.strCrossroadId = m_strCrossId;
        stCapData.wDataLength = msg.get_payload().get_size();
        stCapData.strData = msg.get_payload();
        m_pCb(stCapData);
    }
}

void CCaptureKafka::onMessageReceivedByZJJT(const cppkafka::Message &msg) // 浙江交投
{
    std::string kafka_key = msg.get_key();
    if (kafka_key != m_strCrossId)
    {
        return;
    }
    
    m_mutex.lock();
    m_dwMsgCnt++;
    m_mutex.unlock();

    if (m_pCb)
    {
        TRawData stCapData;
        stCapData.llRecvTime = commonFunc::GetMsTime();
        stCapData.strCrossroadId = m_strCrossId;
        stCapData.wDataLength = msg.get_payload().get_size();
        stCapData.strData = msg.get_payload();
        m_pCb(stCapData);
    }
}