#ifndef _CAPTURE_BASE_H_
#define _CAPTURE_BASE_H_

#include "struct.h"
#include <functional>
#include <thread>


class CDataCaptureManager;

volatile extern int gFactoryType;

class CCaptureBase
{
public:
    CCaptureBase(CDataCaptureManager &manager) : m_manager(manager) { m_bIsRunning = false; m_dwMsgCnt = 0;};
    ~CCaptureBase(){};

    virtual void Start();
    virtual void Stop();
    virtual void SetConfig(TConnectArgs stArgs) = 0;
    virtual void SetCallBack(const std::function<void(TRawData stCapData)> &pCb) = 0;
    virtual void statusSendThread();

protected:
    CDataCaptureManager &m_manager;
    bool m_bIsRunning;
    bool m_bIsCaping;
    float m_fFreq;
    u32 m_dwMsgCnt;
    std::thread m_thread;
};


#endif 
