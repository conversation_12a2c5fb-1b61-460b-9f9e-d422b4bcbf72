#include "captureMqttCpp.h"
#include "commonFunction.h"
#include "data_capture_manager.h"
#include "logger.hpp"
#include <regex>
#include <string>

CCaptureMqttCpp::CCaptureMqttCpp(CDataCaptureManager &manager)
    : CCaptureBase(manager) {
  m_fFreq = 0;
  m_dwMsgCnt = 0;
  m_connectType = MQTT_TCP;
  m_bIsConnectedSucceed = false;
}

CCaptureMqttCpp::~CCaptureMqttCpp() {}

void CCaptureMqttCpp::Start() {
  CCaptureBase::Start();

  // 运行

  m_runThread = std::thread([this]() {
    if (this->m_bIsRunning) {
      INFO("type:{} addr:{} port:{} url:{}",m_connectType ,m_straddr, m_strPort, m_strUrl);

      auto setup = [&](auto &client) {
        using packet_id_t =
            typename std::remove_reference_t<decltype(client)>::packet_id_t;
        // Setup client
        if(m_strUserName!="") client.set_user_name(m_strUserName);
        if(m_strPassword!="") client.set_password(m_strPassword);
        client.set_client_id(m_strClientId);
        client.set_clean_session(true);
        client.set_keep_alive_sec(5);

        boost::asio::steady_timer tim_connect_wait_limit(m_ioc);
        boost::asio::steady_timer tim_reconnect_delay(m_ioc);

        std::function<void()> connect;
        std::function<void()> reconnect;

        connect =
            [&]
        {
          std::cout << "async_connect" << std::endl;
          client.async_connect(
              [&](MQTT_NS::error_code ec)
              {
                std::cout << "async_connect callback: " << ec.message() << std::endl;
                if (ec)
                  reconnect();
              });

          std::cout << "tim_connect_wait_limit set" << std::endl;
          tim_connect_wait_limit.expires_after(std::chrono::seconds(3));
          tim_connect_wait_limit.async_wait(
              [&](boost::system::error_code ec)
              {
                std::cout << "tim_connect_wait_limit callback: " << ec.message() << std::endl;
                if (!ec)
                {
                  client.async_force_disconnect(
                      [&](boost::system::error_code ec)
                      {
                        std::cout << "async_force_disconnect callback: " << ec.message() << std::endl;
                      });
                }
              });
        };
        reconnect =
            [&]
        {
          std::cout << "tim_reconnect_delay set" << std::endl;
          tim_reconnect_delay.expires_after(std::chrono::seconds(3));
          tim_reconnect_delay.async_wait(
              [&](boost::system::error_code ec)
              {
                std::cout << "tim_reconnect_delay callback: " << ec.message() << std::endl;
                if (!ec)
                  connect();
              });
        };

        // 设置回调函数
        //  std::function<bool(bool session_present,
        //  MQTT_NS::connect_return_code return_code)> connectCb = [this](bool
        //  session_present, MQTT_NS::connect_return_code return_code)
        //  {
        //      return this->connected(session_present,return_code);
        //  };
        //  client->set_connack_handler([&c]connectCb);

        m_disconnectFunc = [&client, this] {
          client.async_disconnect(
              // [optional] checking async_disconnect completion code
              [this](MQTT_NS::error_code ec) {
                ERROR("{} async_disconnect callback ", ec.message().c_str());
                // std::cout << "[]" << this->m_strClientId << "]
                // async_disconnect callback: " << ec.message() << std::endl;
              });
        };

        //连接后函数
        client.set_connack_handler(
            [&client, this](bool sp,
                            MQTT_NS::connect_return_code connack_return_code) {
              INFO("{} Connack handler called", this->m_strClientId.c_str());
              // printf("[%s] Connack handler called\n",
              //        this->m_strClientId.c_str());
              INFO("{}Connack Return Code:{}", this->m_strClientId.c_str(),
                   MQTT_NS::connect_return_code_to_str(connack_return_code));
              // printf("[%s]Connack Return Code: %s \n",
              // this->m_strClientId.c_str(),
              //        MQTT_NS::connect_return_code_to_str(connack_return_code));
              if (connack_return_code ==
                  MQTT_NS::connect_return_code::accepted) {
                    m_bIsConnectedSucceed = true;
                this->m_pid_sub1 = client.acquire_unique_packet_id();
                client.async_subscribe(
                    m_pid_sub1, this->m_strTopic, MQTT_NS::qos::at_least_once,
                    // [optional] checking async_subscribe completion code
                    [](MQTT_NS::error_code ec) {
                      INFO("async_subscribe callback: {}", ec.message());
                      // std::cout << " async_subscribe callback: " <<
                      // ec.message()
                      //           << std::endl;
                    });
              }else {
                ERROR("connect is error!");
              }
              return true;
            });

        client.set_close_handler([this,&reconnect]() {
          INFO("{} closed ", this->m_strClientId.c_str());
          // printf("[%s] closed \n", this->m_strClientId.c_str());
          // reconnect();
        });

        client.set_error_handler([this,&reconnect](MQTT_NS::error_code ec) {
          printf("[%s] error %s \n", this->m_strClientId.c_str(),
                 ec.message().c_str());
          ERROR("{} error {}", this->m_strClientId.c_str(),
                ec.message().c_str());
          // std::cout << "[" << this->m_strClientId << "] error: " <<
          // ec.message() << std::endl;
          // reconnect();
        });

        //发送消息的响应消息 具体参考mqtt消息类型 PUBACK发布确认  PUBREV发布接收
        // PUBCOMP发布完成
        client.set_puback_handler([this](packet_id_t packet_id) {
          // std::cout << "puback received. packet_id: " << packet_id <<
          // std::endl; disconnect();
          return true;
        });
        client.set_pubrec_handler([](packet_id_t packet_id) {
          // std::cout << "pubrec received. packet_id: " << packet_id <<
          // std::endl;
          return true;
        });
        client.set_pubcomp_handler([&](packet_id_t packet_id) {
          // std::cout << "pubcomp received. packet_id: " << packet_id <<
          // std::endl; disconnect();
          return true;
        });
        //收到消息
        client.set_publish_handler(
            [this](MQTT_NS::optional<packet_id_t> packet_id,
                   MQTT_NS::publish_options pubopts, MQTT_NS::buffer topic_name,
                   MQTT_NS::buffer contents) {
              // std::cout << "publish received."
              //           << " dup: " << pubopts.get_dup()
              //           << " qos: " << pubopts.get_qos()
              //           << " retain: " << pubopts.get_retain() << std::endl;
              // if (packet_id)
              //     std::cout << "packet_id: " << *packet_id << std::endl;
              // std::cout << "topic_name: " << topic_name << std::endl;
              this->handleMsg(std::string(contents));
              // std::cout << "contents: " << contents << std::endl;
              // disconnect();
              return true;
            });

        // Connect
        client.async_connect(
            // [optional] checking underlying layer completion code
            [this](MQTT_NS::error_code ec) {
              INFO("{} connect {}", this->m_strClientId, ec.message());
              // std::cout << "[" << this->m_strClientId
              //           << "] connect : " << ec.message() << std::endl;
            });
        // std::cout << "before m_ioc " << std::endl;
        this->m_ioc.run();
      };

    switch (m_connectType) {
    case MQTT_TCP: {
      auto c = MQTT_NS::make_async_client(m_ioc, m_straddr, m_strPort);
      setup(*c);
    } break;
    case MQTT_WEBSOCKET: {
      auto c =
          MQTT_NS::make_async_client_ws(m_ioc, m_straddr, m_strPort, "/"+m_strUrl);
      setup(*c);
    } break;
    case MQTT_TLS:
    case MQTT_TLS_WEBSOCKET:
      ERROR("mqtt type is unsupport!");
      break;
    default:
      ERROR("Failed to distinguish mqtt connection types ");
      break;
    }

    }
  });
}

void CCaptureMqttCpp::Stop() {
  if (m_bIsConnectedSucceed) {
    m_bIsConnectedSucceed = false;
    m_disconnectFunc();
    m_ioc.stop();
    m_runThread.join();
  }

  // sleep(1);
  CCaptureBase::Stop();
}

void CCaptureMqttCpp::SetConfig(TConnectArgs stArgs) {

  // // 切割ip和port和url
  // // ip:port/url = 第一部分：第二部分/第三部分
  // //  切割第一部分和第二部分
  // std::vector<std::string> parts1 =
  //     commonFunc::splitString(stArgs.strAddr, ':');
  // std::string part1 = parts1[0];
  // std::string part2_full = parts1[1];

  // // 切割第二部分和第三部分
  // std::vector<std::string> parts2 = commonFunc::splitString(part2_full, '/');
  // std::string part2 = parts2[0]; // 取第一个元素作为第二部分
  // parts2.erase(parts2.begin());  // 移除第一个元素
  // std::string part3 = "/";
  // for (const auto &p : parts2) {
  //   part3 += p + "/";
  // }

  std::regex pattern_websocket(
      "^(ws|wss)://([0-9]+\\.[0-9]+\\.[0-9]+\\.[0-9]+):([0-9]{1,5})/(.+)$");
  std::regex pattern_tcp(
      "^(tcp|ssl)://([0-9]+\\.[0-9]+\\.[0-9]+\\.[0-9]+):([0-9]{1,5})");
  std::smatch matches;

  if (std::regex_match(stArgs.strAddr, matches, pattern_websocket)) {
    if (matches[1].str() == "wss") {
      m_connectType = MQTT_TLS_WEBSOCKET;
    } else {
      m_connectType = MQTT_WEBSOCKET;
    }
    m_straddr = matches[2].str();
    m_strPort = matches[3].str();
    m_strUrl = matches[4].str();

    // std::cout << "协议: " << m_connectType << std::endl;
    // std::cout << "IP地址: " << m_straddr << std::endl;
    // std::cout << "端口号: " << m_strPort << std::endl;
    // std::cout << "路径: " << m_strUrl << std::endl;
  } else if (std::regex_match(stArgs.strAddr, matches, pattern_tcp)) {
    if (matches[1].str() == "ssl") {
      m_connectType = MQTT_TLS;
    } else {
      m_connectType = MQTT_TCP;
    }
    m_straddr = matches[2].str();
    m_strPort = matches[3].str();
    m_strUrl = "";
    // std::cout << "协议: " << m_connectType << std::endl;
    // std::cout << "IP地址: " << m_straddr << std::endl;
    // std::cout << "端口号: " << m_strPort << std::endl;
  } else {
    ERROR("config string format error!");
    // std::cout << "字符串不匹配规则。" << std::endl;
    m_connectType = MQTT_UNKNOW;
  }

  m_strCrossId = stArgs.strRoadId;
  m_strUserName = stArgs.strUsername;
  m_strPassword = stArgs.strPassword;
  m_strTopic = stArgs.strTopic;
  m_strClientId = stArgs.strClientId;
}

void CCaptureMqttCpp::statusSendThread() {
  u64 llLastTime = commonFunc::GetMsTime();
  u32 recvDataCnt = 0;
  while (m_bIsRunning) {
    u64 llNowTime = commonFunc::GetMsTime();
    if (llNowTime - llLastTime >= 60 * 1000) {
      m_fFreq = static_cast<float>(m_dwMsgCnt)  / 60;
      m_mutex.lock();
      recvDataCnt = m_dwMsgCnt;
      m_dwMsgCnt = 0;
      m_mutex.unlock();
      llLastTime = llNowTime;
    }
    m_manager.updateStatus(m_strCrossId, m_bIsCaping, m_fFreq,recvDataCnt);
    m_bIsCaping = false;
    sleep(1);
  }
}

void CCaptureMqttCpp::SetCallBack(
    const std::function<void(TRawData stCapData)> &pCb) {
  if (pCb != NULL) {
    m_pCb = pCb;
  }
}

void CCaptureMqttCpp::handleMsg(const std::string msg) {
  if (!m_bIsRunning) {
    return;
  }

  m_bIsCaping = true;
  m_mutex.lock();
  m_dwMsgCnt++;
  m_mutex.unlock();
  if (m_pCb) {
    TRawData stCapData;
    stCapData.llRecvTime = commonFunc::GetMsTime();
    stCapData.strCrossroadId = m_strCrossId;
    stCapData.wDataLength = msg.length();
    stCapData.strData = msg;
    m_pCb(stCapData);
  }
  return;
}
