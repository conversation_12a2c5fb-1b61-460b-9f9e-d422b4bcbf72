#ifndef _CAPTURE_KAFKA_H_
#define _CAPTURE_KAFKA_H_

#include "captureBase.h"
#include "logger.hpp"
#include <cppkafka/cppkafka.h>

class CCaptureKafka : public CCaptureBase
{
public:
    CCaptureKafka(CDataCaptureManager& manager);
    ~CCaptureKafka();
    void Start() override;
    void Stop() override;
    void SetConfig(TConnectArgs stArgs) override;
    void SetCallBack(const std::function<void(TRawData stCapData)> &pCb) override;
    void statusSendThread();

    void onMessageReceived(const cppkafka::Message& msg);
private:
    void captureRun();

    void onMessageReceivedByUniversal(const cppkafka::Message& msg);  //通用
    void onMessageReceivedByZJJT(const cppkafka::Message& msg); //浙江交投

private:
    cppkafka::Configuration *m_pConfig;
    cppkafka::Consumer *m_pConsumer;
    cppkafka::ConsumerDispatcher *m_pDispatcher;
    std::vector<cppkafka::ConfigurationOption> m_vecConfig;
    std::vector<std::string> m_vecTopic;
    std::function<void(TRawData stCapData)> m_pCb;
    std::string m_strCrossId;
    std::mutex m_mutex;
    std::thread m_runThread;
};

#endif