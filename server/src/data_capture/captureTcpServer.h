#ifndef _CAPTURE_TCP_SERVER_CPP_H_
#define _CAPTURE_TCP_SERVER_CPP_H_
#include "captureBase.h"
#include <unistd.h>
#include <sys/socket.h>
#include <netinet/in.h>
#include <sys/types.h>
#include <arpa/inet.h>
#include <thread>
#include <mutex>
#include <semaphore.h>
#include "queue"
#include "map"
#include "string"
#pragma pack (1)
typedef struct tagShanghai{
    unsigned char cHead;
    int dwLength;
    unsigned char cType;
    unsigned char cVersion;
    long long llTimestamp;
    unsigned char cContrl;
}TShanghaiHead;
#pragma pack ()
volatile extern int gIsStoreRaw;
class CCaptureTcpClientInstance
{
public:
    CCaptureTcpClientInstance(std::string strIp,int deviceId);
    ~CCaptureTcpClientInstance();
    bool Start();
    bool Stop();
    void SetSocketFd(int dwFd);
    int GetSocketFd();
    void SendMsg(const std::string &strMsg);
    void SetMsgCallBack(std::function<void(int dwDeviceID,std::string strMsg)> &);
    void SetStatusCallBack(std::function<void(int dwDeviceId, bool bStatus)>
                             &); // bStatus true:connected,false:disconnected;

private:
    void sendThread();
    void recvThread();
    std::string to_hex_string(const TShanghaiHead& data);

private:
    std::vector<std::thread> m_vecThread;
    bool m_bIsRunning;
    std::string m_strIp;
    int dwPort;
    sem_t m_semMsg;
    int m_dwFd;
    std::queue<std::string> m_queueMsg;
    std::function<void(int dwDeviceId, bool bStatus)> m_pStatusCallback;
    std::function<void(int dwDeviceId, std::string strData)> m_pDataCallback;
    int m_dwPackageMaxLength;
    int m_dwDeviceID;
};


class CCaptureTcpServer : public CCaptureBase
{
public:
    CCaptureTcpServer(CDataCaptureManager &manager);
    ~CCaptureTcpServer();

    void Start() override;
    void Stop() override;
    void SetConfig(TConnectArgs stArgs) override;
    void SetCallBack(const std::function<void(TRawData stCapData)> &pCb) override;
    void statusSendThread() override;

private:
    void acceptConnect();
    void msgArrived(int dwDeviceID,std::string msg);
    void monitorClient();
    void clientStatusUpdate( int dwDeviceID,bool bStatus) ;
    void GetClientInfo(int &deviceId, std::string *strIp, int *pPort);

private:
    int m_dwPort;
    int m_Sokcet;
    int m_clientSocket;
    bool m_bIsConnected;
    int m_dwDeviceCnt;
    sem_t m_sem;
    std::queue<int> m_queueDisconnect;
    std::map<int,CCaptureTcpClientInstance*> m_mapTcpInstance;
    std::function<void(TRawData stCapData)> m_pCb;
    std::map<int,std::pair<std::string,int>> m_mapClientInfo;

    std::mutex m_mutex;
    std::string m_strUserName;
    std::string m_strPassword;
    std::string m_straddr;
    std::string m_strClientId;
    std::string m_strTopic;
    std::string m_strCrossId;
    std::string m_strPort;
    std::string m_strUrl;

    std::thread m_monitorThread;
    std::thread m_acceptThread;
 
};



#endif