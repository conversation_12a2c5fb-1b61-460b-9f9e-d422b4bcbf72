#include "captureBase.h"
#include <thread>
#include <unistd.h>
#include <iostream>

void CCaptureBase::Start()
{
    m_bIsRunning = true;
    m_thread = std::thread(&CCaptureBase::statusSendThread,this);
}

void CCaptureBase::Stop()
{
    m_bIsRunning = false;
    if(m_thread.joinable())
    {
        m_thread.join();
    }
}

void CCaptureBase::statusSendThread()
{
    while(m_bIsRunning)
    {
        sleep(1);
    }
}