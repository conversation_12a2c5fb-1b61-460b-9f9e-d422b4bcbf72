#include "captureTcpServer.h"
#include "commonFunction.h"
#include "logger.hpp"
#include <semaphore.h>
#include "data_capture_manager.h"

/****************************CCaptureTcpClientInstance**************************/
CCaptureTcpClientInstance::CCaptureTcpClientInstance(std::string strIp,int deviceId)
{
    m_bIsRunning = false;
    m_strIp = strIp;
    dwPort = 0;
    m_dwPackageMaxLength = 1024*100;
    m_dwDeviceID = deviceId;
    
}

CCaptureTcpClientInstance::~CCaptureTcpClientInstance()
{
    m_bIsRunning = false;
    for (std::thread &it : m_vecThread)
    {
        it.join();
    }
    sem_destroy(&m_semMsg);
    std::cout << "~CCaptureTcpClientInstance()" << std::endl;
}

bool CCaptureTcpClientInstance::Start()
{
    m_bIsRunning = true;
    std::thread threadSend(&CCaptureTcpClientInstance::sendThread, this);
    m_vecThread.push_back(std::move(threadSend));
    std::thread threadRecv(&CCaptureTcpClientInstance::recvThread, this);
    m_vecThread.push_back(std::move(threadRecv));

    return true;
}

bool CCaptureTcpClientInstance::Stop()
{
    std::cout << "bool CCaptureTcpClientInstance::Stop() " << std::endl;
    m_bIsRunning = false;
    sem_post(&m_semMsg);
    return true;
}

void CCaptureTcpClientInstance::SetSocketFd(int dwFd)
{
    m_dwFd = dwFd;
}

int CCaptureTcpClientInstance::GetSocketFd(){ return m_dwFd; }

void CCaptureTcpClientInstance::SendMsg(const std::string &strMsg)
{
    m_queueMsg.push(strMsg);
    sem_post(&m_semMsg);
}

void CCaptureTcpClientInstance::SetMsgCallBack(std::function<void(int dwDeviceID,std::string strMsg)> &cb)
{
    m_pDataCallback = cb;
}

void CCaptureTcpClientInstance::SetStatusCallBack(
    std::function<void(int dwDeviceId, bool bStatus)> &cb)
{
  if (cb != NULL)
  {
    m_pStatusCallback = cb;
  }
}

void CCaptureTcpClientInstance::sendThread()
{
    while (m_bIsRunning)
    {
        sleep(1);
    }
}

void CCaptureTcpClientInstance::recvThread()
{
    int conTimeOutCnt = 0;
    std::string strRecvData;
    char buffer[1024] = {0};
    std::vector<char> recvBuffer; // 用于保存接收到的数据
    while (m_bIsRunning)
    {
        
        ssize_t bytesRead = recv(m_dwFd, buffer, sizeof(buffer), 0);
        if (bytesRead > 0)
        {
            recvBuffer.insert(recvBuffer.end(), buffer, buffer + bytesRead); // 将新接收的数据加入缓冲区
            conTimeOutCnt=0;
	}
        else if (bytesRead == 0)
        {
            ERROR("CLOSED by peer");
            break; // 连接关闭
        }
        else
        {
            if (errno != EWOULDBLOCK && errno != EAGAIN)
            {
                ERROR("tcp connect error");
                break; // 错误发生
            }
	    conTimeOutCnt++;
	    INFO("recvdata timeout times:{}",conTimeOutCnt);
	    if(conTimeOutCnt==24)//2min
	    {
		INFO("recvdata timeout. close socket!");
		break;
	    }
        }

        // 处理缓冲区的数据
        while (recvBuffer.size() >= sizeof(TShanghaiHead)) // 确保至少有一个包头
        {
	    if(gIsStoreRaw ==1 )
            {
		    std::ostringstream oss;
                for (size_t i = 0; i < sizeof(TShanghaiHead); ++i)
                {
                    oss << std::hex << std::setw(2) << std::setfill('0') << (static_cast<int>(recvBuffer[i])) <<" " ;
                }

                std::string hexString = oss.str();
                //std::string str(recvBuffer.begin(), recvBuffer.begin() + sizeof(TShanghaiHead));
                INFO(hexString);
            }
	    TShanghaiHead* stMsgHead = reinterpret_cast<TShanghaiHead*>(&recvBuffer[0]);
            // 检查包头的有效性
            if (stMsgHead->cHead != 0xf2 && stMsgHead->cHead != 0XF2)
            {
                recvBuffer.erase(recvBuffer.begin()); // 丢弃无效的包头
                continue;
            }
             int dwReadLength = ntohl(stMsgHead->dwLength);
            if (recvBuffer.size() >= sizeof(TShanghaiHead) + dwReadLength) // 确保有足够的数据
            {
                // 读取完整的数据包
                strRecvData.append(reinterpret_cast<char*>(&recvBuffer[sizeof(TShanghaiHead)]), dwReadLength);

                // 清除已处理的数据
                recvBuffer.erase(recvBuffer.begin(), recvBuffer.begin() + sizeof(TShanghaiHead) + dwReadLength);

                // 解析并回调
                m_pDataCallback(m_dwDeviceID, strRecvData);
                strRecvData.clear();
            }
            else
            {
                // 缓冲区中数据不足，等待更多数据
                break;
            }
        }

        // TShanghaiHead stMsgHead{};
        // ssize_t bytesRead = recv(m_dwFd, &stMsgHead, sizeof(stMsgHead), 0);
        // // ssize_t bytesRead = recv(m_dwFd, buf, sizeof(buf), 0);
        // if (bytesRead > 0)
        // {
        //     if (stMsgHead.cHead != 0xf2 || stMsgHead.cHead !=0XF2)
        //     {
        //         continue;
        //     }
        //     char buffer[m_dwPackageMaxLength];
        //     int dwDataLength = 0;
        //     int dwReadLength = ntohl(stMsgHead.dwLength); // 待读长度
        //     // const unsigned char* byteData = reinterpret_cast<const unsigned char*>(&stMsgHead.dwLength);
        //     // for(int i=0;i<4;i++)
        //     // {
        //     //  printf("%02x ",byteData[i]);   
        //     // }
        //     // printf("\n");
        //     bool bIsClosdByPeer = false;
        //     // std::cout << "dwReadLength =" << dwReadLength << std::endl;
        //     do
        //     {
        //         bytesRead = recv(m_dwFd, buffer, dwReadLength - dwDataLength, 0);
        //         if (bytesRead > 0)
        //         {
        //             dwDataLength += bytesRead;
        //             strRecvData.append(buffer, bytesRead);
        //         }
        //         else if (bytesRead == 0)
        //         {
        //             bIsClosdByPeer = true;
        //              ERROR("CLOSED by peer");
        //             break;
        //         }
        //         else
        //         {
        //             if (errno == EWOULDBLOCK || errno == EAGAIN)
        //             {
        //             }
        //             else
        //             {
        //                 bIsClosdByPeer = true;
        //                 ERROR("CLOSED by peer");
        //                 break;
        //             }
        //         }

        //     } while (dwDataLength != dwReadLength);
        //     // std::cout << "dwDataLength = " << dwDataLength << std::endl;
        //     if (bIsClosdByPeer)
        //     {
        //         break;
        //     }
        //     //callback
        //     m_pDataCallback(m_dwDeviceID,strRecvData);
        //     strRecvData.clear();

        // }
        // else if (bytesRead == 0)
        // {
        //     break;
        // }
        // else
        // {
        //     if (errno == EWOULDBLOCK || errno == EAGAIN)
        //     {
        //     }
        //     else
        //     {
        //         break;
        //     }
        // }
    }
    m_bIsRunning = false;
    m_pStatusCallback(m_dwDeviceID, false);
}

/****************************CCaptureTcpServer**************************/

CCaptureTcpServer::CCaptureTcpServer(CDataCaptureManager &manager)
    : CCaptureBase(manager)
{
    m_dwPort = 33120;
    m_bIsConnected = false;
    m_dwDeviceCnt = 0;
    sem_init(&m_sem, 0, 0);
}

CCaptureTcpServer::~CCaptureTcpServer()
{
}

void CCaptureTcpServer::Start()
{
    CCaptureBase::Start();
    do
    {
        // 创建服务器套接字
        m_Sokcet = socket(AF_INET, SOCK_STREAM, 0);
        if (-1 == m_Sokcet)
        {
            ERROR("Failed to create server socket.");
            break;
        }

        // 设置端口复用
        int optval = 1;
        if (setsockopt(m_Sokcet, SOL_SOCKET, SO_REUSEADDR, &optval,
                       sizeof(optval)) < 0)
        {
            ERROR("Error setting SO_REUSEADDR option");
            break;
        }

        // 设置接收超时
        struct timeval stTime;
        stTime.tv_sec = 5;
        stTime.tv_usec = 0;
        setsockopt(m_Sokcet, SOL_SOCKET, SO_RCVTIMEO, (const char *)&stTime,
                   sizeof(stTime));

        // 设置服务器地址和端口
        sockaddr_in serverAddress{};
        serverAddress.sin_family = AF_INET;
        serverAddress.sin_addr.s_addr = INADDR_ANY;
        serverAddress.sin_port = htons(m_dwPort);

        // 绑定套接字到地址和端口
        if (bind(m_Sokcet, (struct sockaddr *)&serverAddress,
                 sizeof(serverAddress)) < 0)
        {
            ERROR("Failed to bind server socket");
            close(m_Sokcet);
            m_Sokcet = -1;
            break;
        }

        // 监听连接
        if (listen(m_Sokcet, 5) < 0)
        {
            ERROR("Failed to listen on server socket");
            close(m_Sokcet);
            m_Sokcet = -1;
            break;
        }
        INFO("start socket {} ", m_Sokcet);

        m_bIsRunning = true;

        // 启动接收线程
        m_acceptThread = std::thread(&CCaptureTcpServer::acceptConnect, this);

        sockaddr_in localAddress;
        socklen_t addressLength = sizeof(localAddress);
        if (getsockname(m_Sokcet, (struct sockaddr *)&localAddress,
                        &addressLength) == -1)
        {
            ERROR("get local address failed!");
        }
        else
        {
            // 将二进制ip转换为字符串
            char ipString[INET_ADDRSTRLEN];
            inet_ntop(AF_INET, &(localAddress.sin_addr), ipString, INET_ADDRSTRLEN);
            INFO("Server started on {} : {} ", ipString, ntohs(localAddress.sin_port));
            // std::cout << "Server started on " << ipString << ":"
            //           << ntohs(localAddress.sin_port) << std::endl;
        }

        // 启动连接关闭监控线程
        m_monitorThread = std::thread(&CCaptureTcpServer::monitorClient, this);

    } while (0);
}

void CCaptureTcpServer::Stop()
{
    m_bIsRunning = false;
    sem_post(&m_sem);
    if(m_monitorThread.joinable())
    {
        m_monitorThread.join();
    }
    if(m_acceptThread.joinable())
    {
        m_acceptThread.join();
    }
    for (auto it = m_mapTcpInstance.begin(); it != m_mapTcpInstance.end(); ) {
        // 释放资源
        it->second->Stop();
        delete it->second; // 释放CCaptureTcpClientInstance
        it = m_mapTcpInstance.erase(it); // 删除map中的元素并返回下一个迭代器
    }
    if(m_thread.joinable())
    {
        m_thread.join();
    }

    if(m_Sokcet != -1)
    {
        // 关闭套接字
        close(m_Sokcet);
        m_Sokcet = -1;
    }
}

void CCaptureTcpServer::SetConfig(TConnectArgs stArgs)
{
    m_strCrossId = stArgs.strRoadId;
    m_dwPort = std::stoi(stArgs.strAddr);
}

void CCaptureTcpServer::SetCallBack(const std::function<void(TRawData stCapData)> &pCb)
{
    if (pCb != NULL)
    {
        m_pCb = pCb;
    }
}
void CCaptureTcpServer::statusSendThread()
{
    u32 recvDataCnt = 0;
    u64 llLastTime = commonFunc::GetMsTime();
    while (m_bIsRunning)
    {   
        u64 llNowTime = commonFunc::GetMsTime();
        if (llNowTime - llLastTime >= 60 * 1000)
        {
            m_fFreq = static_cast<float>(m_dwMsgCnt)  / 60;
            m_mutex.lock();
            recvDataCnt = m_dwMsgCnt;
            m_dwMsgCnt = 0;
            m_mutex.unlock();
            llLastTime = llNowTime;
        }
        m_manager.updateStatus(m_strCrossId, m_bIsCaping, m_fFreq,recvDataCnt);
        m_bIsCaping = false;
        sleep(1);
    }
}

void CCaptureTcpServer::acceptConnect()
{
    while (m_bIsRunning) {
    
    // 接收连接
    sockaddr_in clientAddress{};
    socklen_t clientAddressLength = sizeof(clientAddress);
    int clientSocket = accept(m_Sokcet, (struct sockaddr *)&clientAddress,
                              &clientAddressLength);
    if (clientSocket == -1) {
      continue;
    }

    // 将 IP 地址转换为字符串并输出
    char ipStr[INET_ADDRSTRLEN];
    inet_ntop(AF_INET, &(clientAddress.sin_addr), ipStr, INET_ADDRSTRLEN);
    INFO("Client connected. IP : {}",ipStr);
    // std::cout << "Connected device IP address: " << ipStr << std::endl;

    if (!m_bIsConnected)
    {
        // 记录ip信息
        std::pair<std::string, int> clientInfo = std::make_pair(ipStr, 0);
        m_mapClientInfo[m_dwDeviceCnt] = clientInfo;
        // 创建实例
        CCaptureTcpClientInstance *handle =
            new CCaptureTcpClientInstance(std::string(ipStr),m_dwDeviceCnt);
        handle->SetSocketFd(clientSocket);
        // 设置回调函数
        std::function<void(int dwDeviceID, std::string strMsg)> MsgCallBack =
            [this](int dwDeviceID, std::string strMsg)
        {
            this->msgArrived(dwDeviceID, strMsg);
        };
        handle->SetMsgCallBack(MsgCallBack);

        std::function<void(int dwDeviceID, bool bStatus)> StatusCallBack =
            [this](int dwDeviceID, bool bStatus)
        {
            this->clientStatusUpdate(dwDeviceID, bStatus);
        };
        handle->SetStatusCallBack(StatusCallBack);

        handle->Start();
        // m_pConnectCb(m_dwDeviceCnt);
        m_mapTcpInstance[m_dwDeviceCnt] = handle;
        if (m_mapTcpInstance[m_dwDeviceCnt] == nullptr)
        {
            // printf("handle is empty\n");
        }
        m_dwDeviceCnt++;
        m_bIsConnected = true;
    }
    else
    {
        std::string replayMessage = "server is busy";
        send(clientSocket,replayMessage.c_str(),replayMessage.size(),0);
        close(clientSocket);
        sleep(1);
    }
  }
}

void CCaptureTcpServer::clientStatusUpdate(int dwDeviceID, bool bStatus)
{
   if (!bStatus) {
    m_queueDisconnect.push(dwDeviceID);
    sem_post(&m_sem);
  }
}

void CCaptureTcpServer::monitorClient() {
  while (m_bIsRunning) {
    sem_wait(&m_sem);
    if (!m_bIsRunning) {
      break;
    }
    if (m_queueDisconnect.size() == 0) {
        continue;
    }
    int dwDeviceID = m_queueDisconnect.front();
    m_queueDisconnect.pop();
    if (m_mapTcpInstance[dwDeviceID] != nullptr) {
      m_mapTcpInstance[dwDeviceID]->Stop();
      int ret = close(m_mapTcpInstance[dwDeviceID]->GetSocketFd());
      CCaptureTcpClientInstance *tmp = m_mapTcpInstance[dwDeviceID];
      tmp->~CCaptureTcpClientInstance();
      m_mapTcpInstance[dwDeviceID] = nullptr;
      m_mapTcpInstance.erase(dwDeviceID);
      std::string strIp = "";
      int dwPort = 0;
      GetClientInfo(dwDeviceID, &strIp, &dwPort);
      INFO("{}:{} disconnected!",strIp,dwPort);
      m_bIsConnected = false;
    }
  }
}


void CCaptureTcpServer::msgArrived(int dwDeviceID,std::string msg)
{
    if (!m_bIsRunning)
    {
        return;
    }

    m_bIsCaping = true;
    m_mutex.lock();
    m_dwMsgCnt++;
    m_mutex.unlock();
    if (m_pCb)
    {
        TRawData stCapData;
        stCapData.llRecvTime = commonFunc::GetMsTime();
        stCapData.strCrossroadId = m_strCrossId;
        stCapData.wDataLength = msg.length();
        stCapData.strData = msg;
        m_pCb(stCapData);
    }
    return;
}

void CCaptureTcpServer::GetClientInfo(int &deviceId, std::string *strIp, int *pPort)
{
  for (auto it = m_mapClientInfo.begin(); it != m_mapClientInfo.end(); it++)
  {
    if (it->first == deviceId)
    {
      *strIp = it->second.first;
      *pPort = it->second.second;
    }
  }
}