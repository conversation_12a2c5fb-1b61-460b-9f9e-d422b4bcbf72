#include "dataPusherBase.h"
#include <iostream>
#include <chrono>
#include <cstring>

CDataPusherBase::CDataPusherBase()
    : m_status(EPusherStatus::DISCONNECTED)
    , m_lastError(EPusherError::NONE)
    , m_bRunning(false)
    , m_bPaused(false)
{
    // 初始化统计信息
    memset(&m_statistics, 0, sizeof(m_statistics));
    m_statistics.llConnectTime = std::chrono::duration_cast<std::chrono::milliseconds>(
        std::chrono::system_clock::now().time_since_epoch()).count();
}

CDataPusherBase::~CDataPusherBase()
{
    Stop();
}

bool CDataPusherBase::Init(const TPusherConfig& config)
{
    m_config = config;
    SetStatus(EPusherStatus::DISCONNECTED);
    return true;
}

bool CDataPusherBase::Start()
{
    std::lock_guard<std::mutex> lock(m_statusMutex);
    
    if (m_bRunning)
    {
        std::cout << "推送器已经在运行中" << std::endl;
        return true;
    }

    try
    {
        // 连接到目标
        if (!Connect())
        {
            SetError(EPusherError::CONNECTION_FAILED, "连接失败");
            return false;
        }

        // 启动工作线程
        m_bRunning = true;
        m_workerThread = std::thread(&CDataPusherBase::WorkerThread, this);

        SetStatus(EPusherStatus::CONNECTED);
        std::cout << "推送器启动成功" << std::endl;
        return true;
    }
    catch (const std::exception& e)
    {
        SetError(EPusherError::UNKNOWN, std::string("启动异常: ") + e.what());
        return false;
    }
}

bool CDataPusherBase::Stop()
{
    std::lock_guard<std::mutex> lock(m_statusMutex);
    
    if (!m_bRunning)
    {
        return true;
    }

    try
    {
        // 停止工作线程
        m_bRunning = false;
        m_queueCondition.notify_all();

        if (m_workerThread.joinable())
        {
            m_workerThread.join();
        }

        // 断开连接
        Disconnect();

        SetStatus(EPusherStatus::DISCONNECTED);
        
        std::cout << "推送器已停止" << std::endl;
        return true;
    }
    catch (const std::exception& e)
    {
        std::cout << "停止推送器异常: " << e.what() << std::endl;
        return false;
    }
}

bool CDataPusherBase::Pause()
{
    std::lock_guard<std::mutex> lock(m_statusMutex);
    m_bPaused = true;
    std::cout << "推送器已暂停" << std::endl;
    return true;
}

bool CDataPusherBase::Resume()
{
    std::lock_guard<std::mutex> lock(m_statusMutex);
    m_bPaused = false;
    m_queueCondition.notify_all();
    std::cout << "推送器已恢复" << std::endl;
    return true;
}

bool CDataPusherBase::PushData(const TRawData& data)
{
    return AddToSendQueue(data);
}

bool CDataPusherBase::AddToSendQueue(const TRawData& data)
{
    std::unique_lock<std::mutex> lock(m_queueMutex);

    // 检查队列是否已满
    const size_t MAX_QUEUE_SIZE = 10000;
    if (m_sendQueue.size() >= MAX_QUEUE_SIZE)
    {
        // 更新统计信息
        {
            std::lock_guard<std::mutex> statsLock(m_statisticsMutex);
            m_statistics.llFailedSent++;
        }

        std::cout << "数据队列已满，丢弃数据" << std::endl;
        return false;
    }

    // 添加到队列
    m_sendQueue.push(data);

    // 更新统计信息
    {
        std::lock_guard<std::mutex> statsLock(m_statisticsMutex);
        m_statistics.llTotalSent++;
    }

    // 通知工作线程
    m_queueCondition.notify_one();

    return true;
}

void CDataPusherBase::SetStatusCallback(const std::function<void(EPusherStatus, EPusherError, const std::string&)>& callback)
{
    std::lock_guard<std::mutex> lock(m_statusMutex);
    m_statusCallback = callback;
}



void CDataPusherBase::ResetStatistics()
{
    std::lock_guard<std::mutex> lock(m_statisticsMutex);
    memset(&m_statistics, 0, sizeof(m_statistics));
    m_statistics.llConnectTime = std::chrono::duration_cast<std::chrono::milliseconds>(
        std::chrono::system_clock::now().time_since_epoch()).count();
}

size_t CDataPusherBase::GetQueueSize() const
{
    std::lock_guard<std::mutex> lock(m_queueMutex);
    return m_sendQueue.size();
}

void CDataPusherBase::SetStatus(EPusherStatus status)
{
    EPusherStatus oldStatus = m_status.exchange(status);
    
    if (oldStatus != status)
    {
        // 调用状态回调
        std::lock_guard<std::mutex> lock(m_statusMutex);
        if (m_statusCallback)
        {
            m_statusCallback(status, EPusherError::NONE, "");
        }
    }
}

void CDataPusherBase::SetError(EPusherError error, const std::string& errorMsg)
{
    m_lastError = error;
    m_strLastErrorMsg = errorMsg;

    // 更新统计信息
    {
        std::lock_guard<std::mutex> lock(m_statisticsMutex);
        m_statistics.llFailedSent++;
    }

    SetStatus(EPusherStatus::ERROR);

    // 调用状态回调
    std::lock_guard<std::mutex> callbackLock(m_statusMutex);
    if (m_statusCallback)
    {
        m_statusCallback(EPusherStatus::ERROR, error, errorMsg);
    }

    std::cout << "推送器错误: " << errorMsg << std::endl;
}

void CDataPusherBase::WorkerThread()
{
    std::cout << "推送器工作线程启动" << std::endl;

    while (m_bRunning)
    {
        std::unique_lock<std::mutex> lock(m_queueMutex);

        // 等待数据或停止信号
        m_queueCondition.wait(lock, [this] {
            return !m_sendQueue.empty() || !m_bRunning;
        });

        if (!m_bRunning)
        {
            break;
        }

        // 检查是否暂停
        if (m_bPaused)
        {
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
            continue;
        }

        // 处理队列中的数据
        while (!m_sendQueue.empty() && m_bRunning)
        {
            TRawData data = m_sendQueue.front();
            m_sendQueue.pop();

            lock.unlock();

            // 发送数据
            bool success = SendData(data);

            // 更新统计信息
            {
                std::lock_guard<std::mutex> statsLock(m_statisticsMutex);
                if (success)
                {
                    m_statistics.llSuccessSent++;
                }
                else
                {
                    m_statistics.llFailedSent++;
                }
                m_statistics.llLastSendTime = std::chrono::duration_cast<std::chrono::milliseconds>(
                    std::chrono::system_clock::now().time_since_epoch()).count();
            }

            lock.lock();
        }
    }

    std::cout << "推送器工作线程结束" << std::endl;
}

bool CDataPusherBase::SendDataBatch(const std::vector<TRawData>& dataList)
{
    bool allSuccess = true;
    for (const auto& data : dataList)
    {
        if (!AddToSendQueue(data))
        {
            allSuccess = false;
        }
    }
    return allSuccess;
}

void CDataPusherBase::ProcessSendQueue()
{
    // 这个方法由WorkerThread调用，实际处理在WorkerThread中
}

void CDataPusherBase::ClearSendQueue()
{
    std::lock_guard<std::mutex> lock(m_queueMutex);
    while (!m_sendQueue.empty())
    {
        m_sendQueue.pop();
    }
}

bool CDataPusherBase::ShouldReconnect() const
{
    return m_config.bAutoReconnect && (m_status == EPusherStatus::ERROR || m_status == EPusherStatus::DISCONNECTED);
}

bool CDataPusherBase::DoReconnect()
{
    std::cout << "尝试重连..." << std::endl;

    // 断开当前连接
    Disconnect();

    // 等待一段时间后重连
    std::this_thread::sleep_for(std::chrono::milliseconds(m_config.dwRetryIntervalMs));

    // 尝试重新连接
    if (Connect())
    {
        std::cout << "重连成功" << std::endl;
        return true;
    }
    else
    {
        std::cout << "重连失败" << std::endl;
        return false;
    }
}
