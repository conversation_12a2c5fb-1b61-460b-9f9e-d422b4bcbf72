#pragma once

#include "dataPusherBase.h"
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>

// UDP推送器配置
typedef struct tagUdpPusherConfig
{
    bool bEnable = false;                    // 是否启用
    std::string strAddr = "127.0.0.1";      // 目标地址
    u32 dwPort = 8080;                      // 目标端口
    u32 dwTimeoutMs = 5000;                 // 超时时间(毫秒)
    u32 dwRetryCount = 3;                   // 重试次数
    u32 dwRetryIntervalMs = 1000;           // 重试间隔(毫秒)
    bool bAutoReconnect = true;             // 是否自动重连
    bool bBroadcast = false;                // 是否广播模式
    u32 dwMaxPacketSize = 1400;             // 最大包大小
} TUdpPusherConfig;

class CUdpDataPusher : public CDataPusherBase
{
public:
    CUdpDataPusher();
    virtual ~CUdpDataPusher();

    // 基类接口实现
    virtual bool Init(const TPusherConfig& config) override;
    bool Init(const TUdpPusherConfig& config);
    virtual bool Connect() override;
    virtual bool Disconnect() override;
    virtual bool SendData(const TRawData& data) override;

private:
    // UDP配置
    TUdpPusherConfig m_udpConfig;

    // Socket相关
    int m_socketFd;
    struct sockaddr_in m_targetAddr;
    bool m_bConnected;

    // 统计信息
    u32 m_dwConnectionCheckCount;

    // 内部方法
    bool CreateSocket();
    void CloseSocket();
    bool ConfigureSocket();
    bool SendUdpPacket(const char* data, size_t dataSize);
    bool FragmentAndSend(const std::string& data);
};
