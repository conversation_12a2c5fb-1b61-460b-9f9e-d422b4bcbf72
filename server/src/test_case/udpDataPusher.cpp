#include "udpDataPusher.h"
#include <iostream>
#include <cstring>
#include <unistd.h>
#include <errno.h>
#include <fcntl.h>

CUdpDataPusher::CUdpDataPusher()
    : CDataPusherBase()
    , m_socketFd(-1)
    , m_bConnected(false)
    , m_dwConnectionCheckCount(0)
{
    memset(&m_targetAddr, 0, sizeof(m_targetAddr));
}

CUdpDataPusher::~CUdpDataPusher()
{
    Stop();
}

bool CUdpDataPusher::Init(const TPusherConfig& config)
{
    m_config = config;
    SetStatus(EPusherStatus::DISCONNECTED);
    return true;
}

bool CUdpDataPusher::Init(const TUdpPusherConfig& config)
{
    m_udpConfig = config;

    // 初始化基类配置
    TPusherConfig baseConfig;
    baseConfig.bEnable = config.bEnable;
    baseConfig.strAddr = config.strAddr;
    baseConfig.dwPort = config.dwPort;
    baseConfig.dwTimeoutMs = config.dwTimeoutMs;
    baseConfig.dwRetryCount = config.dwRetryCount;
    baseConfig.dwRetryIntervalMs = config.dwRetryIntervalMs;
    baseConfig.bAutoReconnect = config.bAutoReconnect;

    return Init(baseConfig);
}

bool CUdpDataPusher::Connect()
{
    if (m_bConnected)
    {
        std::cout << "UDP推送器已连接" << std::endl;
        return true;
    }

    try
    {
        // 创建socket
        if (!CreateSocket())
        {
            SetError(EPusherError::CONNECTION_FAILED, "创建UDP socket失败");
            return false;
        }

        // 配置socket
        if (!ConfigureSocket())
        {
            SetError(EPusherError::CONNECTION_FAILED, "配置UDP socket失败");
            CloseSocket();
            return false;
        }

        // 设置目标地址
        memset(&m_targetAddr, 0, sizeof(m_targetAddr));
        m_targetAddr.sin_family = AF_INET;
        m_targetAddr.sin_port = htons(m_udpConfig.dwPort);

        if (inet_pton(AF_INET, m_udpConfig.strAddr.c_str(), &m_targetAddr.sin_addr) <= 0)
        {
            SetError(EPusherError::CONNECTION_FAILED, "无效的UDP目标地址: " + m_udpConfig.strAddr);
            CloseSocket();
            return false;
        }

        m_bConnected = true;
        SetStatus(EPusherStatus::CONNECTED);
        
        std::cout << "UDP推送器连接成功: " << m_udpConfig.strAddr << ":" << m_udpConfig.dwPort << std::endl;
        return true;
    }
    catch (const std::exception& e)
    {
        SetError(EPusherError::CONNECTION_FAILED, std::string("UDP连接异常: ") + e.what());
        CloseSocket();
        return false;
    }
}

bool CUdpDataPusher::Disconnect()
{
    if (!m_bConnected)
    {
        return true;
    }

    try
    {
        CloseSocket();
        m_bConnected = false;
        SetStatus(EPusherStatus::DISCONNECTED);
        
        std::cout << "UDP推送器已断开连接" << std::endl;
        return true;
    }
    catch (const std::exception& e)
    {
        std::cout << "UDP断开连接异常: " << e.what() << std::endl;
        return false;
    }
}

bool CUdpDataPusher::SendData(const TRawData& data)
{
    if (!m_bConnected)
    {
        SetError(EPusherError::CONNECTION_FAILED, "UDP未连接");
        return false;
    }

    try
    {
        // 检查数据大小，如果超过最大包大小则分片发送
        if (data.strData.length() > m_udpConfig.dwMaxPacketSize)
        {
            return FragmentAndSend(data.strData);
        }
        else
        {
            return SendUdpPacket(data.strData.c_str(), data.strData.length());
        }
    }
    catch (const std::exception& e)
    {
        SetError(EPusherError::SEND_FAILED, std::string("UDP发送异常: ") + e.what());
        return false;
    }
}

bool CUdpDataPusher::CreateSocket()
{
    m_socketFd = socket(AF_INET, SOCK_DGRAM, 0);
    if (m_socketFd < 0)
    {
        std::cout << "创建UDP socket失败: " << strerror(errno) << std::endl;
        return false;
    }

    std::cout << "UDP socket创建成功, fd=" << m_socketFd << std::endl;
    return true;
}

void CUdpDataPusher::CloseSocket()
{
    if (m_socketFd >= 0)
    {
        close(m_socketFd);
        std::cout << "UDP socket已关闭, fd=" << m_socketFd << std::endl;
        m_socketFd = -1;
    }
}

bool CUdpDataPusher::ConfigureSocket()
{
    // 设置非阻塞模式
    int flags = fcntl(m_socketFd, F_GETFL, 0);
    if (flags < 0)
    {
        std::cout << "获取socket标志失败: " << strerror(errno) << std::endl;
        return false;
    }

    if (fcntl(m_socketFd, F_SETFL, flags | O_NONBLOCK) < 0)
    {
        std::cout << "设置socket非阻塞模式失败: " << strerror(errno) << std::endl;
        return false;
    }

    // 设置广播模式（如果需要）
    if (m_udpConfig.bBroadcast)
    {
        int broadcast = 1;
        if (setsockopt(m_socketFd, SOL_SOCKET, SO_BROADCAST, &broadcast, sizeof(broadcast)) < 0)
        {
            std::cout << "设置UDP广播模式失败: " << strerror(errno) << std::endl;
            return false;
        }
        std::cout << "UDP广播模式已启用" << std::endl;
    }

    // 设置发送超时
    struct timeval timeout;
    timeout.tv_sec = m_udpConfig.dwTimeoutMs / 1000;
    timeout.tv_usec = (m_udpConfig.dwTimeoutMs % 1000) * 1000;
    
    if (setsockopt(m_socketFd, SOL_SOCKET, SO_SNDTIMEO, &timeout, sizeof(timeout)) < 0)
    {
        std::cout << "设置UDP发送超时失败: " << strerror(errno) << std::endl;
        return false;
    }

    std::cout << "UDP socket配置完成" << std::endl;
    return true;
}

bool CUdpDataPusher::SendUdpPacket(const char* data, size_t dataSize)
{
    if (m_socketFd < 0 || !data || dataSize == 0)
    {
        return false;
    }

    ssize_t sentBytes = sendto(m_socketFd, data, dataSize, 0, 
                              (struct sockaddr*)&m_targetAddr, sizeof(m_targetAddr));
    
    if (sentBytes < 0)
    {
        if (errno == EAGAIN || errno == EWOULDBLOCK)
        {
            // 非阻塞模式下的正常情况，稍后重试
            std::this_thread::sleep_for(std::chrono::milliseconds(10));
            return false;
        }
        else
        {
            std::cout << "UDP发送失败: " << strerror(errno) << std::endl;
            return false;
        }
    }
    else if (static_cast<size_t>(sentBytes) != dataSize)
    {
        std::cout << "UDP发送不完整: 期望=" << dataSize << ", 实际=" << sentBytes << std::endl;
        return false;
    }

    std::cout << "UDP数据发送成功: " << sentBytes << " 字节" << std::endl;
    return true;
}

bool CUdpDataPusher::FragmentAndSend(const std::string& data)
{
    size_t totalSize = data.length();
    size_t maxFragmentSize = m_udpConfig.dwMaxPacketSize - 100; // 预留头部空间
    size_t fragmentCount = (totalSize + maxFragmentSize - 1) / maxFragmentSize;
    
    std::cout << "数据需要分片发送: 总大小=" << totalSize 
              << ", 分片数=" << fragmentCount << std::endl;

    for (size_t i = 0; i < fragmentCount; ++i)
    {
        size_t offset = i * maxFragmentSize;
        size_t fragmentSize = std::min(maxFragmentSize, totalSize - offset);
        
        // 构造分片数据（简单的分片协议）
        std::string fragment = std::to_string(i) + "/" + std::to_string(fragmentCount) + "|";
        fragment += data.substr(offset, fragmentSize);
        
        if (!SendUdpPacket(fragment.c_str(), fragment.length()))
        {
            std::cout << "发送分片 " << i << " 失败" << std::endl;
            return false;
        }
        
        // 分片间稍作延迟
        std::this_thread::sleep_for(std::chrono::milliseconds(1));
    }

    std::cout << "分片数据发送完成" << std::endl;
    return true;
}
