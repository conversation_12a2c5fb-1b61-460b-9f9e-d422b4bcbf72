#include "fileManager.h"
#include "logger.hpp"
#include "zip.h"
#include <fstream>
#include <iostream>
#include "commonFunction.h"
#include <dirent.h>
#include <sys/stat.h>
// #include <experimental/filesystem>
#include <boost/filesystem.hpp>
#include <httplib.h>

CFileManager::CFileManager(CMediator *pMediator) : CModuleBase(pMediator)
{
}

CFileManager::~CFileManager()
{
}
void CFileManager::Init()
{
}
void CFileManager::Start()
{
    if(m_bIsRunning)
    {
        return;
    }
    m_bIsRunning = true;
}
void CFileManager::Stop()
{
    m_bIsRunning = false;
}
void CFileManager::Pause()
{
}

bool CFileManager::MsgFilter(u32 msgType)
{
    bool ret = true;
    if (msgType == SYSTEM_INIT ||
        msgType == SYSTEM_START ||
        msgType == SYSTEM_STOP ||
        msgType == SYSTEM_PAUSE ||
        msgType == DATA_COMPRESS_REQ ||
        msgType == DATA_COMPRESS_QUERY_RES)
    {
        ret = false;
    }
    return ret;
}

void CFileManager::HandleMsg(u32 msgLevel,u32 deviceId,u32 msgType,std::shared_ptr<void> spData)
{
    switch (msgType)
    {
    case SYSTEM_INIT:
        Init();
        break;
    case SYSTEM_START:
        Start();
        break;
    case SYSTEM_STOP:
        Stop();
        break;
    case SYSTEM_PAUSE:
        Pause();
        break;
    case DATA_COMPRESS_REQ:
        dataCompressReq(msgLevel,deviceId,spData);
        break;
    case DATA_COMPRESS_QUERY_RES:
    {
        std::thread handleThread(&CFileManager::dataCompress, this,msgLevel,deviceId,spData);
        handleThread.detach();
    }
        break;
    default:
        break;
    }
}


void CFileManager::dataCompressReq(u32 msgLevel,u32 deviceId,std::shared_ptr<void> spData)
{
    std::shared_ptr<TDataCompressReqList> spDataCompressReq = std::static_pointer_cast<TDataCompressReqList>(spData);
    if(!spDataCompressReq)
    {
        ERROR("response compress request failed!");
        return;
    }
    Notify(msgLevel,deviceId,DATA_COMPRESS_QUERY_REQ,spData);
}   

void CFileManager::dataCompress(u32 msgLevel,u32 deviceId,std::shared_ptr<void> spData)
{
    std::shared_ptr<TDataCompressResList> spDataCompressResList = std::static_pointer_cast<TDataCompressResList>(spData);
    
    do
    {
        if(spDataCompressResList->bIsSucceed == false)
        {
            break;
        }

        DIR *dir;
        struct dirent *entry;
        std::string strDirPath = gStrStorePath + spDataCompressResList->strSerial;
        dir = opendir(strDirPath.c_str());
        if (dir == nullptr)
        {
            ERROR("data compress failed! resaon : can not open dir {}",strDirPath);
            spDataCompressResList->strErr = "can not open dir";
            spDataCompressResList->bIsSucceed = false;
            break;
        }
        std::string comprssPath = gStrStorePath+spDataCompressResList->strPackageName;
        zip_t *zipArchive = zip_open(comprssPath.c_str(), ZIP_CREATE | ZIP_EXCL, nullptr);
        if (zipArchive == nullptr)
        {
            closedir(dir);
            ERROR("data compress failed! resaon : can not open zip file {}",comprssPath.c_str());
            spDataCompressResList->strErr = "can not open zip";
            spDataCompressResList->bIsSucceed = false;
            break;
        }

        while ((entry = readdir(dir)) != nullptr)
        {
            std::string filePath = strDirPath + "/" + entry->d_name;
            addFileToZip(zipArchive, filePath);
        }

        zip_close(zipArchive);

        // 删除临时文件
        removeDirectory(strDirPath);

        // 获取当前路径
        //  std::experimental::filesystem::path path = std::experimental::filesystem::current_path();
        boost::filesystem::path path = boost::filesystem::current_path();
        // std::cout  << "current path = "  << path.string();

        // 将路径添加到包前
        spDataCompressResList->strPath = path.string();
        spDataCompressResList->bIsSucceed = true;

        if (spDataCompressResList->strHttpUrl.length() > 0)
        {
            // 传输文件
            INFO("transfor file url = {}", spDataCompressResList->strHttpUrl.c_str());
            httplib::Client cli(spDataCompressResList->strHttpUrl);
            std::ifstream fin(spDataCompressResList->strPackageName, std::ios::binary);
            std::ostringstream os;
            os << fin.rdbuf();
            std::string content = os.str();
            fin.close();
            httplib::MultipartFormDataItems items = {
                {"compressed-file", content, spDataCompressResList->strPackageName, "application/octet-stream"}};
            auto res = cli.Post("/file", items);
            if (res == nullptr)
            {
                ERROR("file deliver failed! reason : can not post!");
                std::string errInfo = "file transfer failed! can not post";
                std::cout << errInfo << std::endl;
                spDataCompressResList->strErr = errInfo;
                spDataCompressResList->bIsSucceed = false;
                break;
            }
            if (res->status != 200)
            {
                ERROR("file deliver failed! reason : status != 200");
                std::string errInfo = "file transfer failed!";
                std::cout << errInfo << std::endl;
                spDataCompressResList->strErr = errInfo;
                spDataCompressResList->bIsSucceed = false;
            }
        }

    } while (0);

    Notify(msgLevel,deviceId,DATA_COMPRESS_RES,std::static_pointer_cast<void>(spDataCompressResList));
}

void CFileManager::addFileToZip(zip_t *zipArchive,const std::string& filePath)
{
   struct stat fileStat;
    if (stat(filePath.c_str(), &fileStat) == 0 && S_ISREG(fileStat.st_mode)) {
        std::string fileName = filePath.substr(filePath.find_last_of("/") + 1);
        zip_source_t* source = zip_source_file(zipArchive, filePath.c_str(), 0, -1);
        if (source != nullptr) {
            zip_file_add(zipArchive, fileName.c_str(), source, ZIP_FL_OVERWRITE);
        } else {
            ERROR("Failed to add file to zip archive: {}",filePath);
        }
    } 
}

bool CFileManager::removeDirectory(const std::string& directoryPath) {
    DIR* dir;
    struct dirent* entry;

    dir = opendir(directoryPath.c_str());
    if (dir != nullptr) {
        while ((entry = readdir(dir)) != nullptr) {
            std::string filePath = directoryPath + "/" + entry->d_name;

            struct stat fileStat;
            if (stat(filePath.c_str(), &fileStat) == 0) {
                if (S_ISREG(fileStat.st_mode)) {
                    std::remove(filePath.c_str());
                } else if (S_ISDIR(fileStat.st_mode)) {
                    if (std::string(entry->d_name) != "." && std::string(entry->d_name) != "..") {
                        removeDirectory(filePath);
                    }
                }
            }
        }

        closedir(dir);

        std::remove(directoryPath.c_str());

        return true;
    } else {
        return false;
    }
}