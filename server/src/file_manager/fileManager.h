#ifndef _FILE_MANAGER_H_
#define _FILE_MANAGER_H_
#include "moduleBase.h"
#include "zip.h"
#include <random>

extern std::string gStrStorePath;
class CFileManager : public CModuleBase
{
public:
    CFileManager(CMediator *pMediator);
    ~CFileManager();

    void Init() override;
    void Start() override;
    void Stop() override;
    void Pause() override;

    bool Msg<PERSON>ilter(u32 msgType);
    void HandleMsg(u32 msgLevel,u32 deviceId,u32 msgType,std::shared_ptr<void> spData);
private:
    void dataCompressReq(u32 msgLevel,u32 deviceId,std::shared_ptr<void> spData);
    void dataCompress(u32 msgLevel,u32 deviceId,std::shared_ptr<void> spData);

    //zip
    void addFileToZip(zip_t *zipArchive,const std::string& filePath);
    bool removeDirectory(const std::string& directoryPath);

};


#endif