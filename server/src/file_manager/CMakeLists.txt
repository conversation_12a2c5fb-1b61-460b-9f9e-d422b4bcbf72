cmake_minimum_required(VERSION 3.0.2)

aux_source_directory(. DIR_SRC)

add_library(filemanager SHARED ${DIR_SRC})
target_link_libraries(filemanager zip pthread boost_filesystem)

add_custom_command(TARGET filemanager POST_BUILD
 COMMAND
 mv libfilemanager.so libfilemanager.so.${PROJECT_VERSION}
 COMMAND
 ln -s libfilemanager.so.${PROJECT_VERSION} libfilemanager.so
 WORKING_DIRECTORY ${LIBRARY_OUTPUT_PATH})
