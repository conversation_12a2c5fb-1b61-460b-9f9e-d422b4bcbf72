[common]
totalNum = 30
[connect0]
addr = ws://127.0.0.1:8083/mqtt
clientId = tsari0-0
describe = 测试0
enable = 1
id = 1
password = 
roadId = road0
topic = test-0
type = mqtt
username = 
[connect1]
addr = ws://127.0.0.1:8083/mqtt
clientId = tsari0-1
describe = 测试1
enable = 1
id = 2
password = 
roadId = road1
topic = test-1
type = mqtt
username = 
[connect10]
addr = ws://127.0.0.1:8083/mqtt
clientId = tsari0-10
describe = 测试10
enable = 1
id = 11
password = 
roadId = road10
topic = test-10
type = mqtt
username = 
[connect11]
addr = ws://127.0.0.1:8083/mqtt
clientId = tsari0-11
describe = 测试11
enable = 1
id = 12
password = 
roadId = road11
topic = test-11
type = mqtt
username = 
[connect12]
addr = ws://127.0.0.1:8083/mqtt
clientId = tsari0-12
describe = 测试12
enable = 1
id = 13
password = 
roadId = road12
topic = test-12
type = mqtt
username = 
[connect13]
addr = ws://127.0.0.1:8083/mqtt
clientId = tsari0-13
describe = 测试13
enable = 1
id = 14
password = 
roadId = road13
topic = test-13
type = mqtt
username = 
[connect14]
addr = ws://127.0.0.1:8083/mqtt
clientId = tsari0-14
describe = 测试14
enable = 1
id = 15
password = 
roadId = road14
topic = test-14
type = mqtt
username = 
[connect15]
addr = ws://127.0.0.1:8083/mqtt
clientId = tsari0-15
describe = 测试15
enable = 1
id = 16
password = 
roadId = road15
topic = test-15
type = mqtt
username = 
[connect16]
addr = ws://127.0.0.1:8083/mqtt
clientId = tsari0-16
describe = 测试16
enable = 1
id = 17
password = 
roadId = road16
topic = test-16
type = mqtt
username = 
[connect17]
addr = ws://127.0.0.1:8083/mqtt
clientId = tsari0-17
describe = 测试17
enable = 1
id = 18
password = 
roadId = road17
topic = test-17
type = mqtt
username = 
[connect18]
addr = ws://127.0.0.1:8083/mqtt
clientId = tsari0-18
describe = 测试18
enable = 1
id = 19
password = 
roadId = road18
topic = test-18
type = mqtt
username = 
[connect19]
addr = ws://127.0.0.1:8083/mqtt
clientId = tsari0-19
describe = 测试19
enable = 1
id = 20
password = 
roadId = road19
topic = test-19
type = mqtt
username = 
[connect2]
addr = ws://127.0.0.1:8083/mqtt
clientId = tsari0-2
describe = 测试2
enable = 1
id = 3
password = 
roadId = road2
topic = test-2
type = mqtt
username = 
[connect20]
addr = ws://127.0.0.1:8083/mqtt
clientId = tsari0-20
describe = 测试20
enable = 1
id = 21
password = 
roadId = road20
topic = test-20
type = mqtt
username = 
[connect21]
addr = ws://127.0.0.1:8083/mqtt
clientId = tsari0-21
describe = 测试21
enable = 1
id = 22
password = 
roadId = road21
topic = test-21
type = mqtt
username = 
[connect22]
addr = ws://127.0.0.1:8083/mqtt
clientId = tsari0-22
describe = 测试22
enable = 1
id = 23
password = 
roadId = road22
topic = test-22
type = mqtt
username = 
[connect23]
addr = ws://127.0.0.1:8083/mqtt
clientId = tsari0-23
describe = 测试23
enable = 1
id = 24
password = 
roadId = road23
topic = test-23
type = mqtt
username = 
[connect24]
addr = ws://127.0.0.1:8083/mqtt
clientId = tsari0-24
describe = 测试24
enable = 1
id = 25
password = 
roadId = road24
topic = test-24
type = mqtt
username = 
[connect25]
addr = ws://127.0.0.1:8083/mqtt
clientId = tsari0-25
describe = 测试25
enable = 1
id = 26
password = 
roadId = road25
topic = test-25
type = mqtt
username = 
[connect26]
addr = ws://127.0.0.1:8083/mqtt
clientId = tsari0-26
describe = 测试26
enable = 1
id = 27
password = 
roadId = road26
topic = test-26
type = mqtt
username = 
[connect27]
addr = ws://127.0.0.1:8083/mqtt
clientId = tsari0-27
describe = 测试27
enable = 1
id = 28
password = 
roadId = road27
topic = test-27
type = mqtt
username = 
[connect28]
addr = ws://127.0.0.1:8083/mqtt
clientId = tsari0-28
describe = 测试28
enable = 1
id = 29
password = 
roadId = road28
topic = test-28
type = mqtt
username = 
[connect29]
addr = ws://127.0.0.1:8083/mqtt
clientId = tsari0-29
describe = 测试29
enable = 1
id = 30
password = 
roadId = road29
topic = test-29
type = mqtt
username = 
[connect3]
addr = ws://127.0.0.1:8083/mqtt
clientId = tsari0-3
describe = 测试3
enable = 1
id = 4
password = 
roadId = road3
topic = test-3
type = mqtt
username = 
[connect4]
addr = ws://127.0.0.1:8083/mqtt
clientId = tsari0-4
describe = 测试4
enable = 1
id = 5
password = 
roadId = road4
topic = test-4
type = mqtt
username = 
[connect5]
addr = ws://127.0.0.1:8083/mqtt
clientId = tsari0-5
describe = 测试5
enable = 1
id = 6
password = 
roadId = road5
topic = test-5
type = mqtt
username = 
[connect6]
addr = ws://127.0.0.1:8083/mqtt
clientId = tsari0-6
describe = 测试6
enable = 1
id = 7
password = 
roadId = road6
topic = test-6
type = mqtt
username = 
[connect7]
addr = ws://127.0.0.1:8083/mqtt
clientId = tsari0-7
describe = 测试7
enable = 1
id = 8
password = 
roadId = road7
topic = test-7
type = mqtt
username = 
[connect8]
addr = ws://127.0.0.1:8083/mqtt
clientId = tsari0-8
describe = 测试8
enable = 1
id = 9
password = 
roadId = road8
topic = test-8
type = mqtt
username = 
[connect9]
addr = ws://127.0.0.1:8083/mqtt
clientId = tsari0-9
describe = 测试9
enable = 1
id = 10
password = 
roadId = road9
topic = test-9
type = mqtt
username = 
