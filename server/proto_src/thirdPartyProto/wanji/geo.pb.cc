// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: geo.proto

#include "geo.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>

PROTOBUF_PRAGMA_INIT_SEG

namespace _pb = ::PROTOBUF_NAMESPACE_ID;
namespace _pbi = _pb::internal;

namespace esurfing {
namespace proto {
namespace math {
PROTOBUF_CONSTEXPR Vector3i::Vector3i(
    ::_pbi::ConstantInitialized): _impl_{
    /*decltype(_impl_.x_)*/int64_t{0}
  , /*decltype(_impl_.y_)*/int64_t{0}
  , /*decltype(_impl_.z_)*/int64_t{0}
  , /*decltype(_impl_._cached_size_)*/{}} {}
struct Vector3iDefaultTypeInternal {
  PROTOBUF_CONSTEXPR Vector3iDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~Vector3iDefaultTypeInternal() {}
  union {
    Vector3i _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 Vector3iDefaultTypeInternal _Vector3i_default_instance_;
PROTOBUF_CONSTEXPR Polyline::Polyline(
    ::_pbi::ConstantInitialized): _impl_{
    /*decltype(_impl_.points_)*/{}
  , /*decltype(_impl_._cached_size_)*/{}} {}
struct PolylineDefaultTypeInternal {
  PROTOBUF_CONSTEXPR PolylineDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~PolylineDefaultTypeInternal() {}
  union {
    Polyline _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 PolylineDefaultTypeInternal _Polyline_default_instance_;
PROTOBUF_CONSTEXPR Polygon::Polygon(
    ::_pbi::ConstantInitialized): _impl_{
    /*decltype(_impl_.points_)*/{}
  , /*decltype(_impl_._cached_size_)*/{}} {}
struct PolygonDefaultTypeInternal {
  PROTOBUF_CONSTEXPR PolygonDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~PolygonDefaultTypeInternal() {}
  union {
    Polygon _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 PolygonDefaultTypeInternal _Polygon_default_instance_;
PROTOBUF_CONSTEXPR Points::Points(
    ::_pbi::ConstantInitialized): _impl_{
    /*decltype(_impl_.points_)*/{}
  , /*decltype(_impl_._cached_size_)*/{}} {}
struct PointsDefaultTypeInternal {
  PROTOBUF_CONSTEXPR PointsDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~PointsDefaultTypeInternal() {}
  union {
    Points _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 PointsDefaultTypeInternal _Points_default_instance_;
PROTOBUF_CONSTEXPR Vector2d::Vector2d(
    ::_pbi::ConstantInitialized): _impl_{
    /*decltype(_impl_.x_)*/0
  , /*decltype(_impl_.y_)*/0
  , /*decltype(_impl_._cached_size_)*/{}} {}
struct Vector2dDefaultTypeInternal {
  PROTOBUF_CONSTEXPR Vector2dDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~Vector2dDefaultTypeInternal() {}
  union {
    Vector2d _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 Vector2dDefaultTypeInternal _Vector2d_default_instance_;
PROTOBUF_CONSTEXPR Vector2f::Vector2f(
    ::_pbi::ConstantInitialized): _impl_{
    /*decltype(_impl_.x_)*/0
  , /*decltype(_impl_.y_)*/0
  , /*decltype(_impl_._cached_size_)*/{}} {}
struct Vector2fDefaultTypeInternal {
  PROTOBUF_CONSTEXPR Vector2fDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~Vector2fDefaultTypeInternal() {}
  union {
    Vector2f _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 Vector2fDefaultTypeInternal _Vector2f_default_instance_;
PROTOBUF_CONSTEXPR Vector3d::Vector3d(
    ::_pbi::ConstantInitialized): _impl_{
    /*decltype(_impl_.x_)*/0
  , /*decltype(_impl_.y_)*/0
  , /*decltype(_impl_.z_)*/0
  , /*decltype(_impl_._cached_size_)*/{}} {}
struct Vector3dDefaultTypeInternal {
  PROTOBUF_CONSTEXPR Vector3dDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~Vector3dDefaultTypeInternal() {}
  union {
    Vector3d _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 Vector3dDefaultTypeInternal _Vector3d_default_instance_;
PROTOBUF_CONSTEXPR Vector3f::Vector3f(
    ::_pbi::ConstantInitialized): _impl_{
    /*decltype(_impl_.x_)*/0
  , /*decltype(_impl_.y_)*/0
  , /*decltype(_impl_.z_)*/0
  , /*decltype(_impl_._cached_size_)*/{}} {}
struct Vector3fDefaultTypeInternal {
  PROTOBUF_CONSTEXPR Vector3fDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~Vector3fDefaultTypeInternal() {}
  union {
    Vector3f _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 Vector3fDefaultTypeInternal _Vector3f_default_instance_;
PROTOBUF_CONSTEXPR Matrix2d::Matrix2d(
    ::_pbi::ConstantInitialized): _impl_{
    /*decltype(_impl_.e00_)*/0
  , /*decltype(_impl_.e01_)*/0
  , /*decltype(_impl_.e10_)*/0
  , /*decltype(_impl_.e11_)*/0
  , /*decltype(_impl_._cached_size_)*/{}} {}
struct Matrix2dDefaultTypeInternal {
  PROTOBUF_CONSTEXPR Matrix2dDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~Matrix2dDefaultTypeInternal() {}
  union {
    Matrix2d _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 Matrix2dDefaultTypeInternal _Matrix2d_default_instance_;
PROTOBUF_CONSTEXPR Matrix2f::Matrix2f(
    ::_pbi::ConstantInitialized): _impl_{
    /*decltype(_impl_.e00_)*/0
  , /*decltype(_impl_.e01_)*/0
  , /*decltype(_impl_.e10_)*/0
  , /*decltype(_impl_.e11_)*/0
  , /*decltype(_impl_._cached_size_)*/{}} {}
struct Matrix2fDefaultTypeInternal {
  PROTOBUF_CONSTEXPR Matrix2fDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~Matrix2fDefaultTypeInternal() {}
  union {
    Matrix2f _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 Matrix2fDefaultTypeInternal _Matrix2f_default_instance_;
PROTOBUF_CONSTEXPR Matrix3d::Matrix3d(
    ::_pbi::ConstantInitialized): _impl_{
    /*decltype(_impl_.e00_)*/0
  , /*decltype(_impl_.e01_)*/0
  , /*decltype(_impl_.e02_)*/0
  , /*decltype(_impl_.e10_)*/0
  , /*decltype(_impl_.e11_)*/0
  , /*decltype(_impl_.e12_)*/0
  , /*decltype(_impl_.e20_)*/0
  , /*decltype(_impl_.e21_)*/0
  , /*decltype(_impl_.e22_)*/0
  , /*decltype(_impl_._cached_size_)*/{}} {}
struct Matrix3dDefaultTypeInternal {
  PROTOBUF_CONSTEXPR Matrix3dDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~Matrix3dDefaultTypeInternal() {}
  union {
    Matrix3d _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 Matrix3dDefaultTypeInternal _Matrix3d_default_instance_;
PROTOBUF_CONSTEXPR Matrix3f::Matrix3f(
    ::_pbi::ConstantInitialized): _impl_{
    /*decltype(_impl_.e00_)*/0
  , /*decltype(_impl_.e01_)*/0
  , /*decltype(_impl_.e02_)*/0
  , /*decltype(_impl_.e10_)*/0
  , /*decltype(_impl_.e11_)*/0
  , /*decltype(_impl_.e12_)*/0
  , /*decltype(_impl_.e20_)*/0
  , /*decltype(_impl_.e21_)*/0
  , /*decltype(_impl_.e22_)*/0
  , /*decltype(_impl_._cached_size_)*/{}} {}
struct Matrix3fDefaultTypeInternal {
  PROTOBUF_CONSTEXPR Matrix3fDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~Matrix3fDefaultTypeInternal() {}
  union {
    Matrix3f _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 Matrix3fDefaultTypeInternal _Matrix3f_default_instance_;
PROTOBUF_CONSTEXPR Quaterniond::Quaterniond(
    ::_pbi::ConstantInitialized): _impl_{
    /*decltype(_impl_.w_)*/0
  , /*decltype(_impl_.x_)*/0
  , /*decltype(_impl_.y_)*/0
  , /*decltype(_impl_.z_)*/0
  , /*decltype(_impl_._cached_size_)*/{}} {}
struct QuaterniondDefaultTypeInternal {
  PROTOBUF_CONSTEXPR QuaterniondDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~QuaterniondDefaultTypeInternal() {}
  union {
    Quaterniond _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 QuaterniondDefaultTypeInternal _Quaterniond_default_instance_;
PROTOBUF_CONSTEXPR Quaternionf::Quaternionf(
    ::_pbi::ConstantInitialized): _impl_{
    /*decltype(_impl_.w_)*/0
  , /*decltype(_impl_.x_)*/0
  , /*decltype(_impl_.y_)*/0
  , /*decltype(_impl_.z_)*/0
  , /*decltype(_impl_._cached_size_)*/{}} {}
struct QuaternionfDefaultTypeInternal {
  PROTOBUF_CONSTEXPR QuaternionfDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~QuaternionfDefaultTypeInternal() {}
  union {
    Quaternionf _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 QuaternionfDefaultTypeInternal _Quaternionf_default_instance_;
PROTOBUF_CONSTEXPR Transformation3d::Transformation3d(
    ::_pbi::ConstantInitialized): _impl_{
    /*decltype(_impl_.rotation_)*/nullptr
  , /*decltype(_impl_.translation_)*/nullptr
  , /*decltype(_impl_._cached_size_)*/{}} {}
struct Transformation3dDefaultTypeInternal {
  PROTOBUF_CONSTEXPR Transformation3dDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~Transformation3dDefaultTypeInternal() {}
  union {
    Transformation3d _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 Transformation3dDefaultTypeInternal _Transformation3d_default_instance_;
PROTOBUF_CONSTEXPR Transformation3f::Transformation3f(
    ::_pbi::ConstantInitialized): _impl_{
    /*decltype(_impl_.rotation_)*/nullptr
  , /*decltype(_impl_.translation_)*/nullptr
  , /*decltype(_impl_._cached_size_)*/{}} {}
struct Transformation3fDefaultTypeInternal {
  PROTOBUF_CONSTEXPR Transformation3fDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~Transformation3fDefaultTypeInternal() {}
  union {
    Transformation3f _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 Transformation3fDefaultTypeInternal _Transformation3f_default_instance_;
}  // namespace math
}  // namespace proto
}  // namespace esurfing
static ::_pb::Metadata file_level_metadata_geo_2eproto[16];
static constexpr ::_pb::EnumDescriptor const** file_level_enum_descriptors_geo_2eproto = nullptr;
static constexpr ::_pb::ServiceDescriptor const** file_level_service_descriptors_geo_2eproto = nullptr;

const uint32_t TableStruct_geo_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::esurfing::proto::math::Vector3i, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::esurfing::proto::math::Vector3i, _impl_.x_),
  PROTOBUF_FIELD_OFFSET(::esurfing::proto::math::Vector3i, _impl_.y_),
  PROTOBUF_FIELD_OFFSET(::esurfing::proto::math::Vector3i, _impl_.z_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::esurfing::proto::math::Polyline, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::esurfing::proto::math::Polyline, _impl_.points_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::esurfing::proto::math::Polygon, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::esurfing::proto::math::Polygon, _impl_.points_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::esurfing::proto::math::Points, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::esurfing::proto::math::Points, _impl_.points_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::esurfing::proto::math::Vector2d, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::esurfing::proto::math::Vector2d, _impl_.x_),
  PROTOBUF_FIELD_OFFSET(::esurfing::proto::math::Vector2d, _impl_.y_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::esurfing::proto::math::Vector2f, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::esurfing::proto::math::Vector2f, _impl_.x_),
  PROTOBUF_FIELD_OFFSET(::esurfing::proto::math::Vector2f, _impl_.y_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::esurfing::proto::math::Vector3d, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::esurfing::proto::math::Vector3d, _impl_.x_),
  PROTOBUF_FIELD_OFFSET(::esurfing::proto::math::Vector3d, _impl_.y_),
  PROTOBUF_FIELD_OFFSET(::esurfing::proto::math::Vector3d, _impl_.z_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::esurfing::proto::math::Vector3f, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::esurfing::proto::math::Vector3f, _impl_.x_),
  PROTOBUF_FIELD_OFFSET(::esurfing::proto::math::Vector3f, _impl_.y_),
  PROTOBUF_FIELD_OFFSET(::esurfing::proto::math::Vector3f, _impl_.z_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::esurfing::proto::math::Matrix2d, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::esurfing::proto::math::Matrix2d, _impl_.e00_),
  PROTOBUF_FIELD_OFFSET(::esurfing::proto::math::Matrix2d, _impl_.e01_),
  PROTOBUF_FIELD_OFFSET(::esurfing::proto::math::Matrix2d, _impl_.e10_),
  PROTOBUF_FIELD_OFFSET(::esurfing::proto::math::Matrix2d, _impl_.e11_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::esurfing::proto::math::Matrix2f, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::esurfing::proto::math::Matrix2f, _impl_.e00_),
  PROTOBUF_FIELD_OFFSET(::esurfing::proto::math::Matrix2f, _impl_.e01_),
  PROTOBUF_FIELD_OFFSET(::esurfing::proto::math::Matrix2f, _impl_.e10_),
  PROTOBUF_FIELD_OFFSET(::esurfing::proto::math::Matrix2f, _impl_.e11_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::esurfing::proto::math::Matrix3d, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::esurfing::proto::math::Matrix3d, _impl_.e00_),
  PROTOBUF_FIELD_OFFSET(::esurfing::proto::math::Matrix3d, _impl_.e01_),
  PROTOBUF_FIELD_OFFSET(::esurfing::proto::math::Matrix3d, _impl_.e02_),
  PROTOBUF_FIELD_OFFSET(::esurfing::proto::math::Matrix3d, _impl_.e10_),
  PROTOBUF_FIELD_OFFSET(::esurfing::proto::math::Matrix3d, _impl_.e11_),
  PROTOBUF_FIELD_OFFSET(::esurfing::proto::math::Matrix3d, _impl_.e12_),
  PROTOBUF_FIELD_OFFSET(::esurfing::proto::math::Matrix3d, _impl_.e20_),
  PROTOBUF_FIELD_OFFSET(::esurfing::proto::math::Matrix3d, _impl_.e21_),
  PROTOBUF_FIELD_OFFSET(::esurfing::proto::math::Matrix3d, _impl_.e22_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::esurfing::proto::math::Matrix3f, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::esurfing::proto::math::Matrix3f, _impl_.e00_),
  PROTOBUF_FIELD_OFFSET(::esurfing::proto::math::Matrix3f, _impl_.e01_),
  PROTOBUF_FIELD_OFFSET(::esurfing::proto::math::Matrix3f, _impl_.e02_),
  PROTOBUF_FIELD_OFFSET(::esurfing::proto::math::Matrix3f, _impl_.e10_),
  PROTOBUF_FIELD_OFFSET(::esurfing::proto::math::Matrix3f, _impl_.e11_),
  PROTOBUF_FIELD_OFFSET(::esurfing::proto::math::Matrix3f, _impl_.e12_),
  PROTOBUF_FIELD_OFFSET(::esurfing::proto::math::Matrix3f, _impl_.e20_),
  PROTOBUF_FIELD_OFFSET(::esurfing::proto::math::Matrix3f, _impl_.e21_),
  PROTOBUF_FIELD_OFFSET(::esurfing::proto::math::Matrix3f, _impl_.e22_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::esurfing::proto::math::Quaterniond, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::esurfing::proto::math::Quaterniond, _impl_.w_),
  PROTOBUF_FIELD_OFFSET(::esurfing::proto::math::Quaterniond, _impl_.x_),
  PROTOBUF_FIELD_OFFSET(::esurfing::proto::math::Quaterniond, _impl_.y_),
  PROTOBUF_FIELD_OFFSET(::esurfing::proto::math::Quaterniond, _impl_.z_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::esurfing::proto::math::Quaternionf, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::esurfing::proto::math::Quaternionf, _impl_.w_),
  PROTOBUF_FIELD_OFFSET(::esurfing::proto::math::Quaternionf, _impl_.x_),
  PROTOBUF_FIELD_OFFSET(::esurfing::proto::math::Quaternionf, _impl_.y_),
  PROTOBUF_FIELD_OFFSET(::esurfing::proto::math::Quaternionf, _impl_.z_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::esurfing::proto::math::Transformation3d, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::esurfing::proto::math::Transformation3d, _impl_.rotation_),
  PROTOBUF_FIELD_OFFSET(::esurfing::proto::math::Transformation3d, _impl_.translation_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::esurfing::proto::math::Transformation3f, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::esurfing::proto::math::Transformation3f, _impl_.rotation_),
  PROTOBUF_FIELD_OFFSET(::esurfing::proto::math::Transformation3f, _impl_.translation_),
};
static const ::_pbi::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, -1, sizeof(::esurfing::proto::math::Vector3i)},
  { 9, -1, -1, sizeof(::esurfing::proto::math::Polyline)},
  { 16, -1, -1, sizeof(::esurfing::proto::math::Polygon)},
  { 23, -1, -1, sizeof(::esurfing::proto::math::Points)},
  { 30, -1, -1, sizeof(::esurfing::proto::math::Vector2d)},
  { 38, -1, -1, sizeof(::esurfing::proto::math::Vector2f)},
  { 46, -1, -1, sizeof(::esurfing::proto::math::Vector3d)},
  { 55, -1, -1, sizeof(::esurfing::proto::math::Vector3f)},
  { 64, -1, -1, sizeof(::esurfing::proto::math::Matrix2d)},
  { 74, -1, -1, sizeof(::esurfing::proto::math::Matrix2f)},
  { 84, -1, -1, sizeof(::esurfing::proto::math::Matrix3d)},
  { 99, -1, -1, sizeof(::esurfing::proto::math::Matrix3f)},
  { 114, -1, -1, sizeof(::esurfing::proto::math::Quaterniond)},
  { 124, -1, -1, sizeof(::esurfing::proto::math::Quaternionf)},
  { 134, -1, -1, sizeof(::esurfing::proto::math::Transformation3d)},
  { 142, -1, -1, sizeof(::esurfing::proto::math::Transformation3f)},
};

static const ::_pb::Message* const file_default_instances[] = {
  &::esurfing::proto::math::_Vector3i_default_instance_._instance,
  &::esurfing::proto::math::_Polyline_default_instance_._instance,
  &::esurfing::proto::math::_Polygon_default_instance_._instance,
  &::esurfing::proto::math::_Points_default_instance_._instance,
  &::esurfing::proto::math::_Vector2d_default_instance_._instance,
  &::esurfing::proto::math::_Vector2f_default_instance_._instance,
  &::esurfing::proto::math::_Vector3d_default_instance_._instance,
  &::esurfing::proto::math::_Vector3f_default_instance_._instance,
  &::esurfing::proto::math::_Matrix2d_default_instance_._instance,
  &::esurfing::proto::math::_Matrix2f_default_instance_._instance,
  &::esurfing::proto::math::_Matrix3d_default_instance_._instance,
  &::esurfing::proto::math::_Matrix3f_default_instance_._instance,
  &::esurfing::proto::math::_Quaterniond_default_instance_._instance,
  &::esurfing::proto::math::_Quaternionf_default_instance_._instance,
  &::esurfing::proto::math::_Transformation3d_default_instance_._instance,
  &::esurfing::proto::math::_Transformation3f_default_instance_._instance,
};

const char descriptor_table_protodef_geo_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n\tgeo.proto\022\023esurfing.proto.math\"+\n\010Vect"
  "or3i\022\t\n\001x\030\001 \001(\003\022\t\n\001y\030\002 \001(\003\022\t\n\001z\030\003 \001(\003\"9\n"
  "\010Polyline\022-\n\006points\030\001 \003(\0132\035.esurfing.pro"
  "to.math.Vector3d\"8\n\007Polygon\022-\n\006points\030\001 "
  "\003(\0132\035.esurfing.proto.math.Vector2f\"7\n\006Po"
  "ints\022-\n\006points\030\001 \003(\0132\035.esurfing.proto.ma"
  "th.Vector3d\" \n\010Vector2d\022\t\n\001x\030\001 \001(\001\022\t\n\001y\030"
  "\002 \001(\001\" \n\010Vector2f\022\t\n\001x\030\001 \001(\002\022\t\n\001y\030\002 \001(\002\""
  "+\n\010Vector3d\022\t\n\001x\030\001 \001(\001\022\t\n\001y\030\002 \001(\001\022\t\n\001z\030\003"
  " \001(\001\"+\n\010Vector3f\022\t\n\001x\030\001 \001(\002\022\t\n\001y\030\002 \001(\002\022\t"
  "\n\001z\030\003 \001(\002\">\n\010Matrix2d\022\013\n\003e00\030\001 \001(\001\022\013\n\003e0"
  "1\030\002 \001(\001\022\013\n\003e10\030\003 \001(\001\022\013\n\003e11\030\004 \001(\001\">\n\010Mat"
  "rix2f\022\013\n\003e00\030\001 \001(\002\022\013\n\003e01\030\002 \001(\002\022\013\n\003e10\030\003"
  " \001(\002\022\013\n\003e11\030\004 \001(\002\"\177\n\010Matrix3d\022\013\n\003e00\030\001 \001"
  "(\001\022\013\n\003e01\030\002 \001(\001\022\013\n\003e02\030\003 \001(\001\022\013\n\003e10\030\004 \001("
  "\001\022\013\n\003e11\030\005 \001(\001\022\013\n\003e12\030\006 \001(\001\022\013\n\003e20\030\007 \001(\001"
  "\022\013\n\003e21\030\010 \001(\001\022\013\n\003e22\030\t \001(\001\"\177\n\010Matrix3f\022\013"
  "\n\003e00\030\001 \001(\002\022\013\n\003e01\030\002 \001(\002\022\013\n\003e02\030\003 \001(\002\022\013\n"
  "\003e10\030\004 \001(\002\022\013\n\003e11\030\005 \001(\002\022\013\n\003e12\030\006 \001(\002\022\013\n\003"
  "e20\030\007 \001(\002\022\013\n\003e21\030\010 \001(\002\022\013\n\003e22\030\t \001(\002\"9\n\013Q"
  "uaterniond\022\t\n\001w\030\001 \001(\001\022\t\n\001x\030\002 \001(\001\022\t\n\001y\030\003 "
  "\001(\001\022\t\n\001z\030\004 \001(\001\"9\n\013Quaternionf\022\t\n\001w\030\001 \001(\002"
  "\022\t\n\001x\030\002 \001(\002\022\t\n\001y\030\003 \001(\002\022\t\n\001z\030\004 \001(\002\"z\n\020Tra"
  "nsformation3d\0222\n\010rotation\030\001 \001(\0132 .esurfi"
  "ng.proto.math.Quaterniond\0222\n\013translation"
  "\030\002 \001(\0132\035.esurfing.proto.math.Vector3d\"z\n"
  "\020Transformation3f\0222\n\010rotation\030\001 \001(\0132 .es"
  "urfing.proto.math.Quaternionf\0222\n\013transla"
  "tion\030\002 \001(\0132\035.esurfing.proto.math.Vector3"
  "fb\006proto3"
  ;
static ::_pbi::once_flag descriptor_table_geo_2eproto_once;
const ::_pbi::DescriptorTable descriptor_table_geo_2eproto = {
    false, false, 1169, descriptor_table_protodef_geo_2eproto,
    "geo.proto",
    &descriptor_table_geo_2eproto_once, nullptr, 0, 16,
    schemas, file_default_instances, TableStruct_geo_2eproto::offsets,
    file_level_metadata_geo_2eproto, file_level_enum_descriptors_geo_2eproto,
    file_level_service_descriptors_geo_2eproto,
};
PROTOBUF_ATTRIBUTE_WEAK const ::_pbi::DescriptorTable* descriptor_table_geo_2eproto_getter() {
  return &descriptor_table_geo_2eproto;
}

// Force running AddDescriptors() at dynamic initialization time.
PROTOBUF_ATTRIBUTE_INIT_PRIORITY2 static ::_pbi::AddDescriptorsRunner dynamic_init_dummy_geo_2eproto(&descriptor_table_geo_2eproto);
namespace esurfing {
namespace proto {
namespace math {

// ===================================================================

class Vector3i::_Internal {
 public:
};

Vector3i::Vector3i(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor(arena, is_message_owned);
  // @@protoc_insertion_point(arena_constructor:esurfing.proto.math.Vector3i)
}
Vector3i::Vector3i(const Vector3i& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  Vector3i* const _this = this; (void)_this;
  new (&_impl_) Impl_{
      decltype(_impl_.x_){}
    , decltype(_impl_.y_){}
    , decltype(_impl_.z_){}
    , /*decltype(_impl_._cached_size_)*/{}};

  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&_impl_.x_, &from._impl_.x_,
    static_cast<size_t>(reinterpret_cast<char*>(&_impl_.z_) -
    reinterpret_cast<char*>(&_impl_.x_)) + sizeof(_impl_.z_));
  // @@protoc_insertion_point(copy_constructor:esurfing.proto.math.Vector3i)
}

inline void Vector3i::SharedCtor(
    ::_pb::Arena* arena, bool is_message_owned) {
  (void)arena;
  (void)is_message_owned;
  new (&_impl_) Impl_{
      decltype(_impl_.x_){int64_t{0}}
    , decltype(_impl_.y_){int64_t{0}}
    , decltype(_impl_.z_){int64_t{0}}
    , /*decltype(_impl_._cached_size_)*/{}
  };
}

Vector3i::~Vector3i() {
  // @@protoc_insertion_point(destructor:esurfing.proto.math.Vector3i)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void Vector3i::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void Vector3i::SetCachedSize(int size) const {
  _impl_._cached_size_.Set(size);
}

void Vector3i::Clear() {
// @@protoc_insertion_point(message_clear_start:esurfing.proto.math.Vector3i)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&_impl_.x_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&_impl_.z_) -
      reinterpret_cast<char*>(&_impl_.x_)) + sizeof(_impl_.z_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* Vector3i::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // int64 x = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          _impl_.x_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int64 y = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          _impl_.y_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int64 z = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 24)) {
          _impl_.z_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* Vector3i::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:esurfing.proto.math.Vector3i)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // int64 x = 1;
  if (this->_internal_x() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt64ToArray(1, this->_internal_x(), target);
  }

  // int64 y = 2;
  if (this->_internal_y() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt64ToArray(2, this->_internal_y(), target);
  }

  // int64 z = 3;
  if (this->_internal_z() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt64ToArray(3, this->_internal_z(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:esurfing.proto.math.Vector3i)
  return target;
}

size_t Vector3i::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:esurfing.proto.math.Vector3i)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // int64 x = 1;
  if (this->_internal_x() != 0) {
    total_size += ::_pbi::WireFormatLite::Int64SizePlusOne(this->_internal_x());
  }

  // int64 y = 2;
  if (this->_internal_y() != 0) {
    total_size += ::_pbi::WireFormatLite::Int64SizePlusOne(this->_internal_y());
  }

  // int64 z = 3;
  if (this->_internal_z() != 0) {
    total_size += ::_pbi::WireFormatLite::Int64SizePlusOne(this->_internal_z());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_impl_._cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData Vector3i::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSourceCheck,
    Vector3i::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*Vector3i::GetClassData() const { return &_class_data_; }


void Vector3i::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg) {
  auto* const _this = static_cast<Vector3i*>(&to_msg);
  auto& from = static_cast<const Vector3i&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:esurfing.proto.math.Vector3i)
  GOOGLE_DCHECK_NE(&from, _this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_x() != 0) {
    _this->_internal_set_x(from._internal_x());
  }
  if (from._internal_y() != 0) {
    _this->_internal_set_y(from._internal_y());
  }
  if (from._internal_z() != 0) {
    _this->_internal_set_z(from._internal_z());
  }
  _this->_internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void Vector3i::CopyFrom(const Vector3i& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:esurfing.proto.math.Vector3i)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Vector3i::IsInitialized() const {
  return true;
}

void Vector3i::InternalSwap(Vector3i* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(Vector3i, _impl_.z_)
      + sizeof(Vector3i::_impl_.z_)
      - PROTOBUF_FIELD_OFFSET(Vector3i, _impl_.x_)>(
          reinterpret_cast<char*>(&_impl_.x_),
          reinterpret_cast<char*>(&other->_impl_.x_));
}

::PROTOBUF_NAMESPACE_ID::Metadata Vector3i::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_geo_2eproto_getter, &descriptor_table_geo_2eproto_once,
      file_level_metadata_geo_2eproto[0]);
}

// ===================================================================

class Polyline::_Internal {
 public:
};

Polyline::Polyline(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor(arena, is_message_owned);
  // @@protoc_insertion_point(arena_constructor:esurfing.proto.math.Polyline)
}
Polyline::Polyline(const Polyline& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  Polyline* const _this = this; (void)_this;
  new (&_impl_) Impl_{
      decltype(_impl_.points_){from._impl_.points_}
    , /*decltype(_impl_._cached_size_)*/{}};

  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:esurfing.proto.math.Polyline)
}

inline void Polyline::SharedCtor(
    ::_pb::Arena* arena, bool is_message_owned) {
  (void)arena;
  (void)is_message_owned;
  new (&_impl_) Impl_{
      decltype(_impl_.points_){arena}
    , /*decltype(_impl_._cached_size_)*/{}
  };
}

Polyline::~Polyline() {
  // @@protoc_insertion_point(destructor:esurfing.proto.math.Polyline)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void Polyline::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  _impl_.points_.~RepeatedPtrField();
}

void Polyline::SetCachedSize(int size) const {
  _impl_._cached_size_.Set(size);
}

void Polyline::Clear() {
// @@protoc_insertion_point(message_clear_start:esurfing.proto.math.Polyline)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _impl_.points_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* Polyline::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // repeated .esurfing.proto.math.Vector3d points = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_points(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<10>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* Polyline::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:esurfing.proto.math.Polyline)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .esurfing.proto.math.Vector3d points = 1;
  for (unsigned i = 0,
      n = static_cast<unsigned>(this->_internal_points_size()); i < n; i++) {
    const auto& repfield = this->_internal_points(i);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
        InternalWriteMessage(1, repfield, repfield.GetCachedSize(), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:esurfing.proto.math.Polyline)
  return target;
}

size_t Polyline::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:esurfing.proto.math.Polyline)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .esurfing.proto.math.Vector3d points = 1;
  total_size += 1UL * this->_internal_points_size();
  for (const auto& msg : this->_impl_.points_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_impl_._cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData Polyline::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSourceCheck,
    Polyline::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*Polyline::GetClassData() const { return &_class_data_; }


void Polyline::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg) {
  auto* const _this = static_cast<Polyline*>(&to_msg);
  auto& from = static_cast<const Polyline&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:esurfing.proto.math.Polyline)
  GOOGLE_DCHECK_NE(&from, _this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  _this->_impl_.points_.MergeFrom(from._impl_.points_);
  _this->_internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void Polyline::CopyFrom(const Polyline& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:esurfing.proto.math.Polyline)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Polyline::IsInitialized() const {
  return true;
}

void Polyline::InternalSwap(Polyline* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  _impl_.points_.InternalSwap(&other->_impl_.points_);
}

::PROTOBUF_NAMESPACE_ID::Metadata Polyline::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_geo_2eproto_getter, &descriptor_table_geo_2eproto_once,
      file_level_metadata_geo_2eproto[1]);
}

// ===================================================================

class Polygon::_Internal {
 public:
};

Polygon::Polygon(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor(arena, is_message_owned);
  // @@protoc_insertion_point(arena_constructor:esurfing.proto.math.Polygon)
}
Polygon::Polygon(const Polygon& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  Polygon* const _this = this; (void)_this;
  new (&_impl_) Impl_{
      decltype(_impl_.points_){from._impl_.points_}
    , /*decltype(_impl_._cached_size_)*/{}};

  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:esurfing.proto.math.Polygon)
}

inline void Polygon::SharedCtor(
    ::_pb::Arena* arena, bool is_message_owned) {
  (void)arena;
  (void)is_message_owned;
  new (&_impl_) Impl_{
      decltype(_impl_.points_){arena}
    , /*decltype(_impl_._cached_size_)*/{}
  };
}

Polygon::~Polygon() {
  // @@protoc_insertion_point(destructor:esurfing.proto.math.Polygon)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void Polygon::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  _impl_.points_.~RepeatedPtrField();
}

void Polygon::SetCachedSize(int size) const {
  _impl_._cached_size_.Set(size);
}

void Polygon::Clear() {
// @@protoc_insertion_point(message_clear_start:esurfing.proto.math.Polygon)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _impl_.points_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* Polygon::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // repeated .esurfing.proto.math.Vector2f points = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_points(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<10>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* Polygon::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:esurfing.proto.math.Polygon)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .esurfing.proto.math.Vector2f points = 1;
  for (unsigned i = 0,
      n = static_cast<unsigned>(this->_internal_points_size()); i < n; i++) {
    const auto& repfield = this->_internal_points(i);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
        InternalWriteMessage(1, repfield, repfield.GetCachedSize(), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:esurfing.proto.math.Polygon)
  return target;
}

size_t Polygon::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:esurfing.proto.math.Polygon)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .esurfing.proto.math.Vector2f points = 1;
  total_size += 1UL * this->_internal_points_size();
  for (const auto& msg : this->_impl_.points_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_impl_._cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData Polygon::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSourceCheck,
    Polygon::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*Polygon::GetClassData() const { return &_class_data_; }


void Polygon::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg) {
  auto* const _this = static_cast<Polygon*>(&to_msg);
  auto& from = static_cast<const Polygon&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:esurfing.proto.math.Polygon)
  GOOGLE_DCHECK_NE(&from, _this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  _this->_impl_.points_.MergeFrom(from._impl_.points_);
  _this->_internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void Polygon::CopyFrom(const Polygon& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:esurfing.proto.math.Polygon)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Polygon::IsInitialized() const {
  return true;
}

void Polygon::InternalSwap(Polygon* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  _impl_.points_.InternalSwap(&other->_impl_.points_);
}

::PROTOBUF_NAMESPACE_ID::Metadata Polygon::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_geo_2eproto_getter, &descriptor_table_geo_2eproto_once,
      file_level_metadata_geo_2eproto[2]);
}

// ===================================================================

class Points::_Internal {
 public:
};

Points::Points(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor(arena, is_message_owned);
  // @@protoc_insertion_point(arena_constructor:esurfing.proto.math.Points)
}
Points::Points(const Points& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  Points* const _this = this; (void)_this;
  new (&_impl_) Impl_{
      decltype(_impl_.points_){from._impl_.points_}
    , /*decltype(_impl_._cached_size_)*/{}};

  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:esurfing.proto.math.Points)
}

inline void Points::SharedCtor(
    ::_pb::Arena* arena, bool is_message_owned) {
  (void)arena;
  (void)is_message_owned;
  new (&_impl_) Impl_{
      decltype(_impl_.points_){arena}
    , /*decltype(_impl_._cached_size_)*/{}
  };
}

Points::~Points() {
  // @@protoc_insertion_point(destructor:esurfing.proto.math.Points)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void Points::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  _impl_.points_.~RepeatedPtrField();
}

void Points::SetCachedSize(int size) const {
  _impl_._cached_size_.Set(size);
}

void Points::Clear() {
// @@protoc_insertion_point(message_clear_start:esurfing.proto.math.Points)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _impl_.points_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* Points::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // repeated .esurfing.proto.math.Vector3d points = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_points(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<10>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* Points::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:esurfing.proto.math.Points)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .esurfing.proto.math.Vector3d points = 1;
  for (unsigned i = 0,
      n = static_cast<unsigned>(this->_internal_points_size()); i < n; i++) {
    const auto& repfield = this->_internal_points(i);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
        InternalWriteMessage(1, repfield, repfield.GetCachedSize(), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:esurfing.proto.math.Points)
  return target;
}

size_t Points::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:esurfing.proto.math.Points)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .esurfing.proto.math.Vector3d points = 1;
  total_size += 1UL * this->_internal_points_size();
  for (const auto& msg : this->_impl_.points_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_impl_._cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData Points::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSourceCheck,
    Points::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*Points::GetClassData() const { return &_class_data_; }


void Points::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg) {
  auto* const _this = static_cast<Points*>(&to_msg);
  auto& from = static_cast<const Points&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:esurfing.proto.math.Points)
  GOOGLE_DCHECK_NE(&from, _this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  _this->_impl_.points_.MergeFrom(from._impl_.points_);
  _this->_internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void Points::CopyFrom(const Points& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:esurfing.proto.math.Points)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Points::IsInitialized() const {
  return true;
}

void Points::InternalSwap(Points* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  _impl_.points_.InternalSwap(&other->_impl_.points_);
}

::PROTOBUF_NAMESPACE_ID::Metadata Points::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_geo_2eproto_getter, &descriptor_table_geo_2eproto_once,
      file_level_metadata_geo_2eproto[3]);
}

// ===================================================================

class Vector2d::_Internal {
 public:
};

Vector2d::Vector2d(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor(arena, is_message_owned);
  // @@protoc_insertion_point(arena_constructor:esurfing.proto.math.Vector2d)
}
Vector2d::Vector2d(const Vector2d& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  Vector2d* const _this = this; (void)_this;
  new (&_impl_) Impl_{
      decltype(_impl_.x_){}
    , decltype(_impl_.y_){}
    , /*decltype(_impl_._cached_size_)*/{}};

  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&_impl_.x_, &from._impl_.x_,
    static_cast<size_t>(reinterpret_cast<char*>(&_impl_.y_) -
    reinterpret_cast<char*>(&_impl_.x_)) + sizeof(_impl_.y_));
  // @@protoc_insertion_point(copy_constructor:esurfing.proto.math.Vector2d)
}

inline void Vector2d::SharedCtor(
    ::_pb::Arena* arena, bool is_message_owned) {
  (void)arena;
  (void)is_message_owned;
  new (&_impl_) Impl_{
      decltype(_impl_.x_){0}
    , decltype(_impl_.y_){0}
    , /*decltype(_impl_._cached_size_)*/{}
  };
}

Vector2d::~Vector2d() {
  // @@protoc_insertion_point(destructor:esurfing.proto.math.Vector2d)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void Vector2d::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void Vector2d::SetCachedSize(int size) const {
  _impl_._cached_size_.Set(size);
}

void Vector2d::Clear() {
// @@protoc_insertion_point(message_clear_start:esurfing.proto.math.Vector2d)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&_impl_.x_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&_impl_.y_) -
      reinterpret_cast<char*>(&_impl_.x_)) + sizeof(_impl_.y_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* Vector2d::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // double x = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 9)) {
          _impl_.x_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr);
          ptr += sizeof(double);
        } else
          goto handle_unusual;
        continue;
      // double y = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 17)) {
          _impl_.y_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr);
          ptr += sizeof(double);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* Vector2d::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:esurfing.proto.math.Vector2d)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // double x = 1;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_x = this->_internal_x();
  uint64_t raw_x;
  memcpy(&raw_x, &tmp_x, sizeof(tmp_x));
  if (raw_x != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteDoubleToArray(1, this->_internal_x(), target);
  }

  // double y = 2;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_y = this->_internal_y();
  uint64_t raw_y;
  memcpy(&raw_y, &tmp_y, sizeof(tmp_y));
  if (raw_y != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteDoubleToArray(2, this->_internal_y(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:esurfing.proto.math.Vector2d)
  return target;
}

size_t Vector2d::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:esurfing.proto.math.Vector2d)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // double x = 1;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_x = this->_internal_x();
  uint64_t raw_x;
  memcpy(&raw_x, &tmp_x, sizeof(tmp_x));
  if (raw_x != 0) {
    total_size += 1 + 8;
  }

  // double y = 2;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_y = this->_internal_y();
  uint64_t raw_y;
  memcpy(&raw_y, &tmp_y, sizeof(tmp_y));
  if (raw_y != 0) {
    total_size += 1 + 8;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_impl_._cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData Vector2d::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSourceCheck,
    Vector2d::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*Vector2d::GetClassData() const { return &_class_data_; }


void Vector2d::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg) {
  auto* const _this = static_cast<Vector2d*>(&to_msg);
  auto& from = static_cast<const Vector2d&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:esurfing.proto.math.Vector2d)
  GOOGLE_DCHECK_NE(&from, _this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_x = from._internal_x();
  uint64_t raw_x;
  memcpy(&raw_x, &tmp_x, sizeof(tmp_x));
  if (raw_x != 0) {
    _this->_internal_set_x(from._internal_x());
  }
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_y = from._internal_y();
  uint64_t raw_y;
  memcpy(&raw_y, &tmp_y, sizeof(tmp_y));
  if (raw_y != 0) {
    _this->_internal_set_y(from._internal_y());
  }
  _this->_internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void Vector2d::CopyFrom(const Vector2d& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:esurfing.proto.math.Vector2d)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Vector2d::IsInitialized() const {
  return true;
}

void Vector2d::InternalSwap(Vector2d* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(Vector2d, _impl_.y_)
      + sizeof(Vector2d::_impl_.y_)
      - PROTOBUF_FIELD_OFFSET(Vector2d, _impl_.x_)>(
          reinterpret_cast<char*>(&_impl_.x_),
          reinterpret_cast<char*>(&other->_impl_.x_));
}

::PROTOBUF_NAMESPACE_ID::Metadata Vector2d::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_geo_2eproto_getter, &descriptor_table_geo_2eproto_once,
      file_level_metadata_geo_2eproto[4]);
}

// ===================================================================

class Vector2f::_Internal {
 public:
};

Vector2f::Vector2f(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor(arena, is_message_owned);
  // @@protoc_insertion_point(arena_constructor:esurfing.proto.math.Vector2f)
}
Vector2f::Vector2f(const Vector2f& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  Vector2f* const _this = this; (void)_this;
  new (&_impl_) Impl_{
      decltype(_impl_.x_){}
    , decltype(_impl_.y_){}
    , /*decltype(_impl_._cached_size_)*/{}};

  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&_impl_.x_, &from._impl_.x_,
    static_cast<size_t>(reinterpret_cast<char*>(&_impl_.y_) -
    reinterpret_cast<char*>(&_impl_.x_)) + sizeof(_impl_.y_));
  // @@protoc_insertion_point(copy_constructor:esurfing.proto.math.Vector2f)
}

inline void Vector2f::SharedCtor(
    ::_pb::Arena* arena, bool is_message_owned) {
  (void)arena;
  (void)is_message_owned;
  new (&_impl_) Impl_{
      decltype(_impl_.x_){0}
    , decltype(_impl_.y_){0}
    , /*decltype(_impl_._cached_size_)*/{}
  };
}

Vector2f::~Vector2f() {
  // @@protoc_insertion_point(destructor:esurfing.proto.math.Vector2f)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void Vector2f::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void Vector2f::SetCachedSize(int size) const {
  _impl_._cached_size_.Set(size);
}

void Vector2f::Clear() {
// @@protoc_insertion_point(message_clear_start:esurfing.proto.math.Vector2f)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&_impl_.x_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&_impl_.y_) -
      reinterpret_cast<char*>(&_impl_.x_)) + sizeof(_impl_.y_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* Vector2f::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // float x = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 13)) {
          _impl_.x_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // float y = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 21)) {
          _impl_.y_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* Vector2f::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:esurfing.proto.math.Vector2f)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // float x = 1;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_x = this->_internal_x();
  uint32_t raw_x;
  memcpy(&raw_x, &tmp_x, sizeof(tmp_x));
  if (raw_x != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteFloatToArray(1, this->_internal_x(), target);
  }

  // float y = 2;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_y = this->_internal_y();
  uint32_t raw_y;
  memcpy(&raw_y, &tmp_y, sizeof(tmp_y));
  if (raw_y != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteFloatToArray(2, this->_internal_y(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:esurfing.proto.math.Vector2f)
  return target;
}

size_t Vector2f::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:esurfing.proto.math.Vector2f)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // float x = 1;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_x = this->_internal_x();
  uint32_t raw_x;
  memcpy(&raw_x, &tmp_x, sizeof(tmp_x));
  if (raw_x != 0) {
    total_size += 1 + 4;
  }

  // float y = 2;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_y = this->_internal_y();
  uint32_t raw_y;
  memcpy(&raw_y, &tmp_y, sizeof(tmp_y));
  if (raw_y != 0) {
    total_size += 1 + 4;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_impl_._cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData Vector2f::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSourceCheck,
    Vector2f::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*Vector2f::GetClassData() const { return &_class_data_; }


void Vector2f::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg) {
  auto* const _this = static_cast<Vector2f*>(&to_msg);
  auto& from = static_cast<const Vector2f&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:esurfing.proto.math.Vector2f)
  GOOGLE_DCHECK_NE(&from, _this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_x = from._internal_x();
  uint32_t raw_x;
  memcpy(&raw_x, &tmp_x, sizeof(tmp_x));
  if (raw_x != 0) {
    _this->_internal_set_x(from._internal_x());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_y = from._internal_y();
  uint32_t raw_y;
  memcpy(&raw_y, &tmp_y, sizeof(tmp_y));
  if (raw_y != 0) {
    _this->_internal_set_y(from._internal_y());
  }
  _this->_internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void Vector2f::CopyFrom(const Vector2f& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:esurfing.proto.math.Vector2f)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Vector2f::IsInitialized() const {
  return true;
}

void Vector2f::InternalSwap(Vector2f* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(Vector2f, _impl_.y_)
      + sizeof(Vector2f::_impl_.y_)
      - PROTOBUF_FIELD_OFFSET(Vector2f, _impl_.x_)>(
          reinterpret_cast<char*>(&_impl_.x_),
          reinterpret_cast<char*>(&other->_impl_.x_));
}

::PROTOBUF_NAMESPACE_ID::Metadata Vector2f::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_geo_2eproto_getter, &descriptor_table_geo_2eproto_once,
      file_level_metadata_geo_2eproto[5]);
}

// ===================================================================

class Vector3d::_Internal {
 public:
};

Vector3d::Vector3d(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor(arena, is_message_owned);
  // @@protoc_insertion_point(arena_constructor:esurfing.proto.math.Vector3d)
}
Vector3d::Vector3d(const Vector3d& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  Vector3d* const _this = this; (void)_this;
  new (&_impl_) Impl_{
      decltype(_impl_.x_){}
    , decltype(_impl_.y_){}
    , decltype(_impl_.z_){}
    , /*decltype(_impl_._cached_size_)*/{}};

  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&_impl_.x_, &from._impl_.x_,
    static_cast<size_t>(reinterpret_cast<char*>(&_impl_.z_) -
    reinterpret_cast<char*>(&_impl_.x_)) + sizeof(_impl_.z_));
  // @@protoc_insertion_point(copy_constructor:esurfing.proto.math.Vector3d)
}

inline void Vector3d::SharedCtor(
    ::_pb::Arena* arena, bool is_message_owned) {
  (void)arena;
  (void)is_message_owned;
  new (&_impl_) Impl_{
      decltype(_impl_.x_){0}
    , decltype(_impl_.y_){0}
    , decltype(_impl_.z_){0}
    , /*decltype(_impl_._cached_size_)*/{}
  };
}

Vector3d::~Vector3d() {
  // @@protoc_insertion_point(destructor:esurfing.proto.math.Vector3d)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void Vector3d::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void Vector3d::SetCachedSize(int size) const {
  _impl_._cached_size_.Set(size);
}

void Vector3d::Clear() {
// @@protoc_insertion_point(message_clear_start:esurfing.proto.math.Vector3d)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&_impl_.x_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&_impl_.z_) -
      reinterpret_cast<char*>(&_impl_.x_)) + sizeof(_impl_.z_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* Vector3d::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // double x = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 9)) {
          _impl_.x_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr);
          ptr += sizeof(double);
        } else
          goto handle_unusual;
        continue;
      // double y = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 17)) {
          _impl_.y_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr);
          ptr += sizeof(double);
        } else
          goto handle_unusual;
        continue;
      // double z = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 25)) {
          _impl_.z_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr);
          ptr += sizeof(double);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* Vector3d::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:esurfing.proto.math.Vector3d)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // double x = 1;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_x = this->_internal_x();
  uint64_t raw_x;
  memcpy(&raw_x, &tmp_x, sizeof(tmp_x));
  if (raw_x != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteDoubleToArray(1, this->_internal_x(), target);
  }

  // double y = 2;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_y = this->_internal_y();
  uint64_t raw_y;
  memcpy(&raw_y, &tmp_y, sizeof(tmp_y));
  if (raw_y != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteDoubleToArray(2, this->_internal_y(), target);
  }

  // double z = 3;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_z = this->_internal_z();
  uint64_t raw_z;
  memcpy(&raw_z, &tmp_z, sizeof(tmp_z));
  if (raw_z != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteDoubleToArray(3, this->_internal_z(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:esurfing.proto.math.Vector3d)
  return target;
}

size_t Vector3d::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:esurfing.proto.math.Vector3d)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // double x = 1;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_x = this->_internal_x();
  uint64_t raw_x;
  memcpy(&raw_x, &tmp_x, sizeof(tmp_x));
  if (raw_x != 0) {
    total_size += 1 + 8;
  }

  // double y = 2;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_y = this->_internal_y();
  uint64_t raw_y;
  memcpy(&raw_y, &tmp_y, sizeof(tmp_y));
  if (raw_y != 0) {
    total_size += 1 + 8;
  }

  // double z = 3;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_z = this->_internal_z();
  uint64_t raw_z;
  memcpy(&raw_z, &tmp_z, sizeof(tmp_z));
  if (raw_z != 0) {
    total_size += 1 + 8;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_impl_._cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData Vector3d::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSourceCheck,
    Vector3d::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*Vector3d::GetClassData() const { return &_class_data_; }


void Vector3d::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg) {
  auto* const _this = static_cast<Vector3d*>(&to_msg);
  auto& from = static_cast<const Vector3d&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:esurfing.proto.math.Vector3d)
  GOOGLE_DCHECK_NE(&from, _this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_x = from._internal_x();
  uint64_t raw_x;
  memcpy(&raw_x, &tmp_x, sizeof(tmp_x));
  if (raw_x != 0) {
    _this->_internal_set_x(from._internal_x());
  }
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_y = from._internal_y();
  uint64_t raw_y;
  memcpy(&raw_y, &tmp_y, sizeof(tmp_y));
  if (raw_y != 0) {
    _this->_internal_set_y(from._internal_y());
  }
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_z = from._internal_z();
  uint64_t raw_z;
  memcpy(&raw_z, &tmp_z, sizeof(tmp_z));
  if (raw_z != 0) {
    _this->_internal_set_z(from._internal_z());
  }
  _this->_internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void Vector3d::CopyFrom(const Vector3d& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:esurfing.proto.math.Vector3d)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Vector3d::IsInitialized() const {
  return true;
}

void Vector3d::InternalSwap(Vector3d* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(Vector3d, _impl_.z_)
      + sizeof(Vector3d::_impl_.z_)
      - PROTOBUF_FIELD_OFFSET(Vector3d, _impl_.x_)>(
          reinterpret_cast<char*>(&_impl_.x_),
          reinterpret_cast<char*>(&other->_impl_.x_));
}

::PROTOBUF_NAMESPACE_ID::Metadata Vector3d::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_geo_2eproto_getter, &descriptor_table_geo_2eproto_once,
      file_level_metadata_geo_2eproto[6]);
}

// ===================================================================

class Vector3f::_Internal {
 public:
};

Vector3f::Vector3f(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor(arena, is_message_owned);
  // @@protoc_insertion_point(arena_constructor:esurfing.proto.math.Vector3f)
}
Vector3f::Vector3f(const Vector3f& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  Vector3f* const _this = this; (void)_this;
  new (&_impl_) Impl_{
      decltype(_impl_.x_){}
    , decltype(_impl_.y_){}
    , decltype(_impl_.z_){}
    , /*decltype(_impl_._cached_size_)*/{}};

  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&_impl_.x_, &from._impl_.x_,
    static_cast<size_t>(reinterpret_cast<char*>(&_impl_.z_) -
    reinterpret_cast<char*>(&_impl_.x_)) + sizeof(_impl_.z_));
  // @@protoc_insertion_point(copy_constructor:esurfing.proto.math.Vector3f)
}

inline void Vector3f::SharedCtor(
    ::_pb::Arena* arena, bool is_message_owned) {
  (void)arena;
  (void)is_message_owned;
  new (&_impl_) Impl_{
      decltype(_impl_.x_){0}
    , decltype(_impl_.y_){0}
    , decltype(_impl_.z_){0}
    , /*decltype(_impl_._cached_size_)*/{}
  };
}

Vector3f::~Vector3f() {
  // @@protoc_insertion_point(destructor:esurfing.proto.math.Vector3f)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void Vector3f::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void Vector3f::SetCachedSize(int size) const {
  _impl_._cached_size_.Set(size);
}

void Vector3f::Clear() {
// @@protoc_insertion_point(message_clear_start:esurfing.proto.math.Vector3f)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&_impl_.x_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&_impl_.z_) -
      reinterpret_cast<char*>(&_impl_.x_)) + sizeof(_impl_.z_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* Vector3f::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // float x = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 13)) {
          _impl_.x_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // float y = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 21)) {
          _impl_.y_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // float z = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 29)) {
          _impl_.z_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* Vector3f::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:esurfing.proto.math.Vector3f)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // float x = 1;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_x = this->_internal_x();
  uint32_t raw_x;
  memcpy(&raw_x, &tmp_x, sizeof(tmp_x));
  if (raw_x != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteFloatToArray(1, this->_internal_x(), target);
  }

  // float y = 2;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_y = this->_internal_y();
  uint32_t raw_y;
  memcpy(&raw_y, &tmp_y, sizeof(tmp_y));
  if (raw_y != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteFloatToArray(2, this->_internal_y(), target);
  }

  // float z = 3;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_z = this->_internal_z();
  uint32_t raw_z;
  memcpy(&raw_z, &tmp_z, sizeof(tmp_z));
  if (raw_z != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteFloatToArray(3, this->_internal_z(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:esurfing.proto.math.Vector3f)
  return target;
}

size_t Vector3f::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:esurfing.proto.math.Vector3f)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // float x = 1;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_x = this->_internal_x();
  uint32_t raw_x;
  memcpy(&raw_x, &tmp_x, sizeof(tmp_x));
  if (raw_x != 0) {
    total_size += 1 + 4;
  }

  // float y = 2;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_y = this->_internal_y();
  uint32_t raw_y;
  memcpy(&raw_y, &tmp_y, sizeof(tmp_y));
  if (raw_y != 0) {
    total_size += 1 + 4;
  }

  // float z = 3;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_z = this->_internal_z();
  uint32_t raw_z;
  memcpy(&raw_z, &tmp_z, sizeof(tmp_z));
  if (raw_z != 0) {
    total_size += 1 + 4;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_impl_._cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData Vector3f::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSourceCheck,
    Vector3f::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*Vector3f::GetClassData() const { return &_class_data_; }


void Vector3f::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg) {
  auto* const _this = static_cast<Vector3f*>(&to_msg);
  auto& from = static_cast<const Vector3f&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:esurfing.proto.math.Vector3f)
  GOOGLE_DCHECK_NE(&from, _this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_x = from._internal_x();
  uint32_t raw_x;
  memcpy(&raw_x, &tmp_x, sizeof(tmp_x));
  if (raw_x != 0) {
    _this->_internal_set_x(from._internal_x());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_y = from._internal_y();
  uint32_t raw_y;
  memcpy(&raw_y, &tmp_y, sizeof(tmp_y));
  if (raw_y != 0) {
    _this->_internal_set_y(from._internal_y());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_z = from._internal_z();
  uint32_t raw_z;
  memcpy(&raw_z, &tmp_z, sizeof(tmp_z));
  if (raw_z != 0) {
    _this->_internal_set_z(from._internal_z());
  }
  _this->_internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void Vector3f::CopyFrom(const Vector3f& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:esurfing.proto.math.Vector3f)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Vector3f::IsInitialized() const {
  return true;
}

void Vector3f::InternalSwap(Vector3f* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(Vector3f, _impl_.z_)
      + sizeof(Vector3f::_impl_.z_)
      - PROTOBUF_FIELD_OFFSET(Vector3f, _impl_.x_)>(
          reinterpret_cast<char*>(&_impl_.x_),
          reinterpret_cast<char*>(&other->_impl_.x_));
}

::PROTOBUF_NAMESPACE_ID::Metadata Vector3f::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_geo_2eproto_getter, &descriptor_table_geo_2eproto_once,
      file_level_metadata_geo_2eproto[7]);
}

// ===================================================================

class Matrix2d::_Internal {
 public:
};

Matrix2d::Matrix2d(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor(arena, is_message_owned);
  // @@protoc_insertion_point(arena_constructor:esurfing.proto.math.Matrix2d)
}
Matrix2d::Matrix2d(const Matrix2d& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  Matrix2d* const _this = this; (void)_this;
  new (&_impl_) Impl_{
      decltype(_impl_.e00_){}
    , decltype(_impl_.e01_){}
    , decltype(_impl_.e10_){}
    , decltype(_impl_.e11_){}
    , /*decltype(_impl_._cached_size_)*/{}};

  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&_impl_.e00_, &from._impl_.e00_,
    static_cast<size_t>(reinterpret_cast<char*>(&_impl_.e11_) -
    reinterpret_cast<char*>(&_impl_.e00_)) + sizeof(_impl_.e11_));
  // @@protoc_insertion_point(copy_constructor:esurfing.proto.math.Matrix2d)
}

inline void Matrix2d::SharedCtor(
    ::_pb::Arena* arena, bool is_message_owned) {
  (void)arena;
  (void)is_message_owned;
  new (&_impl_) Impl_{
      decltype(_impl_.e00_){0}
    , decltype(_impl_.e01_){0}
    , decltype(_impl_.e10_){0}
    , decltype(_impl_.e11_){0}
    , /*decltype(_impl_._cached_size_)*/{}
  };
}

Matrix2d::~Matrix2d() {
  // @@protoc_insertion_point(destructor:esurfing.proto.math.Matrix2d)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void Matrix2d::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void Matrix2d::SetCachedSize(int size) const {
  _impl_._cached_size_.Set(size);
}

void Matrix2d::Clear() {
// @@protoc_insertion_point(message_clear_start:esurfing.proto.math.Matrix2d)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&_impl_.e00_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&_impl_.e11_) -
      reinterpret_cast<char*>(&_impl_.e00_)) + sizeof(_impl_.e11_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* Matrix2d::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // double e00 = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 9)) {
          _impl_.e00_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr);
          ptr += sizeof(double);
        } else
          goto handle_unusual;
        continue;
      // double e01 = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 17)) {
          _impl_.e01_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr);
          ptr += sizeof(double);
        } else
          goto handle_unusual;
        continue;
      // double e10 = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 25)) {
          _impl_.e10_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr);
          ptr += sizeof(double);
        } else
          goto handle_unusual;
        continue;
      // double e11 = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 33)) {
          _impl_.e11_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr);
          ptr += sizeof(double);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* Matrix2d::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:esurfing.proto.math.Matrix2d)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // double e00 = 1;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_e00 = this->_internal_e00();
  uint64_t raw_e00;
  memcpy(&raw_e00, &tmp_e00, sizeof(tmp_e00));
  if (raw_e00 != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteDoubleToArray(1, this->_internal_e00(), target);
  }

  // double e01 = 2;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_e01 = this->_internal_e01();
  uint64_t raw_e01;
  memcpy(&raw_e01, &tmp_e01, sizeof(tmp_e01));
  if (raw_e01 != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteDoubleToArray(2, this->_internal_e01(), target);
  }

  // double e10 = 3;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_e10 = this->_internal_e10();
  uint64_t raw_e10;
  memcpy(&raw_e10, &tmp_e10, sizeof(tmp_e10));
  if (raw_e10 != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteDoubleToArray(3, this->_internal_e10(), target);
  }

  // double e11 = 4;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_e11 = this->_internal_e11();
  uint64_t raw_e11;
  memcpy(&raw_e11, &tmp_e11, sizeof(tmp_e11));
  if (raw_e11 != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteDoubleToArray(4, this->_internal_e11(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:esurfing.proto.math.Matrix2d)
  return target;
}

size_t Matrix2d::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:esurfing.proto.math.Matrix2d)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // double e00 = 1;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_e00 = this->_internal_e00();
  uint64_t raw_e00;
  memcpy(&raw_e00, &tmp_e00, sizeof(tmp_e00));
  if (raw_e00 != 0) {
    total_size += 1 + 8;
  }

  // double e01 = 2;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_e01 = this->_internal_e01();
  uint64_t raw_e01;
  memcpy(&raw_e01, &tmp_e01, sizeof(tmp_e01));
  if (raw_e01 != 0) {
    total_size += 1 + 8;
  }

  // double e10 = 3;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_e10 = this->_internal_e10();
  uint64_t raw_e10;
  memcpy(&raw_e10, &tmp_e10, sizeof(tmp_e10));
  if (raw_e10 != 0) {
    total_size += 1 + 8;
  }

  // double e11 = 4;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_e11 = this->_internal_e11();
  uint64_t raw_e11;
  memcpy(&raw_e11, &tmp_e11, sizeof(tmp_e11));
  if (raw_e11 != 0) {
    total_size += 1 + 8;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_impl_._cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData Matrix2d::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSourceCheck,
    Matrix2d::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*Matrix2d::GetClassData() const { return &_class_data_; }


void Matrix2d::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg) {
  auto* const _this = static_cast<Matrix2d*>(&to_msg);
  auto& from = static_cast<const Matrix2d&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:esurfing.proto.math.Matrix2d)
  GOOGLE_DCHECK_NE(&from, _this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_e00 = from._internal_e00();
  uint64_t raw_e00;
  memcpy(&raw_e00, &tmp_e00, sizeof(tmp_e00));
  if (raw_e00 != 0) {
    _this->_internal_set_e00(from._internal_e00());
  }
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_e01 = from._internal_e01();
  uint64_t raw_e01;
  memcpy(&raw_e01, &tmp_e01, sizeof(tmp_e01));
  if (raw_e01 != 0) {
    _this->_internal_set_e01(from._internal_e01());
  }
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_e10 = from._internal_e10();
  uint64_t raw_e10;
  memcpy(&raw_e10, &tmp_e10, sizeof(tmp_e10));
  if (raw_e10 != 0) {
    _this->_internal_set_e10(from._internal_e10());
  }
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_e11 = from._internal_e11();
  uint64_t raw_e11;
  memcpy(&raw_e11, &tmp_e11, sizeof(tmp_e11));
  if (raw_e11 != 0) {
    _this->_internal_set_e11(from._internal_e11());
  }
  _this->_internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void Matrix2d::CopyFrom(const Matrix2d& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:esurfing.proto.math.Matrix2d)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Matrix2d::IsInitialized() const {
  return true;
}

void Matrix2d::InternalSwap(Matrix2d* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(Matrix2d, _impl_.e11_)
      + sizeof(Matrix2d::_impl_.e11_)
      - PROTOBUF_FIELD_OFFSET(Matrix2d, _impl_.e00_)>(
          reinterpret_cast<char*>(&_impl_.e00_),
          reinterpret_cast<char*>(&other->_impl_.e00_));
}

::PROTOBUF_NAMESPACE_ID::Metadata Matrix2d::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_geo_2eproto_getter, &descriptor_table_geo_2eproto_once,
      file_level_metadata_geo_2eproto[8]);
}

// ===================================================================

class Matrix2f::_Internal {
 public:
};

Matrix2f::Matrix2f(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor(arena, is_message_owned);
  // @@protoc_insertion_point(arena_constructor:esurfing.proto.math.Matrix2f)
}
Matrix2f::Matrix2f(const Matrix2f& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  Matrix2f* const _this = this; (void)_this;
  new (&_impl_) Impl_{
      decltype(_impl_.e00_){}
    , decltype(_impl_.e01_){}
    , decltype(_impl_.e10_){}
    , decltype(_impl_.e11_){}
    , /*decltype(_impl_._cached_size_)*/{}};

  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&_impl_.e00_, &from._impl_.e00_,
    static_cast<size_t>(reinterpret_cast<char*>(&_impl_.e11_) -
    reinterpret_cast<char*>(&_impl_.e00_)) + sizeof(_impl_.e11_));
  // @@protoc_insertion_point(copy_constructor:esurfing.proto.math.Matrix2f)
}

inline void Matrix2f::SharedCtor(
    ::_pb::Arena* arena, bool is_message_owned) {
  (void)arena;
  (void)is_message_owned;
  new (&_impl_) Impl_{
      decltype(_impl_.e00_){0}
    , decltype(_impl_.e01_){0}
    , decltype(_impl_.e10_){0}
    , decltype(_impl_.e11_){0}
    , /*decltype(_impl_._cached_size_)*/{}
  };
}

Matrix2f::~Matrix2f() {
  // @@protoc_insertion_point(destructor:esurfing.proto.math.Matrix2f)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void Matrix2f::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void Matrix2f::SetCachedSize(int size) const {
  _impl_._cached_size_.Set(size);
}

void Matrix2f::Clear() {
// @@protoc_insertion_point(message_clear_start:esurfing.proto.math.Matrix2f)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&_impl_.e00_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&_impl_.e11_) -
      reinterpret_cast<char*>(&_impl_.e00_)) + sizeof(_impl_.e11_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* Matrix2f::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // float e00 = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 13)) {
          _impl_.e00_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // float e01 = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 21)) {
          _impl_.e01_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // float e10 = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 29)) {
          _impl_.e10_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // float e11 = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 37)) {
          _impl_.e11_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* Matrix2f::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:esurfing.proto.math.Matrix2f)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // float e00 = 1;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_e00 = this->_internal_e00();
  uint32_t raw_e00;
  memcpy(&raw_e00, &tmp_e00, sizeof(tmp_e00));
  if (raw_e00 != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteFloatToArray(1, this->_internal_e00(), target);
  }

  // float e01 = 2;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_e01 = this->_internal_e01();
  uint32_t raw_e01;
  memcpy(&raw_e01, &tmp_e01, sizeof(tmp_e01));
  if (raw_e01 != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteFloatToArray(2, this->_internal_e01(), target);
  }

  // float e10 = 3;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_e10 = this->_internal_e10();
  uint32_t raw_e10;
  memcpy(&raw_e10, &tmp_e10, sizeof(tmp_e10));
  if (raw_e10 != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteFloatToArray(3, this->_internal_e10(), target);
  }

  // float e11 = 4;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_e11 = this->_internal_e11();
  uint32_t raw_e11;
  memcpy(&raw_e11, &tmp_e11, sizeof(tmp_e11));
  if (raw_e11 != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteFloatToArray(4, this->_internal_e11(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:esurfing.proto.math.Matrix2f)
  return target;
}

size_t Matrix2f::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:esurfing.proto.math.Matrix2f)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // float e00 = 1;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_e00 = this->_internal_e00();
  uint32_t raw_e00;
  memcpy(&raw_e00, &tmp_e00, sizeof(tmp_e00));
  if (raw_e00 != 0) {
    total_size += 1 + 4;
  }

  // float e01 = 2;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_e01 = this->_internal_e01();
  uint32_t raw_e01;
  memcpy(&raw_e01, &tmp_e01, sizeof(tmp_e01));
  if (raw_e01 != 0) {
    total_size += 1 + 4;
  }

  // float e10 = 3;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_e10 = this->_internal_e10();
  uint32_t raw_e10;
  memcpy(&raw_e10, &tmp_e10, sizeof(tmp_e10));
  if (raw_e10 != 0) {
    total_size += 1 + 4;
  }

  // float e11 = 4;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_e11 = this->_internal_e11();
  uint32_t raw_e11;
  memcpy(&raw_e11, &tmp_e11, sizeof(tmp_e11));
  if (raw_e11 != 0) {
    total_size += 1 + 4;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_impl_._cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData Matrix2f::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSourceCheck,
    Matrix2f::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*Matrix2f::GetClassData() const { return &_class_data_; }


void Matrix2f::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg) {
  auto* const _this = static_cast<Matrix2f*>(&to_msg);
  auto& from = static_cast<const Matrix2f&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:esurfing.proto.math.Matrix2f)
  GOOGLE_DCHECK_NE(&from, _this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_e00 = from._internal_e00();
  uint32_t raw_e00;
  memcpy(&raw_e00, &tmp_e00, sizeof(tmp_e00));
  if (raw_e00 != 0) {
    _this->_internal_set_e00(from._internal_e00());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_e01 = from._internal_e01();
  uint32_t raw_e01;
  memcpy(&raw_e01, &tmp_e01, sizeof(tmp_e01));
  if (raw_e01 != 0) {
    _this->_internal_set_e01(from._internal_e01());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_e10 = from._internal_e10();
  uint32_t raw_e10;
  memcpy(&raw_e10, &tmp_e10, sizeof(tmp_e10));
  if (raw_e10 != 0) {
    _this->_internal_set_e10(from._internal_e10());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_e11 = from._internal_e11();
  uint32_t raw_e11;
  memcpy(&raw_e11, &tmp_e11, sizeof(tmp_e11));
  if (raw_e11 != 0) {
    _this->_internal_set_e11(from._internal_e11());
  }
  _this->_internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void Matrix2f::CopyFrom(const Matrix2f& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:esurfing.proto.math.Matrix2f)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Matrix2f::IsInitialized() const {
  return true;
}

void Matrix2f::InternalSwap(Matrix2f* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(Matrix2f, _impl_.e11_)
      + sizeof(Matrix2f::_impl_.e11_)
      - PROTOBUF_FIELD_OFFSET(Matrix2f, _impl_.e00_)>(
          reinterpret_cast<char*>(&_impl_.e00_),
          reinterpret_cast<char*>(&other->_impl_.e00_));
}

::PROTOBUF_NAMESPACE_ID::Metadata Matrix2f::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_geo_2eproto_getter, &descriptor_table_geo_2eproto_once,
      file_level_metadata_geo_2eproto[9]);
}

// ===================================================================

class Matrix3d::_Internal {
 public:
};

Matrix3d::Matrix3d(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor(arena, is_message_owned);
  // @@protoc_insertion_point(arena_constructor:esurfing.proto.math.Matrix3d)
}
Matrix3d::Matrix3d(const Matrix3d& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  Matrix3d* const _this = this; (void)_this;
  new (&_impl_) Impl_{
      decltype(_impl_.e00_){}
    , decltype(_impl_.e01_){}
    , decltype(_impl_.e02_){}
    , decltype(_impl_.e10_){}
    , decltype(_impl_.e11_){}
    , decltype(_impl_.e12_){}
    , decltype(_impl_.e20_){}
    , decltype(_impl_.e21_){}
    , decltype(_impl_.e22_){}
    , /*decltype(_impl_._cached_size_)*/{}};

  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&_impl_.e00_, &from._impl_.e00_,
    static_cast<size_t>(reinterpret_cast<char*>(&_impl_.e22_) -
    reinterpret_cast<char*>(&_impl_.e00_)) + sizeof(_impl_.e22_));
  // @@protoc_insertion_point(copy_constructor:esurfing.proto.math.Matrix3d)
}

inline void Matrix3d::SharedCtor(
    ::_pb::Arena* arena, bool is_message_owned) {
  (void)arena;
  (void)is_message_owned;
  new (&_impl_) Impl_{
      decltype(_impl_.e00_){0}
    , decltype(_impl_.e01_){0}
    , decltype(_impl_.e02_){0}
    , decltype(_impl_.e10_){0}
    , decltype(_impl_.e11_){0}
    , decltype(_impl_.e12_){0}
    , decltype(_impl_.e20_){0}
    , decltype(_impl_.e21_){0}
    , decltype(_impl_.e22_){0}
    , /*decltype(_impl_._cached_size_)*/{}
  };
}

Matrix3d::~Matrix3d() {
  // @@protoc_insertion_point(destructor:esurfing.proto.math.Matrix3d)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void Matrix3d::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void Matrix3d::SetCachedSize(int size) const {
  _impl_._cached_size_.Set(size);
}

void Matrix3d::Clear() {
// @@protoc_insertion_point(message_clear_start:esurfing.proto.math.Matrix3d)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&_impl_.e00_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&_impl_.e22_) -
      reinterpret_cast<char*>(&_impl_.e00_)) + sizeof(_impl_.e22_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* Matrix3d::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // double e00 = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 9)) {
          _impl_.e00_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr);
          ptr += sizeof(double);
        } else
          goto handle_unusual;
        continue;
      // double e01 = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 17)) {
          _impl_.e01_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr);
          ptr += sizeof(double);
        } else
          goto handle_unusual;
        continue;
      // double e02 = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 25)) {
          _impl_.e02_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr);
          ptr += sizeof(double);
        } else
          goto handle_unusual;
        continue;
      // double e10 = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 33)) {
          _impl_.e10_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr);
          ptr += sizeof(double);
        } else
          goto handle_unusual;
        continue;
      // double e11 = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 41)) {
          _impl_.e11_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr);
          ptr += sizeof(double);
        } else
          goto handle_unusual;
        continue;
      // double e12 = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 49)) {
          _impl_.e12_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr);
          ptr += sizeof(double);
        } else
          goto handle_unusual;
        continue;
      // double e20 = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 57)) {
          _impl_.e20_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr);
          ptr += sizeof(double);
        } else
          goto handle_unusual;
        continue;
      // double e21 = 8;
      case 8:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 65)) {
          _impl_.e21_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr);
          ptr += sizeof(double);
        } else
          goto handle_unusual;
        continue;
      // double e22 = 9;
      case 9:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 73)) {
          _impl_.e22_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr);
          ptr += sizeof(double);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* Matrix3d::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:esurfing.proto.math.Matrix3d)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // double e00 = 1;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_e00 = this->_internal_e00();
  uint64_t raw_e00;
  memcpy(&raw_e00, &tmp_e00, sizeof(tmp_e00));
  if (raw_e00 != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteDoubleToArray(1, this->_internal_e00(), target);
  }

  // double e01 = 2;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_e01 = this->_internal_e01();
  uint64_t raw_e01;
  memcpy(&raw_e01, &tmp_e01, sizeof(tmp_e01));
  if (raw_e01 != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteDoubleToArray(2, this->_internal_e01(), target);
  }

  // double e02 = 3;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_e02 = this->_internal_e02();
  uint64_t raw_e02;
  memcpy(&raw_e02, &tmp_e02, sizeof(tmp_e02));
  if (raw_e02 != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteDoubleToArray(3, this->_internal_e02(), target);
  }

  // double e10 = 4;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_e10 = this->_internal_e10();
  uint64_t raw_e10;
  memcpy(&raw_e10, &tmp_e10, sizeof(tmp_e10));
  if (raw_e10 != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteDoubleToArray(4, this->_internal_e10(), target);
  }

  // double e11 = 5;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_e11 = this->_internal_e11();
  uint64_t raw_e11;
  memcpy(&raw_e11, &tmp_e11, sizeof(tmp_e11));
  if (raw_e11 != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteDoubleToArray(5, this->_internal_e11(), target);
  }

  // double e12 = 6;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_e12 = this->_internal_e12();
  uint64_t raw_e12;
  memcpy(&raw_e12, &tmp_e12, sizeof(tmp_e12));
  if (raw_e12 != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteDoubleToArray(6, this->_internal_e12(), target);
  }

  // double e20 = 7;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_e20 = this->_internal_e20();
  uint64_t raw_e20;
  memcpy(&raw_e20, &tmp_e20, sizeof(tmp_e20));
  if (raw_e20 != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteDoubleToArray(7, this->_internal_e20(), target);
  }

  // double e21 = 8;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_e21 = this->_internal_e21();
  uint64_t raw_e21;
  memcpy(&raw_e21, &tmp_e21, sizeof(tmp_e21));
  if (raw_e21 != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteDoubleToArray(8, this->_internal_e21(), target);
  }

  // double e22 = 9;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_e22 = this->_internal_e22();
  uint64_t raw_e22;
  memcpy(&raw_e22, &tmp_e22, sizeof(tmp_e22));
  if (raw_e22 != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteDoubleToArray(9, this->_internal_e22(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:esurfing.proto.math.Matrix3d)
  return target;
}

size_t Matrix3d::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:esurfing.proto.math.Matrix3d)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // double e00 = 1;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_e00 = this->_internal_e00();
  uint64_t raw_e00;
  memcpy(&raw_e00, &tmp_e00, sizeof(tmp_e00));
  if (raw_e00 != 0) {
    total_size += 1 + 8;
  }

  // double e01 = 2;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_e01 = this->_internal_e01();
  uint64_t raw_e01;
  memcpy(&raw_e01, &tmp_e01, sizeof(tmp_e01));
  if (raw_e01 != 0) {
    total_size += 1 + 8;
  }

  // double e02 = 3;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_e02 = this->_internal_e02();
  uint64_t raw_e02;
  memcpy(&raw_e02, &tmp_e02, sizeof(tmp_e02));
  if (raw_e02 != 0) {
    total_size += 1 + 8;
  }

  // double e10 = 4;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_e10 = this->_internal_e10();
  uint64_t raw_e10;
  memcpy(&raw_e10, &tmp_e10, sizeof(tmp_e10));
  if (raw_e10 != 0) {
    total_size += 1 + 8;
  }

  // double e11 = 5;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_e11 = this->_internal_e11();
  uint64_t raw_e11;
  memcpy(&raw_e11, &tmp_e11, sizeof(tmp_e11));
  if (raw_e11 != 0) {
    total_size += 1 + 8;
  }

  // double e12 = 6;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_e12 = this->_internal_e12();
  uint64_t raw_e12;
  memcpy(&raw_e12, &tmp_e12, sizeof(tmp_e12));
  if (raw_e12 != 0) {
    total_size += 1 + 8;
  }

  // double e20 = 7;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_e20 = this->_internal_e20();
  uint64_t raw_e20;
  memcpy(&raw_e20, &tmp_e20, sizeof(tmp_e20));
  if (raw_e20 != 0) {
    total_size += 1 + 8;
  }

  // double e21 = 8;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_e21 = this->_internal_e21();
  uint64_t raw_e21;
  memcpy(&raw_e21, &tmp_e21, sizeof(tmp_e21));
  if (raw_e21 != 0) {
    total_size += 1 + 8;
  }

  // double e22 = 9;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_e22 = this->_internal_e22();
  uint64_t raw_e22;
  memcpy(&raw_e22, &tmp_e22, sizeof(tmp_e22));
  if (raw_e22 != 0) {
    total_size += 1 + 8;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_impl_._cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData Matrix3d::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSourceCheck,
    Matrix3d::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*Matrix3d::GetClassData() const { return &_class_data_; }


void Matrix3d::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg) {
  auto* const _this = static_cast<Matrix3d*>(&to_msg);
  auto& from = static_cast<const Matrix3d&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:esurfing.proto.math.Matrix3d)
  GOOGLE_DCHECK_NE(&from, _this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_e00 = from._internal_e00();
  uint64_t raw_e00;
  memcpy(&raw_e00, &tmp_e00, sizeof(tmp_e00));
  if (raw_e00 != 0) {
    _this->_internal_set_e00(from._internal_e00());
  }
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_e01 = from._internal_e01();
  uint64_t raw_e01;
  memcpy(&raw_e01, &tmp_e01, sizeof(tmp_e01));
  if (raw_e01 != 0) {
    _this->_internal_set_e01(from._internal_e01());
  }
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_e02 = from._internal_e02();
  uint64_t raw_e02;
  memcpy(&raw_e02, &tmp_e02, sizeof(tmp_e02));
  if (raw_e02 != 0) {
    _this->_internal_set_e02(from._internal_e02());
  }
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_e10 = from._internal_e10();
  uint64_t raw_e10;
  memcpy(&raw_e10, &tmp_e10, sizeof(tmp_e10));
  if (raw_e10 != 0) {
    _this->_internal_set_e10(from._internal_e10());
  }
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_e11 = from._internal_e11();
  uint64_t raw_e11;
  memcpy(&raw_e11, &tmp_e11, sizeof(tmp_e11));
  if (raw_e11 != 0) {
    _this->_internal_set_e11(from._internal_e11());
  }
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_e12 = from._internal_e12();
  uint64_t raw_e12;
  memcpy(&raw_e12, &tmp_e12, sizeof(tmp_e12));
  if (raw_e12 != 0) {
    _this->_internal_set_e12(from._internal_e12());
  }
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_e20 = from._internal_e20();
  uint64_t raw_e20;
  memcpy(&raw_e20, &tmp_e20, sizeof(tmp_e20));
  if (raw_e20 != 0) {
    _this->_internal_set_e20(from._internal_e20());
  }
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_e21 = from._internal_e21();
  uint64_t raw_e21;
  memcpy(&raw_e21, &tmp_e21, sizeof(tmp_e21));
  if (raw_e21 != 0) {
    _this->_internal_set_e21(from._internal_e21());
  }
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_e22 = from._internal_e22();
  uint64_t raw_e22;
  memcpy(&raw_e22, &tmp_e22, sizeof(tmp_e22));
  if (raw_e22 != 0) {
    _this->_internal_set_e22(from._internal_e22());
  }
  _this->_internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void Matrix3d::CopyFrom(const Matrix3d& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:esurfing.proto.math.Matrix3d)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Matrix3d::IsInitialized() const {
  return true;
}

void Matrix3d::InternalSwap(Matrix3d* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(Matrix3d, _impl_.e22_)
      + sizeof(Matrix3d::_impl_.e22_)
      - PROTOBUF_FIELD_OFFSET(Matrix3d, _impl_.e00_)>(
          reinterpret_cast<char*>(&_impl_.e00_),
          reinterpret_cast<char*>(&other->_impl_.e00_));
}

::PROTOBUF_NAMESPACE_ID::Metadata Matrix3d::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_geo_2eproto_getter, &descriptor_table_geo_2eproto_once,
      file_level_metadata_geo_2eproto[10]);
}

// ===================================================================

class Matrix3f::_Internal {
 public:
};

Matrix3f::Matrix3f(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor(arena, is_message_owned);
  // @@protoc_insertion_point(arena_constructor:esurfing.proto.math.Matrix3f)
}
Matrix3f::Matrix3f(const Matrix3f& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  Matrix3f* const _this = this; (void)_this;
  new (&_impl_) Impl_{
      decltype(_impl_.e00_){}
    , decltype(_impl_.e01_){}
    , decltype(_impl_.e02_){}
    , decltype(_impl_.e10_){}
    , decltype(_impl_.e11_){}
    , decltype(_impl_.e12_){}
    , decltype(_impl_.e20_){}
    , decltype(_impl_.e21_){}
    , decltype(_impl_.e22_){}
    , /*decltype(_impl_._cached_size_)*/{}};

  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&_impl_.e00_, &from._impl_.e00_,
    static_cast<size_t>(reinterpret_cast<char*>(&_impl_.e22_) -
    reinterpret_cast<char*>(&_impl_.e00_)) + sizeof(_impl_.e22_));
  // @@protoc_insertion_point(copy_constructor:esurfing.proto.math.Matrix3f)
}

inline void Matrix3f::SharedCtor(
    ::_pb::Arena* arena, bool is_message_owned) {
  (void)arena;
  (void)is_message_owned;
  new (&_impl_) Impl_{
      decltype(_impl_.e00_){0}
    , decltype(_impl_.e01_){0}
    , decltype(_impl_.e02_){0}
    , decltype(_impl_.e10_){0}
    , decltype(_impl_.e11_){0}
    , decltype(_impl_.e12_){0}
    , decltype(_impl_.e20_){0}
    , decltype(_impl_.e21_){0}
    , decltype(_impl_.e22_){0}
    , /*decltype(_impl_._cached_size_)*/{}
  };
}

Matrix3f::~Matrix3f() {
  // @@protoc_insertion_point(destructor:esurfing.proto.math.Matrix3f)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void Matrix3f::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void Matrix3f::SetCachedSize(int size) const {
  _impl_._cached_size_.Set(size);
}

void Matrix3f::Clear() {
// @@protoc_insertion_point(message_clear_start:esurfing.proto.math.Matrix3f)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&_impl_.e00_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&_impl_.e22_) -
      reinterpret_cast<char*>(&_impl_.e00_)) + sizeof(_impl_.e22_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* Matrix3f::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // float e00 = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 13)) {
          _impl_.e00_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // float e01 = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 21)) {
          _impl_.e01_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // float e02 = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 29)) {
          _impl_.e02_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // float e10 = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 37)) {
          _impl_.e10_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // float e11 = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 45)) {
          _impl_.e11_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // float e12 = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 53)) {
          _impl_.e12_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // float e20 = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 61)) {
          _impl_.e20_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // float e21 = 8;
      case 8:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 69)) {
          _impl_.e21_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // float e22 = 9;
      case 9:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 77)) {
          _impl_.e22_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* Matrix3f::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:esurfing.proto.math.Matrix3f)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // float e00 = 1;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_e00 = this->_internal_e00();
  uint32_t raw_e00;
  memcpy(&raw_e00, &tmp_e00, sizeof(tmp_e00));
  if (raw_e00 != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteFloatToArray(1, this->_internal_e00(), target);
  }

  // float e01 = 2;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_e01 = this->_internal_e01();
  uint32_t raw_e01;
  memcpy(&raw_e01, &tmp_e01, sizeof(tmp_e01));
  if (raw_e01 != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteFloatToArray(2, this->_internal_e01(), target);
  }

  // float e02 = 3;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_e02 = this->_internal_e02();
  uint32_t raw_e02;
  memcpy(&raw_e02, &tmp_e02, sizeof(tmp_e02));
  if (raw_e02 != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteFloatToArray(3, this->_internal_e02(), target);
  }

  // float e10 = 4;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_e10 = this->_internal_e10();
  uint32_t raw_e10;
  memcpy(&raw_e10, &tmp_e10, sizeof(tmp_e10));
  if (raw_e10 != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteFloatToArray(4, this->_internal_e10(), target);
  }

  // float e11 = 5;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_e11 = this->_internal_e11();
  uint32_t raw_e11;
  memcpy(&raw_e11, &tmp_e11, sizeof(tmp_e11));
  if (raw_e11 != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteFloatToArray(5, this->_internal_e11(), target);
  }

  // float e12 = 6;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_e12 = this->_internal_e12();
  uint32_t raw_e12;
  memcpy(&raw_e12, &tmp_e12, sizeof(tmp_e12));
  if (raw_e12 != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteFloatToArray(6, this->_internal_e12(), target);
  }

  // float e20 = 7;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_e20 = this->_internal_e20();
  uint32_t raw_e20;
  memcpy(&raw_e20, &tmp_e20, sizeof(tmp_e20));
  if (raw_e20 != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteFloatToArray(7, this->_internal_e20(), target);
  }

  // float e21 = 8;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_e21 = this->_internal_e21();
  uint32_t raw_e21;
  memcpy(&raw_e21, &tmp_e21, sizeof(tmp_e21));
  if (raw_e21 != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteFloatToArray(8, this->_internal_e21(), target);
  }

  // float e22 = 9;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_e22 = this->_internal_e22();
  uint32_t raw_e22;
  memcpy(&raw_e22, &tmp_e22, sizeof(tmp_e22));
  if (raw_e22 != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteFloatToArray(9, this->_internal_e22(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:esurfing.proto.math.Matrix3f)
  return target;
}

size_t Matrix3f::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:esurfing.proto.math.Matrix3f)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // float e00 = 1;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_e00 = this->_internal_e00();
  uint32_t raw_e00;
  memcpy(&raw_e00, &tmp_e00, sizeof(tmp_e00));
  if (raw_e00 != 0) {
    total_size += 1 + 4;
  }

  // float e01 = 2;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_e01 = this->_internal_e01();
  uint32_t raw_e01;
  memcpy(&raw_e01, &tmp_e01, sizeof(tmp_e01));
  if (raw_e01 != 0) {
    total_size += 1 + 4;
  }

  // float e02 = 3;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_e02 = this->_internal_e02();
  uint32_t raw_e02;
  memcpy(&raw_e02, &tmp_e02, sizeof(tmp_e02));
  if (raw_e02 != 0) {
    total_size += 1 + 4;
  }

  // float e10 = 4;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_e10 = this->_internal_e10();
  uint32_t raw_e10;
  memcpy(&raw_e10, &tmp_e10, sizeof(tmp_e10));
  if (raw_e10 != 0) {
    total_size += 1 + 4;
  }

  // float e11 = 5;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_e11 = this->_internal_e11();
  uint32_t raw_e11;
  memcpy(&raw_e11, &tmp_e11, sizeof(tmp_e11));
  if (raw_e11 != 0) {
    total_size += 1 + 4;
  }

  // float e12 = 6;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_e12 = this->_internal_e12();
  uint32_t raw_e12;
  memcpy(&raw_e12, &tmp_e12, sizeof(tmp_e12));
  if (raw_e12 != 0) {
    total_size += 1 + 4;
  }

  // float e20 = 7;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_e20 = this->_internal_e20();
  uint32_t raw_e20;
  memcpy(&raw_e20, &tmp_e20, sizeof(tmp_e20));
  if (raw_e20 != 0) {
    total_size += 1 + 4;
  }

  // float e21 = 8;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_e21 = this->_internal_e21();
  uint32_t raw_e21;
  memcpy(&raw_e21, &tmp_e21, sizeof(tmp_e21));
  if (raw_e21 != 0) {
    total_size += 1 + 4;
  }

  // float e22 = 9;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_e22 = this->_internal_e22();
  uint32_t raw_e22;
  memcpy(&raw_e22, &tmp_e22, sizeof(tmp_e22));
  if (raw_e22 != 0) {
    total_size += 1 + 4;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_impl_._cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData Matrix3f::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSourceCheck,
    Matrix3f::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*Matrix3f::GetClassData() const { return &_class_data_; }


void Matrix3f::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg) {
  auto* const _this = static_cast<Matrix3f*>(&to_msg);
  auto& from = static_cast<const Matrix3f&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:esurfing.proto.math.Matrix3f)
  GOOGLE_DCHECK_NE(&from, _this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_e00 = from._internal_e00();
  uint32_t raw_e00;
  memcpy(&raw_e00, &tmp_e00, sizeof(tmp_e00));
  if (raw_e00 != 0) {
    _this->_internal_set_e00(from._internal_e00());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_e01 = from._internal_e01();
  uint32_t raw_e01;
  memcpy(&raw_e01, &tmp_e01, sizeof(tmp_e01));
  if (raw_e01 != 0) {
    _this->_internal_set_e01(from._internal_e01());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_e02 = from._internal_e02();
  uint32_t raw_e02;
  memcpy(&raw_e02, &tmp_e02, sizeof(tmp_e02));
  if (raw_e02 != 0) {
    _this->_internal_set_e02(from._internal_e02());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_e10 = from._internal_e10();
  uint32_t raw_e10;
  memcpy(&raw_e10, &tmp_e10, sizeof(tmp_e10));
  if (raw_e10 != 0) {
    _this->_internal_set_e10(from._internal_e10());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_e11 = from._internal_e11();
  uint32_t raw_e11;
  memcpy(&raw_e11, &tmp_e11, sizeof(tmp_e11));
  if (raw_e11 != 0) {
    _this->_internal_set_e11(from._internal_e11());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_e12 = from._internal_e12();
  uint32_t raw_e12;
  memcpy(&raw_e12, &tmp_e12, sizeof(tmp_e12));
  if (raw_e12 != 0) {
    _this->_internal_set_e12(from._internal_e12());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_e20 = from._internal_e20();
  uint32_t raw_e20;
  memcpy(&raw_e20, &tmp_e20, sizeof(tmp_e20));
  if (raw_e20 != 0) {
    _this->_internal_set_e20(from._internal_e20());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_e21 = from._internal_e21();
  uint32_t raw_e21;
  memcpy(&raw_e21, &tmp_e21, sizeof(tmp_e21));
  if (raw_e21 != 0) {
    _this->_internal_set_e21(from._internal_e21());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_e22 = from._internal_e22();
  uint32_t raw_e22;
  memcpy(&raw_e22, &tmp_e22, sizeof(tmp_e22));
  if (raw_e22 != 0) {
    _this->_internal_set_e22(from._internal_e22());
  }
  _this->_internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void Matrix3f::CopyFrom(const Matrix3f& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:esurfing.proto.math.Matrix3f)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Matrix3f::IsInitialized() const {
  return true;
}

void Matrix3f::InternalSwap(Matrix3f* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(Matrix3f, _impl_.e22_)
      + sizeof(Matrix3f::_impl_.e22_)
      - PROTOBUF_FIELD_OFFSET(Matrix3f, _impl_.e00_)>(
          reinterpret_cast<char*>(&_impl_.e00_),
          reinterpret_cast<char*>(&other->_impl_.e00_));
}

::PROTOBUF_NAMESPACE_ID::Metadata Matrix3f::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_geo_2eproto_getter, &descriptor_table_geo_2eproto_once,
      file_level_metadata_geo_2eproto[11]);
}

// ===================================================================

class Quaterniond::_Internal {
 public:
};

Quaterniond::Quaterniond(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor(arena, is_message_owned);
  // @@protoc_insertion_point(arena_constructor:esurfing.proto.math.Quaterniond)
}
Quaterniond::Quaterniond(const Quaterniond& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  Quaterniond* const _this = this; (void)_this;
  new (&_impl_) Impl_{
      decltype(_impl_.w_){}
    , decltype(_impl_.x_){}
    , decltype(_impl_.y_){}
    , decltype(_impl_.z_){}
    , /*decltype(_impl_._cached_size_)*/{}};

  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&_impl_.w_, &from._impl_.w_,
    static_cast<size_t>(reinterpret_cast<char*>(&_impl_.z_) -
    reinterpret_cast<char*>(&_impl_.w_)) + sizeof(_impl_.z_));
  // @@protoc_insertion_point(copy_constructor:esurfing.proto.math.Quaterniond)
}

inline void Quaterniond::SharedCtor(
    ::_pb::Arena* arena, bool is_message_owned) {
  (void)arena;
  (void)is_message_owned;
  new (&_impl_) Impl_{
      decltype(_impl_.w_){0}
    , decltype(_impl_.x_){0}
    , decltype(_impl_.y_){0}
    , decltype(_impl_.z_){0}
    , /*decltype(_impl_._cached_size_)*/{}
  };
}

Quaterniond::~Quaterniond() {
  // @@protoc_insertion_point(destructor:esurfing.proto.math.Quaterniond)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void Quaterniond::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void Quaterniond::SetCachedSize(int size) const {
  _impl_._cached_size_.Set(size);
}

void Quaterniond::Clear() {
// @@protoc_insertion_point(message_clear_start:esurfing.proto.math.Quaterniond)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&_impl_.w_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&_impl_.z_) -
      reinterpret_cast<char*>(&_impl_.w_)) + sizeof(_impl_.z_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* Quaterniond::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // double w = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 9)) {
          _impl_.w_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr);
          ptr += sizeof(double);
        } else
          goto handle_unusual;
        continue;
      // double x = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 17)) {
          _impl_.x_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr);
          ptr += sizeof(double);
        } else
          goto handle_unusual;
        continue;
      // double y = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 25)) {
          _impl_.y_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr);
          ptr += sizeof(double);
        } else
          goto handle_unusual;
        continue;
      // double z = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 33)) {
          _impl_.z_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr);
          ptr += sizeof(double);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* Quaterniond::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:esurfing.proto.math.Quaterniond)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // double w = 1;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_w = this->_internal_w();
  uint64_t raw_w;
  memcpy(&raw_w, &tmp_w, sizeof(tmp_w));
  if (raw_w != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteDoubleToArray(1, this->_internal_w(), target);
  }

  // double x = 2;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_x = this->_internal_x();
  uint64_t raw_x;
  memcpy(&raw_x, &tmp_x, sizeof(tmp_x));
  if (raw_x != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteDoubleToArray(2, this->_internal_x(), target);
  }

  // double y = 3;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_y = this->_internal_y();
  uint64_t raw_y;
  memcpy(&raw_y, &tmp_y, sizeof(tmp_y));
  if (raw_y != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteDoubleToArray(3, this->_internal_y(), target);
  }

  // double z = 4;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_z = this->_internal_z();
  uint64_t raw_z;
  memcpy(&raw_z, &tmp_z, sizeof(tmp_z));
  if (raw_z != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteDoubleToArray(4, this->_internal_z(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:esurfing.proto.math.Quaterniond)
  return target;
}

size_t Quaterniond::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:esurfing.proto.math.Quaterniond)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // double w = 1;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_w = this->_internal_w();
  uint64_t raw_w;
  memcpy(&raw_w, &tmp_w, sizeof(tmp_w));
  if (raw_w != 0) {
    total_size += 1 + 8;
  }

  // double x = 2;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_x = this->_internal_x();
  uint64_t raw_x;
  memcpy(&raw_x, &tmp_x, sizeof(tmp_x));
  if (raw_x != 0) {
    total_size += 1 + 8;
  }

  // double y = 3;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_y = this->_internal_y();
  uint64_t raw_y;
  memcpy(&raw_y, &tmp_y, sizeof(tmp_y));
  if (raw_y != 0) {
    total_size += 1 + 8;
  }

  // double z = 4;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_z = this->_internal_z();
  uint64_t raw_z;
  memcpy(&raw_z, &tmp_z, sizeof(tmp_z));
  if (raw_z != 0) {
    total_size += 1 + 8;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_impl_._cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData Quaterniond::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSourceCheck,
    Quaterniond::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*Quaterniond::GetClassData() const { return &_class_data_; }


void Quaterniond::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg) {
  auto* const _this = static_cast<Quaterniond*>(&to_msg);
  auto& from = static_cast<const Quaterniond&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:esurfing.proto.math.Quaterniond)
  GOOGLE_DCHECK_NE(&from, _this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_w = from._internal_w();
  uint64_t raw_w;
  memcpy(&raw_w, &tmp_w, sizeof(tmp_w));
  if (raw_w != 0) {
    _this->_internal_set_w(from._internal_w());
  }
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_x = from._internal_x();
  uint64_t raw_x;
  memcpy(&raw_x, &tmp_x, sizeof(tmp_x));
  if (raw_x != 0) {
    _this->_internal_set_x(from._internal_x());
  }
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_y = from._internal_y();
  uint64_t raw_y;
  memcpy(&raw_y, &tmp_y, sizeof(tmp_y));
  if (raw_y != 0) {
    _this->_internal_set_y(from._internal_y());
  }
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_z = from._internal_z();
  uint64_t raw_z;
  memcpy(&raw_z, &tmp_z, sizeof(tmp_z));
  if (raw_z != 0) {
    _this->_internal_set_z(from._internal_z());
  }
  _this->_internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void Quaterniond::CopyFrom(const Quaterniond& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:esurfing.proto.math.Quaterniond)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Quaterniond::IsInitialized() const {
  return true;
}

void Quaterniond::InternalSwap(Quaterniond* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(Quaterniond, _impl_.z_)
      + sizeof(Quaterniond::_impl_.z_)
      - PROTOBUF_FIELD_OFFSET(Quaterniond, _impl_.w_)>(
          reinterpret_cast<char*>(&_impl_.w_),
          reinterpret_cast<char*>(&other->_impl_.w_));
}

::PROTOBUF_NAMESPACE_ID::Metadata Quaterniond::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_geo_2eproto_getter, &descriptor_table_geo_2eproto_once,
      file_level_metadata_geo_2eproto[12]);
}

// ===================================================================

class Quaternionf::_Internal {
 public:
};

Quaternionf::Quaternionf(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor(arena, is_message_owned);
  // @@protoc_insertion_point(arena_constructor:esurfing.proto.math.Quaternionf)
}
Quaternionf::Quaternionf(const Quaternionf& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  Quaternionf* const _this = this; (void)_this;
  new (&_impl_) Impl_{
      decltype(_impl_.w_){}
    , decltype(_impl_.x_){}
    , decltype(_impl_.y_){}
    , decltype(_impl_.z_){}
    , /*decltype(_impl_._cached_size_)*/{}};

  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&_impl_.w_, &from._impl_.w_,
    static_cast<size_t>(reinterpret_cast<char*>(&_impl_.z_) -
    reinterpret_cast<char*>(&_impl_.w_)) + sizeof(_impl_.z_));
  // @@protoc_insertion_point(copy_constructor:esurfing.proto.math.Quaternionf)
}

inline void Quaternionf::SharedCtor(
    ::_pb::Arena* arena, bool is_message_owned) {
  (void)arena;
  (void)is_message_owned;
  new (&_impl_) Impl_{
      decltype(_impl_.w_){0}
    , decltype(_impl_.x_){0}
    , decltype(_impl_.y_){0}
    , decltype(_impl_.z_){0}
    , /*decltype(_impl_._cached_size_)*/{}
  };
}

Quaternionf::~Quaternionf() {
  // @@protoc_insertion_point(destructor:esurfing.proto.math.Quaternionf)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void Quaternionf::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void Quaternionf::SetCachedSize(int size) const {
  _impl_._cached_size_.Set(size);
}

void Quaternionf::Clear() {
// @@protoc_insertion_point(message_clear_start:esurfing.proto.math.Quaternionf)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&_impl_.w_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&_impl_.z_) -
      reinterpret_cast<char*>(&_impl_.w_)) + sizeof(_impl_.z_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* Quaternionf::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // float w = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 13)) {
          _impl_.w_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // float x = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 21)) {
          _impl_.x_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // float y = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 29)) {
          _impl_.y_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // float z = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 37)) {
          _impl_.z_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* Quaternionf::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:esurfing.proto.math.Quaternionf)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // float w = 1;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_w = this->_internal_w();
  uint32_t raw_w;
  memcpy(&raw_w, &tmp_w, sizeof(tmp_w));
  if (raw_w != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteFloatToArray(1, this->_internal_w(), target);
  }

  // float x = 2;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_x = this->_internal_x();
  uint32_t raw_x;
  memcpy(&raw_x, &tmp_x, sizeof(tmp_x));
  if (raw_x != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteFloatToArray(2, this->_internal_x(), target);
  }

  // float y = 3;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_y = this->_internal_y();
  uint32_t raw_y;
  memcpy(&raw_y, &tmp_y, sizeof(tmp_y));
  if (raw_y != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteFloatToArray(3, this->_internal_y(), target);
  }

  // float z = 4;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_z = this->_internal_z();
  uint32_t raw_z;
  memcpy(&raw_z, &tmp_z, sizeof(tmp_z));
  if (raw_z != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteFloatToArray(4, this->_internal_z(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:esurfing.proto.math.Quaternionf)
  return target;
}

size_t Quaternionf::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:esurfing.proto.math.Quaternionf)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // float w = 1;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_w = this->_internal_w();
  uint32_t raw_w;
  memcpy(&raw_w, &tmp_w, sizeof(tmp_w));
  if (raw_w != 0) {
    total_size += 1 + 4;
  }

  // float x = 2;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_x = this->_internal_x();
  uint32_t raw_x;
  memcpy(&raw_x, &tmp_x, sizeof(tmp_x));
  if (raw_x != 0) {
    total_size += 1 + 4;
  }

  // float y = 3;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_y = this->_internal_y();
  uint32_t raw_y;
  memcpy(&raw_y, &tmp_y, sizeof(tmp_y));
  if (raw_y != 0) {
    total_size += 1 + 4;
  }

  // float z = 4;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_z = this->_internal_z();
  uint32_t raw_z;
  memcpy(&raw_z, &tmp_z, sizeof(tmp_z));
  if (raw_z != 0) {
    total_size += 1 + 4;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_impl_._cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData Quaternionf::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSourceCheck,
    Quaternionf::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*Quaternionf::GetClassData() const { return &_class_data_; }


void Quaternionf::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg) {
  auto* const _this = static_cast<Quaternionf*>(&to_msg);
  auto& from = static_cast<const Quaternionf&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:esurfing.proto.math.Quaternionf)
  GOOGLE_DCHECK_NE(&from, _this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_w = from._internal_w();
  uint32_t raw_w;
  memcpy(&raw_w, &tmp_w, sizeof(tmp_w));
  if (raw_w != 0) {
    _this->_internal_set_w(from._internal_w());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_x = from._internal_x();
  uint32_t raw_x;
  memcpy(&raw_x, &tmp_x, sizeof(tmp_x));
  if (raw_x != 0) {
    _this->_internal_set_x(from._internal_x());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_y = from._internal_y();
  uint32_t raw_y;
  memcpy(&raw_y, &tmp_y, sizeof(tmp_y));
  if (raw_y != 0) {
    _this->_internal_set_y(from._internal_y());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_z = from._internal_z();
  uint32_t raw_z;
  memcpy(&raw_z, &tmp_z, sizeof(tmp_z));
  if (raw_z != 0) {
    _this->_internal_set_z(from._internal_z());
  }
  _this->_internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void Quaternionf::CopyFrom(const Quaternionf& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:esurfing.proto.math.Quaternionf)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Quaternionf::IsInitialized() const {
  return true;
}

void Quaternionf::InternalSwap(Quaternionf* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(Quaternionf, _impl_.z_)
      + sizeof(Quaternionf::_impl_.z_)
      - PROTOBUF_FIELD_OFFSET(Quaternionf, _impl_.w_)>(
          reinterpret_cast<char*>(&_impl_.w_),
          reinterpret_cast<char*>(&other->_impl_.w_));
}

::PROTOBUF_NAMESPACE_ID::Metadata Quaternionf::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_geo_2eproto_getter, &descriptor_table_geo_2eproto_once,
      file_level_metadata_geo_2eproto[13]);
}

// ===================================================================

class Transformation3d::_Internal {
 public:
  static const ::esurfing::proto::math::Quaterniond& rotation(const Transformation3d* msg);
  static const ::esurfing::proto::math::Vector3d& translation(const Transformation3d* msg);
};

const ::esurfing::proto::math::Quaterniond&
Transformation3d::_Internal::rotation(const Transformation3d* msg) {
  return *msg->_impl_.rotation_;
}
const ::esurfing::proto::math::Vector3d&
Transformation3d::_Internal::translation(const Transformation3d* msg) {
  return *msg->_impl_.translation_;
}
Transformation3d::Transformation3d(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor(arena, is_message_owned);
  // @@protoc_insertion_point(arena_constructor:esurfing.proto.math.Transformation3d)
}
Transformation3d::Transformation3d(const Transformation3d& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  Transformation3d* const _this = this; (void)_this;
  new (&_impl_) Impl_{
      decltype(_impl_.rotation_){nullptr}
    , decltype(_impl_.translation_){nullptr}
    , /*decltype(_impl_._cached_size_)*/{}};

  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_rotation()) {
    _this->_impl_.rotation_ = new ::esurfing::proto::math::Quaterniond(*from._impl_.rotation_);
  }
  if (from._internal_has_translation()) {
    _this->_impl_.translation_ = new ::esurfing::proto::math::Vector3d(*from._impl_.translation_);
  }
  // @@protoc_insertion_point(copy_constructor:esurfing.proto.math.Transformation3d)
}

inline void Transformation3d::SharedCtor(
    ::_pb::Arena* arena, bool is_message_owned) {
  (void)arena;
  (void)is_message_owned;
  new (&_impl_) Impl_{
      decltype(_impl_.rotation_){nullptr}
    , decltype(_impl_.translation_){nullptr}
    , /*decltype(_impl_._cached_size_)*/{}
  };
}

Transformation3d::~Transformation3d() {
  // @@protoc_insertion_point(destructor:esurfing.proto.math.Transformation3d)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void Transformation3d::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete _impl_.rotation_;
  if (this != internal_default_instance()) delete _impl_.translation_;
}

void Transformation3d::SetCachedSize(int size) const {
  _impl_._cached_size_.Set(size);
}

void Transformation3d::Clear() {
// @@protoc_insertion_point(message_clear_start:esurfing.proto.math.Transformation3d)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaForAllocation() == nullptr && _impl_.rotation_ != nullptr) {
    delete _impl_.rotation_;
  }
  _impl_.rotation_ = nullptr;
  if (GetArenaForAllocation() == nullptr && _impl_.translation_ != nullptr) {
    delete _impl_.translation_;
  }
  _impl_.translation_ = nullptr;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* Transformation3d::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .esurfing.proto.math.Quaterniond rotation = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_rotation(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .esurfing.proto.math.Vector3d translation = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr = ctx->ParseMessage(_internal_mutable_translation(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* Transformation3d::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:esurfing.proto.math.Transformation3d)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .esurfing.proto.math.Quaterniond rotation = 1;
  if (this->_internal_has_rotation()) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(1, _Internal::rotation(this),
        _Internal::rotation(this).GetCachedSize(), target, stream);
  }

  // .esurfing.proto.math.Vector3d translation = 2;
  if (this->_internal_has_translation()) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(2, _Internal::translation(this),
        _Internal::translation(this).GetCachedSize(), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:esurfing.proto.math.Transformation3d)
  return target;
}

size_t Transformation3d::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:esurfing.proto.math.Transformation3d)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .esurfing.proto.math.Quaterniond rotation = 1;
  if (this->_internal_has_rotation()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *_impl_.rotation_);
  }

  // .esurfing.proto.math.Vector3d translation = 2;
  if (this->_internal_has_translation()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *_impl_.translation_);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_impl_._cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData Transformation3d::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSourceCheck,
    Transformation3d::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*Transformation3d::GetClassData() const { return &_class_data_; }


void Transformation3d::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg) {
  auto* const _this = static_cast<Transformation3d*>(&to_msg);
  auto& from = static_cast<const Transformation3d&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:esurfing.proto.math.Transformation3d)
  GOOGLE_DCHECK_NE(&from, _this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_has_rotation()) {
    _this->_internal_mutable_rotation()->::esurfing::proto::math::Quaterniond::MergeFrom(
        from._internal_rotation());
  }
  if (from._internal_has_translation()) {
    _this->_internal_mutable_translation()->::esurfing::proto::math::Vector3d::MergeFrom(
        from._internal_translation());
  }
  _this->_internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void Transformation3d::CopyFrom(const Transformation3d& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:esurfing.proto.math.Transformation3d)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Transformation3d::IsInitialized() const {
  return true;
}

void Transformation3d::InternalSwap(Transformation3d* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(Transformation3d, _impl_.translation_)
      + sizeof(Transformation3d::_impl_.translation_)
      - PROTOBUF_FIELD_OFFSET(Transformation3d, _impl_.rotation_)>(
          reinterpret_cast<char*>(&_impl_.rotation_),
          reinterpret_cast<char*>(&other->_impl_.rotation_));
}

::PROTOBUF_NAMESPACE_ID::Metadata Transformation3d::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_geo_2eproto_getter, &descriptor_table_geo_2eproto_once,
      file_level_metadata_geo_2eproto[14]);
}

// ===================================================================

class Transformation3f::_Internal {
 public:
  static const ::esurfing::proto::math::Quaternionf& rotation(const Transformation3f* msg);
  static const ::esurfing::proto::math::Vector3f& translation(const Transformation3f* msg);
};

const ::esurfing::proto::math::Quaternionf&
Transformation3f::_Internal::rotation(const Transformation3f* msg) {
  return *msg->_impl_.rotation_;
}
const ::esurfing::proto::math::Vector3f&
Transformation3f::_Internal::translation(const Transformation3f* msg) {
  return *msg->_impl_.translation_;
}
Transformation3f::Transformation3f(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor(arena, is_message_owned);
  // @@protoc_insertion_point(arena_constructor:esurfing.proto.math.Transformation3f)
}
Transformation3f::Transformation3f(const Transformation3f& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  Transformation3f* const _this = this; (void)_this;
  new (&_impl_) Impl_{
      decltype(_impl_.rotation_){nullptr}
    , decltype(_impl_.translation_){nullptr}
    , /*decltype(_impl_._cached_size_)*/{}};

  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_rotation()) {
    _this->_impl_.rotation_ = new ::esurfing::proto::math::Quaternionf(*from._impl_.rotation_);
  }
  if (from._internal_has_translation()) {
    _this->_impl_.translation_ = new ::esurfing::proto::math::Vector3f(*from._impl_.translation_);
  }
  // @@protoc_insertion_point(copy_constructor:esurfing.proto.math.Transformation3f)
}

inline void Transformation3f::SharedCtor(
    ::_pb::Arena* arena, bool is_message_owned) {
  (void)arena;
  (void)is_message_owned;
  new (&_impl_) Impl_{
      decltype(_impl_.rotation_){nullptr}
    , decltype(_impl_.translation_){nullptr}
    , /*decltype(_impl_._cached_size_)*/{}
  };
}

Transformation3f::~Transformation3f() {
  // @@protoc_insertion_point(destructor:esurfing.proto.math.Transformation3f)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void Transformation3f::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete _impl_.rotation_;
  if (this != internal_default_instance()) delete _impl_.translation_;
}

void Transformation3f::SetCachedSize(int size) const {
  _impl_._cached_size_.Set(size);
}

void Transformation3f::Clear() {
// @@protoc_insertion_point(message_clear_start:esurfing.proto.math.Transformation3f)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaForAllocation() == nullptr && _impl_.rotation_ != nullptr) {
    delete _impl_.rotation_;
  }
  _impl_.rotation_ = nullptr;
  if (GetArenaForAllocation() == nullptr && _impl_.translation_ != nullptr) {
    delete _impl_.translation_;
  }
  _impl_.translation_ = nullptr;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* Transformation3f::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .esurfing.proto.math.Quaternionf rotation = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_rotation(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .esurfing.proto.math.Vector3f translation = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr = ctx->ParseMessage(_internal_mutable_translation(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* Transformation3f::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:esurfing.proto.math.Transformation3f)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .esurfing.proto.math.Quaternionf rotation = 1;
  if (this->_internal_has_rotation()) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(1, _Internal::rotation(this),
        _Internal::rotation(this).GetCachedSize(), target, stream);
  }

  // .esurfing.proto.math.Vector3f translation = 2;
  if (this->_internal_has_translation()) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(2, _Internal::translation(this),
        _Internal::translation(this).GetCachedSize(), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:esurfing.proto.math.Transformation3f)
  return target;
}

size_t Transformation3f::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:esurfing.proto.math.Transformation3f)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .esurfing.proto.math.Quaternionf rotation = 1;
  if (this->_internal_has_rotation()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *_impl_.rotation_);
  }

  // .esurfing.proto.math.Vector3f translation = 2;
  if (this->_internal_has_translation()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *_impl_.translation_);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_impl_._cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData Transformation3f::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSourceCheck,
    Transformation3f::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*Transformation3f::GetClassData() const { return &_class_data_; }


void Transformation3f::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg) {
  auto* const _this = static_cast<Transformation3f*>(&to_msg);
  auto& from = static_cast<const Transformation3f&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:esurfing.proto.math.Transformation3f)
  GOOGLE_DCHECK_NE(&from, _this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_has_rotation()) {
    _this->_internal_mutable_rotation()->::esurfing::proto::math::Quaternionf::MergeFrom(
        from._internal_rotation());
  }
  if (from._internal_has_translation()) {
    _this->_internal_mutable_translation()->::esurfing::proto::math::Vector3f::MergeFrom(
        from._internal_translation());
  }
  _this->_internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void Transformation3f::CopyFrom(const Transformation3f& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:esurfing.proto.math.Transformation3f)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Transformation3f::IsInitialized() const {
  return true;
}

void Transformation3f::InternalSwap(Transformation3f* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(Transformation3f, _impl_.translation_)
      + sizeof(Transformation3f::_impl_.translation_)
      - PROTOBUF_FIELD_OFFSET(Transformation3f, _impl_.rotation_)>(
          reinterpret_cast<char*>(&_impl_.rotation_),
          reinterpret_cast<char*>(&other->_impl_.rotation_));
}

::PROTOBUF_NAMESPACE_ID::Metadata Transformation3f::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_geo_2eproto_getter, &descriptor_table_geo_2eproto_once,
      file_level_metadata_geo_2eproto[15]);
}

// @@protoc_insertion_point(namespace_scope)
}  // namespace math
}  // namespace proto
}  // namespace esurfing
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::esurfing::proto::math::Vector3i*
Arena::CreateMaybeMessage< ::esurfing::proto::math::Vector3i >(Arena* arena) {
  return Arena::CreateMessageInternal< ::esurfing::proto::math::Vector3i >(arena);
}
template<> PROTOBUF_NOINLINE ::esurfing::proto::math::Polyline*
Arena::CreateMaybeMessage< ::esurfing::proto::math::Polyline >(Arena* arena) {
  return Arena::CreateMessageInternal< ::esurfing::proto::math::Polyline >(arena);
}
template<> PROTOBUF_NOINLINE ::esurfing::proto::math::Polygon*
Arena::CreateMaybeMessage< ::esurfing::proto::math::Polygon >(Arena* arena) {
  return Arena::CreateMessageInternal< ::esurfing::proto::math::Polygon >(arena);
}
template<> PROTOBUF_NOINLINE ::esurfing::proto::math::Points*
Arena::CreateMaybeMessage< ::esurfing::proto::math::Points >(Arena* arena) {
  return Arena::CreateMessageInternal< ::esurfing::proto::math::Points >(arena);
}
template<> PROTOBUF_NOINLINE ::esurfing::proto::math::Vector2d*
Arena::CreateMaybeMessage< ::esurfing::proto::math::Vector2d >(Arena* arena) {
  return Arena::CreateMessageInternal< ::esurfing::proto::math::Vector2d >(arena);
}
template<> PROTOBUF_NOINLINE ::esurfing::proto::math::Vector2f*
Arena::CreateMaybeMessage< ::esurfing::proto::math::Vector2f >(Arena* arena) {
  return Arena::CreateMessageInternal< ::esurfing::proto::math::Vector2f >(arena);
}
template<> PROTOBUF_NOINLINE ::esurfing::proto::math::Vector3d*
Arena::CreateMaybeMessage< ::esurfing::proto::math::Vector3d >(Arena* arena) {
  return Arena::CreateMessageInternal< ::esurfing::proto::math::Vector3d >(arena);
}
template<> PROTOBUF_NOINLINE ::esurfing::proto::math::Vector3f*
Arena::CreateMaybeMessage< ::esurfing::proto::math::Vector3f >(Arena* arena) {
  return Arena::CreateMessageInternal< ::esurfing::proto::math::Vector3f >(arena);
}
template<> PROTOBUF_NOINLINE ::esurfing::proto::math::Matrix2d*
Arena::CreateMaybeMessage< ::esurfing::proto::math::Matrix2d >(Arena* arena) {
  return Arena::CreateMessageInternal< ::esurfing::proto::math::Matrix2d >(arena);
}
template<> PROTOBUF_NOINLINE ::esurfing::proto::math::Matrix2f*
Arena::CreateMaybeMessage< ::esurfing::proto::math::Matrix2f >(Arena* arena) {
  return Arena::CreateMessageInternal< ::esurfing::proto::math::Matrix2f >(arena);
}
template<> PROTOBUF_NOINLINE ::esurfing::proto::math::Matrix3d*
Arena::CreateMaybeMessage< ::esurfing::proto::math::Matrix3d >(Arena* arena) {
  return Arena::CreateMessageInternal< ::esurfing::proto::math::Matrix3d >(arena);
}
template<> PROTOBUF_NOINLINE ::esurfing::proto::math::Matrix3f*
Arena::CreateMaybeMessage< ::esurfing::proto::math::Matrix3f >(Arena* arena) {
  return Arena::CreateMessageInternal< ::esurfing::proto::math::Matrix3f >(arena);
}
template<> PROTOBUF_NOINLINE ::esurfing::proto::math::Quaterniond*
Arena::CreateMaybeMessage< ::esurfing::proto::math::Quaterniond >(Arena* arena) {
  return Arena::CreateMessageInternal< ::esurfing::proto::math::Quaterniond >(arena);
}
template<> PROTOBUF_NOINLINE ::esurfing::proto::math::Quaternionf*
Arena::CreateMaybeMessage< ::esurfing::proto::math::Quaternionf >(Arena* arena) {
  return Arena::CreateMessageInternal< ::esurfing::proto::math::Quaternionf >(arena);
}
template<> PROTOBUF_NOINLINE ::esurfing::proto::math::Transformation3d*
Arena::CreateMaybeMessage< ::esurfing::proto::math::Transformation3d >(Arena* arena) {
  return Arena::CreateMessageInternal< ::esurfing::proto::math::Transformation3d >(arena);
}
template<> PROTOBUF_NOINLINE ::esurfing::proto::math::Transformation3f*
Arena::CreateMaybeMessage< ::esurfing::proto::math::Transformation3f >(Arena* arena) {
  return Arena::CreateMessageInternal< ::esurfing::proto::math::Transformation3f >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>
