// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: group_detected.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_group_5fdetected_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_group_5fdetected_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3021000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3021006 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/generated_enum_reflection.h>
#include <google/protobuf/unknown_field_set.h>
#include "geo.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_group_5fdetected_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_group_5fdetected_2eproto {
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_group_5fdetected_2eproto;
namespace esurfing {
namespace proto {
namespace perc {
class Color;
struct ColorDefaultTypeInternal;
extern ColorDefaultTypeInternal _Color_default_instance_;
class DetectedObject;
struct DetectedObjectDefaultTypeInternal;
extern DetectedObjectDefaultTypeInternal _DetectedObject_default_instance_;
class DetectedObjects;
struct DetectedObjectsDefaultTypeInternal;
extern DetectedObjectsDefaultTypeInternal _DetectedObjects_default_instance_;
class LicenePlate;
struct LicenePlateDefaultTypeInternal;
extern LicenePlateDefaultTypeInternal _LicenePlate_default_instance_;
class Velocity;
struct VelocityDefaultTypeInternal;
extern VelocityDefaultTypeInternal _Velocity_default_instance_;
class WayPoint;
struct WayPointDefaultTypeInternal;
extern WayPointDefaultTypeInternal _WayPoint_default_instance_;
class WayPoints;
struct WayPointsDefaultTypeInternal;
extern WayPointsDefaultTypeInternal _WayPoints_default_instance_;
}  // namespace perc
}  // namespace proto
}  // namespace esurfing
PROTOBUF_NAMESPACE_OPEN
template<> ::esurfing::proto::perc::Color* Arena::CreateMaybeMessage<::esurfing::proto::perc::Color>(Arena*);
template<> ::esurfing::proto::perc::DetectedObject* Arena::CreateMaybeMessage<::esurfing::proto::perc::DetectedObject>(Arena*);
template<> ::esurfing::proto::perc::DetectedObjects* Arena::CreateMaybeMessage<::esurfing::proto::perc::DetectedObjects>(Arena*);
template<> ::esurfing::proto::perc::LicenePlate* Arena::CreateMaybeMessage<::esurfing::proto::perc::LicenePlate>(Arena*);
template<> ::esurfing::proto::perc::Velocity* Arena::CreateMaybeMessage<::esurfing::proto::perc::Velocity>(Arena*);
template<> ::esurfing::proto::perc::WayPoint* Arena::CreateMaybeMessage<::esurfing::proto::perc::WayPoint>(Arena*);
template<> ::esurfing::proto::perc::WayPoints* Arena::CreateMaybeMessage<::esurfing::proto::perc::WayPoints>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace esurfing {
namespace proto {
namespace perc {

enum ObjectType : int {
  UNKNOWN = 0,
  CAR = 1,
  PEDESTRIAN = 2,
  CYCLIST = 3,
  TRUCK = 4,
  VAN = 5,
  BUS = 6,
  STATIC = 7,
  STATIC_EDGE = 8,
  CONE = 9,
  TROLLEY = 10,
  ROBOT = 11,
  GATE = 12,
  ObjectType_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::min(),
  ObjectType_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::max()
};
bool ObjectType_IsValid(int value);
constexpr ObjectType ObjectType_MIN = UNKNOWN;
constexpr ObjectType ObjectType_MAX = GATE;
constexpr int ObjectType_ARRAYSIZE = ObjectType_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* ObjectType_descriptor();
template<typename T>
inline const std::string& ObjectType_Name(T enum_t_value) {
  static_assert(::std::is_same<T, ObjectType>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function ObjectType_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    ObjectType_descriptor(), enum_t_value);
}
inline bool ObjectType_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, ObjectType* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<ObjectType>(
    ObjectType_descriptor(), name, value);
}
// ===================================================================

class Color final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:esurfing.proto.perc.Color) */ {
 public:
  inline Color() : Color(nullptr) {}
  ~Color() override;
  explicit PROTOBUF_CONSTEXPR Color(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  Color(const Color& from);
  Color(Color&& from) noexcept
    : Color() {
    *this = ::std::move(from);
  }

  inline Color& operator=(const Color& from) {
    CopyFrom(from);
    return *this;
  }
  inline Color& operator=(Color&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const Color& default_instance() {
    return *internal_default_instance();
  }
  static inline const Color* internal_default_instance() {
    return reinterpret_cast<const Color*>(
               &_Color_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(Color& a, Color& b) {
    a.Swap(&b);
  }
  inline void Swap(Color* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(Color* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  Color* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<Color>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const Color& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const Color& from) {
    Color::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Color* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "esurfing.proto.perc.Color";
  }
  protected:
  explicit Color(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kRFieldNumber = 1,
    kGFieldNumber = 2,
    kBFieldNumber = 3,
    kAFieldNumber = 4,
  };
  // int32 r = 1;
  void clear_r();
  int32_t r() const;
  void set_r(int32_t value);
  private:
  int32_t _internal_r() const;
  void _internal_set_r(int32_t value);
  public:

  // int32 g = 2;
  void clear_g();
  int32_t g() const;
  void set_g(int32_t value);
  private:
  int32_t _internal_g() const;
  void _internal_set_g(int32_t value);
  public:

  // int32 b = 3;
  void clear_b();
  int32_t b() const;
  void set_b(int32_t value);
  private:
  int32_t _internal_b() const;
  void _internal_set_b(int32_t value);
  public:

  // float a = 4;
  void clear_a();
  float a() const;
  void set_a(float value);
  private:
  float _internal_a() const;
  void _internal_set_a(float value);
  public:

  // @@protoc_insertion_point(class_scope:esurfing.proto.perc.Color)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    int32_t r_;
    int32_t g_;
    int32_t b_;
    float a_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_group_5fdetected_2eproto;
};
// -------------------------------------------------------------------

class DetectedObjects final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:esurfing.proto.perc.DetectedObjects) */ {
 public:
  inline DetectedObjects() : DetectedObjects(nullptr) {}
  ~DetectedObjects() override;
  explicit PROTOBUF_CONSTEXPR DetectedObjects(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  DetectedObjects(const DetectedObjects& from);
  DetectedObjects(DetectedObjects&& from) noexcept
    : DetectedObjects() {
    *this = ::std::move(from);
  }

  inline DetectedObjects& operator=(const DetectedObjects& from) {
    CopyFrom(from);
    return *this;
  }
  inline DetectedObjects& operator=(DetectedObjects&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const DetectedObjects& default_instance() {
    return *internal_default_instance();
  }
  static inline const DetectedObjects* internal_default_instance() {
    return reinterpret_cast<const DetectedObjects*>(
               &_DetectedObjects_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(DetectedObjects& a, DetectedObjects& b) {
    a.Swap(&b);
  }
  inline void Swap(DetectedObjects* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(DetectedObjects* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  DetectedObjects* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<DetectedObjects>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const DetectedObjects& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const DetectedObjects& from) {
    DetectedObjects::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(DetectedObjects* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "esurfing.proto.perc.DetectedObjects";
  }
  protected:
  explicit DetectedObjects(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kObjectsFieldNumber = 5,
    kGroupNameFieldNumber = 3,
    kGroupInfoFieldNumber = 4,
    kTimeMeasFieldNumber = 1,
    kTimePubFieldNumber = 2,
  };
  // repeated .esurfing.proto.perc.DetectedObject objects = 5;
  int objects_size() const;
  private:
  int _internal_objects_size() const;
  public:
  void clear_objects();
  ::esurfing::proto::perc::DetectedObject* mutable_objects(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::esurfing::proto::perc::DetectedObject >*
      mutable_objects();
  private:
  const ::esurfing::proto::perc::DetectedObject& _internal_objects(int index) const;
  ::esurfing::proto::perc::DetectedObject* _internal_add_objects();
  public:
  const ::esurfing::proto::perc::DetectedObject& objects(int index) const;
  ::esurfing::proto::perc::DetectedObject* add_objects();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::esurfing::proto::perc::DetectedObject >&
      objects() const;

  // bytes group_name = 3;
  void clear_group_name();
  const std::string& group_name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_group_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_group_name();
  PROTOBUF_NODISCARD std::string* release_group_name();
  void set_allocated_group_name(std::string* group_name);
  private:
  const std::string& _internal_group_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_group_name(const std::string& value);
  std::string* _internal_mutable_group_name();
  public:

  // bytes group_info = 4;
  void clear_group_info();
  const std::string& group_info() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_group_info(ArgT0&& arg0, ArgT... args);
  std::string* mutable_group_info();
  PROTOBUF_NODISCARD std::string* release_group_info();
  void set_allocated_group_info(std::string* group_info);
  private:
  const std::string& _internal_group_info() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_group_info(const std::string& value);
  std::string* _internal_mutable_group_info();
  public:

  // sfixed64 time_meas = 1;
  void clear_time_meas();
  int64_t time_meas() const;
  void set_time_meas(int64_t value);
  private:
  int64_t _internal_time_meas() const;
  void _internal_set_time_meas(int64_t value);
  public:

  // sfixed64 time_pub = 2;
  void clear_time_pub();
  int64_t time_pub() const;
  void set_time_pub(int64_t value);
  private:
  int64_t _internal_time_pub() const;
  void _internal_set_time_pub(int64_t value);
  public:

  // @@protoc_insertion_point(class_scope:esurfing.proto.perc.DetectedObjects)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::esurfing::proto::perc::DetectedObject > objects_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr group_name_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr group_info_;
    int64_t time_meas_;
    int64_t time_pub_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_group_5fdetected_2eproto;
};
// -------------------------------------------------------------------

class LicenePlate final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:esurfing.proto.perc.LicenePlate) */ {
 public:
  inline LicenePlate() : LicenePlate(nullptr) {}
  ~LicenePlate() override;
  explicit PROTOBUF_CONSTEXPR LicenePlate(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  LicenePlate(const LicenePlate& from);
  LicenePlate(LicenePlate&& from) noexcept
    : LicenePlate() {
    *this = ::std::move(from);
  }

  inline LicenePlate& operator=(const LicenePlate& from) {
    CopyFrom(from);
    return *this;
  }
  inline LicenePlate& operator=(LicenePlate&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const LicenePlate& default_instance() {
    return *internal_default_instance();
  }
  static inline const LicenePlate* internal_default_instance() {
    return reinterpret_cast<const LicenePlate*>(
               &_LicenePlate_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(LicenePlate& a, LicenePlate& b) {
    a.Swap(&b);
  }
  inline void Swap(LicenePlate* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(LicenePlate* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  LicenePlate* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<LicenePlate>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const LicenePlate& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const LicenePlate& from) {
    LicenePlate::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(LicenePlate* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "esurfing.proto.perc.LicenePlate";
  }
  protected:
  explicit LicenePlate(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kNumberFieldNumber = 3,
    kColorFieldNumber = 1,
    kColorConfidenceFieldNumber = 2,
    kNumberConfidenceFieldNumber = 4,
  };
  // string number = 3;
  void clear_number();
  const std::string& number() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_number(ArgT0&& arg0, ArgT... args);
  std::string* mutable_number();
  PROTOBUF_NODISCARD std::string* release_number();
  void set_allocated_number(std::string* number);
  private:
  const std::string& _internal_number() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_number(const std::string& value);
  std::string* _internal_mutable_number();
  public:

  // int32 color = 1;
  void clear_color();
  int32_t color() const;
  void set_color(int32_t value);
  private:
  int32_t _internal_color() const;
  void _internal_set_color(int32_t value);
  public:

  // float color_confidence = 2;
  void clear_color_confidence();
  float color_confidence() const;
  void set_color_confidence(float value);
  private:
  float _internal_color_confidence() const;
  void _internal_set_color_confidence(float value);
  public:

  // float number_confidence = 4;
  void clear_number_confidence();
  float number_confidence() const;
  void set_number_confidence(float value);
  private:
  float _internal_number_confidence() const;
  void _internal_set_number_confidence(float value);
  public:

  // @@protoc_insertion_point(class_scope:esurfing.proto.perc.LicenePlate)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr number_;
    int32_t color_;
    float color_confidence_;
    float number_confidence_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_group_5fdetected_2eproto;
};
// -------------------------------------------------------------------

class DetectedObject final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:esurfing.proto.perc.DetectedObject) */ {
 public:
  inline DetectedObject() : DetectedObject(nullptr) {}
  ~DetectedObject() override;
  explicit PROTOBUF_CONSTEXPR DetectedObject(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  DetectedObject(const DetectedObject& from);
  DetectedObject(DetectedObject&& from) noexcept
    : DetectedObject() {
    *this = ::std::move(from);
  }

  inline DetectedObject& operator=(const DetectedObject& from) {
    CopyFrom(from);
    return *this;
  }
  inline DetectedObject& operator=(DetectedObject&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const DetectedObject& default_instance() {
    return *internal_default_instance();
  }
  static inline const DetectedObject* internal_default_instance() {
    return reinterpret_cast<const DetectedObject*>(
               &_DetectedObject_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(DetectedObject& a, DetectedObject& b) {
    a.Swap(&b);
  }
  inline void Swap(DetectedObject* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(DetectedObject* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  DetectedObject* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<DetectedObject>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const DetectedObject& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const DetectedObject& from) {
    DetectedObject::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(DetectedObject* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "esurfing.proto.perc.DetectedObject";
  }
  protected:
  explicit DetectedObject(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kFeatureFieldNumber = 12,
    kTrajectoriesFieldNumber = 13,
    kStrArrayFieldNumber = 14,
    kIntArrayFieldNumber = 15,
    kOverlapNameFieldNumber = 1,
    kPositionFieldNumber = 5,
    kShapeFieldNumber = 6,
    kHullFieldNumber = 7,
    kVelocityFieldNumber = 9,
    kColorFieldNumber = 11,
    kPlateFieldNumber = 17,
    kUuidFieldNumber = 2,
    kTypeFieldNumber = 3,
    kConfidenceFieldNumber = 4,
    kOrientationFieldNumber = 8,
    kIsStaticFieldNumber = 10,
    kParkingTimeFieldNumber = 16,
    kObjColorFieldNumber = 18,
  };
  // repeated float feature = 12;
  int feature_size() const;
  private:
  int _internal_feature_size() const;
  public:
  void clear_feature();
  private:
  float _internal_feature(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >&
      _internal_feature() const;
  void _internal_add_feature(float value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >*
      _internal_mutable_feature();
  public:
  float feature(int index) const;
  void set_feature(int index, float value);
  void add_feature(float value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >&
      feature() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >*
      mutable_feature();

  // repeated .esurfing.proto.perc.WayPoints trajectories = 13;
  int trajectories_size() const;
  private:
  int _internal_trajectories_size() const;
  public:
  void clear_trajectories();
  ::esurfing::proto::perc::WayPoints* mutable_trajectories(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::esurfing::proto::perc::WayPoints >*
      mutable_trajectories();
  private:
  const ::esurfing::proto::perc::WayPoints& _internal_trajectories(int index) const;
  ::esurfing::proto::perc::WayPoints* _internal_add_trajectories();
  public:
  const ::esurfing::proto::perc::WayPoints& trajectories(int index) const;
  ::esurfing::proto::perc::WayPoints* add_trajectories();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::esurfing::proto::perc::WayPoints >&
      trajectories() const;

  // repeated bytes str_array = 14;
  int str_array_size() const;
  private:
  int _internal_str_array_size() const;
  public:
  void clear_str_array();
  const std::string& str_array(int index) const;
  std::string* mutable_str_array(int index);
  void set_str_array(int index, const std::string& value);
  void set_str_array(int index, std::string&& value);
  void set_str_array(int index, const char* value);
  void set_str_array(int index, const void* value, size_t size);
  std::string* add_str_array();
  void add_str_array(const std::string& value);
  void add_str_array(std::string&& value);
  void add_str_array(const char* value);
  void add_str_array(const void* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& str_array() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_str_array();
  private:
  const std::string& _internal_str_array(int index) const;
  std::string* _internal_add_str_array();
  public:

  // repeated sint32 int_array = 15;
  int int_array_size() const;
  private:
  int _internal_int_array_size() const;
  public:
  void clear_int_array();
  private:
  int32_t _internal_int_array(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >&
      _internal_int_array() const;
  void _internal_add_int_array(int32_t value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >*
      _internal_mutable_int_array();
  public:
  int32_t int_array(int index) const;
  void set_int_array(int index, int32_t value);
  void add_int_array(int32_t value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >&
      int_array() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >*
      mutable_int_array();

  // bytes overlap_name = 1;
  void clear_overlap_name();
  const std::string& overlap_name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_overlap_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_overlap_name();
  PROTOBUF_NODISCARD std::string* release_overlap_name();
  void set_allocated_overlap_name(std::string* overlap_name);
  private:
  const std::string& _internal_overlap_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_overlap_name(const std::string& value);
  std::string* _internal_mutable_overlap_name();
  public:

  // .esurfing.proto.math.Vector3f position = 5;
  bool has_position() const;
  private:
  bool _internal_has_position() const;
  public:
  void clear_position();
  const ::esurfing::proto::math::Vector3f& position() const;
  PROTOBUF_NODISCARD ::esurfing::proto::math::Vector3f* release_position();
  ::esurfing::proto::math::Vector3f* mutable_position();
  void set_allocated_position(::esurfing::proto::math::Vector3f* position);
  private:
  const ::esurfing::proto::math::Vector3f& _internal_position() const;
  ::esurfing::proto::math::Vector3f* _internal_mutable_position();
  public:
  void unsafe_arena_set_allocated_position(
      ::esurfing::proto::math::Vector3f* position);
  ::esurfing::proto::math::Vector3f* unsafe_arena_release_position();

  // .esurfing.proto.math.Vector3f shape = 6;
  bool has_shape() const;
  private:
  bool _internal_has_shape() const;
  public:
  void clear_shape();
  const ::esurfing::proto::math::Vector3f& shape() const;
  PROTOBUF_NODISCARD ::esurfing::proto::math::Vector3f* release_shape();
  ::esurfing::proto::math::Vector3f* mutable_shape();
  void set_allocated_shape(::esurfing::proto::math::Vector3f* shape);
  private:
  const ::esurfing::proto::math::Vector3f& _internal_shape() const;
  ::esurfing::proto::math::Vector3f* _internal_mutable_shape();
  public:
  void unsafe_arena_set_allocated_shape(
      ::esurfing::proto::math::Vector3f* shape);
  ::esurfing::proto::math::Vector3f* unsafe_arena_release_shape();

  // .esurfing.proto.math.Polygon hull = 7;
  bool has_hull() const;
  private:
  bool _internal_has_hull() const;
  public:
  void clear_hull();
  const ::esurfing::proto::math::Polygon& hull() const;
  PROTOBUF_NODISCARD ::esurfing::proto::math::Polygon* release_hull();
  ::esurfing::proto::math::Polygon* mutable_hull();
  void set_allocated_hull(::esurfing::proto::math::Polygon* hull);
  private:
  const ::esurfing::proto::math::Polygon& _internal_hull() const;
  ::esurfing::proto::math::Polygon* _internal_mutable_hull();
  public:
  void unsafe_arena_set_allocated_hull(
      ::esurfing::proto::math::Polygon* hull);
  ::esurfing::proto::math::Polygon* unsafe_arena_release_hull();

  // .esurfing.proto.perc.Velocity velocity = 9;
  bool has_velocity() const;
  private:
  bool _internal_has_velocity() const;
  public:
  void clear_velocity();
  const ::esurfing::proto::perc::Velocity& velocity() const;
  PROTOBUF_NODISCARD ::esurfing::proto::perc::Velocity* release_velocity();
  ::esurfing::proto::perc::Velocity* mutable_velocity();
  void set_allocated_velocity(::esurfing::proto::perc::Velocity* velocity);
  private:
  const ::esurfing::proto::perc::Velocity& _internal_velocity() const;
  ::esurfing::proto::perc::Velocity* _internal_mutable_velocity();
  public:
  void unsafe_arena_set_allocated_velocity(
      ::esurfing::proto::perc::Velocity* velocity);
  ::esurfing::proto::perc::Velocity* unsafe_arena_release_velocity();

  // .esurfing.proto.perc.Color color = 11;
  bool has_color() const;
  private:
  bool _internal_has_color() const;
  public:
  void clear_color();
  const ::esurfing::proto::perc::Color& color() const;
  PROTOBUF_NODISCARD ::esurfing::proto::perc::Color* release_color();
  ::esurfing::proto::perc::Color* mutable_color();
  void set_allocated_color(::esurfing::proto::perc::Color* color);
  private:
  const ::esurfing::proto::perc::Color& _internal_color() const;
  ::esurfing::proto::perc::Color* _internal_mutable_color();
  public:
  void unsafe_arena_set_allocated_color(
      ::esurfing::proto::perc::Color* color);
  ::esurfing::proto::perc::Color* unsafe_arena_release_color();

  // .esurfing.proto.perc.LicenePlate plate = 17;
  bool has_plate() const;
  private:
  bool _internal_has_plate() const;
  public:
  void clear_plate();
  const ::esurfing::proto::perc::LicenePlate& plate() const;
  PROTOBUF_NODISCARD ::esurfing::proto::perc::LicenePlate* release_plate();
  ::esurfing::proto::perc::LicenePlate* mutable_plate();
  void set_allocated_plate(::esurfing::proto::perc::LicenePlate* plate);
  private:
  const ::esurfing::proto::perc::LicenePlate& _internal_plate() const;
  ::esurfing::proto::perc::LicenePlate* _internal_mutable_plate();
  public:
  void unsafe_arena_set_allocated_plate(
      ::esurfing::proto::perc::LicenePlate* plate);
  ::esurfing::proto::perc::LicenePlate* unsafe_arena_release_plate();

  // fixed64 uuid = 2;
  void clear_uuid();
  uint64_t uuid() const;
  void set_uuid(uint64_t value);
  private:
  uint64_t _internal_uuid() const;
  void _internal_set_uuid(uint64_t value);
  public:

  // sint32 type = 3;
  void clear_type();
  int32_t type() const;
  void set_type(int32_t value);
  private:
  int32_t _internal_type() const;
  void _internal_set_type(int32_t value);
  public:

  // float confidence = 4;
  void clear_confidence();
  float confidence() const;
  void set_confidence(float value);
  private:
  float _internal_confidence() const;
  void _internal_set_confidence(float value);
  public:

  // float orientation = 8;
  void clear_orientation();
  float orientation() const;
  void set_orientation(float value);
  private:
  float _internal_orientation() const;
  void _internal_set_orientation(float value);
  public:

  // bool is_static = 10;
  void clear_is_static();
  bool is_static() const;
  void set_is_static(bool value);
  private:
  bool _internal_is_static() const;
  void _internal_set_is_static(bool value);
  public:

  // int64 parking_time = 16;
  void clear_parking_time();
  int64_t parking_time() const;
  void set_parking_time(int64_t value);
  private:
  int64_t _internal_parking_time() const;
  void _internal_set_parking_time(int64_t value);
  public:

  // int32 obj_color = 18;
  void clear_obj_color();
  int32_t obj_color() const;
  void set_obj_color(int32_t value);
  private:
  int32_t _internal_obj_color() const;
  void _internal_set_obj_color(int32_t value);
  public:

  // @@protoc_insertion_point(class_scope:esurfing.proto.perc.DetectedObject)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedField< float > feature_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::esurfing::proto::perc::WayPoints > trajectories_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> str_array_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t > int_array_;
    mutable std::atomic<int> _int_array_cached_byte_size_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr overlap_name_;
    ::esurfing::proto::math::Vector3f* position_;
    ::esurfing::proto::math::Vector3f* shape_;
    ::esurfing::proto::math::Polygon* hull_;
    ::esurfing::proto::perc::Velocity* velocity_;
    ::esurfing::proto::perc::Color* color_;
    ::esurfing::proto::perc::LicenePlate* plate_;
    uint64_t uuid_;
    int32_t type_;
    float confidence_;
    float orientation_;
    bool is_static_;
    int64_t parking_time_;
    int32_t obj_color_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_group_5fdetected_2eproto;
};
// -------------------------------------------------------------------

class Velocity final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:esurfing.proto.perc.Velocity) */ {
 public:
  inline Velocity() : Velocity(nullptr) {}
  ~Velocity() override;
  explicit PROTOBUF_CONSTEXPR Velocity(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  Velocity(const Velocity& from);
  Velocity(Velocity&& from) noexcept
    : Velocity() {
    *this = ::std::move(from);
  }

  inline Velocity& operator=(const Velocity& from) {
    CopyFrom(from);
    return *this;
  }
  inline Velocity& operator=(Velocity&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const Velocity& default_instance() {
    return *internal_default_instance();
  }
  static inline const Velocity* internal_default_instance() {
    return reinterpret_cast<const Velocity*>(
               &_Velocity_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  friend void swap(Velocity& a, Velocity& b) {
    a.Swap(&b);
  }
  inline void Swap(Velocity* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(Velocity* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  Velocity* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<Velocity>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const Velocity& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const Velocity& from) {
    Velocity::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Velocity* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "esurfing.proto.perc.Velocity";
  }
  protected:
  explicit Velocity(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kHeadingFieldNumber = 1,
    kSpeedFieldNumber = 2,
    kAccelerationFieldNumber = 3,
  };
  // float heading = 1;
  void clear_heading();
  float heading() const;
  void set_heading(float value);
  private:
  float _internal_heading() const;
  void _internal_set_heading(float value);
  public:

  // float speed = 2;
  void clear_speed();
  float speed() const;
  void set_speed(float value);
  private:
  float _internal_speed() const;
  void _internal_set_speed(float value);
  public:

  // float acceleration = 3;
  void clear_acceleration();
  float acceleration() const;
  void set_acceleration(float value);
  private:
  float _internal_acceleration() const;
  void _internal_set_acceleration(float value);
  public:

  // @@protoc_insertion_point(class_scope:esurfing.proto.perc.Velocity)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    float heading_;
    float speed_;
    float acceleration_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_group_5fdetected_2eproto;
};
// -------------------------------------------------------------------

class WayPoints final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:esurfing.proto.perc.WayPoints) */ {
 public:
  inline WayPoints() : WayPoints(nullptr) {}
  ~WayPoints() override;
  explicit PROTOBUF_CONSTEXPR WayPoints(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  WayPoints(const WayPoints& from);
  WayPoints(WayPoints&& from) noexcept
    : WayPoints() {
    *this = ::std::move(from);
  }

  inline WayPoints& operator=(const WayPoints& from) {
    CopyFrom(from);
    return *this;
  }
  inline WayPoints& operator=(WayPoints&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const WayPoints& default_instance() {
    return *internal_default_instance();
  }
  static inline const WayPoints* internal_default_instance() {
    return reinterpret_cast<const WayPoints*>(
               &_WayPoints_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    5;

  friend void swap(WayPoints& a, WayPoints& b) {
    a.Swap(&b);
  }
  inline void Swap(WayPoints* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(WayPoints* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  WayPoints* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<WayPoints>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const WayPoints& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const WayPoints& from) {
    WayPoints::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(WayPoints* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "esurfing.proto.perc.WayPoints";
  }
  protected:
  explicit WayPoints(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kWaypointsFieldNumber = 1,
    kProbabilityFieldNumber = 2,
  };
  // repeated .esurfing.proto.perc.WayPoint waypoints = 1;
  int waypoints_size() const;
  private:
  int _internal_waypoints_size() const;
  public:
  void clear_waypoints();
  ::esurfing::proto::perc::WayPoint* mutable_waypoints(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::esurfing::proto::perc::WayPoint >*
      mutable_waypoints();
  private:
  const ::esurfing::proto::perc::WayPoint& _internal_waypoints(int index) const;
  ::esurfing::proto::perc::WayPoint* _internal_add_waypoints();
  public:
  const ::esurfing::proto::perc::WayPoint& waypoints(int index) const;
  ::esurfing::proto::perc::WayPoint* add_waypoints();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::esurfing::proto::perc::WayPoint >&
      waypoints() const;

  // float probability = 2;
  void clear_probability();
  float probability() const;
  void set_probability(float value);
  private:
  float _internal_probability() const;
  void _internal_set_probability(float value);
  public:

  // @@protoc_insertion_point(class_scope:esurfing.proto.perc.WayPoints)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::esurfing::proto::perc::WayPoint > waypoints_;
    float probability_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_group_5fdetected_2eproto;
};
// -------------------------------------------------------------------

class WayPoint final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:esurfing.proto.perc.WayPoint) */ {
 public:
  inline WayPoint() : WayPoint(nullptr) {}
  ~WayPoint() override;
  explicit PROTOBUF_CONSTEXPR WayPoint(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  WayPoint(const WayPoint& from);
  WayPoint(WayPoint&& from) noexcept
    : WayPoint() {
    *this = ::std::move(from);
  }

  inline WayPoint& operator=(const WayPoint& from) {
    CopyFrom(from);
    return *this;
  }
  inline WayPoint& operator=(WayPoint&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const WayPoint& default_instance() {
    return *internal_default_instance();
  }
  static inline const WayPoint* internal_default_instance() {
    return reinterpret_cast<const WayPoint*>(
               &_WayPoint_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    6;

  friend void swap(WayPoint& a, WayPoint& b) {
    a.Swap(&b);
  }
  inline void Swap(WayPoint* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(WayPoint* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  WayPoint* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<WayPoint>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const WayPoint& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const WayPoint& from) {
    WayPoint::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(WayPoint* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "esurfing.proto.perc.WayPoint";
  }
  protected:
  explicit WayPoint(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kPositionFieldNumber = 2,
    kVelocityFieldNumber = 3,
    kTimeMeasFieldNumber = 1,
  };
  // .esurfing.proto.math.Vector3f position = 2;
  bool has_position() const;
  private:
  bool _internal_has_position() const;
  public:
  void clear_position();
  const ::esurfing::proto::math::Vector3f& position() const;
  PROTOBUF_NODISCARD ::esurfing::proto::math::Vector3f* release_position();
  ::esurfing::proto::math::Vector3f* mutable_position();
  void set_allocated_position(::esurfing::proto::math::Vector3f* position);
  private:
  const ::esurfing::proto::math::Vector3f& _internal_position() const;
  ::esurfing::proto::math::Vector3f* _internal_mutable_position();
  public:
  void unsafe_arena_set_allocated_position(
      ::esurfing::proto::math::Vector3f* position);
  ::esurfing::proto::math::Vector3f* unsafe_arena_release_position();

  // .esurfing.proto.perc.Velocity velocity = 3;
  bool has_velocity() const;
  private:
  bool _internal_has_velocity() const;
  public:
  void clear_velocity();
  const ::esurfing::proto::perc::Velocity& velocity() const;
  PROTOBUF_NODISCARD ::esurfing::proto::perc::Velocity* release_velocity();
  ::esurfing::proto::perc::Velocity* mutable_velocity();
  void set_allocated_velocity(::esurfing::proto::perc::Velocity* velocity);
  private:
  const ::esurfing::proto::perc::Velocity& _internal_velocity() const;
  ::esurfing::proto::perc::Velocity* _internal_mutable_velocity();
  public:
  void unsafe_arena_set_allocated_velocity(
      ::esurfing::proto::perc::Velocity* velocity);
  ::esurfing::proto::perc::Velocity* unsafe_arena_release_velocity();

  // sfixed64 time_meas = 1;
  void clear_time_meas();
  int64_t time_meas() const;
  void set_time_meas(int64_t value);
  private:
  int64_t _internal_time_meas() const;
  void _internal_set_time_meas(int64_t value);
  public:

  // @@protoc_insertion_point(class_scope:esurfing.proto.perc.WayPoint)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::esurfing::proto::math::Vector3f* position_;
    ::esurfing::proto::perc::Velocity* velocity_;
    int64_t time_meas_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_group_5fdetected_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// Color

// int32 r = 1;
inline void Color::clear_r() {
  _impl_.r_ = 0;
}
inline int32_t Color::_internal_r() const {
  return _impl_.r_;
}
inline int32_t Color::r() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.perc.Color.r)
  return _internal_r();
}
inline void Color::_internal_set_r(int32_t value) {
  
  _impl_.r_ = value;
}
inline void Color::set_r(int32_t value) {
  _internal_set_r(value);
  // @@protoc_insertion_point(field_set:esurfing.proto.perc.Color.r)
}

// int32 g = 2;
inline void Color::clear_g() {
  _impl_.g_ = 0;
}
inline int32_t Color::_internal_g() const {
  return _impl_.g_;
}
inline int32_t Color::g() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.perc.Color.g)
  return _internal_g();
}
inline void Color::_internal_set_g(int32_t value) {
  
  _impl_.g_ = value;
}
inline void Color::set_g(int32_t value) {
  _internal_set_g(value);
  // @@protoc_insertion_point(field_set:esurfing.proto.perc.Color.g)
}

// int32 b = 3;
inline void Color::clear_b() {
  _impl_.b_ = 0;
}
inline int32_t Color::_internal_b() const {
  return _impl_.b_;
}
inline int32_t Color::b() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.perc.Color.b)
  return _internal_b();
}
inline void Color::_internal_set_b(int32_t value) {
  
  _impl_.b_ = value;
}
inline void Color::set_b(int32_t value) {
  _internal_set_b(value);
  // @@protoc_insertion_point(field_set:esurfing.proto.perc.Color.b)
}

// float a = 4;
inline void Color::clear_a() {
  _impl_.a_ = 0;
}
inline float Color::_internal_a() const {
  return _impl_.a_;
}
inline float Color::a() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.perc.Color.a)
  return _internal_a();
}
inline void Color::_internal_set_a(float value) {
  
  _impl_.a_ = value;
}
inline void Color::set_a(float value) {
  _internal_set_a(value);
  // @@protoc_insertion_point(field_set:esurfing.proto.perc.Color.a)
}

// -------------------------------------------------------------------

// DetectedObjects

// sfixed64 time_meas = 1;
inline void DetectedObjects::clear_time_meas() {
  _impl_.time_meas_ = int64_t{0};
}
inline int64_t DetectedObjects::_internal_time_meas() const {
  return _impl_.time_meas_;
}
inline int64_t DetectedObjects::time_meas() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.perc.DetectedObjects.time_meas)
  return _internal_time_meas();
}
inline void DetectedObjects::_internal_set_time_meas(int64_t value) {
  
  _impl_.time_meas_ = value;
}
inline void DetectedObjects::set_time_meas(int64_t value) {
  _internal_set_time_meas(value);
  // @@protoc_insertion_point(field_set:esurfing.proto.perc.DetectedObjects.time_meas)
}

// sfixed64 time_pub = 2;
inline void DetectedObjects::clear_time_pub() {
  _impl_.time_pub_ = int64_t{0};
}
inline int64_t DetectedObjects::_internal_time_pub() const {
  return _impl_.time_pub_;
}
inline int64_t DetectedObjects::time_pub() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.perc.DetectedObjects.time_pub)
  return _internal_time_pub();
}
inline void DetectedObjects::_internal_set_time_pub(int64_t value) {
  
  _impl_.time_pub_ = value;
}
inline void DetectedObjects::set_time_pub(int64_t value) {
  _internal_set_time_pub(value);
  // @@protoc_insertion_point(field_set:esurfing.proto.perc.DetectedObjects.time_pub)
}

// bytes group_name = 3;
inline void DetectedObjects::clear_group_name() {
  _impl_.group_name_.ClearToEmpty();
}
inline const std::string& DetectedObjects::group_name() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.perc.DetectedObjects.group_name)
  return _internal_group_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void DetectedObjects::set_group_name(ArgT0&& arg0, ArgT... args) {
 
 _impl_.group_name_.SetBytes(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:esurfing.proto.perc.DetectedObjects.group_name)
}
inline std::string* DetectedObjects::mutable_group_name() {
  std::string* _s = _internal_mutable_group_name();
  // @@protoc_insertion_point(field_mutable:esurfing.proto.perc.DetectedObjects.group_name)
  return _s;
}
inline const std::string& DetectedObjects::_internal_group_name() const {
  return _impl_.group_name_.Get();
}
inline void DetectedObjects::_internal_set_group_name(const std::string& value) {
  
  _impl_.group_name_.Set(value, GetArenaForAllocation());
}
inline std::string* DetectedObjects::_internal_mutable_group_name() {
  
  return _impl_.group_name_.Mutable(GetArenaForAllocation());
}
inline std::string* DetectedObjects::release_group_name() {
  // @@protoc_insertion_point(field_release:esurfing.proto.perc.DetectedObjects.group_name)
  return _impl_.group_name_.Release();
}
inline void DetectedObjects::set_allocated_group_name(std::string* group_name) {
  if (group_name != nullptr) {
    
  } else {
    
  }
  _impl_.group_name_.SetAllocated(group_name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.group_name_.IsDefault()) {
    _impl_.group_name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:esurfing.proto.perc.DetectedObjects.group_name)
}

// bytes group_info = 4;
inline void DetectedObjects::clear_group_info() {
  _impl_.group_info_.ClearToEmpty();
}
inline const std::string& DetectedObjects::group_info() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.perc.DetectedObjects.group_info)
  return _internal_group_info();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void DetectedObjects::set_group_info(ArgT0&& arg0, ArgT... args) {
 
 _impl_.group_info_.SetBytes(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:esurfing.proto.perc.DetectedObjects.group_info)
}
inline std::string* DetectedObjects::mutable_group_info() {
  std::string* _s = _internal_mutable_group_info();
  // @@protoc_insertion_point(field_mutable:esurfing.proto.perc.DetectedObjects.group_info)
  return _s;
}
inline const std::string& DetectedObjects::_internal_group_info() const {
  return _impl_.group_info_.Get();
}
inline void DetectedObjects::_internal_set_group_info(const std::string& value) {
  
  _impl_.group_info_.Set(value, GetArenaForAllocation());
}
inline std::string* DetectedObjects::_internal_mutable_group_info() {
  
  return _impl_.group_info_.Mutable(GetArenaForAllocation());
}
inline std::string* DetectedObjects::release_group_info() {
  // @@protoc_insertion_point(field_release:esurfing.proto.perc.DetectedObjects.group_info)
  return _impl_.group_info_.Release();
}
inline void DetectedObjects::set_allocated_group_info(std::string* group_info) {
  if (group_info != nullptr) {
    
  } else {
    
  }
  _impl_.group_info_.SetAllocated(group_info, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.group_info_.IsDefault()) {
    _impl_.group_info_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:esurfing.proto.perc.DetectedObjects.group_info)
}

// repeated .esurfing.proto.perc.DetectedObject objects = 5;
inline int DetectedObjects::_internal_objects_size() const {
  return _impl_.objects_.size();
}
inline int DetectedObjects::objects_size() const {
  return _internal_objects_size();
}
inline void DetectedObjects::clear_objects() {
  _impl_.objects_.Clear();
}
inline ::esurfing::proto::perc::DetectedObject* DetectedObjects::mutable_objects(int index) {
  // @@protoc_insertion_point(field_mutable:esurfing.proto.perc.DetectedObjects.objects)
  return _impl_.objects_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::esurfing::proto::perc::DetectedObject >*
DetectedObjects::mutable_objects() {
  // @@protoc_insertion_point(field_mutable_list:esurfing.proto.perc.DetectedObjects.objects)
  return &_impl_.objects_;
}
inline const ::esurfing::proto::perc::DetectedObject& DetectedObjects::_internal_objects(int index) const {
  return _impl_.objects_.Get(index);
}
inline const ::esurfing::proto::perc::DetectedObject& DetectedObjects::objects(int index) const {
  // @@protoc_insertion_point(field_get:esurfing.proto.perc.DetectedObjects.objects)
  return _internal_objects(index);
}
inline ::esurfing::proto::perc::DetectedObject* DetectedObjects::_internal_add_objects() {
  return _impl_.objects_.Add();
}
inline ::esurfing::proto::perc::DetectedObject* DetectedObjects::add_objects() {
  ::esurfing::proto::perc::DetectedObject* _add = _internal_add_objects();
  // @@protoc_insertion_point(field_add:esurfing.proto.perc.DetectedObjects.objects)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::esurfing::proto::perc::DetectedObject >&
DetectedObjects::objects() const {
  // @@protoc_insertion_point(field_list:esurfing.proto.perc.DetectedObjects.objects)
  return _impl_.objects_;
}

// -------------------------------------------------------------------

// LicenePlate

// int32 color = 1;
inline void LicenePlate::clear_color() {
  _impl_.color_ = 0;
}
inline int32_t LicenePlate::_internal_color() const {
  return _impl_.color_;
}
inline int32_t LicenePlate::color() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.perc.LicenePlate.color)
  return _internal_color();
}
inline void LicenePlate::_internal_set_color(int32_t value) {
  
  _impl_.color_ = value;
}
inline void LicenePlate::set_color(int32_t value) {
  _internal_set_color(value);
  // @@protoc_insertion_point(field_set:esurfing.proto.perc.LicenePlate.color)
}

// float color_confidence = 2;
inline void LicenePlate::clear_color_confidence() {
  _impl_.color_confidence_ = 0;
}
inline float LicenePlate::_internal_color_confidence() const {
  return _impl_.color_confidence_;
}
inline float LicenePlate::color_confidence() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.perc.LicenePlate.color_confidence)
  return _internal_color_confidence();
}
inline void LicenePlate::_internal_set_color_confidence(float value) {
  
  _impl_.color_confidence_ = value;
}
inline void LicenePlate::set_color_confidence(float value) {
  _internal_set_color_confidence(value);
  // @@protoc_insertion_point(field_set:esurfing.proto.perc.LicenePlate.color_confidence)
}

// string number = 3;
inline void LicenePlate::clear_number() {
  _impl_.number_.ClearToEmpty();
}
inline const std::string& LicenePlate::number() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.perc.LicenePlate.number)
  return _internal_number();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void LicenePlate::set_number(ArgT0&& arg0, ArgT... args) {
 
 _impl_.number_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:esurfing.proto.perc.LicenePlate.number)
}
inline std::string* LicenePlate::mutable_number() {
  std::string* _s = _internal_mutable_number();
  // @@protoc_insertion_point(field_mutable:esurfing.proto.perc.LicenePlate.number)
  return _s;
}
inline const std::string& LicenePlate::_internal_number() const {
  return _impl_.number_.Get();
}
inline void LicenePlate::_internal_set_number(const std::string& value) {
  
  _impl_.number_.Set(value, GetArenaForAllocation());
}
inline std::string* LicenePlate::_internal_mutable_number() {
  
  return _impl_.number_.Mutable(GetArenaForAllocation());
}
inline std::string* LicenePlate::release_number() {
  // @@protoc_insertion_point(field_release:esurfing.proto.perc.LicenePlate.number)
  return _impl_.number_.Release();
}
inline void LicenePlate::set_allocated_number(std::string* number) {
  if (number != nullptr) {
    
  } else {
    
  }
  _impl_.number_.SetAllocated(number, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.number_.IsDefault()) {
    _impl_.number_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:esurfing.proto.perc.LicenePlate.number)
}

// float number_confidence = 4;
inline void LicenePlate::clear_number_confidence() {
  _impl_.number_confidence_ = 0;
}
inline float LicenePlate::_internal_number_confidence() const {
  return _impl_.number_confidence_;
}
inline float LicenePlate::number_confidence() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.perc.LicenePlate.number_confidence)
  return _internal_number_confidence();
}
inline void LicenePlate::_internal_set_number_confidence(float value) {
  
  _impl_.number_confidence_ = value;
}
inline void LicenePlate::set_number_confidence(float value) {
  _internal_set_number_confidence(value);
  // @@protoc_insertion_point(field_set:esurfing.proto.perc.LicenePlate.number_confidence)
}

// -------------------------------------------------------------------

// DetectedObject

// bytes overlap_name = 1;
inline void DetectedObject::clear_overlap_name() {
  _impl_.overlap_name_.ClearToEmpty();
}
inline const std::string& DetectedObject::overlap_name() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.perc.DetectedObject.overlap_name)
  return _internal_overlap_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void DetectedObject::set_overlap_name(ArgT0&& arg0, ArgT... args) {
 
 _impl_.overlap_name_.SetBytes(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:esurfing.proto.perc.DetectedObject.overlap_name)
}
inline std::string* DetectedObject::mutable_overlap_name() {
  std::string* _s = _internal_mutable_overlap_name();
  // @@protoc_insertion_point(field_mutable:esurfing.proto.perc.DetectedObject.overlap_name)
  return _s;
}
inline const std::string& DetectedObject::_internal_overlap_name() const {
  return _impl_.overlap_name_.Get();
}
inline void DetectedObject::_internal_set_overlap_name(const std::string& value) {
  
  _impl_.overlap_name_.Set(value, GetArenaForAllocation());
}
inline std::string* DetectedObject::_internal_mutable_overlap_name() {
  
  return _impl_.overlap_name_.Mutable(GetArenaForAllocation());
}
inline std::string* DetectedObject::release_overlap_name() {
  // @@protoc_insertion_point(field_release:esurfing.proto.perc.DetectedObject.overlap_name)
  return _impl_.overlap_name_.Release();
}
inline void DetectedObject::set_allocated_overlap_name(std::string* overlap_name) {
  if (overlap_name != nullptr) {
    
  } else {
    
  }
  _impl_.overlap_name_.SetAllocated(overlap_name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.overlap_name_.IsDefault()) {
    _impl_.overlap_name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:esurfing.proto.perc.DetectedObject.overlap_name)
}

// fixed64 uuid = 2;
inline void DetectedObject::clear_uuid() {
  _impl_.uuid_ = uint64_t{0u};
}
inline uint64_t DetectedObject::_internal_uuid() const {
  return _impl_.uuid_;
}
inline uint64_t DetectedObject::uuid() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.perc.DetectedObject.uuid)
  return _internal_uuid();
}
inline void DetectedObject::_internal_set_uuid(uint64_t value) {
  
  _impl_.uuid_ = value;
}
inline void DetectedObject::set_uuid(uint64_t value) {
  _internal_set_uuid(value);
  // @@protoc_insertion_point(field_set:esurfing.proto.perc.DetectedObject.uuid)
}

// sint32 type = 3;
inline void DetectedObject::clear_type() {
  _impl_.type_ = 0;
}
inline int32_t DetectedObject::_internal_type() const {
  return _impl_.type_;
}
inline int32_t DetectedObject::type() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.perc.DetectedObject.type)
  return _internal_type();
}
inline void DetectedObject::_internal_set_type(int32_t value) {
  
  _impl_.type_ = value;
}
inline void DetectedObject::set_type(int32_t value) {
  _internal_set_type(value);
  // @@protoc_insertion_point(field_set:esurfing.proto.perc.DetectedObject.type)
}

// float confidence = 4;
inline void DetectedObject::clear_confidence() {
  _impl_.confidence_ = 0;
}
inline float DetectedObject::_internal_confidence() const {
  return _impl_.confidence_;
}
inline float DetectedObject::confidence() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.perc.DetectedObject.confidence)
  return _internal_confidence();
}
inline void DetectedObject::_internal_set_confidence(float value) {
  
  _impl_.confidence_ = value;
}
inline void DetectedObject::set_confidence(float value) {
  _internal_set_confidence(value);
  // @@protoc_insertion_point(field_set:esurfing.proto.perc.DetectedObject.confidence)
}

// .esurfing.proto.math.Vector3f position = 5;
inline bool DetectedObject::_internal_has_position() const {
  return this != internal_default_instance() && _impl_.position_ != nullptr;
}
inline bool DetectedObject::has_position() const {
  return _internal_has_position();
}
inline const ::esurfing::proto::math::Vector3f& DetectedObject::_internal_position() const {
  const ::esurfing::proto::math::Vector3f* p = _impl_.position_;
  return p != nullptr ? *p : reinterpret_cast<const ::esurfing::proto::math::Vector3f&>(
      ::esurfing::proto::math::_Vector3f_default_instance_);
}
inline const ::esurfing::proto::math::Vector3f& DetectedObject::position() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.perc.DetectedObject.position)
  return _internal_position();
}
inline void DetectedObject::unsafe_arena_set_allocated_position(
    ::esurfing::proto::math::Vector3f* position) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.position_);
  }
  _impl_.position_ = position;
  if (position) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:esurfing.proto.perc.DetectedObject.position)
}
inline ::esurfing::proto::math::Vector3f* DetectedObject::release_position() {
  
  ::esurfing::proto::math::Vector3f* temp = _impl_.position_;
  _impl_.position_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::esurfing::proto::math::Vector3f* DetectedObject::unsafe_arena_release_position() {
  // @@protoc_insertion_point(field_release:esurfing.proto.perc.DetectedObject.position)
  
  ::esurfing::proto::math::Vector3f* temp = _impl_.position_;
  _impl_.position_ = nullptr;
  return temp;
}
inline ::esurfing::proto::math::Vector3f* DetectedObject::_internal_mutable_position() {
  
  if (_impl_.position_ == nullptr) {
    auto* p = CreateMaybeMessage<::esurfing::proto::math::Vector3f>(GetArenaForAllocation());
    _impl_.position_ = p;
  }
  return _impl_.position_;
}
inline ::esurfing::proto::math::Vector3f* DetectedObject::mutable_position() {
  ::esurfing::proto::math::Vector3f* _msg = _internal_mutable_position();
  // @@protoc_insertion_point(field_mutable:esurfing.proto.perc.DetectedObject.position)
  return _msg;
}
inline void DetectedObject::set_allocated_position(::esurfing::proto::math::Vector3f* position) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.position_);
  }
  if (position) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(position));
    if (message_arena != submessage_arena) {
      position = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, position, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.position_ = position;
  // @@protoc_insertion_point(field_set_allocated:esurfing.proto.perc.DetectedObject.position)
}

// .esurfing.proto.math.Vector3f shape = 6;
inline bool DetectedObject::_internal_has_shape() const {
  return this != internal_default_instance() && _impl_.shape_ != nullptr;
}
inline bool DetectedObject::has_shape() const {
  return _internal_has_shape();
}
inline const ::esurfing::proto::math::Vector3f& DetectedObject::_internal_shape() const {
  const ::esurfing::proto::math::Vector3f* p = _impl_.shape_;
  return p != nullptr ? *p : reinterpret_cast<const ::esurfing::proto::math::Vector3f&>(
      ::esurfing::proto::math::_Vector3f_default_instance_);
}
inline const ::esurfing::proto::math::Vector3f& DetectedObject::shape() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.perc.DetectedObject.shape)
  return _internal_shape();
}
inline void DetectedObject::unsafe_arena_set_allocated_shape(
    ::esurfing::proto::math::Vector3f* shape) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.shape_);
  }
  _impl_.shape_ = shape;
  if (shape) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:esurfing.proto.perc.DetectedObject.shape)
}
inline ::esurfing::proto::math::Vector3f* DetectedObject::release_shape() {
  
  ::esurfing::proto::math::Vector3f* temp = _impl_.shape_;
  _impl_.shape_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::esurfing::proto::math::Vector3f* DetectedObject::unsafe_arena_release_shape() {
  // @@protoc_insertion_point(field_release:esurfing.proto.perc.DetectedObject.shape)
  
  ::esurfing::proto::math::Vector3f* temp = _impl_.shape_;
  _impl_.shape_ = nullptr;
  return temp;
}
inline ::esurfing::proto::math::Vector3f* DetectedObject::_internal_mutable_shape() {
  
  if (_impl_.shape_ == nullptr) {
    auto* p = CreateMaybeMessage<::esurfing::proto::math::Vector3f>(GetArenaForAllocation());
    _impl_.shape_ = p;
  }
  return _impl_.shape_;
}
inline ::esurfing::proto::math::Vector3f* DetectedObject::mutable_shape() {
  ::esurfing::proto::math::Vector3f* _msg = _internal_mutable_shape();
  // @@protoc_insertion_point(field_mutable:esurfing.proto.perc.DetectedObject.shape)
  return _msg;
}
inline void DetectedObject::set_allocated_shape(::esurfing::proto::math::Vector3f* shape) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.shape_);
  }
  if (shape) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(shape));
    if (message_arena != submessage_arena) {
      shape = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, shape, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.shape_ = shape;
  // @@protoc_insertion_point(field_set_allocated:esurfing.proto.perc.DetectedObject.shape)
}

// .esurfing.proto.math.Polygon hull = 7;
inline bool DetectedObject::_internal_has_hull() const {
  return this != internal_default_instance() && _impl_.hull_ != nullptr;
}
inline bool DetectedObject::has_hull() const {
  return _internal_has_hull();
}
inline const ::esurfing::proto::math::Polygon& DetectedObject::_internal_hull() const {
  const ::esurfing::proto::math::Polygon* p = _impl_.hull_;
  return p != nullptr ? *p : reinterpret_cast<const ::esurfing::proto::math::Polygon&>(
      ::esurfing::proto::math::_Polygon_default_instance_);
}
inline const ::esurfing::proto::math::Polygon& DetectedObject::hull() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.perc.DetectedObject.hull)
  return _internal_hull();
}
inline void DetectedObject::unsafe_arena_set_allocated_hull(
    ::esurfing::proto::math::Polygon* hull) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.hull_);
  }
  _impl_.hull_ = hull;
  if (hull) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:esurfing.proto.perc.DetectedObject.hull)
}
inline ::esurfing::proto::math::Polygon* DetectedObject::release_hull() {
  
  ::esurfing::proto::math::Polygon* temp = _impl_.hull_;
  _impl_.hull_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::esurfing::proto::math::Polygon* DetectedObject::unsafe_arena_release_hull() {
  // @@protoc_insertion_point(field_release:esurfing.proto.perc.DetectedObject.hull)
  
  ::esurfing::proto::math::Polygon* temp = _impl_.hull_;
  _impl_.hull_ = nullptr;
  return temp;
}
inline ::esurfing::proto::math::Polygon* DetectedObject::_internal_mutable_hull() {
  
  if (_impl_.hull_ == nullptr) {
    auto* p = CreateMaybeMessage<::esurfing::proto::math::Polygon>(GetArenaForAllocation());
    _impl_.hull_ = p;
  }
  return _impl_.hull_;
}
inline ::esurfing::proto::math::Polygon* DetectedObject::mutable_hull() {
  ::esurfing::proto::math::Polygon* _msg = _internal_mutable_hull();
  // @@protoc_insertion_point(field_mutable:esurfing.proto.perc.DetectedObject.hull)
  return _msg;
}
inline void DetectedObject::set_allocated_hull(::esurfing::proto::math::Polygon* hull) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.hull_);
  }
  if (hull) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(hull));
    if (message_arena != submessage_arena) {
      hull = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, hull, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.hull_ = hull;
  // @@protoc_insertion_point(field_set_allocated:esurfing.proto.perc.DetectedObject.hull)
}

// float orientation = 8;
inline void DetectedObject::clear_orientation() {
  _impl_.orientation_ = 0;
}
inline float DetectedObject::_internal_orientation() const {
  return _impl_.orientation_;
}
inline float DetectedObject::orientation() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.perc.DetectedObject.orientation)
  return _internal_orientation();
}
inline void DetectedObject::_internal_set_orientation(float value) {
  
  _impl_.orientation_ = value;
}
inline void DetectedObject::set_orientation(float value) {
  _internal_set_orientation(value);
  // @@protoc_insertion_point(field_set:esurfing.proto.perc.DetectedObject.orientation)
}

// .esurfing.proto.perc.Velocity velocity = 9;
inline bool DetectedObject::_internal_has_velocity() const {
  return this != internal_default_instance() && _impl_.velocity_ != nullptr;
}
inline bool DetectedObject::has_velocity() const {
  return _internal_has_velocity();
}
inline void DetectedObject::clear_velocity() {
  if (GetArenaForAllocation() == nullptr && _impl_.velocity_ != nullptr) {
    delete _impl_.velocity_;
  }
  _impl_.velocity_ = nullptr;
}
inline const ::esurfing::proto::perc::Velocity& DetectedObject::_internal_velocity() const {
  const ::esurfing::proto::perc::Velocity* p = _impl_.velocity_;
  return p != nullptr ? *p : reinterpret_cast<const ::esurfing::proto::perc::Velocity&>(
      ::esurfing::proto::perc::_Velocity_default_instance_);
}
inline const ::esurfing::proto::perc::Velocity& DetectedObject::velocity() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.perc.DetectedObject.velocity)
  return _internal_velocity();
}
inline void DetectedObject::unsafe_arena_set_allocated_velocity(
    ::esurfing::proto::perc::Velocity* velocity) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.velocity_);
  }
  _impl_.velocity_ = velocity;
  if (velocity) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:esurfing.proto.perc.DetectedObject.velocity)
}
inline ::esurfing::proto::perc::Velocity* DetectedObject::release_velocity() {
  
  ::esurfing::proto::perc::Velocity* temp = _impl_.velocity_;
  _impl_.velocity_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::esurfing::proto::perc::Velocity* DetectedObject::unsafe_arena_release_velocity() {
  // @@protoc_insertion_point(field_release:esurfing.proto.perc.DetectedObject.velocity)
  
  ::esurfing::proto::perc::Velocity* temp = _impl_.velocity_;
  _impl_.velocity_ = nullptr;
  return temp;
}
inline ::esurfing::proto::perc::Velocity* DetectedObject::_internal_mutable_velocity() {
  
  if (_impl_.velocity_ == nullptr) {
    auto* p = CreateMaybeMessage<::esurfing::proto::perc::Velocity>(GetArenaForAllocation());
    _impl_.velocity_ = p;
  }
  return _impl_.velocity_;
}
inline ::esurfing::proto::perc::Velocity* DetectedObject::mutable_velocity() {
  ::esurfing::proto::perc::Velocity* _msg = _internal_mutable_velocity();
  // @@protoc_insertion_point(field_mutable:esurfing.proto.perc.DetectedObject.velocity)
  return _msg;
}
inline void DetectedObject::set_allocated_velocity(::esurfing::proto::perc::Velocity* velocity) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.velocity_;
  }
  if (velocity) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(velocity);
    if (message_arena != submessage_arena) {
      velocity = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, velocity, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.velocity_ = velocity;
  // @@protoc_insertion_point(field_set_allocated:esurfing.proto.perc.DetectedObject.velocity)
}

// bool is_static = 10;
inline void DetectedObject::clear_is_static() {
  _impl_.is_static_ = false;
}
inline bool DetectedObject::_internal_is_static() const {
  return _impl_.is_static_;
}
inline bool DetectedObject::is_static() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.perc.DetectedObject.is_static)
  return _internal_is_static();
}
inline void DetectedObject::_internal_set_is_static(bool value) {
  
  _impl_.is_static_ = value;
}
inline void DetectedObject::set_is_static(bool value) {
  _internal_set_is_static(value);
  // @@protoc_insertion_point(field_set:esurfing.proto.perc.DetectedObject.is_static)
}

// .esurfing.proto.perc.Color color = 11;
inline bool DetectedObject::_internal_has_color() const {
  return this != internal_default_instance() && _impl_.color_ != nullptr;
}
inline bool DetectedObject::has_color() const {
  return _internal_has_color();
}
inline void DetectedObject::clear_color() {
  if (GetArenaForAllocation() == nullptr && _impl_.color_ != nullptr) {
    delete _impl_.color_;
  }
  _impl_.color_ = nullptr;
}
inline const ::esurfing::proto::perc::Color& DetectedObject::_internal_color() const {
  const ::esurfing::proto::perc::Color* p = _impl_.color_;
  return p != nullptr ? *p : reinterpret_cast<const ::esurfing::proto::perc::Color&>(
      ::esurfing::proto::perc::_Color_default_instance_);
}
inline const ::esurfing::proto::perc::Color& DetectedObject::color() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.perc.DetectedObject.color)
  return _internal_color();
}
inline void DetectedObject::unsafe_arena_set_allocated_color(
    ::esurfing::proto::perc::Color* color) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.color_);
  }
  _impl_.color_ = color;
  if (color) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:esurfing.proto.perc.DetectedObject.color)
}
inline ::esurfing::proto::perc::Color* DetectedObject::release_color() {
  
  ::esurfing::proto::perc::Color* temp = _impl_.color_;
  _impl_.color_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::esurfing::proto::perc::Color* DetectedObject::unsafe_arena_release_color() {
  // @@protoc_insertion_point(field_release:esurfing.proto.perc.DetectedObject.color)
  
  ::esurfing::proto::perc::Color* temp = _impl_.color_;
  _impl_.color_ = nullptr;
  return temp;
}
inline ::esurfing::proto::perc::Color* DetectedObject::_internal_mutable_color() {
  
  if (_impl_.color_ == nullptr) {
    auto* p = CreateMaybeMessage<::esurfing::proto::perc::Color>(GetArenaForAllocation());
    _impl_.color_ = p;
  }
  return _impl_.color_;
}
inline ::esurfing::proto::perc::Color* DetectedObject::mutable_color() {
  ::esurfing::proto::perc::Color* _msg = _internal_mutable_color();
  // @@protoc_insertion_point(field_mutable:esurfing.proto.perc.DetectedObject.color)
  return _msg;
}
inline void DetectedObject::set_allocated_color(::esurfing::proto::perc::Color* color) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.color_;
  }
  if (color) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(color);
    if (message_arena != submessage_arena) {
      color = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, color, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.color_ = color;
  // @@protoc_insertion_point(field_set_allocated:esurfing.proto.perc.DetectedObject.color)
}

// repeated float feature = 12;
inline int DetectedObject::_internal_feature_size() const {
  return _impl_.feature_.size();
}
inline int DetectedObject::feature_size() const {
  return _internal_feature_size();
}
inline void DetectedObject::clear_feature() {
  _impl_.feature_.Clear();
}
inline float DetectedObject::_internal_feature(int index) const {
  return _impl_.feature_.Get(index);
}
inline float DetectedObject::feature(int index) const {
  // @@protoc_insertion_point(field_get:esurfing.proto.perc.DetectedObject.feature)
  return _internal_feature(index);
}
inline void DetectedObject::set_feature(int index, float value) {
  _impl_.feature_.Set(index, value);
  // @@protoc_insertion_point(field_set:esurfing.proto.perc.DetectedObject.feature)
}
inline void DetectedObject::_internal_add_feature(float value) {
  _impl_.feature_.Add(value);
}
inline void DetectedObject::add_feature(float value) {
  _internal_add_feature(value);
  // @@protoc_insertion_point(field_add:esurfing.proto.perc.DetectedObject.feature)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >&
DetectedObject::_internal_feature() const {
  return _impl_.feature_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >&
DetectedObject::feature() const {
  // @@protoc_insertion_point(field_list:esurfing.proto.perc.DetectedObject.feature)
  return _internal_feature();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >*
DetectedObject::_internal_mutable_feature() {
  return &_impl_.feature_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >*
DetectedObject::mutable_feature() {
  // @@protoc_insertion_point(field_mutable_list:esurfing.proto.perc.DetectedObject.feature)
  return _internal_mutable_feature();
}

// repeated .esurfing.proto.perc.WayPoints trajectories = 13;
inline int DetectedObject::_internal_trajectories_size() const {
  return _impl_.trajectories_.size();
}
inline int DetectedObject::trajectories_size() const {
  return _internal_trajectories_size();
}
inline void DetectedObject::clear_trajectories() {
  _impl_.trajectories_.Clear();
}
inline ::esurfing::proto::perc::WayPoints* DetectedObject::mutable_trajectories(int index) {
  // @@protoc_insertion_point(field_mutable:esurfing.proto.perc.DetectedObject.trajectories)
  return _impl_.trajectories_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::esurfing::proto::perc::WayPoints >*
DetectedObject::mutable_trajectories() {
  // @@protoc_insertion_point(field_mutable_list:esurfing.proto.perc.DetectedObject.trajectories)
  return &_impl_.trajectories_;
}
inline const ::esurfing::proto::perc::WayPoints& DetectedObject::_internal_trajectories(int index) const {
  return _impl_.trajectories_.Get(index);
}
inline const ::esurfing::proto::perc::WayPoints& DetectedObject::trajectories(int index) const {
  // @@protoc_insertion_point(field_get:esurfing.proto.perc.DetectedObject.trajectories)
  return _internal_trajectories(index);
}
inline ::esurfing::proto::perc::WayPoints* DetectedObject::_internal_add_trajectories() {
  return _impl_.trajectories_.Add();
}
inline ::esurfing::proto::perc::WayPoints* DetectedObject::add_trajectories() {
  ::esurfing::proto::perc::WayPoints* _add = _internal_add_trajectories();
  // @@protoc_insertion_point(field_add:esurfing.proto.perc.DetectedObject.trajectories)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::esurfing::proto::perc::WayPoints >&
DetectedObject::trajectories() const {
  // @@protoc_insertion_point(field_list:esurfing.proto.perc.DetectedObject.trajectories)
  return _impl_.trajectories_;
}

// repeated bytes str_array = 14;
inline int DetectedObject::_internal_str_array_size() const {
  return _impl_.str_array_.size();
}
inline int DetectedObject::str_array_size() const {
  return _internal_str_array_size();
}
inline void DetectedObject::clear_str_array() {
  _impl_.str_array_.Clear();
}
inline std::string* DetectedObject::add_str_array() {
  std::string* _s = _internal_add_str_array();
  // @@protoc_insertion_point(field_add_mutable:esurfing.proto.perc.DetectedObject.str_array)
  return _s;
}
inline const std::string& DetectedObject::_internal_str_array(int index) const {
  return _impl_.str_array_.Get(index);
}
inline const std::string& DetectedObject::str_array(int index) const {
  // @@protoc_insertion_point(field_get:esurfing.proto.perc.DetectedObject.str_array)
  return _internal_str_array(index);
}
inline std::string* DetectedObject::mutable_str_array(int index) {
  // @@protoc_insertion_point(field_mutable:esurfing.proto.perc.DetectedObject.str_array)
  return _impl_.str_array_.Mutable(index);
}
inline void DetectedObject::set_str_array(int index, const std::string& value) {
  _impl_.str_array_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set:esurfing.proto.perc.DetectedObject.str_array)
}
inline void DetectedObject::set_str_array(int index, std::string&& value) {
  _impl_.str_array_.Mutable(index)->assign(std::move(value));
  // @@protoc_insertion_point(field_set:esurfing.proto.perc.DetectedObject.str_array)
}
inline void DetectedObject::set_str_array(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.str_array_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:esurfing.proto.perc.DetectedObject.str_array)
}
inline void DetectedObject::set_str_array(int index, const void* value, size_t size) {
  _impl_.str_array_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:esurfing.proto.perc.DetectedObject.str_array)
}
inline std::string* DetectedObject::_internal_add_str_array() {
  return _impl_.str_array_.Add();
}
inline void DetectedObject::add_str_array(const std::string& value) {
  _impl_.str_array_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:esurfing.proto.perc.DetectedObject.str_array)
}
inline void DetectedObject::add_str_array(std::string&& value) {
  _impl_.str_array_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:esurfing.proto.perc.DetectedObject.str_array)
}
inline void DetectedObject::add_str_array(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.str_array_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:esurfing.proto.perc.DetectedObject.str_array)
}
inline void DetectedObject::add_str_array(const void* value, size_t size) {
  _impl_.str_array_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:esurfing.proto.perc.DetectedObject.str_array)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
DetectedObject::str_array() const {
  // @@protoc_insertion_point(field_list:esurfing.proto.perc.DetectedObject.str_array)
  return _impl_.str_array_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
DetectedObject::mutable_str_array() {
  // @@protoc_insertion_point(field_mutable_list:esurfing.proto.perc.DetectedObject.str_array)
  return &_impl_.str_array_;
}

// repeated sint32 int_array = 15;
inline int DetectedObject::_internal_int_array_size() const {
  return _impl_.int_array_.size();
}
inline int DetectedObject::int_array_size() const {
  return _internal_int_array_size();
}
inline void DetectedObject::clear_int_array() {
  _impl_.int_array_.Clear();
}
inline int32_t DetectedObject::_internal_int_array(int index) const {
  return _impl_.int_array_.Get(index);
}
inline int32_t DetectedObject::int_array(int index) const {
  // @@protoc_insertion_point(field_get:esurfing.proto.perc.DetectedObject.int_array)
  return _internal_int_array(index);
}
inline void DetectedObject::set_int_array(int index, int32_t value) {
  _impl_.int_array_.Set(index, value);
  // @@protoc_insertion_point(field_set:esurfing.proto.perc.DetectedObject.int_array)
}
inline void DetectedObject::_internal_add_int_array(int32_t value) {
  _impl_.int_array_.Add(value);
}
inline void DetectedObject::add_int_array(int32_t value) {
  _internal_add_int_array(value);
  // @@protoc_insertion_point(field_add:esurfing.proto.perc.DetectedObject.int_array)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >&
DetectedObject::_internal_int_array() const {
  return _impl_.int_array_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >&
DetectedObject::int_array() const {
  // @@protoc_insertion_point(field_list:esurfing.proto.perc.DetectedObject.int_array)
  return _internal_int_array();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >*
DetectedObject::_internal_mutable_int_array() {
  return &_impl_.int_array_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >*
DetectedObject::mutable_int_array() {
  // @@protoc_insertion_point(field_mutable_list:esurfing.proto.perc.DetectedObject.int_array)
  return _internal_mutable_int_array();
}

// int64 parking_time = 16;
inline void DetectedObject::clear_parking_time() {
  _impl_.parking_time_ = int64_t{0};
}
inline int64_t DetectedObject::_internal_parking_time() const {
  return _impl_.parking_time_;
}
inline int64_t DetectedObject::parking_time() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.perc.DetectedObject.parking_time)
  return _internal_parking_time();
}
inline void DetectedObject::_internal_set_parking_time(int64_t value) {
  
  _impl_.parking_time_ = value;
}
inline void DetectedObject::set_parking_time(int64_t value) {
  _internal_set_parking_time(value);
  // @@protoc_insertion_point(field_set:esurfing.proto.perc.DetectedObject.parking_time)
}

// .esurfing.proto.perc.LicenePlate plate = 17;
inline bool DetectedObject::_internal_has_plate() const {
  return this != internal_default_instance() && _impl_.plate_ != nullptr;
}
inline bool DetectedObject::has_plate() const {
  return _internal_has_plate();
}
inline void DetectedObject::clear_plate() {
  if (GetArenaForAllocation() == nullptr && _impl_.plate_ != nullptr) {
    delete _impl_.plate_;
  }
  _impl_.plate_ = nullptr;
}
inline const ::esurfing::proto::perc::LicenePlate& DetectedObject::_internal_plate() const {
  const ::esurfing::proto::perc::LicenePlate* p = _impl_.plate_;
  return p != nullptr ? *p : reinterpret_cast<const ::esurfing::proto::perc::LicenePlate&>(
      ::esurfing::proto::perc::_LicenePlate_default_instance_);
}
inline const ::esurfing::proto::perc::LicenePlate& DetectedObject::plate() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.perc.DetectedObject.plate)
  return _internal_plate();
}
inline void DetectedObject::unsafe_arena_set_allocated_plate(
    ::esurfing::proto::perc::LicenePlate* plate) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.plate_);
  }
  _impl_.plate_ = plate;
  if (plate) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:esurfing.proto.perc.DetectedObject.plate)
}
inline ::esurfing::proto::perc::LicenePlate* DetectedObject::release_plate() {
  
  ::esurfing::proto::perc::LicenePlate* temp = _impl_.plate_;
  _impl_.plate_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::esurfing::proto::perc::LicenePlate* DetectedObject::unsafe_arena_release_plate() {
  // @@protoc_insertion_point(field_release:esurfing.proto.perc.DetectedObject.plate)
  
  ::esurfing::proto::perc::LicenePlate* temp = _impl_.plate_;
  _impl_.plate_ = nullptr;
  return temp;
}
inline ::esurfing::proto::perc::LicenePlate* DetectedObject::_internal_mutable_plate() {
  
  if (_impl_.plate_ == nullptr) {
    auto* p = CreateMaybeMessage<::esurfing::proto::perc::LicenePlate>(GetArenaForAllocation());
    _impl_.plate_ = p;
  }
  return _impl_.plate_;
}
inline ::esurfing::proto::perc::LicenePlate* DetectedObject::mutable_plate() {
  ::esurfing::proto::perc::LicenePlate* _msg = _internal_mutable_plate();
  // @@protoc_insertion_point(field_mutable:esurfing.proto.perc.DetectedObject.plate)
  return _msg;
}
inline void DetectedObject::set_allocated_plate(::esurfing::proto::perc::LicenePlate* plate) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.plate_;
  }
  if (plate) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(plate);
    if (message_arena != submessage_arena) {
      plate = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, plate, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.plate_ = plate;
  // @@protoc_insertion_point(field_set_allocated:esurfing.proto.perc.DetectedObject.plate)
}

// int32 obj_color = 18;
inline void DetectedObject::clear_obj_color() {
  _impl_.obj_color_ = 0;
}
inline int32_t DetectedObject::_internal_obj_color() const {
  return _impl_.obj_color_;
}
inline int32_t DetectedObject::obj_color() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.perc.DetectedObject.obj_color)
  return _internal_obj_color();
}
inline void DetectedObject::_internal_set_obj_color(int32_t value) {
  
  _impl_.obj_color_ = value;
}
inline void DetectedObject::set_obj_color(int32_t value) {
  _internal_set_obj_color(value);
  // @@protoc_insertion_point(field_set:esurfing.proto.perc.DetectedObject.obj_color)
}

// -------------------------------------------------------------------

// Velocity

// float heading = 1;
inline void Velocity::clear_heading() {
  _impl_.heading_ = 0;
}
inline float Velocity::_internal_heading() const {
  return _impl_.heading_;
}
inline float Velocity::heading() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.perc.Velocity.heading)
  return _internal_heading();
}
inline void Velocity::_internal_set_heading(float value) {
  
  _impl_.heading_ = value;
}
inline void Velocity::set_heading(float value) {
  _internal_set_heading(value);
  // @@protoc_insertion_point(field_set:esurfing.proto.perc.Velocity.heading)
}

// float speed = 2;
inline void Velocity::clear_speed() {
  _impl_.speed_ = 0;
}
inline float Velocity::_internal_speed() const {
  return _impl_.speed_;
}
inline float Velocity::speed() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.perc.Velocity.speed)
  return _internal_speed();
}
inline void Velocity::_internal_set_speed(float value) {
  
  _impl_.speed_ = value;
}
inline void Velocity::set_speed(float value) {
  _internal_set_speed(value);
  // @@protoc_insertion_point(field_set:esurfing.proto.perc.Velocity.speed)
}

// float acceleration = 3;
inline void Velocity::clear_acceleration() {
  _impl_.acceleration_ = 0;
}
inline float Velocity::_internal_acceleration() const {
  return _impl_.acceleration_;
}
inline float Velocity::acceleration() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.perc.Velocity.acceleration)
  return _internal_acceleration();
}
inline void Velocity::_internal_set_acceleration(float value) {
  
  _impl_.acceleration_ = value;
}
inline void Velocity::set_acceleration(float value) {
  _internal_set_acceleration(value);
  // @@protoc_insertion_point(field_set:esurfing.proto.perc.Velocity.acceleration)
}

// -------------------------------------------------------------------

// WayPoints

// repeated .esurfing.proto.perc.WayPoint waypoints = 1;
inline int WayPoints::_internal_waypoints_size() const {
  return _impl_.waypoints_.size();
}
inline int WayPoints::waypoints_size() const {
  return _internal_waypoints_size();
}
inline void WayPoints::clear_waypoints() {
  _impl_.waypoints_.Clear();
}
inline ::esurfing::proto::perc::WayPoint* WayPoints::mutable_waypoints(int index) {
  // @@protoc_insertion_point(field_mutable:esurfing.proto.perc.WayPoints.waypoints)
  return _impl_.waypoints_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::esurfing::proto::perc::WayPoint >*
WayPoints::mutable_waypoints() {
  // @@protoc_insertion_point(field_mutable_list:esurfing.proto.perc.WayPoints.waypoints)
  return &_impl_.waypoints_;
}
inline const ::esurfing::proto::perc::WayPoint& WayPoints::_internal_waypoints(int index) const {
  return _impl_.waypoints_.Get(index);
}
inline const ::esurfing::proto::perc::WayPoint& WayPoints::waypoints(int index) const {
  // @@protoc_insertion_point(field_get:esurfing.proto.perc.WayPoints.waypoints)
  return _internal_waypoints(index);
}
inline ::esurfing::proto::perc::WayPoint* WayPoints::_internal_add_waypoints() {
  return _impl_.waypoints_.Add();
}
inline ::esurfing::proto::perc::WayPoint* WayPoints::add_waypoints() {
  ::esurfing::proto::perc::WayPoint* _add = _internal_add_waypoints();
  // @@protoc_insertion_point(field_add:esurfing.proto.perc.WayPoints.waypoints)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::esurfing::proto::perc::WayPoint >&
WayPoints::waypoints() const {
  // @@protoc_insertion_point(field_list:esurfing.proto.perc.WayPoints.waypoints)
  return _impl_.waypoints_;
}

// float probability = 2;
inline void WayPoints::clear_probability() {
  _impl_.probability_ = 0;
}
inline float WayPoints::_internal_probability() const {
  return _impl_.probability_;
}
inline float WayPoints::probability() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.perc.WayPoints.probability)
  return _internal_probability();
}
inline void WayPoints::_internal_set_probability(float value) {
  
  _impl_.probability_ = value;
}
inline void WayPoints::set_probability(float value) {
  _internal_set_probability(value);
  // @@protoc_insertion_point(field_set:esurfing.proto.perc.WayPoints.probability)
}

// -------------------------------------------------------------------

// WayPoint

// sfixed64 time_meas = 1;
inline void WayPoint::clear_time_meas() {
  _impl_.time_meas_ = int64_t{0};
}
inline int64_t WayPoint::_internal_time_meas() const {
  return _impl_.time_meas_;
}
inline int64_t WayPoint::time_meas() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.perc.WayPoint.time_meas)
  return _internal_time_meas();
}
inline void WayPoint::_internal_set_time_meas(int64_t value) {
  
  _impl_.time_meas_ = value;
}
inline void WayPoint::set_time_meas(int64_t value) {
  _internal_set_time_meas(value);
  // @@protoc_insertion_point(field_set:esurfing.proto.perc.WayPoint.time_meas)
}

// .esurfing.proto.math.Vector3f position = 2;
inline bool WayPoint::_internal_has_position() const {
  return this != internal_default_instance() && _impl_.position_ != nullptr;
}
inline bool WayPoint::has_position() const {
  return _internal_has_position();
}
inline const ::esurfing::proto::math::Vector3f& WayPoint::_internal_position() const {
  const ::esurfing::proto::math::Vector3f* p = _impl_.position_;
  return p != nullptr ? *p : reinterpret_cast<const ::esurfing::proto::math::Vector3f&>(
      ::esurfing::proto::math::_Vector3f_default_instance_);
}
inline const ::esurfing::proto::math::Vector3f& WayPoint::position() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.perc.WayPoint.position)
  return _internal_position();
}
inline void WayPoint::unsafe_arena_set_allocated_position(
    ::esurfing::proto::math::Vector3f* position) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.position_);
  }
  _impl_.position_ = position;
  if (position) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:esurfing.proto.perc.WayPoint.position)
}
inline ::esurfing::proto::math::Vector3f* WayPoint::release_position() {
  
  ::esurfing::proto::math::Vector3f* temp = _impl_.position_;
  _impl_.position_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::esurfing::proto::math::Vector3f* WayPoint::unsafe_arena_release_position() {
  // @@protoc_insertion_point(field_release:esurfing.proto.perc.WayPoint.position)
  
  ::esurfing::proto::math::Vector3f* temp = _impl_.position_;
  _impl_.position_ = nullptr;
  return temp;
}
inline ::esurfing::proto::math::Vector3f* WayPoint::_internal_mutable_position() {
  
  if (_impl_.position_ == nullptr) {
    auto* p = CreateMaybeMessage<::esurfing::proto::math::Vector3f>(GetArenaForAllocation());
    _impl_.position_ = p;
  }
  return _impl_.position_;
}
inline ::esurfing::proto::math::Vector3f* WayPoint::mutable_position() {
  ::esurfing::proto::math::Vector3f* _msg = _internal_mutable_position();
  // @@protoc_insertion_point(field_mutable:esurfing.proto.perc.WayPoint.position)
  return _msg;
}
inline void WayPoint::set_allocated_position(::esurfing::proto::math::Vector3f* position) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.position_);
  }
  if (position) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(position));
    if (message_arena != submessage_arena) {
      position = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, position, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.position_ = position;
  // @@protoc_insertion_point(field_set_allocated:esurfing.proto.perc.WayPoint.position)
}

// .esurfing.proto.perc.Velocity velocity = 3;
inline bool WayPoint::_internal_has_velocity() const {
  return this != internal_default_instance() && _impl_.velocity_ != nullptr;
}
inline bool WayPoint::has_velocity() const {
  return _internal_has_velocity();
}
inline void WayPoint::clear_velocity() {
  if (GetArenaForAllocation() == nullptr && _impl_.velocity_ != nullptr) {
    delete _impl_.velocity_;
  }
  _impl_.velocity_ = nullptr;
}
inline const ::esurfing::proto::perc::Velocity& WayPoint::_internal_velocity() const {
  const ::esurfing::proto::perc::Velocity* p = _impl_.velocity_;
  return p != nullptr ? *p : reinterpret_cast<const ::esurfing::proto::perc::Velocity&>(
      ::esurfing::proto::perc::_Velocity_default_instance_);
}
inline const ::esurfing::proto::perc::Velocity& WayPoint::velocity() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.perc.WayPoint.velocity)
  return _internal_velocity();
}
inline void WayPoint::unsafe_arena_set_allocated_velocity(
    ::esurfing::proto::perc::Velocity* velocity) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.velocity_);
  }
  _impl_.velocity_ = velocity;
  if (velocity) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:esurfing.proto.perc.WayPoint.velocity)
}
inline ::esurfing::proto::perc::Velocity* WayPoint::release_velocity() {
  
  ::esurfing::proto::perc::Velocity* temp = _impl_.velocity_;
  _impl_.velocity_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::esurfing::proto::perc::Velocity* WayPoint::unsafe_arena_release_velocity() {
  // @@protoc_insertion_point(field_release:esurfing.proto.perc.WayPoint.velocity)
  
  ::esurfing::proto::perc::Velocity* temp = _impl_.velocity_;
  _impl_.velocity_ = nullptr;
  return temp;
}
inline ::esurfing::proto::perc::Velocity* WayPoint::_internal_mutable_velocity() {
  
  if (_impl_.velocity_ == nullptr) {
    auto* p = CreateMaybeMessage<::esurfing::proto::perc::Velocity>(GetArenaForAllocation());
    _impl_.velocity_ = p;
  }
  return _impl_.velocity_;
}
inline ::esurfing::proto::perc::Velocity* WayPoint::mutable_velocity() {
  ::esurfing::proto::perc::Velocity* _msg = _internal_mutable_velocity();
  // @@protoc_insertion_point(field_mutable:esurfing.proto.perc.WayPoint.velocity)
  return _msg;
}
inline void WayPoint::set_allocated_velocity(::esurfing::proto::perc::Velocity* velocity) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.velocity_;
  }
  if (velocity) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(velocity);
    if (message_arena != submessage_arena) {
      velocity = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, velocity, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.velocity_ = velocity;
  // @@protoc_insertion_point(field_set_allocated:esurfing.proto.perc.WayPoint.velocity)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace perc
}  // namespace proto
}  // namespace esurfing

PROTOBUF_NAMESPACE_OPEN

template <> struct is_proto_enum< ::esurfing::proto::perc::ObjectType> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::esurfing::proto::perc::ObjectType>() {
  return ::esurfing::proto::perc::ObjectType_descriptor();
}

PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_group_5fdetected_2eproto
