syntax = "proto3";

package esurfing.proto.perc;

import "geo.proto";

message Color {
  int32 r = 1;
  int32 g = 2;
  int32 b = 3;
  float a = 4;
}

message DetectedObjects {
  sfixed64 time_meas = 1; // in ms, data time_stamp from driver
  sfixed64 time_pub = 2; // in ms, time_stamp when publish
  bytes group_name = 3; // group id
  bytes group_info = 4; // user defined info
  repeated DetectedObject objects = 5;
}

enum ObjectType { // 仅供标准规范用
  UNKNOWN = 0;    // 未知
  CAR = 1;      // 小汽车
  PEDESTRIAN = 2;  // 行人
  CYCLIST = 3;    // 骑自行车的人
  TRUCK = 4;     // 卡车
  VAN = 5;      // 箱式货车
  BUS = 6;      // 公共汽车
  STATIC = 7;     // 静态
  STATIC_EDGE = 8;
  CONE = 9;
  TROLLEY = 10;   // 三轮车
  ROBOT = 11;
  GATE = 12;
}

message LicenePlate {
  int32 color = 1;
  float color_confidence = 2;
  string number = 3;
  float number_confidence = 4;
}


message DetectedObject {
  bytes overlap_name = 1; // belongs to which overlapped region
  fixed64 uuid = 2; // unique_id
  sint32 type = 3;
  float confidence = 4;
  math.Vector3f position = 5; // in map frame, in meter
  math.Vector3f shape = 6; // length, width, height, in meter
  math.Polygon hull = 7; // in map frame, BEV (2D) contour
  float orientation = 8; // physical object heading in map frame [-pi, pi)
  Velocity velocity = 9; // heading and speed in map frame
  bool is_static = 10; // the object is static (true) or not (false)
  // math.Color color = 11; // vehicle color
  Color color = 11; // vehicle color          

  repeated float feature = 12; // ReID feature vector	
  repeated WayPoints trajectories = 13; // history/predicted trajectories    
  repeated bytes str_array = 14; // user defined info
  repeated sint32 int_array = 15; // user defined infoorientation 
  int64 parking_time = 16;					

  LicenePlate plate = 17;  //vehicle plate    	
  int32 obj_color = 18;   //vehicle color	
}

message Velocity {
  float heading = 1; // velocity heading [-pi, pi)
  float speed = 2; // velocity value in m/s
  float acceleration = 3; // in m/s2
}

message WayPoints {
  repeated WayPoint waypoints = 1;
  float probability = 2;
}

message WayPoint {
  sfixed64 time_meas = 1; // in ms
  math.Vector3f position = 2; // in map frame, in meter
  Velocity velocity = 3; // in map frame
}