// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: geo.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_geo_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_geo_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3021000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3021006 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_geo_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_geo_2eproto {
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_geo_2eproto;
namespace esurfing {
namespace proto {
namespace math {
class Matrix2d;
struct Matrix2dDefaultTypeInternal;
extern Matrix2dDefaultTypeInternal _Matrix2d_default_instance_;
class Matrix2f;
struct Matrix2fDefaultTypeInternal;
extern Matrix2fDefaultTypeInternal _Matrix2f_default_instance_;
class Matrix3d;
struct Matrix3dDefaultTypeInternal;
extern Matrix3dDefaultTypeInternal _Matrix3d_default_instance_;
class Matrix3f;
struct Matrix3fDefaultTypeInternal;
extern Matrix3fDefaultTypeInternal _Matrix3f_default_instance_;
class Points;
struct PointsDefaultTypeInternal;
extern PointsDefaultTypeInternal _Points_default_instance_;
class Polygon;
struct PolygonDefaultTypeInternal;
extern PolygonDefaultTypeInternal _Polygon_default_instance_;
class Polyline;
struct PolylineDefaultTypeInternal;
extern PolylineDefaultTypeInternal _Polyline_default_instance_;
class Quaterniond;
struct QuaterniondDefaultTypeInternal;
extern QuaterniondDefaultTypeInternal _Quaterniond_default_instance_;
class Quaternionf;
struct QuaternionfDefaultTypeInternal;
extern QuaternionfDefaultTypeInternal _Quaternionf_default_instance_;
class Transformation3d;
struct Transformation3dDefaultTypeInternal;
extern Transformation3dDefaultTypeInternal _Transformation3d_default_instance_;
class Transformation3f;
struct Transformation3fDefaultTypeInternal;
extern Transformation3fDefaultTypeInternal _Transformation3f_default_instance_;
class Vector2d;
struct Vector2dDefaultTypeInternal;
extern Vector2dDefaultTypeInternal _Vector2d_default_instance_;
class Vector2f;
struct Vector2fDefaultTypeInternal;
extern Vector2fDefaultTypeInternal _Vector2f_default_instance_;
class Vector3d;
struct Vector3dDefaultTypeInternal;
extern Vector3dDefaultTypeInternal _Vector3d_default_instance_;
class Vector3f;
struct Vector3fDefaultTypeInternal;
extern Vector3fDefaultTypeInternal _Vector3f_default_instance_;
class Vector3i;
struct Vector3iDefaultTypeInternal;
extern Vector3iDefaultTypeInternal _Vector3i_default_instance_;
}  // namespace math
}  // namespace proto
}  // namespace esurfing
PROTOBUF_NAMESPACE_OPEN
template<> ::esurfing::proto::math::Matrix2d* Arena::CreateMaybeMessage<::esurfing::proto::math::Matrix2d>(Arena*);
template<> ::esurfing::proto::math::Matrix2f* Arena::CreateMaybeMessage<::esurfing::proto::math::Matrix2f>(Arena*);
template<> ::esurfing::proto::math::Matrix3d* Arena::CreateMaybeMessage<::esurfing::proto::math::Matrix3d>(Arena*);
template<> ::esurfing::proto::math::Matrix3f* Arena::CreateMaybeMessage<::esurfing::proto::math::Matrix3f>(Arena*);
template<> ::esurfing::proto::math::Points* Arena::CreateMaybeMessage<::esurfing::proto::math::Points>(Arena*);
template<> ::esurfing::proto::math::Polygon* Arena::CreateMaybeMessage<::esurfing::proto::math::Polygon>(Arena*);
template<> ::esurfing::proto::math::Polyline* Arena::CreateMaybeMessage<::esurfing::proto::math::Polyline>(Arena*);
template<> ::esurfing::proto::math::Quaterniond* Arena::CreateMaybeMessage<::esurfing::proto::math::Quaterniond>(Arena*);
template<> ::esurfing::proto::math::Quaternionf* Arena::CreateMaybeMessage<::esurfing::proto::math::Quaternionf>(Arena*);
template<> ::esurfing::proto::math::Transformation3d* Arena::CreateMaybeMessage<::esurfing::proto::math::Transformation3d>(Arena*);
template<> ::esurfing::proto::math::Transformation3f* Arena::CreateMaybeMessage<::esurfing::proto::math::Transformation3f>(Arena*);
template<> ::esurfing::proto::math::Vector2d* Arena::CreateMaybeMessage<::esurfing::proto::math::Vector2d>(Arena*);
template<> ::esurfing::proto::math::Vector2f* Arena::CreateMaybeMessage<::esurfing::proto::math::Vector2f>(Arena*);
template<> ::esurfing::proto::math::Vector3d* Arena::CreateMaybeMessage<::esurfing::proto::math::Vector3d>(Arena*);
template<> ::esurfing::proto::math::Vector3f* Arena::CreateMaybeMessage<::esurfing::proto::math::Vector3f>(Arena*);
template<> ::esurfing::proto::math::Vector3i* Arena::CreateMaybeMessage<::esurfing::proto::math::Vector3i>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace esurfing {
namespace proto {
namespace math {

// ===================================================================

class Vector3i final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:esurfing.proto.math.Vector3i) */ {
 public:
  inline Vector3i() : Vector3i(nullptr) {}
  ~Vector3i() override;
  explicit PROTOBUF_CONSTEXPR Vector3i(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  Vector3i(const Vector3i& from);
  Vector3i(Vector3i&& from) noexcept
    : Vector3i() {
    *this = ::std::move(from);
  }

  inline Vector3i& operator=(const Vector3i& from) {
    CopyFrom(from);
    return *this;
  }
  inline Vector3i& operator=(Vector3i&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const Vector3i& default_instance() {
    return *internal_default_instance();
  }
  static inline const Vector3i* internal_default_instance() {
    return reinterpret_cast<const Vector3i*>(
               &_Vector3i_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(Vector3i& a, Vector3i& b) {
    a.Swap(&b);
  }
  inline void Swap(Vector3i* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(Vector3i* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  Vector3i* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<Vector3i>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const Vector3i& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const Vector3i& from) {
    Vector3i::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Vector3i* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "esurfing.proto.math.Vector3i";
  }
  protected:
  explicit Vector3i(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kXFieldNumber = 1,
    kYFieldNumber = 2,
    kZFieldNumber = 3,
  };
  // int64 x = 1;
  void clear_x();
  int64_t x() const;
  void set_x(int64_t value);
  private:
  int64_t _internal_x() const;
  void _internal_set_x(int64_t value);
  public:

  // int64 y = 2;
  void clear_y();
  int64_t y() const;
  void set_y(int64_t value);
  private:
  int64_t _internal_y() const;
  void _internal_set_y(int64_t value);
  public:

  // int64 z = 3;
  void clear_z();
  int64_t z() const;
  void set_z(int64_t value);
  private:
  int64_t _internal_z() const;
  void _internal_set_z(int64_t value);
  public:

  // @@protoc_insertion_point(class_scope:esurfing.proto.math.Vector3i)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    int64_t x_;
    int64_t y_;
    int64_t z_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_geo_2eproto;
};
// -------------------------------------------------------------------

class Polyline final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:esurfing.proto.math.Polyline) */ {
 public:
  inline Polyline() : Polyline(nullptr) {}
  ~Polyline() override;
  explicit PROTOBUF_CONSTEXPR Polyline(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  Polyline(const Polyline& from);
  Polyline(Polyline&& from) noexcept
    : Polyline() {
    *this = ::std::move(from);
  }

  inline Polyline& operator=(const Polyline& from) {
    CopyFrom(from);
    return *this;
  }
  inline Polyline& operator=(Polyline&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const Polyline& default_instance() {
    return *internal_default_instance();
  }
  static inline const Polyline* internal_default_instance() {
    return reinterpret_cast<const Polyline*>(
               &_Polyline_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(Polyline& a, Polyline& b) {
    a.Swap(&b);
  }
  inline void Swap(Polyline* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(Polyline* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  Polyline* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<Polyline>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const Polyline& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const Polyline& from) {
    Polyline::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Polyline* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "esurfing.proto.math.Polyline";
  }
  protected:
  explicit Polyline(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kPointsFieldNumber = 1,
  };
  // repeated .esurfing.proto.math.Vector3d points = 1;
  int points_size() const;
  private:
  int _internal_points_size() const;
  public:
  void clear_points();
  ::esurfing::proto::math::Vector3d* mutable_points(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::esurfing::proto::math::Vector3d >*
      mutable_points();
  private:
  const ::esurfing::proto::math::Vector3d& _internal_points(int index) const;
  ::esurfing::proto::math::Vector3d* _internal_add_points();
  public:
  const ::esurfing::proto::math::Vector3d& points(int index) const;
  ::esurfing::proto::math::Vector3d* add_points();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::esurfing::proto::math::Vector3d >&
      points() const;

  // @@protoc_insertion_point(class_scope:esurfing.proto.math.Polyline)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::esurfing::proto::math::Vector3d > points_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_geo_2eproto;
};
// -------------------------------------------------------------------

class Polygon final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:esurfing.proto.math.Polygon) */ {
 public:
  inline Polygon() : Polygon(nullptr) {}
  ~Polygon() override;
  explicit PROTOBUF_CONSTEXPR Polygon(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  Polygon(const Polygon& from);
  Polygon(Polygon&& from) noexcept
    : Polygon() {
    *this = ::std::move(from);
  }

  inline Polygon& operator=(const Polygon& from) {
    CopyFrom(from);
    return *this;
  }
  inline Polygon& operator=(Polygon&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const Polygon& default_instance() {
    return *internal_default_instance();
  }
  static inline const Polygon* internal_default_instance() {
    return reinterpret_cast<const Polygon*>(
               &_Polygon_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(Polygon& a, Polygon& b) {
    a.Swap(&b);
  }
  inline void Swap(Polygon* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(Polygon* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  Polygon* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<Polygon>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const Polygon& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const Polygon& from) {
    Polygon::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Polygon* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "esurfing.proto.math.Polygon";
  }
  protected:
  explicit Polygon(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kPointsFieldNumber = 1,
  };
  // repeated .esurfing.proto.math.Vector2f points = 1;
  int points_size() const;
  private:
  int _internal_points_size() const;
  public:
  void clear_points();
  ::esurfing::proto::math::Vector2f* mutable_points(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::esurfing::proto::math::Vector2f >*
      mutable_points();
  private:
  const ::esurfing::proto::math::Vector2f& _internal_points(int index) const;
  ::esurfing::proto::math::Vector2f* _internal_add_points();
  public:
  const ::esurfing::proto::math::Vector2f& points(int index) const;
  ::esurfing::proto::math::Vector2f* add_points();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::esurfing::proto::math::Vector2f >&
      points() const;

  // @@protoc_insertion_point(class_scope:esurfing.proto.math.Polygon)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::esurfing::proto::math::Vector2f > points_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_geo_2eproto;
};
// -------------------------------------------------------------------

class Points final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:esurfing.proto.math.Points) */ {
 public:
  inline Points() : Points(nullptr) {}
  ~Points() override;
  explicit PROTOBUF_CONSTEXPR Points(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  Points(const Points& from);
  Points(Points&& from) noexcept
    : Points() {
    *this = ::std::move(from);
  }

  inline Points& operator=(const Points& from) {
    CopyFrom(from);
    return *this;
  }
  inline Points& operator=(Points&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const Points& default_instance() {
    return *internal_default_instance();
  }
  static inline const Points* internal_default_instance() {
    return reinterpret_cast<const Points*>(
               &_Points_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(Points& a, Points& b) {
    a.Swap(&b);
  }
  inline void Swap(Points* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(Points* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  Points* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<Points>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const Points& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const Points& from) {
    Points::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Points* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "esurfing.proto.math.Points";
  }
  protected:
  explicit Points(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kPointsFieldNumber = 1,
  };
  // repeated .esurfing.proto.math.Vector3d points = 1;
  int points_size() const;
  private:
  int _internal_points_size() const;
  public:
  void clear_points();
  ::esurfing::proto::math::Vector3d* mutable_points(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::esurfing::proto::math::Vector3d >*
      mutable_points();
  private:
  const ::esurfing::proto::math::Vector3d& _internal_points(int index) const;
  ::esurfing::proto::math::Vector3d* _internal_add_points();
  public:
  const ::esurfing::proto::math::Vector3d& points(int index) const;
  ::esurfing::proto::math::Vector3d* add_points();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::esurfing::proto::math::Vector3d >&
      points() const;

  // @@protoc_insertion_point(class_scope:esurfing.proto.math.Points)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::esurfing::proto::math::Vector3d > points_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_geo_2eproto;
};
// -------------------------------------------------------------------

class Vector2d final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:esurfing.proto.math.Vector2d) */ {
 public:
  inline Vector2d() : Vector2d(nullptr) {}
  ~Vector2d() override;
  explicit PROTOBUF_CONSTEXPR Vector2d(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  Vector2d(const Vector2d& from);
  Vector2d(Vector2d&& from) noexcept
    : Vector2d() {
    *this = ::std::move(from);
  }

  inline Vector2d& operator=(const Vector2d& from) {
    CopyFrom(from);
    return *this;
  }
  inline Vector2d& operator=(Vector2d&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const Vector2d& default_instance() {
    return *internal_default_instance();
  }
  static inline const Vector2d* internal_default_instance() {
    return reinterpret_cast<const Vector2d*>(
               &_Vector2d_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  friend void swap(Vector2d& a, Vector2d& b) {
    a.Swap(&b);
  }
  inline void Swap(Vector2d* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(Vector2d* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  Vector2d* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<Vector2d>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const Vector2d& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const Vector2d& from) {
    Vector2d::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Vector2d* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "esurfing.proto.math.Vector2d";
  }
  protected:
  explicit Vector2d(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kXFieldNumber = 1,
    kYFieldNumber = 2,
  };
  // double x = 1;
  void clear_x();
  double x() const;
  void set_x(double value);
  private:
  double _internal_x() const;
  void _internal_set_x(double value);
  public:

  // double y = 2;
  void clear_y();
  double y() const;
  void set_y(double value);
  private:
  double _internal_y() const;
  void _internal_set_y(double value);
  public:

  // @@protoc_insertion_point(class_scope:esurfing.proto.math.Vector2d)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    double x_;
    double y_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_geo_2eproto;
};
// -------------------------------------------------------------------

class Vector2f final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:esurfing.proto.math.Vector2f) */ {
 public:
  inline Vector2f() : Vector2f(nullptr) {}
  ~Vector2f() override;
  explicit PROTOBUF_CONSTEXPR Vector2f(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  Vector2f(const Vector2f& from);
  Vector2f(Vector2f&& from) noexcept
    : Vector2f() {
    *this = ::std::move(from);
  }

  inline Vector2f& operator=(const Vector2f& from) {
    CopyFrom(from);
    return *this;
  }
  inline Vector2f& operator=(Vector2f&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const Vector2f& default_instance() {
    return *internal_default_instance();
  }
  static inline const Vector2f* internal_default_instance() {
    return reinterpret_cast<const Vector2f*>(
               &_Vector2f_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    5;

  friend void swap(Vector2f& a, Vector2f& b) {
    a.Swap(&b);
  }
  inline void Swap(Vector2f* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(Vector2f* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  Vector2f* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<Vector2f>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const Vector2f& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const Vector2f& from) {
    Vector2f::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Vector2f* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "esurfing.proto.math.Vector2f";
  }
  protected:
  explicit Vector2f(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kXFieldNumber = 1,
    kYFieldNumber = 2,
  };
  // float x = 1;
  void clear_x();
  float x() const;
  void set_x(float value);
  private:
  float _internal_x() const;
  void _internal_set_x(float value);
  public:

  // float y = 2;
  void clear_y();
  float y() const;
  void set_y(float value);
  private:
  float _internal_y() const;
  void _internal_set_y(float value);
  public:

  // @@protoc_insertion_point(class_scope:esurfing.proto.math.Vector2f)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    float x_;
    float y_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_geo_2eproto;
};
// -------------------------------------------------------------------

class Vector3d final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:esurfing.proto.math.Vector3d) */ {
 public:
  inline Vector3d() : Vector3d(nullptr) {}
  ~Vector3d() override;
  explicit PROTOBUF_CONSTEXPR Vector3d(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  Vector3d(const Vector3d& from);
  Vector3d(Vector3d&& from) noexcept
    : Vector3d() {
    *this = ::std::move(from);
  }

  inline Vector3d& operator=(const Vector3d& from) {
    CopyFrom(from);
    return *this;
  }
  inline Vector3d& operator=(Vector3d&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const Vector3d& default_instance() {
    return *internal_default_instance();
  }
  static inline const Vector3d* internal_default_instance() {
    return reinterpret_cast<const Vector3d*>(
               &_Vector3d_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    6;

  friend void swap(Vector3d& a, Vector3d& b) {
    a.Swap(&b);
  }
  inline void Swap(Vector3d* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(Vector3d* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  Vector3d* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<Vector3d>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const Vector3d& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const Vector3d& from) {
    Vector3d::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Vector3d* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "esurfing.proto.math.Vector3d";
  }
  protected:
  explicit Vector3d(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kXFieldNumber = 1,
    kYFieldNumber = 2,
    kZFieldNumber = 3,
  };
  // double x = 1;
  void clear_x();
  double x() const;
  void set_x(double value);
  private:
  double _internal_x() const;
  void _internal_set_x(double value);
  public:

  // double y = 2;
  void clear_y();
  double y() const;
  void set_y(double value);
  private:
  double _internal_y() const;
  void _internal_set_y(double value);
  public:

  // double z = 3;
  void clear_z();
  double z() const;
  void set_z(double value);
  private:
  double _internal_z() const;
  void _internal_set_z(double value);
  public:

  // @@protoc_insertion_point(class_scope:esurfing.proto.math.Vector3d)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    double x_;
    double y_;
    double z_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_geo_2eproto;
};
// -------------------------------------------------------------------

class Vector3f final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:esurfing.proto.math.Vector3f) */ {
 public:
  inline Vector3f() : Vector3f(nullptr) {}
  ~Vector3f() override;
  explicit PROTOBUF_CONSTEXPR Vector3f(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  Vector3f(const Vector3f& from);
  Vector3f(Vector3f&& from) noexcept
    : Vector3f() {
    *this = ::std::move(from);
  }

  inline Vector3f& operator=(const Vector3f& from) {
    CopyFrom(from);
    return *this;
  }
  inline Vector3f& operator=(Vector3f&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const Vector3f& default_instance() {
    return *internal_default_instance();
  }
  static inline const Vector3f* internal_default_instance() {
    return reinterpret_cast<const Vector3f*>(
               &_Vector3f_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    7;

  friend void swap(Vector3f& a, Vector3f& b) {
    a.Swap(&b);
  }
  inline void Swap(Vector3f* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(Vector3f* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  Vector3f* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<Vector3f>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const Vector3f& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const Vector3f& from) {
    Vector3f::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Vector3f* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "esurfing.proto.math.Vector3f";
  }
  protected:
  explicit Vector3f(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kXFieldNumber = 1,
    kYFieldNumber = 2,
    kZFieldNumber = 3,
  };
  // float x = 1;
  void clear_x();
  float x() const;
  void set_x(float value);
  private:
  float _internal_x() const;
  void _internal_set_x(float value);
  public:

  // float y = 2;
  void clear_y();
  float y() const;
  void set_y(float value);
  private:
  float _internal_y() const;
  void _internal_set_y(float value);
  public:

  // float z = 3;
  void clear_z();
  float z() const;
  void set_z(float value);
  private:
  float _internal_z() const;
  void _internal_set_z(float value);
  public:

  // @@protoc_insertion_point(class_scope:esurfing.proto.math.Vector3f)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    float x_;
    float y_;
    float z_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_geo_2eproto;
};
// -------------------------------------------------------------------

class Matrix2d final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:esurfing.proto.math.Matrix2d) */ {
 public:
  inline Matrix2d() : Matrix2d(nullptr) {}
  ~Matrix2d() override;
  explicit PROTOBUF_CONSTEXPR Matrix2d(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  Matrix2d(const Matrix2d& from);
  Matrix2d(Matrix2d&& from) noexcept
    : Matrix2d() {
    *this = ::std::move(from);
  }

  inline Matrix2d& operator=(const Matrix2d& from) {
    CopyFrom(from);
    return *this;
  }
  inline Matrix2d& operator=(Matrix2d&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const Matrix2d& default_instance() {
    return *internal_default_instance();
  }
  static inline const Matrix2d* internal_default_instance() {
    return reinterpret_cast<const Matrix2d*>(
               &_Matrix2d_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    8;

  friend void swap(Matrix2d& a, Matrix2d& b) {
    a.Swap(&b);
  }
  inline void Swap(Matrix2d* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(Matrix2d* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  Matrix2d* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<Matrix2d>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const Matrix2d& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const Matrix2d& from) {
    Matrix2d::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Matrix2d* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "esurfing.proto.math.Matrix2d";
  }
  protected:
  explicit Matrix2d(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kE00FieldNumber = 1,
    kE01FieldNumber = 2,
    kE10FieldNumber = 3,
    kE11FieldNumber = 4,
  };
  // double e00 = 1;
  void clear_e00();
  double e00() const;
  void set_e00(double value);
  private:
  double _internal_e00() const;
  void _internal_set_e00(double value);
  public:

  // double e01 = 2;
  void clear_e01();
  double e01() const;
  void set_e01(double value);
  private:
  double _internal_e01() const;
  void _internal_set_e01(double value);
  public:

  // double e10 = 3;
  void clear_e10();
  double e10() const;
  void set_e10(double value);
  private:
  double _internal_e10() const;
  void _internal_set_e10(double value);
  public:

  // double e11 = 4;
  void clear_e11();
  double e11() const;
  void set_e11(double value);
  private:
  double _internal_e11() const;
  void _internal_set_e11(double value);
  public:

  // @@protoc_insertion_point(class_scope:esurfing.proto.math.Matrix2d)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    double e00_;
    double e01_;
    double e10_;
    double e11_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_geo_2eproto;
};
// -------------------------------------------------------------------

class Matrix2f final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:esurfing.proto.math.Matrix2f) */ {
 public:
  inline Matrix2f() : Matrix2f(nullptr) {}
  ~Matrix2f() override;
  explicit PROTOBUF_CONSTEXPR Matrix2f(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  Matrix2f(const Matrix2f& from);
  Matrix2f(Matrix2f&& from) noexcept
    : Matrix2f() {
    *this = ::std::move(from);
  }

  inline Matrix2f& operator=(const Matrix2f& from) {
    CopyFrom(from);
    return *this;
  }
  inline Matrix2f& operator=(Matrix2f&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const Matrix2f& default_instance() {
    return *internal_default_instance();
  }
  static inline const Matrix2f* internal_default_instance() {
    return reinterpret_cast<const Matrix2f*>(
               &_Matrix2f_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    9;

  friend void swap(Matrix2f& a, Matrix2f& b) {
    a.Swap(&b);
  }
  inline void Swap(Matrix2f* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(Matrix2f* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  Matrix2f* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<Matrix2f>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const Matrix2f& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const Matrix2f& from) {
    Matrix2f::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Matrix2f* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "esurfing.proto.math.Matrix2f";
  }
  protected:
  explicit Matrix2f(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kE00FieldNumber = 1,
    kE01FieldNumber = 2,
    kE10FieldNumber = 3,
    kE11FieldNumber = 4,
  };
  // float e00 = 1;
  void clear_e00();
  float e00() const;
  void set_e00(float value);
  private:
  float _internal_e00() const;
  void _internal_set_e00(float value);
  public:

  // float e01 = 2;
  void clear_e01();
  float e01() const;
  void set_e01(float value);
  private:
  float _internal_e01() const;
  void _internal_set_e01(float value);
  public:

  // float e10 = 3;
  void clear_e10();
  float e10() const;
  void set_e10(float value);
  private:
  float _internal_e10() const;
  void _internal_set_e10(float value);
  public:

  // float e11 = 4;
  void clear_e11();
  float e11() const;
  void set_e11(float value);
  private:
  float _internal_e11() const;
  void _internal_set_e11(float value);
  public:

  // @@protoc_insertion_point(class_scope:esurfing.proto.math.Matrix2f)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    float e00_;
    float e01_;
    float e10_;
    float e11_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_geo_2eproto;
};
// -------------------------------------------------------------------

class Matrix3d final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:esurfing.proto.math.Matrix3d) */ {
 public:
  inline Matrix3d() : Matrix3d(nullptr) {}
  ~Matrix3d() override;
  explicit PROTOBUF_CONSTEXPR Matrix3d(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  Matrix3d(const Matrix3d& from);
  Matrix3d(Matrix3d&& from) noexcept
    : Matrix3d() {
    *this = ::std::move(from);
  }

  inline Matrix3d& operator=(const Matrix3d& from) {
    CopyFrom(from);
    return *this;
  }
  inline Matrix3d& operator=(Matrix3d&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const Matrix3d& default_instance() {
    return *internal_default_instance();
  }
  static inline const Matrix3d* internal_default_instance() {
    return reinterpret_cast<const Matrix3d*>(
               &_Matrix3d_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    10;

  friend void swap(Matrix3d& a, Matrix3d& b) {
    a.Swap(&b);
  }
  inline void Swap(Matrix3d* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(Matrix3d* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  Matrix3d* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<Matrix3d>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const Matrix3d& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const Matrix3d& from) {
    Matrix3d::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Matrix3d* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "esurfing.proto.math.Matrix3d";
  }
  protected:
  explicit Matrix3d(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kE00FieldNumber = 1,
    kE01FieldNumber = 2,
    kE02FieldNumber = 3,
    kE10FieldNumber = 4,
    kE11FieldNumber = 5,
    kE12FieldNumber = 6,
    kE20FieldNumber = 7,
    kE21FieldNumber = 8,
    kE22FieldNumber = 9,
  };
  // double e00 = 1;
  void clear_e00();
  double e00() const;
  void set_e00(double value);
  private:
  double _internal_e00() const;
  void _internal_set_e00(double value);
  public:

  // double e01 = 2;
  void clear_e01();
  double e01() const;
  void set_e01(double value);
  private:
  double _internal_e01() const;
  void _internal_set_e01(double value);
  public:

  // double e02 = 3;
  void clear_e02();
  double e02() const;
  void set_e02(double value);
  private:
  double _internal_e02() const;
  void _internal_set_e02(double value);
  public:

  // double e10 = 4;
  void clear_e10();
  double e10() const;
  void set_e10(double value);
  private:
  double _internal_e10() const;
  void _internal_set_e10(double value);
  public:

  // double e11 = 5;
  void clear_e11();
  double e11() const;
  void set_e11(double value);
  private:
  double _internal_e11() const;
  void _internal_set_e11(double value);
  public:

  // double e12 = 6;
  void clear_e12();
  double e12() const;
  void set_e12(double value);
  private:
  double _internal_e12() const;
  void _internal_set_e12(double value);
  public:

  // double e20 = 7;
  void clear_e20();
  double e20() const;
  void set_e20(double value);
  private:
  double _internal_e20() const;
  void _internal_set_e20(double value);
  public:

  // double e21 = 8;
  void clear_e21();
  double e21() const;
  void set_e21(double value);
  private:
  double _internal_e21() const;
  void _internal_set_e21(double value);
  public:

  // double e22 = 9;
  void clear_e22();
  double e22() const;
  void set_e22(double value);
  private:
  double _internal_e22() const;
  void _internal_set_e22(double value);
  public:

  // @@protoc_insertion_point(class_scope:esurfing.proto.math.Matrix3d)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    double e00_;
    double e01_;
    double e02_;
    double e10_;
    double e11_;
    double e12_;
    double e20_;
    double e21_;
    double e22_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_geo_2eproto;
};
// -------------------------------------------------------------------

class Matrix3f final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:esurfing.proto.math.Matrix3f) */ {
 public:
  inline Matrix3f() : Matrix3f(nullptr) {}
  ~Matrix3f() override;
  explicit PROTOBUF_CONSTEXPR Matrix3f(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  Matrix3f(const Matrix3f& from);
  Matrix3f(Matrix3f&& from) noexcept
    : Matrix3f() {
    *this = ::std::move(from);
  }

  inline Matrix3f& operator=(const Matrix3f& from) {
    CopyFrom(from);
    return *this;
  }
  inline Matrix3f& operator=(Matrix3f&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const Matrix3f& default_instance() {
    return *internal_default_instance();
  }
  static inline const Matrix3f* internal_default_instance() {
    return reinterpret_cast<const Matrix3f*>(
               &_Matrix3f_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    11;

  friend void swap(Matrix3f& a, Matrix3f& b) {
    a.Swap(&b);
  }
  inline void Swap(Matrix3f* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(Matrix3f* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  Matrix3f* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<Matrix3f>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const Matrix3f& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const Matrix3f& from) {
    Matrix3f::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Matrix3f* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "esurfing.proto.math.Matrix3f";
  }
  protected:
  explicit Matrix3f(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kE00FieldNumber = 1,
    kE01FieldNumber = 2,
    kE02FieldNumber = 3,
    kE10FieldNumber = 4,
    kE11FieldNumber = 5,
    kE12FieldNumber = 6,
    kE20FieldNumber = 7,
    kE21FieldNumber = 8,
    kE22FieldNumber = 9,
  };
  // float e00 = 1;
  void clear_e00();
  float e00() const;
  void set_e00(float value);
  private:
  float _internal_e00() const;
  void _internal_set_e00(float value);
  public:

  // float e01 = 2;
  void clear_e01();
  float e01() const;
  void set_e01(float value);
  private:
  float _internal_e01() const;
  void _internal_set_e01(float value);
  public:

  // float e02 = 3;
  void clear_e02();
  float e02() const;
  void set_e02(float value);
  private:
  float _internal_e02() const;
  void _internal_set_e02(float value);
  public:

  // float e10 = 4;
  void clear_e10();
  float e10() const;
  void set_e10(float value);
  private:
  float _internal_e10() const;
  void _internal_set_e10(float value);
  public:

  // float e11 = 5;
  void clear_e11();
  float e11() const;
  void set_e11(float value);
  private:
  float _internal_e11() const;
  void _internal_set_e11(float value);
  public:

  // float e12 = 6;
  void clear_e12();
  float e12() const;
  void set_e12(float value);
  private:
  float _internal_e12() const;
  void _internal_set_e12(float value);
  public:

  // float e20 = 7;
  void clear_e20();
  float e20() const;
  void set_e20(float value);
  private:
  float _internal_e20() const;
  void _internal_set_e20(float value);
  public:

  // float e21 = 8;
  void clear_e21();
  float e21() const;
  void set_e21(float value);
  private:
  float _internal_e21() const;
  void _internal_set_e21(float value);
  public:

  // float e22 = 9;
  void clear_e22();
  float e22() const;
  void set_e22(float value);
  private:
  float _internal_e22() const;
  void _internal_set_e22(float value);
  public:

  // @@protoc_insertion_point(class_scope:esurfing.proto.math.Matrix3f)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    float e00_;
    float e01_;
    float e02_;
    float e10_;
    float e11_;
    float e12_;
    float e20_;
    float e21_;
    float e22_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_geo_2eproto;
};
// -------------------------------------------------------------------

class Quaterniond final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:esurfing.proto.math.Quaterniond) */ {
 public:
  inline Quaterniond() : Quaterniond(nullptr) {}
  ~Quaterniond() override;
  explicit PROTOBUF_CONSTEXPR Quaterniond(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  Quaterniond(const Quaterniond& from);
  Quaterniond(Quaterniond&& from) noexcept
    : Quaterniond() {
    *this = ::std::move(from);
  }

  inline Quaterniond& operator=(const Quaterniond& from) {
    CopyFrom(from);
    return *this;
  }
  inline Quaterniond& operator=(Quaterniond&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const Quaterniond& default_instance() {
    return *internal_default_instance();
  }
  static inline const Quaterniond* internal_default_instance() {
    return reinterpret_cast<const Quaterniond*>(
               &_Quaterniond_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    12;

  friend void swap(Quaterniond& a, Quaterniond& b) {
    a.Swap(&b);
  }
  inline void Swap(Quaterniond* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(Quaterniond* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  Quaterniond* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<Quaterniond>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const Quaterniond& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const Quaterniond& from) {
    Quaterniond::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Quaterniond* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "esurfing.proto.math.Quaterniond";
  }
  protected:
  explicit Quaterniond(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kWFieldNumber = 1,
    kXFieldNumber = 2,
    kYFieldNumber = 3,
    kZFieldNumber = 4,
  };
  // double w = 1;
  void clear_w();
  double w() const;
  void set_w(double value);
  private:
  double _internal_w() const;
  void _internal_set_w(double value);
  public:

  // double x = 2;
  void clear_x();
  double x() const;
  void set_x(double value);
  private:
  double _internal_x() const;
  void _internal_set_x(double value);
  public:

  // double y = 3;
  void clear_y();
  double y() const;
  void set_y(double value);
  private:
  double _internal_y() const;
  void _internal_set_y(double value);
  public:

  // double z = 4;
  void clear_z();
  double z() const;
  void set_z(double value);
  private:
  double _internal_z() const;
  void _internal_set_z(double value);
  public:

  // @@protoc_insertion_point(class_scope:esurfing.proto.math.Quaterniond)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    double w_;
    double x_;
    double y_;
    double z_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_geo_2eproto;
};
// -------------------------------------------------------------------

class Quaternionf final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:esurfing.proto.math.Quaternionf) */ {
 public:
  inline Quaternionf() : Quaternionf(nullptr) {}
  ~Quaternionf() override;
  explicit PROTOBUF_CONSTEXPR Quaternionf(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  Quaternionf(const Quaternionf& from);
  Quaternionf(Quaternionf&& from) noexcept
    : Quaternionf() {
    *this = ::std::move(from);
  }

  inline Quaternionf& operator=(const Quaternionf& from) {
    CopyFrom(from);
    return *this;
  }
  inline Quaternionf& operator=(Quaternionf&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const Quaternionf& default_instance() {
    return *internal_default_instance();
  }
  static inline const Quaternionf* internal_default_instance() {
    return reinterpret_cast<const Quaternionf*>(
               &_Quaternionf_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    13;

  friend void swap(Quaternionf& a, Quaternionf& b) {
    a.Swap(&b);
  }
  inline void Swap(Quaternionf* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(Quaternionf* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  Quaternionf* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<Quaternionf>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const Quaternionf& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const Quaternionf& from) {
    Quaternionf::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Quaternionf* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "esurfing.proto.math.Quaternionf";
  }
  protected:
  explicit Quaternionf(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kWFieldNumber = 1,
    kXFieldNumber = 2,
    kYFieldNumber = 3,
    kZFieldNumber = 4,
  };
  // float w = 1;
  void clear_w();
  float w() const;
  void set_w(float value);
  private:
  float _internal_w() const;
  void _internal_set_w(float value);
  public:

  // float x = 2;
  void clear_x();
  float x() const;
  void set_x(float value);
  private:
  float _internal_x() const;
  void _internal_set_x(float value);
  public:

  // float y = 3;
  void clear_y();
  float y() const;
  void set_y(float value);
  private:
  float _internal_y() const;
  void _internal_set_y(float value);
  public:

  // float z = 4;
  void clear_z();
  float z() const;
  void set_z(float value);
  private:
  float _internal_z() const;
  void _internal_set_z(float value);
  public:

  // @@protoc_insertion_point(class_scope:esurfing.proto.math.Quaternionf)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    float w_;
    float x_;
    float y_;
    float z_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_geo_2eproto;
};
// -------------------------------------------------------------------

class Transformation3d final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:esurfing.proto.math.Transformation3d) */ {
 public:
  inline Transformation3d() : Transformation3d(nullptr) {}
  ~Transformation3d() override;
  explicit PROTOBUF_CONSTEXPR Transformation3d(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  Transformation3d(const Transformation3d& from);
  Transformation3d(Transformation3d&& from) noexcept
    : Transformation3d() {
    *this = ::std::move(from);
  }

  inline Transformation3d& operator=(const Transformation3d& from) {
    CopyFrom(from);
    return *this;
  }
  inline Transformation3d& operator=(Transformation3d&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const Transformation3d& default_instance() {
    return *internal_default_instance();
  }
  static inline const Transformation3d* internal_default_instance() {
    return reinterpret_cast<const Transformation3d*>(
               &_Transformation3d_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    14;

  friend void swap(Transformation3d& a, Transformation3d& b) {
    a.Swap(&b);
  }
  inline void Swap(Transformation3d* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(Transformation3d* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  Transformation3d* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<Transformation3d>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const Transformation3d& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const Transformation3d& from) {
    Transformation3d::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Transformation3d* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "esurfing.proto.math.Transformation3d";
  }
  protected:
  explicit Transformation3d(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kRotationFieldNumber = 1,
    kTranslationFieldNumber = 2,
  };
  // .esurfing.proto.math.Quaterniond rotation = 1;
  bool has_rotation() const;
  private:
  bool _internal_has_rotation() const;
  public:
  void clear_rotation();
  const ::esurfing::proto::math::Quaterniond& rotation() const;
  PROTOBUF_NODISCARD ::esurfing::proto::math::Quaterniond* release_rotation();
  ::esurfing::proto::math::Quaterniond* mutable_rotation();
  void set_allocated_rotation(::esurfing::proto::math::Quaterniond* rotation);
  private:
  const ::esurfing::proto::math::Quaterniond& _internal_rotation() const;
  ::esurfing::proto::math::Quaterniond* _internal_mutable_rotation();
  public:
  void unsafe_arena_set_allocated_rotation(
      ::esurfing::proto::math::Quaterniond* rotation);
  ::esurfing::proto::math::Quaterniond* unsafe_arena_release_rotation();

  // .esurfing.proto.math.Vector3d translation = 2;
  bool has_translation() const;
  private:
  bool _internal_has_translation() const;
  public:
  void clear_translation();
  const ::esurfing::proto::math::Vector3d& translation() const;
  PROTOBUF_NODISCARD ::esurfing::proto::math::Vector3d* release_translation();
  ::esurfing::proto::math::Vector3d* mutable_translation();
  void set_allocated_translation(::esurfing::proto::math::Vector3d* translation);
  private:
  const ::esurfing::proto::math::Vector3d& _internal_translation() const;
  ::esurfing::proto::math::Vector3d* _internal_mutable_translation();
  public:
  void unsafe_arena_set_allocated_translation(
      ::esurfing::proto::math::Vector3d* translation);
  ::esurfing::proto::math::Vector3d* unsafe_arena_release_translation();

  // @@protoc_insertion_point(class_scope:esurfing.proto.math.Transformation3d)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::esurfing::proto::math::Quaterniond* rotation_;
    ::esurfing::proto::math::Vector3d* translation_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_geo_2eproto;
};
// -------------------------------------------------------------------

class Transformation3f final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:esurfing.proto.math.Transformation3f) */ {
 public:
  inline Transformation3f() : Transformation3f(nullptr) {}
  ~Transformation3f() override;
  explicit PROTOBUF_CONSTEXPR Transformation3f(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  Transformation3f(const Transformation3f& from);
  Transformation3f(Transformation3f&& from) noexcept
    : Transformation3f() {
    *this = ::std::move(from);
  }

  inline Transformation3f& operator=(const Transformation3f& from) {
    CopyFrom(from);
    return *this;
  }
  inline Transformation3f& operator=(Transformation3f&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const Transformation3f& default_instance() {
    return *internal_default_instance();
  }
  static inline const Transformation3f* internal_default_instance() {
    return reinterpret_cast<const Transformation3f*>(
               &_Transformation3f_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    15;

  friend void swap(Transformation3f& a, Transformation3f& b) {
    a.Swap(&b);
  }
  inline void Swap(Transformation3f* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(Transformation3f* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  Transformation3f* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<Transformation3f>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const Transformation3f& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const Transformation3f& from) {
    Transformation3f::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Transformation3f* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "esurfing.proto.math.Transformation3f";
  }
  protected:
  explicit Transformation3f(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kRotationFieldNumber = 1,
    kTranslationFieldNumber = 2,
  };
  // .esurfing.proto.math.Quaternionf rotation = 1;
  bool has_rotation() const;
  private:
  bool _internal_has_rotation() const;
  public:
  void clear_rotation();
  const ::esurfing::proto::math::Quaternionf& rotation() const;
  PROTOBUF_NODISCARD ::esurfing::proto::math::Quaternionf* release_rotation();
  ::esurfing::proto::math::Quaternionf* mutable_rotation();
  void set_allocated_rotation(::esurfing::proto::math::Quaternionf* rotation);
  private:
  const ::esurfing::proto::math::Quaternionf& _internal_rotation() const;
  ::esurfing::proto::math::Quaternionf* _internal_mutable_rotation();
  public:
  void unsafe_arena_set_allocated_rotation(
      ::esurfing::proto::math::Quaternionf* rotation);
  ::esurfing::proto::math::Quaternionf* unsafe_arena_release_rotation();

  // .esurfing.proto.math.Vector3f translation = 2;
  bool has_translation() const;
  private:
  bool _internal_has_translation() const;
  public:
  void clear_translation();
  const ::esurfing::proto::math::Vector3f& translation() const;
  PROTOBUF_NODISCARD ::esurfing::proto::math::Vector3f* release_translation();
  ::esurfing::proto::math::Vector3f* mutable_translation();
  void set_allocated_translation(::esurfing::proto::math::Vector3f* translation);
  private:
  const ::esurfing::proto::math::Vector3f& _internal_translation() const;
  ::esurfing::proto::math::Vector3f* _internal_mutable_translation();
  public:
  void unsafe_arena_set_allocated_translation(
      ::esurfing::proto::math::Vector3f* translation);
  ::esurfing::proto::math::Vector3f* unsafe_arena_release_translation();

  // @@protoc_insertion_point(class_scope:esurfing.proto.math.Transformation3f)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::esurfing::proto::math::Quaternionf* rotation_;
    ::esurfing::proto::math::Vector3f* translation_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_geo_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// Vector3i

// int64 x = 1;
inline void Vector3i::clear_x() {
  _impl_.x_ = int64_t{0};
}
inline int64_t Vector3i::_internal_x() const {
  return _impl_.x_;
}
inline int64_t Vector3i::x() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.math.Vector3i.x)
  return _internal_x();
}
inline void Vector3i::_internal_set_x(int64_t value) {
  
  _impl_.x_ = value;
}
inline void Vector3i::set_x(int64_t value) {
  _internal_set_x(value);
  // @@protoc_insertion_point(field_set:esurfing.proto.math.Vector3i.x)
}

// int64 y = 2;
inline void Vector3i::clear_y() {
  _impl_.y_ = int64_t{0};
}
inline int64_t Vector3i::_internal_y() const {
  return _impl_.y_;
}
inline int64_t Vector3i::y() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.math.Vector3i.y)
  return _internal_y();
}
inline void Vector3i::_internal_set_y(int64_t value) {
  
  _impl_.y_ = value;
}
inline void Vector3i::set_y(int64_t value) {
  _internal_set_y(value);
  // @@protoc_insertion_point(field_set:esurfing.proto.math.Vector3i.y)
}

// int64 z = 3;
inline void Vector3i::clear_z() {
  _impl_.z_ = int64_t{0};
}
inline int64_t Vector3i::_internal_z() const {
  return _impl_.z_;
}
inline int64_t Vector3i::z() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.math.Vector3i.z)
  return _internal_z();
}
inline void Vector3i::_internal_set_z(int64_t value) {
  
  _impl_.z_ = value;
}
inline void Vector3i::set_z(int64_t value) {
  _internal_set_z(value);
  // @@protoc_insertion_point(field_set:esurfing.proto.math.Vector3i.z)
}

// -------------------------------------------------------------------

// Polyline

// repeated .esurfing.proto.math.Vector3d points = 1;
inline int Polyline::_internal_points_size() const {
  return _impl_.points_.size();
}
inline int Polyline::points_size() const {
  return _internal_points_size();
}
inline void Polyline::clear_points() {
  _impl_.points_.Clear();
}
inline ::esurfing::proto::math::Vector3d* Polyline::mutable_points(int index) {
  // @@protoc_insertion_point(field_mutable:esurfing.proto.math.Polyline.points)
  return _impl_.points_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::esurfing::proto::math::Vector3d >*
Polyline::mutable_points() {
  // @@protoc_insertion_point(field_mutable_list:esurfing.proto.math.Polyline.points)
  return &_impl_.points_;
}
inline const ::esurfing::proto::math::Vector3d& Polyline::_internal_points(int index) const {
  return _impl_.points_.Get(index);
}
inline const ::esurfing::proto::math::Vector3d& Polyline::points(int index) const {
  // @@protoc_insertion_point(field_get:esurfing.proto.math.Polyline.points)
  return _internal_points(index);
}
inline ::esurfing::proto::math::Vector3d* Polyline::_internal_add_points() {
  return _impl_.points_.Add();
}
inline ::esurfing::proto::math::Vector3d* Polyline::add_points() {
  ::esurfing::proto::math::Vector3d* _add = _internal_add_points();
  // @@protoc_insertion_point(field_add:esurfing.proto.math.Polyline.points)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::esurfing::proto::math::Vector3d >&
Polyline::points() const {
  // @@protoc_insertion_point(field_list:esurfing.proto.math.Polyline.points)
  return _impl_.points_;
}

// -------------------------------------------------------------------

// Polygon

// repeated .esurfing.proto.math.Vector2f points = 1;
inline int Polygon::_internal_points_size() const {
  return _impl_.points_.size();
}
inline int Polygon::points_size() const {
  return _internal_points_size();
}
inline void Polygon::clear_points() {
  _impl_.points_.Clear();
}
inline ::esurfing::proto::math::Vector2f* Polygon::mutable_points(int index) {
  // @@protoc_insertion_point(field_mutable:esurfing.proto.math.Polygon.points)
  return _impl_.points_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::esurfing::proto::math::Vector2f >*
Polygon::mutable_points() {
  // @@protoc_insertion_point(field_mutable_list:esurfing.proto.math.Polygon.points)
  return &_impl_.points_;
}
inline const ::esurfing::proto::math::Vector2f& Polygon::_internal_points(int index) const {
  return _impl_.points_.Get(index);
}
inline const ::esurfing::proto::math::Vector2f& Polygon::points(int index) const {
  // @@protoc_insertion_point(field_get:esurfing.proto.math.Polygon.points)
  return _internal_points(index);
}
inline ::esurfing::proto::math::Vector2f* Polygon::_internal_add_points() {
  return _impl_.points_.Add();
}
inline ::esurfing::proto::math::Vector2f* Polygon::add_points() {
  ::esurfing::proto::math::Vector2f* _add = _internal_add_points();
  // @@protoc_insertion_point(field_add:esurfing.proto.math.Polygon.points)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::esurfing::proto::math::Vector2f >&
Polygon::points() const {
  // @@protoc_insertion_point(field_list:esurfing.proto.math.Polygon.points)
  return _impl_.points_;
}

// -------------------------------------------------------------------

// Points

// repeated .esurfing.proto.math.Vector3d points = 1;
inline int Points::_internal_points_size() const {
  return _impl_.points_.size();
}
inline int Points::points_size() const {
  return _internal_points_size();
}
inline void Points::clear_points() {
  _impl_.points_.Clear();
}
inline ::esurfing::proto::math::Vector3d* Points::mutable_points(int index) {
  // @@protoc_insertion_point(field_mutable:esurfing.proto.math.Points.points)
  return _impl_.points_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::esurfing::proto::math::Vector3d >*
Points::mutable_points() {
  // @@protoc_insertion_point(field_mutable_list:esurfing.proto.math.Points.points)
  return &_impl_.points_;
}
inline const ::esurfing::proto::math::Vector3d& Points::_internal_points(int index) const {
  return _impl_.points_.Get(index);
}
inline const ::esurfing::proto::math::Vector3d& Points::points(int index) const {
  // @@protoc_insertion_point(field_get:esurfing.proto.math.Points.points)
  return _internal_points(index);
}
inline ::esurfing::proto::math::Vector3d* Points::_internal_add_points() {
  return _impl_.points_.Add();
}
inline ::esurfing::proto::math::Vector3d* Points::add_points() {
  ::esurfing::proto::math::Vector3d* _add = _internal_add_points();
  // @@protoc_insertion_point(field_add:esurfing.proto.math.Points.points)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::esurfing::proto::math::Vector3d >&
Points::points() const {
  // @@protoc_insertion_point(field_list:esurfing.proto.math.Points.points)
  return _impl_.points_;
}

// -------------------------------------------------------------------

// Vector2d

// double x = 1;
inline void Vector2d::clear_x() {
  _impl_.x_ = 0;
}
inline double Vector2d::_internal_x() const {
  return _impl_.x_;
}
inline double Vector2d::x() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.math.Vector2d.x)
  return _internal_x();
}
inline void Vector2d::_internal_set_x(double value) {
  
  _impl_.x_ = value;
}
inline void Vector2d::set_x(double value) {
  _internal_set_x(value);
  // @@protoc_insertion_point(field_set:esurfing.proto.math.Vector2d.x)
}

// double y = 2;
inline void Vector2d::clear_y() {
  _impl_.y_ = 0;
}
inline double Vector2d::_internal_y() const {
  return _impl_.y_;
}
inline double Vector2d::y() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.math.Vector2d.y)
  return _internal_y();
}
inline void Vector2d::_internal_set_y(double value) {
  
  _impl_.y_ = value;
}
inline void Vector2d::set_y(double value) {
  _internal_set_y(value);
  // @@protoc_insertion_point(field_set:esurfing.proto.math.Vector2d.y)
}

// -------------------------------------------------------------------

// Vector2f

// float x = 1;
inline void Vector2f::clear_x() {
  _impl_.x_ = 0;
}
inline float Vector2f::_internal_x() const {
  return _impl_.x_;
}
inline float Vector2f::x() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.math.Vector2f.x)
  return _internal_x();
}
inline void Vector2f::_internal_set_x(float value) {
  
  _impl_.x_ = value;
}
inline void Vector2f::set_x(float value) {
  _internal_set_x(value);
  // @@protoc_insertion_point(field_set:esurfing.proto.math.Vector2f.x)
}

// float y = 2;
inline void Vector2f::clear_y() {
  _impl_.y_ = 0;
}
inline float Vector2f::_internal_y() const {
  return _impl_.y_;
}
inline float Vector2f::y() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.math.Vector2f.y)
  return _internal_y();
}
inline void Vector2f::_internal_set_y(float value) {
  
  _impl_.y_ = value;
}
inline void Vector2f::set_y(float value) {
  _internal_set_y(value);
  // @@protoc_insertion_point(field_set:esurfing.proto.math.Vector2f.y)
}

// -------------------------------------------------------------------

// Vector3d

// double x = 1;
inline void Vector3d::clear_x() {
  _impl_.x_ = 0;
}
inline double Vector3d::_internal_x() const {
  return _impl_.x_;
}
inline double Vector3d::x() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.math.Vector3d.x)
  return _internal_x();
}
inline void Vector3d::_internal_set_x(double value) {
  
  _impl_.x_ = value;
}
inline void Vector3d::set_x(double value) {
  _internal_set_x(value);
  // @@protoc_insertion_point(field_set:esurfing.proto.math.Vector3d.x)
}

// double y = 2;
inline void Vector3d::clear_y() {
  _impl_.y_ = 0;
}
inline double Vector3d::_internal_y() const {
  return _impl_.y_;
}
inline double Vector3d::y() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.math.Vector3d.y)
  return _internal_y();
}
inline void Vector3d::_internal_set_y(double value) {
  
  _impl_.y_ = value;
}
inline void Vector3d::set_y(double value) {
  _internal_set_y(value);
  // @@protoc_insertion_point(field_set:esurfing.proto.math.Vector3d.y)
}

// double z = 3;
inline void Vector3d::clear_z() {
  _impl_.z_ = 0;
}
inline double Vector3d::_internal_z() const {
  return _impl_.z_;
}
inline double Vector3d::z() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.math.Vector3d.z)
  return _internal_z();
}
inline void Vector3d::_internal_set_z(double value) {
  
  _impl_.z_ = value;
}
inline void Vector3d::set_z(double value) {
  _internal_set_z(value);
  // @@protoc_insertion_point(field_set:esurfing.proto.math.Vector3d.z)
}

// -------------------------------------------------------------------

// Vector3f

// float x = 1;
inline void Vector3f::clear_x() {
  _impl_.x_ = 0;
}
inline float Vector3f::_internal_x() const {
  return _impl_.x_;
}
inline float Vector3f::x() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.math.Vector3f.x)
  return _internal_x();
}
inline void Vector3f::_internal_set_x(float value) {
  
  _impl_.x_ = value;
}
inline void Vector3f::set_x(float value) {
  _internal_set_x(value);
  // @@protoc_insertion_point(field_set:esurfing.proto.math.Vector3f.x)
}

// float y = 2;
inline void Vector3f::clear_y() {
  _impl_.y_ = 0;
}
inline float Vector3f::_internal_y() const {
  return _impl_.y_;
}
inline float Vector3f::y() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.math.Vector3f.y)
  return _internal_y();
}
inline void Vector3f::_internal_set_y(float value) {
  
  _impl_.y_ = value;
}
inline void Vector3f::set_y(float value) {
  _internal_set_y(value);
  // @@protoc_insertion_point(field_set:esurfing.proto.math.Vector3f.y)
}

// float z = 3;
inline void Vector3f::clear_z() {
  _impl_.z_ = 0;
}
inline float Vector3f::_internal_z() const {
  return _impl_.z_;
}
inline float Vector3f::z() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.math.Vector3f.z)
  return _internal_z();
}
inline void Vector3f::_internal_set_z(float value) {
  
  _impl_.z_ = value;
}
inline void Vector3f::set_z(float value) {
  _internal_set_z(value);
  // @@protoc_insertion_point(field_set:esurfing.proto.math.Vector3f.z)
}

// -------------------------------------------------------------------

// Matrix2d

// double e00 = 1;
inline void Matrix2d::clear_e00() {
  _impl_.e00_ = 0;
}
inline double Matrix2d::_internal_e00() const {
  return _impl_.e00_;
}
inline double Matrix2d::e00() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.math.Matrix2d.e00)
  return _internal_e00();
}
inline void Matrix2d::_internal_set_e00(double value) {
  
  _impl_.e00_ = value;
}
inline void Matrix2d::set_e00(double value) {
  _internal_set_e00(value);
  // @@protoc_insertion_point(field_set:esurfing.proto.math.Matrix2d.e00)
}

// double e01 = 2;
inline void Matrix2d::clear_e01() {
  _impl_.e01_ = 0;
}
inline double Matrix2d::_internal_e01() const {
  return _impl_.e01_;
}
inline double Matrix2d::e01() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.math.Matrix2d.e01)
  return _internal_e01();
}
inline void Matrix2d::_internal_set_e01(double value) {
  
  _impl_.e01_ = value;
}
inline void Matrix2d::set_e01(double value) {
  _internal_set_e01(value);
  // @@protoc_insertion_point(field_set:esurfing.proto.math.Matrix2d.e01)
}

// double e10 = 3;
inline void Matrix2d::clear_e10() {
  _impl_.e10_ = 0;
}
inline double Matrix2d::_internal_e10() const {
  return _impl_.e10_;
}
inline double Matrix2d::e10() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.math.Matrix2d.e10)
  return _internal_e10();
}
inline void Matrix2d::_internal_set_e10(double value) {
  
  _impl_.e10_ = value;
}
inline void Matrix2d::set_e10(double value) {
  _internal_set_e10(value);
  // @@protoc_insertion_point(field_set:esurfing.proto.math.Matrix2d.e10)
}

// double e11 = 4;
inline void Matrix2d::clear_e11() {
  _impl_.e11_ = 0;
}
inline double Matrix2d::_internal_e11() const {
  return _impl_.e11_;
}
inline double Matrix2d::e11() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.math.Matrix2d.e11)
  return _internal_e11();
}
inline void Matrix2d::_internal_set_e11(double value) {
  
  _impl_.e11_ = value;
}
inline void Matrix2d::set_e11(double value) {
  _internal_set_e11(value);
  // @@protoc_insertion_point(field_set:esurfing.proto.math.Matrix2d.e11)
}

// -------------------------------------------------------------------

// Matrix2f

// float e00 = 1;
inline void Matrix2f::clear_e00() {
  _impl_.e00_ = 0;
}
inline float Matrix2f::_internal_e00() const {
  return _impl_.e00_;
}
inline float Matrix2f::e00() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.math.Matrix2f.e00)
  return _internal_e00();
}
inline void Matrix2f::_internal_set_e00(float value) {
  
  _impl_.e00_ = value;
}
inline void Matrix2f::set_e00(float value) {
  _internal_set_e00(value);
  // @@protoc_insertion_point(field_set:esurfing.proto.math.Matrix2f.e00)
}

// float e01 = 2;
inline void Matrix2f::clear_e01() {
  _impl_.e01_ = 0;
}
inline float Matrix2f::_internal_e01() const {
  return _impl_.e01_;
}
inline float Matrix2f::e01() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.math.Matrix2f.e01)
  return _internal_e01();
}
inline void Matrix2f::_internal_set_e01(float value) {
  
  _impl_.e01_ = value;
}
inline void Matrix2f::set_e01(float value) {
  _internal_set_e01(value);
  // @@protoc_insertion_point(field_set:esurfing.proto.math.Matrix2f.e01)
}

// float e10 = 3;
inline void Matrix2f::clear_e10() {
  _impl_.e10_ = 0;
}
inline float Matrix2f::_internal_e10() const {
  return _impl_.e10_;
}
inline float Matrix2f::e10() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.math.Matrix2f.e10)
  return _internal_e10();
}
inline void Matrix2f::_internal_set_e10(float value) {
  
  _impl_.e10_ = value;
}
inline void Matrix2f::set_e10(float value) {
  _internal_set_e10(value);
  // @@protoc_insertion_point(field_set:esurfing.proto.math.Matrix2f.e10)
}

// float e11 = 4;
inline void Matrix2f::clear_e11() {
  _impl_.e11_ = 0;
}
inline float Matrix2f::_internal_e11() const {
  return _impl_.e11_;
}
inline float Matrix2f::e11() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.math.Matrix2f.e11)
  return _internal_e11();
}
inline void Matrix2f::_internal_set_e11(float value) {
  
  _impl_.e11_ = value;
}
inline void Matrix2f::set_e11(float value) {
  _internal_set_e11(value);
  // @@protoc_insertion_point(field_set:esurfing.proto.math.Matrix2f.e11)
}

// -------------------------------------------------------------------

// Matrix3d

// double e00 = 1;
inline void Matrix3d::clear_e00() {
  _impl_.e00_ = 0;
}
inline double Matrix3d::_internal_e00() const {
  return _impl_.e00_;
}
inline double Matrix3d::e00() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.math.Matrix3d.e00)
  return _internal_e00();
}
inline void Matrix3d::_internal_set_e00(double value) {
  
  _impl_.e00_ = value;
}
inline void Matrix3d::set_e00(double value) {
  _internal_set_e00(value);
  // @@protoc_insertion_point(field_set:esurfing.proto.math.Matrix3d.e00)
}

// double e01 = 2;
inline void Matrix3d::clear_e01() {
  _impl_.e01_ = 0;
}
inline double Matrix3d::_internal_e01() const {
  return _impl_.e01_;
}
inline double Matrix3d::e01() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.math.Matrix3d.e01)
  return _internal_e01();
}
inline void Matrix3d::_internal_set_e01(double value) {
  
  _impl_.e01_ = value;
}
inline void Matrix3d::set_e01(double value) {
  _internal_set_e01(value);
  // @@protoc_insertion_point(field_set:esurfing.proto.math.Matrix3d.e01)
}

// double e02 = 3;
inline void Matrix3d::clear_e02() {
  _impl_.e02_ = 0;
}
inline double Matrix3d::_internal_e02() const {
  return _impl_.e02_;
}
inline double Matrix3d::e02() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.math.Matrix3d.e02)
  return _internal_e02();
}
inline void Matrix3d::_internal_set_e02(double value) {
  
  _impl_.e02_ = value;
}
inline void Matrix3d::set_e02(double value) {
  _internal_set_e02(value);
  // @@protoc_insertion_point(field_set:esurfing.proto.math.Matrix3d.e02)
}

// double e10 = 4;
inline void Matrix3d::clear_e10() {
  _impl_.e10_ = 0;
}
inline double Matrix3d::_internal_e10() const {
  return _impl_.e10_;
}
inline double Matrix3d::e10() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.math.Matrix3d.e10)
  return _internal_e10();
}
inline void Matrix3d::_internal_set_e10(double value) {
  
  _impl_.e10_ = value;
}
inline void Matrix3d::set_e10(double value) {
  _internal_set_e10(value);
  // @@protoc_insertion_point(field_set:esurfing.proto.math.Matrix3d.e10)
}

// double e11 = 5;
inline void Matrix3d::clear_e11() {
  _impl_.e11_ = 0;
}
inline double Matrix3d::_internal_e11() const {
  return _impl_.e11_;
}
inline double Matrix3d::e11() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.math.Matrix3d.e11)
  return _internal_e11();
}
inline void Matrix3d::_internal_set_e11(double value) {
  
  _impl_.e11_ = value;
}
inline void Matrix3d::set_e11(double value) {
  _internal_set_e11(value);
  // @@protoc_insertion_point(field_set:esurfing.proto.math.Matrix3d.e11)
}

// double e12 = 6;
inline void Matrix3d::clear_e12() {
  _impl_.e12_ = 0;
}
inline double Matrix3d::_internal_e12() const {
  return _impl_.e12_;
}
inline double Matrix3d::e12() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.math.Matrix3d.e12)
  return _internal_e12();
}
inline void Matrix3d::_internal_set_e12(double value) {
  
  _impl_.e12_ = value;
}
inline void Matrix3d::set_e12(double value) {
  _internal_set_e12(value);
  // @@protoc_insertion_point(field_set:esurfing.proto.math.Matrix3d.e12)
}

// double e20 = 7;
inline void Matrix3d::clear_e20() {
  _impl_.e20_ = 0;
}
inline double Matrix3d::_internal_e20() const {
  return _impl_.e20_;
}
inline double Matrix3d::e20() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.math.Matrix3d.e20)
  return _internal_e20();
}
inline void Matrix3d::_internal_set_e20(double value) {
  
  _impl_.e20_ = value;
}
inline void Matrix3d::set_e20(double value) {
  _internal_set_e20(value);
  // @@protoc_insertion_point(field_set:esurfing.proto.math.Matrix3d.e20)
}

// double e21 = 8;
inline void Matrix3d::clear_e21() {
  _impl_.e21_ = 0;
}
inline double Matrix3d::_internal_e21() const {
  return _impl_.e21_;
}
inline double Matrix3d::e21() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.math.Matrix3d.e21)
  return _internal_e21();
}
inline void Matrix3d::_internal_set_e21(double value) {
  
  _impl_.e21_ = value;
}
inline void Matrix3d::set_e21(double value) {
  _internal_set_e21(value);
  // @@protoc_insertion_point(field_set:esurfing.proto.math.Matrix3d.e21)
}

// double e22 = 9;
inline void Matrix3d::clear_e22() {
  _impl_.e22_ = 0;
}
inline double Matrix3d::_internal_e22() const {
  return _impl_.e22_;
}
inline double Matrix3d::e22() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.math.Matrix3d.e22)
  return _internal_e22();
}
inline void Matrix3d::_internal_set_e22(double value) {
  
  _impl_.e22_ = value;
}
inline void Matrix3d::set_e22(double value) {
  _internal_set_e22(value);
  // @@protoc_insertion_point(field_set:esurfing.proto.math.Matrix3d.e22)
}

// -------------------------------------------------------------------

// Matrix3f

// float e00 = 1;
inline void Matrix3f::clear_e00() {
  _impl_.e00_ = 0;
}
inline float Matrix3f::_internal_e00() const {
  return _impl_.e00_;
}
inline float Matrix3f::e00() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.math.Matrix3f.e00)
  return _internal_e00();
}
inline void Matrix3f::_internal_set_e00(float value) {
  
  _impl_.e00_ = value;
}
inline void Matrix3f::set_e00(float value) {
  _internal_set_e00(value);
  // @@protoc_insertion_point(field_set:esurfing.proto.math.Matrix3f.e00)
}

// float e01 = 2;
inline void Matrix3f::clear_e01() {
  _impl_.e01_ = 0;
}
inline float Matrix3f::_internal_e01() const {
  return _impl_.e01_;
}
inline float Matrix3f::e01() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.math.Matrix3f.e01)
  return _internal_e01();
}
inline void Matrix3f::_internal_set_e01(float value) {
  
  _impl_.e01_ = value;
}
inline void Matrix3f::set_e01(float value) {
  _internal_set_e01(value);
  // @@protoc_insertion_point(field_set:esurfing.proto.math.Matrix3f.e01)
}

// float e02 = 3;
inline void Matrix3f::clear_e02() {
  _impl_.e02_ = 0;
}
inline float Matrix3f::_internal_e02() const {
  return _impl_.e02_;
}
inline float Matrix3f::e02() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.math.Matrix3f.e02)
  return _internal_e02();
}
inline void Matrix3f::_internal_set_e02(float value) {
  
  _impl_.e02_ = value;
}
inline void Matrix3f::set_e02(float value) {
  _internal_set_e02(value);
  // @@protoc_insertion_point(field_set:esurfing.proto.math.Matrix3f.e02)
}

// float e10 = 4;
inline void Matrix3f::clear_e10() {
  _impl_.e10_ = 0;
}
inline float Matrix3f::_internal_e10() const {
  return _impl_.e10_;
}
inline float Matrix3f::e10() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.math.Matrix3f.e10)
  return _internal_e10();
}
inline void Matrix3f::_internal_set_e10(float value) {
  
  _impl_.e10_ = value;
}
inline void Matrix3f::set_e10(float value) {
  _internal_set_e10(value);
  // @@protoc_insertion_point(field_set:esurfing.proto.math.Matrix3f.e10)
}

// float e11 = 5;
inline void Matrix3f::clear_e11() {
  _impl_.e11_ = 0;
}
inline float Matrix3f::_internal_e11() const {
  return _impl_.e11_;
}
inline float Matrix3f::e11() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.math.Matrix3f.e11)
  return _internal_e11();
}
inline void Matrix3f::_internal_set_e11(float value) {
  
  _impl_.e11_ = value;
}
inline void Matrix3f::set_e11(float value) {
  _internal_set_e11(value);
  // @@protoc_insertion_point(field_set:esurfing.proto.math.Matrix3f.e11)
}

// float e12 = 6;
inline void Matrix3f::clear_e12() {
  _impl_.e12_ = 0;
}
inline float Matrix3f::_internal_e12() const {
  return _impl_.e12_;
}
inline float Matrix3f::e12() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.math.Matrix3f.e12)
  return _internal_e12();
}
inline void Matrix3f::_internal_set_e12(float value) {
  
  _impl_.e12_ = value;
}
inline void Matrix3f::set_e12(float value) {
  _internal_set_e12(value);
  // @@protoc_insertion_point(field_set:esurfing.proto.math.Matrix3f.e12)
}

// float e20 = 7;
inline void Matrix3f::clear_e20() {
  _impl_.e20_ = 0;
}
inline float Matrix3f::_internal_e20() const {
  return _impl_.e20_;
}
inline float Matrix3f::e20() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.math.Matrix3f.e20)
  return _internal_e20();
}
inline void Matrix3f::_internal_set_e20(float value) {
  
  _impl_.e20_ = value;
}
inline void Matrix3f::set_e20(float value) {
  _internal_set_e20(value);
  // @@protoc_insertion_point(field_set:esurfing.proto.math.Matrix3f.e20)
}

// float e21 = 8;
inline void Matrix3f::clear_e21() {
  _impl_.e21_ = 0;
}
inline float Matrix3f::_internal_e21() const {
  return _impl_.e21_;
}
inline float Matrix3f::e21() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.math.Matrix3f.e21)
  return _internal_e21();
}
inline void Matrix3f::_internal_set_e21(float value) {
  
  _impl_.e21_ = value;
}
inline void Matrix3f::set_e21(float value) {
  _internal_set_e21(value);
  // @@protoc_insertion_point(field_set:esurfing.proto.math.Matrix3f.e21)
}

// float e22 = 9;
inline void Matrix3f::clear_e22() {
  _impl_.e22_ = 0;
}
inline float Matrix3f::_internal_e22() const {
  return _impl_.e22_;
}
inline float Matrix3f::e22() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.math.Matrix3f.e22)
  return _internal_e22();
}
inline void Matrix3f::_internal_set_e22(float value) {
  
  _impl_.e22_ = value;
}
inline void Matrix3f::set_e22(float value) {
  _internal_set_e22(value);
  // @@protoc_insertion_point(field_set:esurfing.proto.math.Matrix3f.e22)
}

// -------------------------------------------------------------------

// Quaterniond

// double w = 1;
inline void Quaterniond::clear_w() {
  _impl_.w_ = 0;
}
inline double Quaterniond::_internal_w() const {
  return _impl_.w_;
}
inline double Quaterniond::w() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.math.Quaterniond.w)
  return _internal_w();
}
inline void Quaterniond::_internal_set_w(double value) {
  
  _impl_.w_ = value;
}
inline void Quaterniond::set_w(double value) {
  _internal_set_w(value);
  // @@protoc_insertion_point(field_set:esurfing.proto.math.Quaterniond.w)
}

// double x = 2;
inline void Quaterniond::clear_x() {
  _impl_.x_ = 0;
}
inline double Quaterniond::_internal_x() const {
  return _impl_.x_;
}
inline double Quaterniond::x() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.math.Quaterniond.x)
  return _internal_x();
}
inline void Quaterniond::_internal_set_x(double value) {
  
  _impl_.x_ = value;
}
inline void Quaterniond::set_x(double value) {
  _internal_set_x(value);
  // @@protoc_insertion_point(field_set:esurfing.proto.math.Quaterniond.x)
}

// double y = 3;
inline void Quaterniond::clear_y() {
  _impl_.y_ = 0;
}
inline double Quaterniond::_internal_y() const {
  return _impl_.y_;
}
inline double Quaterniond::y() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.math.Quaterniond.y)
  return _internal_y();
}
inline void Quaterniond::_internal_set_y(double value) {
  
  _impl_.y_ = value;
}
inline void Quaterniond::set_y(double value) {
  _internal_set_y(value);
  // @@protoc_insertion_point(field_set:esurfing.proto.math.Quaterniond.y)
}

// double z = 4;
inline void Quaterniond::clear_z() {
  _impl_.z_ = 0;
}
inline double Quaterniond::_internal_z() const {
  return _impl_.z_;
}
inline double Quaterniond::z() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.math.Quaterniond.z)
  return _internal_z();
}
inline void Quaterniond::_internal_set_z(double value) {
  
  _impl_.z_ = value;
}
inline void Quaterniond::set_z(double value) {
  _internal_set_z(value);
  // @@protoc_insertion_point(field_set:esurfing.proto.math.Quaterniond.z)
}

// -------------------------------------------------------------------

// Quaternionf

// float w = 1;
inline void Quaternionf::clear_w() {
  _impl_.w_ = 0;
}
inline float Quaternionf::_internal_w() const {
  return _impl_.w_;
}
inline float Quaternionf::w() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.math.Quaternionf.w)
  return _internal_w();
}
inline void Quaternionf::_internal_set_w(float value) {
  
  _impl_.w_ = value;
}
inline void Quaternionf::set_w(float value) {
  _internal_set_w(value);
  // @@protoc_insertion_point(field_set:esurfing.proto.math.Quaternionf.w)
}

// float x = 2;
inline void Quaternionf::clear_x() {
  _impl_.x_ = 0;
}
inline float Quaternionf::_internal_x() const {
  return _impl_.x_;
}
inline float Quaternionf::x() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.math.Quaternionf.x)
  return _internal_x();
}
inline void Quaternionf::_internal_set_x(float value) {
  
  _impl_.x_ = value;
}
inline void Quaternionf::set_x(float value) {
  _internal_set_x(value);
  // @@protoc_insertion_point(field_set:esurfing.proto.math.Quaternionf.x)
}

// float y = 3;
inline void Quaternionf::clear_y() {
  _impl_.y_ = 0;
}
inline float Quaternionf::_internal_y() const {
  return _impl_.y_;
}
inline float Quaternionf::y() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.math.Quaternionf.y)
  return _internal_y();
}
inline void Quaternionf::_internal_set_y(float value) {
  
  _impl_.y_ = value;
}
inline void Quaternionf::set_y(float value) {
  _internal_set_y(value);
  // @@protoc_insertion_point(field_set:esurfing.proto.math.Quaternionf.y)
}

// float z = 4;
inline void Quaternionf::clear_z() {
  _impl_.z_ = 0;
}
inline float Quaternionf::_internal_z() const {
  return _impl_.z_;
}
inline float Quaternionf::z() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.math.Quaternionf.z)
  return _internal_z();
}
inline void Quaternionf::_internal_set_z(float value) {
  
  _impl_.z_ = value;
}
inline void Quaternionf::set_z(float value) {
  _internal_set_z(value);
  // @@protoc_insertion_point(field_set:esurfing.proto.math.Quaternionf.z)
}

// -------------------------------------------------------------------

// Transformation3d

// .esurfing.proto.math.Quaterniond rotation = 1;
inline bool Transformation3d::_internal_has_rotation() const {
  return this != internal_default_instance() && _impl_.rotation_ != nullptr;
}
inline bool Transformation3d::has_rotation() const {
  return _internal_has_rotation();
}
inline void Transformation3d::clear_rotation() {
  if (GetArenaForAllocation() == nullptr && _impl_.rotation_ != nullptr) {
    delete _impl_.rotation_;
  }
  _impl_.rotation_ = nullptr;
}
inline const ::esurfing::proto::math::Quaterniond& Transformation3d::_internal_rotation() const {
  const ::esurfing::proto::math::Quaterniond* p = _impl_.rotation_;
  return p != nullptr ? *p : reinterpret_cast<const ::esurfing::proto::math::Quaterniond&>(
      ::esurfing::proto::math::_Quaterniond_default_instance_);
}
inline const ::esurfing::proto::math::Quaterniond& Transformation3d::rotation() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.math.Transformation3d.rotation)
  return _internal_rotation();
}
inline void Transformation3d::unsafe_arena_set_allocated_rotation(
    ::esurfing::proto::math::Quaterniond* rotation) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.rotation_);
  }
  _impl_.rotation_ = rotation;
  if (rotation) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:esurfing.proto.math.Transformation3d.rotation)
}
inline ::esurfing::proto::math::Quaterniond* Transformation3d::release_rotation() {
  
  ::esurfing::proto::math::Quaterniond* temp = _impl_.rotation_;
  _impl_.rotation_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::esurfing::proto::math::Quaterniond* Transformation3d::unsafe_arena_release_rotation() {
  // @@protoc_insertion_point(field_release:esurfing.proto.math.Transformation3d.rotation)
  
  ::esurfing::proto::math::Quaterniond* temp = _impl_.rotation_;
  _impl_.rotation_ = nullptr;
  return temp;
}
inline ::esurfing::proto::math::Quaterniond* Transformation3d::_internal_mutable_rotation() {
  
  if (_impl_.rotation_ == nullptr) {
    auto* p = CreateMaybeMessage<::esurfing::proto::math::Quaterniond>(GetArenaForAllocation());
    _impl_.rotation_ = p;
  }
  return _impl_.rotation_;
}
inline ::esurfing::proto::math::Quaterniond* Transformation3d::mutable_rotation() {
  ::esurfing::proto::math::Quaterniond* _msg = _internal_mutable_rotation();
  // @@protoc_insertion_point(field_mutable:esurfing.proto.math.Transformation3d.rotation)
  return _msg;
}
inline void Transformation3d::set_allocated_rotation(::esurfing::proto::math::Quaterniond* rotation) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.rotation_;
  }
  if (rotation) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(rotation);
    if (message_arena != submessage_arena) {
      rotation = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, rotation, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.rotation_ = rotation;
  // @@protoc_insertion_point(field_set_allocated:esurfing.proto.math.Transformation3d.rotation)
}

// .esurfing.proto.math.Vector3d translation = 2;
inline bool Transformation3d::_internal_has_translation() const {
  return this != internal_default_instance() && _impl_.translation_ != nullptr;
}
inline bool Transformation3d::has_translation() const {
  return _internal_has_translation();
}
inline void Transformation3d::clear_translation() {
  if (GetArenaForAllocation() == nullptr && _impl_.translation_ != nullptr) {
    delete _impl_.translation_;
  }
  _impl_.translation_ = nullptr;
}
inline const ::esurfing::proto::math::Vector3d& Transformation3d::_internal_translation() const {
  const ::esurfing::proto::math::Vector3d* p = _impl_.translation_;
  return p != nullptr ? *p : reinterpret_cast<const ::esurfing::proto::math::Vector3d&>(
      ::esurfing::proto::math::_Vector3d_default_instance_);
}
inline const ::esurfing::proto::math::Vector3d& Transformation3d::translation() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.math.Transformation3d.translation)
  return _internal_translation();
}
inline void Transformation3d::unsafe_arena_set_allocated_translation(
    ::esurfing::proto::math::Vector3d* translation) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.translation_);
  }
  _impl_.translation_ = translation;
  if (translation) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:esurfing.proto.math.Transformation3d.translation)
}
inline ::esurfing::proto::math::Vector3d* Transformation3d::release_translation() {
  
  ::esurfing::proto::math::Vector3d* temp = _impl_.translation_;
  _impl_.translation_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::esurfing::proto::math::Vector3d* Transformation3d::unsafe_arena_release_translation() {
  // @@protoc_insertion_point(field_release:esurfing.proto.math.Transformation3d.translation)
  
  ::esurfing::proto::math::Vector3d* temp = _impl_.translation_;
  _impl_.translation_ = nullptr;
  return temp;
}
inline ::esurfing::proto::math::Vector3d* Transformation3d::_internal_mutable_translation() {
  
  if (_impl_.translation_ == nullptr) {
    auto* p = CreateMaybeMessage<::esurfing::proto::math::Vector3d>(GetArenaForAllocation());
    _impl_.translation_ = p;
  }
  return _impl_.translation_;
}
inline ::esurfing::proto::math::Vector3d* Transformation3d::mutable_translation() {
  ::esurfing::proto::math::Vector3d* _msg = _internal_mutable_translation();
  // @@protoc_insertion_point(field_mutable:esurfing.proto.math.Transformation3d.translation)
  return _msg;
}
inline void Transformation3d::set_allocated_translation(::esurfing::proto::math::Vector3d* translation) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.translation_;
  }
  if (translation) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(translation);
    if (message_arena != submessage_arena) {
      translation = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, translation, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.translation_ = translation;
  // @@protoc_insertion_point(field_set_allocated:esurfing.proto.math.Transformation3d.translation)
}

// -------------------------------------------------------------------

// Transformation3f

// .esurfing.proto.math.Quaternionf rotation = 1;
inline bool Transformation3f::_internal_has_rotation() const {
  return this != internal_default_instance() && _impl_.rotation_ != nullptr;
}
inline bool Transformation3f::has_rotation() const {
  return _internal_has_rotation();
}
inline void Transformation3f::clear_rotation() {
  if (GetArenaForAllocation() == nullptr && _impl_.rotation_ != nullptr) {
    delete _impl_.rotation_;
  }
  _impl_.rotation_ = nullptr;
}
inline const ::esurfing::proto::math::Quaternionf& Transformation3f::_internal_rotation() const {
  const ::esurfing::proto::math::Quaternionf* p = _impl_.rotation_;
  return p != nullptr ? *p : reinterpret_cast<const ::esurfing::proto::math::Quaternionf&>(
      ::esurfing::proto::math::_Quaternionf_default_instance_);
}
inline const ::esurfing::proto::math::Quaternionf& Transformation3f::rotation() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.math.Transformation3f.rotation)
  return _internal_rotation();
}
inline void Transformation3f::unsafe_arena_set_allocated_rotation(
    ::esurfing::proto::math::Quaternionf* rotation) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.rotation_);
  }
  _impl_.rotation_ = rotation;
  if (rotation) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:esurfing.proto.math.Transformation3f.rotation)
}
inline ::esurfing::proto::math::Quaternionf* Transformation3f::release_rotation() {
  
  ::esurfing::proto::math::Quaternionf* temp = _impl_.rotation_;
  _impl_.rotation_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::esurfing::proto::math::Quaternionf* Transformation3f::unsafe_arena_release_rotation() {
  // @@protoc_insertion_point(field_release:esurfing.proto.math.Transformation3f.rotation)
  
  ::esurfing::proto::math::Quaternionf* temp = _impl_.rotation_;
  _impl_.rotation_ = nullptr;
  return temp;
}
inline ::esurfing::proto::math::Quaternionf* Transformation3f::_internal_mutable_rotation() {
  
  if (_impl_.rotation_ == nullptr) {
    auto* p = CreateMaybeMessage<::esurfing::proto::math::Quaternionf>(GetArenaForAllocation());
    _impl_.rotation_ = p;
  }
  return _impl_.rotation_;
}
inline ::esurfing::proto::math::Quaternionf* Transformation3f::mutable_rotation() {
  ::esurfing::proto::math::Quaternionf* _msg = _internal_mutable_rotation();
  // @@protoc_insertion_point(field_mutable:esurfing.proto.math.Transformation3f.rotation)
  return _msg;
}
inline void Transformation3f::set_allocated_rotation(::esurfing::proto::math::Quaternionf* rotation) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.rotation_;
  }
  if (rotation) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(rotation);
    if (message_arena != submessage_arena) {
      rotation = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, rotation, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.rotation_ = rotation;
  // @@protoc_insertion_point(field_set_allocated:esurfing.proto.math.Transformation3f.rotation)
}

// .esurfing.proto.math.Vector3f translation = 2;
inline bool Transformation3f::_internal_has_translation() const {
  return this != internal_default_instance() && _impl_.translation_ != nullptr;
}
inline bool Transformation3f::has_translation() const {
  return _internal_has_translation();
}
inline void Transformation3f::clear_translation() {
  if (GetArenaForAllocation() == nullptr && _impl_.translation_ != nullptr) {
    delete _impl_.translation_;
  }
  _impl_.translation_ = nullptr;
}
inline const ::esurfing::proto::math::Vector3f& Transformation3f::_internal_translation() const {
  const ::esurfing::proto::math::Vector3f* p = _impl_.translation_;
  return p != nullptr ? *p : reinterpret_cast<const ::esurfing::proto::math::Vector3f&>(
      ::esurfing::proto::math::_Vector3f_default_instance_);
}
inline const ::esurfing::proto::math::Vector3f& Transformation3f::translation() const {
  // @@protoc_insertion_point(field_get:esurfing.proto.math.Transformation3f.translation)
  return _internal_translation();
}
inline void Transformation3f::unsafe_arena_set_allocated_translation(
    ::esurfing::proto::math::Vector3f* translation) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.translation_);
  }
  _impl_.translation_ = translation;
  if (translation) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:esurfing.proto.math.Transformation3f.translation)
}
inline ::esurfing::proto::math::Vector3f* Transformation3f::release_translation() {
  
  ::esurfing::proto::math::Vector3f* temp = _impl_.translation_;
  _impl_.translation_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::esurfing::proto::math::Vector3f* Transformation3f::unsafe_arena_release_translation() {
  // @@protoc_insertion_point(field_release:esurfing.proto.math.Transformation3f.translation)
  
  ::esurfing::proto::math::Vector3f* temp = _impl_.translation_;
  _impl_.translation_ = nullptr;
  return temp;
}
inline ::esurfing::proto::math::Vector3f* Transformation3f::_internal_mutable_translation() {
  
  if (_impl_.translation_ == nullptr) {
    auto* p = CreateMaybeMessage<::esurfing::proto::math::Vector3f>(GetArenaForAllocation());
    _impl_.translation_ = p;
  }
  return _impl_.translation_;
}
inline ::esurfing::proto::math::Vector3f* Transformation3f::mutable_translation() {
  ::esurfing::proto::math::Vector3f* _msg = _internal_mutable_translation();
  // @@protoc_insertion_point(field_mutable:esurfing.proto.math.Transformation3f.translation)
  return _msg;
}
inline void Transformation3f::set_allocated_translation(::esurfing::proto::math::Vector3f* translation) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.translation_;
  }
  if (translation) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(translation);
    if (message_arena != submessage_arena) {
      translation = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, translation, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.translation_ = translation;
  // @@protoc_insertion_point(field_set_allocated:esurfing.proto.math.Transformation3f.translation)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace math
}  // namespace proto
}  // namespace esurfing

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_geo_2eproto
