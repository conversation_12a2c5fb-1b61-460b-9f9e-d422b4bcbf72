// Copyright 2022 Allride.ai. All Rights Reserved.

syntax = "proto3";

package esurfing.proto.perc;

option go_package = "esurfing.com/proto/perc/detected_object";
option java_package = "com.esurfing.proto.perc";
option java_outer_classname = "PercObject";
option java_multiple_files = false;

message LicenePlate {
  int32 color = 1;
  float color_confidence = 2;
  string number = 3;
  float number_confidence = 4;
}

message DetectedObjects {
  sfixed64 time_meas = 1; // in ms, data time_stamp from driver
  sfixed64 time_pub = 2; // in ms, time_stamp when publish
  bytes group_name = 3; // group id, sub group
  bytes group_info = 4; // user defined info
  repeated DetectedObject objects = 5;
  repeated LidarPose lidar_poses = 6;
}

message DetectedObject {
  bytes overlap_name = 1; // belongs to which overlapped region
  fixed64 uuid = 2; // unique_id
  sint32 type = 3;
  float confidence = 4;
  Vector3f position = 5; // in map frame, in meter
  Vector3f shape = 6; // length, width, height, in meter
  Polygon hull = 7; // in map frame, BEV (2D) contour
  float orientation = 8; // physical object heading in map frame [-pi, pi)
  Velocity velocity = 9; // heading and speed in map frame
  bool is_static = 10; // the object is static (true) or not (false)
  Color color = 11; // vehicle color

  repeated float feature = 12; // ReID feature vector
  repeated WayPoints trajectories = 13; // history/predicted trajectories
  repeated bytes str_array = 14; // user defined info
  repeated sint32 int_array = 15; // user defined info
  int64 parking_time = 16;

  LicenePlate plate = 17;  //vehicle plate
  int32 obj_color = 18;   //vehicle color

  enum TrafficEvent {
    NORMAL= 0; // 正常
    OVERSPEED = 1; // 超速
    SLOWSPEED = 2; // 慢速
    CONTRAFLOW = 3; // 逆行
    ILLEGAL_PARKING = 4; // 违停
    VRU_WARNING = 5; // VRU警告
    Throwing_object = 6; // 抛撒物
    Traffic_event = 7;  // 交通事故
    Construction_area = 8; // 施工区域
  }

  TrafficEvent event= 19;
}

message Vector3f {
  float x = 1;
  float y = 2;
  float z = 3;
}

message Vector2f {
  float x = 1;
  float y = 2;
}

message Polygon {
  repeated Vector2f points = 1;
}

message Velocity {
  float heading = 1; // velocity heading [-pi, pi)
  float speed = 2; // velocity value in m/s
  float acceleration = 3; // in m/s2
}

message Color {
  int32 r = 1;
  int32 g = 2;
  int32 b = 3;
  float a = 4;
}

message WayPoints {
  repeated WayPoint waypoints = 1;
  float probability = 2;
}

message WayPoint {
  sfixed64 time_meas = 1; // in ms
  Vector3f position = 2; // in map frame, in meter
  Velocity velocity = 3; // in map frame
}

message Quaternionf {
  float x = 1;
  float y = 2;
  float z = 3;
  float w = 4;
}

message LidarPose {
  string name = 1;
  Vector3f position = 2;
  Quaternionf orientation = 3;
}