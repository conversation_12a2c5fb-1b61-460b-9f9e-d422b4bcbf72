// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: esurfing_group_detected.proto

#include "esurfing_group_detected.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>

PROTOBUF_PRAGMA_INIT_SEG

namespace _pb = ::PROTOBUF_NAMESPACE_ID;
namespace _pbi = _pb::internal;

namespace esurfing {
namespace proto {
namespace perc {
PROTOBUF_CONSTEXPR LicenePlate::LicenePlate(
    ::_pbi::ConstantInitialized): _impl_{
    /*decltype(_impl_.number_)*/{&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{}}
  , /*decltype(_impl_.color_)*/0
  , /*decltype(_impl_.color_confidence_)*/0
  , /*decltype(_impl_.number_confidence_)*/0
  , /*decltype(_impl_._cached_size_)*/{}} {}
struct LicenePlateDefaultTypeInternal {
  PROTOBUF_CONSTEXPR LicenePlateDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~LicenePlateDefaultTypeInternal() {}
  union {
    LicenePlate _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 LicenePlateDefaultTypeInternal _LicenePlate_default_instance_;
PROTOBUF_CONSTEXPR DetectedObjects::DetectedObjects(
    ::_pbi::ConstantInitialized): _impl_{
    /*decltype(_impl_.objects_)*/{}
  , /*decltype(_impl_.lidar_poses_)*/{}
  , /*decltype(_impl_.group_name_)*/{&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{}}
  , /*decltype(_impl_.group_info_)*/{&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{}}
  , /*decltype(_impl_.time_meas_)*/int64_t{0}
  , /*decltype(_impl_.time_pub_)*/int64_t{0}
  , /*decltype(_impl_._cached_size_)*/{}} {}
struct DetectedObjectsDefaultTypeInternal {
  PROTOBUF_CONSTEXPR DetectedObjectsDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~DetectedObjectsDefaultTypeInternal() {}
  union {
    DetectedObjects _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 DetectedObjectsDefaultTypeInternal _DetectedObjects_default_instance_;
PROTOBUF_CONSTEXPR DetectedObject::DetectedObject(
    ::_pbi::ConstantInitialized): _impl_{
    /*decltype(_impl_.feature_)*/{}
  , /*decltype(_impl_.trajectories_)*/{}
  , /*decltype(_impl_.str_array_)*/{}
  , /*decltype(_impl_.int_array_)*/{}
  , /*decltype(_impl_._int_array_cached_byte_size_)*/{0}
  , /*decltype(_impl_.overlap_name_)*/{&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{}}
  , /*decltype(_impl_.position_)*/nullptr
  , /*decltype(_impl_.shape_)*/nullptr
  , /*decltype(_impl_.hull_)*/nullptr
  , /*decltype(_impl_.velocity_)*/nullptr
  , /*decltype(_impl_.color_)*/nullptr
  , /*decltype(_impl_.plate_)*/nullptr
  , /*decltype(_impl_.uuid_)*/uint64_t{0u}
  , /*decltype(_impl_.type_)*/0
  , /*decltype(_impl_.confidence_)*/0
  , /*decltype(_impl_.orientation_)*/0
  , /*decltype(_impl_.is_static_)*/false
  , /*decltype(_impl_.parking_time_)*/int64_t{0}
  , /*decltype(_impl_.obj_color_)*/0
  , /*decltype(_impl_.event_)*/0
  , /*decltype(_impl_._cached_size_)*/{}} {}
struct DetectedObjectDefaultTypeInternal {
  PROTOBUF_CONSTEXPR DetectedObjectDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~DetectedObjectDefaultTypeInternal() {}
  union {
    DetectedObject _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 DetectedObjectDefaultTypeInternal _DetectedObject_default_instance_;
PROTOBUF_CONSTEXPR Vector3f::Vector3f(
    ::_pbi::ConstantInitialized): _impl_{
    /*decltype(_impl_.x_)*/0
  , /*decltype(_impl_.y_)*/0
  , /*decltype(_impl_.z_)*/0
  , /*decltype(_impl_._cached_size_)*/{}} {}
struct Vector3fDefaultTypeInternal {
  PROTOBUF_CONSTEXPR Vector3fDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~Vector3fDefaultTypeInternal() {}
  union {
    Vector3f _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 Vector3fDefaultTypeInternal _Vector3f_default_instance_;
PROTOBUF_CONSTEXPR Vector2f::Vector2f(
    ::_pbi::ConstantInitialized): _impl_{
    /*decltype(_impl_.x_)*/0
  , /*decltype(_impl_.y_)*/0
  , /*decltype(_impl_._cached_size_)*/{}} {}
struct Vector2fDefaultTypeInternal {
  PROTOBUF_CONSTEXPR Vector2fDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~Vector2fDefaultTypeInternal() {}
  union {
    Vector2f _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 Vector2fDefaultTypeInternal _Vector2f_default_instance_;
PROTOBUF_CONSTEXPR Polygon::Polygon(
    ::_pbi::ConstantInitialized): _impl_{
    /*decltype(_impl_.points_)*/{}
  , /*decltype(_impl_._cached_size_)*/{}} {}
struct PolygonDefaultTypeInternal {
  PROTOBUF_CONSTEXPR PolygonDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~PolygonDefaultTypeInternal() {}
  union {
    Polygon _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 PolygonDefaultTypeInternal _Polygon_default_instance_;
PROTOBUF_CONSTEXPR Velocity::Velocity(
    ::_pbi::ConstantInitialized): _impl_{
    /*decltype(_impl_.heading_)*/0
  , /*decltype(_impl_.speed_)*/0
  , /*decltype(_impl_.acceleration_)*/0
  , /*decltype(_impl_._cached_size_)*/{}} {}
struct VelocityDefaultTypeInternal {
  PROTOBUF_CONSTEXPR VelocityDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~VelocityDefaultTypeInternal() {}
  union {
    Velocity _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 VelocityDefaultTypeInternal _Velocity_default_instance_;
PROTOBUF_CONSTEXPR Color::Color(
    ::_pbi::ConstantInitialized): _impl_{
    /*decltype(_impl_.r_)*/0
  , /*decltype(_impl_.g_)*/0
  , /*decltype(_impl_.b_)*/0
  , /*decltype(_impl_.a_)*/0
  , /*decltype(_impl_._cached_size_)*/{}} {}
struct ColorDefaultTypeInternal {
  PROTOBUF_CONSTEXPR ColorDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~ColorDefaultTypeInternal() {}
  union {
    Color _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 ColorDefaultTypeInternal _Color_default_instance_;
PROTOBUF_CONSTEXPR WayPoints::WayPoints(
    ::_pbi::ConstantInitialized): _impl_{
    /*decltype(_impl_.waypoints_)*/{}
  , /*decltype(_impl_.probability_)*/0
  , /*decltype(_impl_._cached_size_)*/{}} {}
struct WayPointsDefaultTypeInternal {
  PROTOBUF_CONSTEXPR WayPointsDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~WayPointsDefaultTypeInternal() {}
  union {
    WayPoints _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 WayPointsDefaultTypeInternal _WayPoints_default_instance_;
PROTOBUF_CONSTEXPR WayPoint::WayPoint(
    ::_pbi::ConstantInitialized): _impl_{
    /*decltype(_impl_.position_)*/nullptr
  , /*decltype(_impl_.velocity_)*/nullptr
  , /*decltype(_impl_.time_meas_)*/int64_t{0}
  , /*decltype(_impl_._cached_size_)*/{}} {}
struct WayPointDefaultTypeInternal {
  PROTOBUF_CONSTEXPR WayPointDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~WayPointDefaultTypeInternal() {}
  union {
    WayPoint _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 WayPointDefaultTypeInternal _WayPoint_default_instance_;
PROTOBUF_CONSTEXPR Quaternionf::Quaternionf(
    ::_pbi::ConstantInitialized): _impl_{
    /*decltype(_impl_.x_)*/0
  , /*decltype(_impl_.y_)*/0
  , /*decltype(_impl_.z_)*/0
  , /*decltype(_impl_.w_)*/0
  , /*decltype(_impl_._cached_size_)*/{}} {}
struct QuaternionfDefaultTypeInternal {
  PROTOBUF_CONSTEXPR QuaternionfDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~QuaternionfDefaultTypeInternal() {}
  union {
    Quaternionf _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 QuaternionfDefaultTypeInternal _Quaternionf_default_instance_;
PROTOBUF_CONSTEXPR LidarPose::LidarPose(
    ::_pbi::ConstantInitialized): _impl_{
    /*decltype(_impl_.name_)*/{&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{}}
  , /*decltype(_impl_.position_)*/nullptr
  , /*decltype(_impl_.orientation_)*/nullptr
  , /*decltype(_impl_._cached_size_)*/{}} {}
struct LidarPoseDefaultTypeInternal {
  PROTOBUF_CONSTEXPR LidarPoseDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~LidarPoseDefaultTypeInternal() {}
  union {
    LidarPose _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 LidarPoseDefaultTypeInternal _LidarPose_default_instance_;
}  // namespace perc
}  // namespace proto
}  // namespace esurfing
static ::_pb::Metadata file_level_metadata_esurfing_5fgroup_5fdetected_2eproto[12];
static const ::_pb::EnumDescriptor* file_level_enum_descriptors_esurfing_5fgroup_5fdetected_2eproto[1];
static constexpr ::_pb::ServiceDescriptor const** file_level_service_descriptors_esurfing_5fgroup_5fdetected_2eproto = nullptr;

const uint32_t TableStruct_esurfing_5fgroup_5fdetected_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::esurfing::proto::perc::LicenePlate, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::esurfing::proto::perc::LicenePlate, _impl_.color_),
  PROTOBUF_FIELD_OFFSET(::esurfing::proto::perc::LicenePlate, _impl_.color_confidence_),
  PROTOBUF_FIELD_OFFSET(::esurfing::proto::perc::LicenePlate, _impl_.number_),
  PROTOBUF_FIELD_OFFSET(::esurfing::proto::perc::LicenePlate, _impl_.number_confidence_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::esurfing::proto::perc::DetectedObjects, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::esurfing::proto::perc::DetectedObjects, _impl_.time_meas_),
  PROTOBUF_FIELD_OFFSET(::esurfing::proto::perc::DetectedObjects, _impl_.time_pub_),
  PROTOBUF_FIELD_OFFSET(::esurfing::proto::perc::DetectedObjects, _impl_.group_name_),
  PROTOBUF_FIELD_OFFSET(::esurfing::proto::perc::DetectedObjects, _impl_.group_info_),
  PROTOBUF_FIELD_OFFSET(::esurfing::proto::perc::DetectedObjects, _impl_.objects_),
  PROTOBUF_FIELD_OFFSET(::esurfing::proto::perc::DetectedObjects, _impl_.lidar_poses_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::esurfing::proto::perc::DetectedObject, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::esurfing::proto::perc::DetectedObject, _impl_.overlap_name_),
  PROTOBUF_FIELD_OFFSET(::esurfing::proto::perc::DetectedObject, _impl_.uuid_),
  PROTOBUF_FIELD_OFFSET(::esurfing::proto::perc::DetectedObject, _impl_.type_),
  PROTOBUF_FIELD_OFFSET(::esurfing::proto::perc::DetectedObject, _impl_.confidence_),
  PROTOBUF_FIELD_OFFSET(::esurfing::proto::perc::DetectedObject, _impl_.position_),
  PROTOBUF_FIELD_OFFSET(::esurfing::proto::perc::DetectedObject, _impl_.shape_),
  PROTOBUF_FIELD_OFFSET(::esurfing::proto::perc::DetectedObject, _impl_.hull_),
  PROTOBUF_FIELD_OFFSET(::esurfing::proto::perc::DetectedObject, _impl_.orientation_),
  PROTOBUF_FIELD_OFFSET(::esurfing::proto::perc::DetectedObject, _impl_.velocity_),
  PROTOBUF_FIELD_OFFSET(::esurfing::proto::perc::DetectedObject, _impl_.is_static_),
  PROTOBUF_FIELD_OFFSET(::esurfing::proto::perc::DetectedObject, _impl_.color_),
  PROTOBUF_FIELD_OFFSET(::esurfing::proto::perc::DetectedObject, _impl_.feature_),
  PROTOBUF_FIELD_OFFSET(::esurfing::proto::perc::DetectedObject, _impl_.trajectories_),
  PROTOBUF_FIELD_OFFSET(::esurfing::proto::perc::DetectedObject, _impl_.str_array_),
  PROTOBUF_FIELD_OFFSET(::esurfing::proto::perc::DetectedObject, _impl_.int_array_),
  PROTOBUF_FIELD_OFFSET(::esurfing::proto::perc::DetectedObject, _impl_.parking_time_),
  PROTOBUF_FIELD_OFFSET(::esurfing::proto::perc::DetectedObject, _impl_.plate_),
  PROTOBUF_FIELD_OFFSET(::esurfing::proto::perc::DetectedObject, _impl_.obj_color_),
  PROTOBUF_FIELD_OFFSET(::esurfing::proto::perc::DetectedObject, _impl_.event_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::esurfing::proto::perc::Vector3f, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::esurfing::proto::perc::Vector3f, _impl_.x_),
  PROTOBUF_FIELD_OFFSET(::esurfing::proto::perc::Vector3f, _impl_.y_),
  PROTOBUF_FIELD_OFFSET(::esurfing::proto::perc::Vector3f, _impl_.z_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::esurfing::proto::perc::Vector2f, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::esurfing::proto::perc::Vector2f, _impl_.x_),
  PROTOBUF_FIELD_OFFSET(::esurfing::proto::perc::Vector2f, _impl_.y_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::esurfing::proto::perc::Polygon, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::esurfing::proto::perc::Polygon, _impl_.points_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::esurfing::proto::perc::Velocity, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::esurfing::proto::perc::Velocity, _impl_.heading_),
  PROTOBUF_FIELD_OFFSET(::esurfing::proto::perc::Velocity, _impl_.speed_),
  PROTOBUF_FIELD_OFFSET(::esurfing::proto::perc::Velocity, _impl_.acceleration_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::esurfing::proto::perc::Color, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::esurfing::proto::perc::Color, _impl_.r_),
  PROTOBUF_FIELD_OFFSET(::esurfing::proto::perc::Color, _impl_.g_),
  PROTOBUF_FIELD_OFFSET(::esurfing::proto::perc::Color, _impl_.b_),
  PROTOBUF_FIELD_OFFSET(::esurfing::proto::perc::Color, _impl_.a_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::esurfing::proto::perc::WayPoints, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::esurfing::proto::perc::WayPoints, _impl_.waypoints_),
  PROTOBUF_FIELD_OFFSET(::esurfing::proto::perc::WayPoints, _impl_.probability_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::esurfing::proto::perc::WayPoint, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::esurfing::proto::perc::WayPoint, _impl_.time_meas_),
  PROTOBUF_FIELD_OFFSET(::esurfing::proto::perc::WayPoint, _impl_.position_),
  PROTOBUF_FIELD_OFFSET(::esurfing::proto::perc::WayPoint, _impl_.velocity_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::esurfing::proto::perc::Quaternionf, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::esurfing::proto::perc::Quaternionf, _impl_.x_),
  PROTOBUF_FIELD_OFFSET(::esurfing::proto::perc::Quaternionf, _impl_.y_),
  PROTOBUF_FIELD_OFFSET(::esurfing::proto::perc::Quaternionf, _impl_.z_),
  PROTOBUF_FIELD_OFFSET(::esurfing::proto::perc::Quaternionf, _impl_.w_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::esurfing::proto::perc::LidarPose, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::esurfing::proto::perc::LidarPose, _impl_.name_),
  PROTOBUF_FIELD_OFFSET(::esurfing::proto::perc::LidarPose, _impl_.position_),
  PROTOBUF_FIELD_OFFSET(::esurfing::proto::perc::LidarPose, _impl_.orientation_),
};
static const ::_pbi::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, -1, sizeof(::esurfing::proto::perc::LicenePlate)},
  { 10, -1, -1, sizeof(::esurfing::proto::perc::DetectedObjects)},
  { 22, -1, -1, sizeof(::esurfing::proto::perc::DetectedObject)},
  { 47, -1, -1, sizeof(::esurfing::proto::perc::Vector3f)},
  { 56, -1, -1, sizeof(::esurfing::proto::perc::Vector2f)},
  { 64, -1, -1, sizeof(::esurfing::proto::perc::Polygon)},
  { 71, -1, -1, sizeof(::esurfing::proto::perc::Velocity)},
  { 80, -1, -1, sizeof(::esurfing::proto::perc::Color)},
  { 90, -1, -1, sizeof(::esurfing::proto::perc::WayPoints)},
  { 98, -1, -1, sizeof(::esurfing::proto::perc::WayPoint)},
  { 107, -1, -1, sizeof(::esurfing::proto::perc::Quaternionf)},
  { 117, -1, -1, sizeof(::esurfing::proto::perc::LidarPose)},
};

static const ::_pb::Message* const file_default_instances[] = {
  &::esurfing::proto::perc::_LicenePlate_default_instance_._instance,
  &::esurfing::proto::perc::_DetectedObjects_default_instance_._instance,
  &::esurfing::proto::perc::_DetectedObject_default_instance_._instance,
  &::esurfing::proto::perc::_Vector3f_default_instance_._instance,
  &::esurfing::proto::perc::_Vector2f_default_instance_._instance,
  &::esurfing::proto::perc::_Polygon_default_instance_._instance,
  &::esurfing::proto::perc::_Velocity_default_instance_._instance,
  &::esurfing::proto::perc::_Color_default_instance_._instance,
  &::esurfing::proto::perc::_WayPoints_default_instance_._instance,
  &::esurfing::proto::perc::_WayPoint_default_instance_._instance,
  &::esurfing::proto::perc::_Quaternionf_default_instance_._instance,
  &::esurfing::proto::perc::_LidarPose_default_instance_._instance,
};

const char descriptor_table_protodef_esurfing_5fgroup_5fdetected_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n\035esurfing_group_detected.proto\022\023esurfin"
  "g.proto.perc\"a\n\013LicenePlate\022\r\n\005color\030\001 \001"
  "(\005\022\030\n\020color_confidence\030\002 \001(\002\022\016\n\006number\030\003"
  " \001(\t\022\031\n\021number_confidence\030\004 \001(\002\"\311\001\n\017Dete"
  "ctedObjects\022\021\n\ttime_meas\030\001 \001(\020\022\020\n\010time_p"
  "ub\030\002 \001(\020\022\022\n\ngroup_name\030\003 \001(\014\022\022\n\ngroup_in"
  "fo\030\004 \001(\014\0224\n\007objects\030\005 \003(\0132#.esurfing.pro"
  "to.perc.DetectedObject\0223\n\013lidar_poses\030\006 "
  "\003(\0132\036.esurfing.proto.perc.LidarPose\"\235\006\n\016"
  "DetectedObject\022\024\n\014overlap_name\030\001 \001(\014\022\014\n\004"
  "uuid\030\002 \001(\006\022\014\n\004type\030\003 \001(\021\022\022\n\nconfidence\030\004"
  " \001(\002\022/\n\010position\030\005 \001(\0132\035.esurfing.proto."
  "perc.Vector3f\022,\n\005shape\030\006 \001(\0132\035.esurfing."
  "proto.perc.Vector3f\022*\n\004hull\030\007 \001(\0132\034.esur"
  "fing.proto.perc.Polygon\022\023\n\013orientation\030\010"
  " \001(\002\022/\n\010velocity\030\t \001(\0132\035.esurfing.proto."
  "perc.Velocity\022\021\n\tis_static\030\n \001(\010\022)\n\005colo"
  "r\030\013 \001(\0132\032.esurfing.proto.perc.Color\022\017\n\007f"
  "eature\030\014 \003(\002\0224\n\014trajectories\030\r \003(\0132\036.esu"
  "rfing.proto.perc.WayPoints\022\021\n\tstr_array\030"
  "\016 \003(\014\022\021\n\tint_array\030\017 \003(\021\022\024\n\014parking_time"
  "\030\020 \001(\003\022/\n\005plate\030\021 \001(\0132 .esurfing.proto.p"
  "erc.LicenePlate\022\021\n\tobj_color\030\022 \001(\005\022\?\n\005ev"
  "ent\030\023 \001(\01620.esurfing.proto.perc.Detected"
  "Object.TrafficEvent\"\255\001\n\014TrafficEvent\022\n\n\006"
  "NORMAL\020\000\022\r\n\tOVERSPEED\020\001\022\r\n\tSLOWSPEED\020\002\022\016"
  "\n\nCONTRAFLOW\020\003\022\023\n\017ILLEGAL_PARKING\020\004\022\017\n\013V"
  "RU_WARNING\020\005\022\023\n\017Throwing_object\020\006\022\021\n\rTra"
  "ffic_event\020\007\022\025\n\021Construction_area\020\010\"+\n\010V"
  "ector3f\022\t\n\001x\030\001 \001(\002\022\t\n\001y\030\002 \001(\002\022\t\n\001z\030\003 \001(\002"
  "\" \n\010Vector2f\022\t\n\001x\030\001 \001(\002\022\t\n\001y\030\002 \001(\002\"8\n\007Po"
  "lygon\022-\n\006points\030\001 \003(\0132\035.esurfing.proto.p"
  "erc.Vector2f\"@\n\010Velocity\022\017\n\007heading\030\001 \001("
  "\002\022\r\n\005speed\030\002 \001(\002\022\024\n\014acceleration\030\003 \001(\002\"3"
  "\n\005Color\022\t\n\001r\030\001 \001(\005\022\t\n\001g\030\002 \001(\005\022\t\n\001b\030\003 \001(\005"
  "\022\t\n\001a\030\004 \001(\002\"R\n\tWayPoints\0220\n\twaypoints\030\001 "
  "\003(\0132\035.esurfing.proto.perc.WayPoint\022\023\n\013pr"
  "obability\030\002 \001(\002\"\177\n\010WayPoint\022\021\n\ttime_meas"
  "\030\001 \001(\020\022/\n\010position\030\002 \001(\0132\035.esurfing.prot"
  "o.perc.Vector3f\022/\n\010velocity\030\003 \001(\0132\035.esur"
  "fing.proto.perc.Velocity\"9\n\013Quaternionf\022"
  "\t\n\001x\030\001 \001(\002\022\t\n\001y\030\002 \001(\002\022\t\n\001z\030\003 \001(\002\022\t\n\001w\030\004 "
  "\001(\002\"\201\001\n\tLidarPose\022\014\n\004name\030\001 \001(\t\022/\n\010posit"
  "ion\030\002 \001(\0132\035.esurfing.proto.perc.Vector3f"
  "\0225\n\013orientation\030\003 \001(\0132 .esurfing.proto.p"
  "erc.QuaternionfBP\n\027com.esurfing.proto.pe"
  "rcB\nPercObjectP\000Z\'esurfing.com/proto/per"
  "c/detected_objectb\006proto3"
  ;
static ::_pbi::once_flag descriptor_table_esurfing_5fgroup_5fdetected_2eproto_once;
const ::_pbi::DescriptorTable descriptor_table_esurfing_5fgroup_5fdetected_2eproto = {
    false, false, 1905, descriptor_table_protodef_esurfing_5fgroup_5fdetected_2eproto,
    "esurfing_group_detected.proto",
    &descriptor_table_esurfing_5fgroup_5fdetected_2eproto_once, nullptr, 0, 12,
    schemas, file_default_instances, TableStruct_esurfing_5fgroup_5fdetected_2eproto::offsets,
    file_level_metadata_esurfing_5fgroup_5fdetected_2eproto, file_level_enum_descriptors_esurfing_5fgroup_5fdetected_2eproto,
    file_level_service_descriptors_esurfing_5fgroup_5fdetected_2eproto,
};
PROTOBUF_ATTRIBUTE_WEAK const ::_pbi::DescriptorTable* descriptor_table_esurfing_5fgroup_5fdetected_2eproto_getter() {
  return &descriptor_table_esurfing_5fgroup_5fdetected_2eproto;
}

// Force running AddDescriptors() at dynamic initialization time.
PROTOBUF_ATTRIBUTE_INIT_PRIORITY2 static ::_pbi::AddDescriptorsRunner dynamic_init_dummy_esurfing_5fgroup_5fdetected_2eproto(&descriptor_table_esurfing_5fgroup_5fdetected_2eproto);
namespace esurfing {
namespace proto {
namespace perc {
const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* DetectedObject_TrafficEvent_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_esurfing_5fgroup_5fdetected_2eproto);
  return file_level_enum_descriptors_esurfing_5fgroup_5fdetected_2eproto[0];
}
bool DetectedObject_TrafficEvent_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
    case 3:
    case 4:
    case 5:
    case 6:
    case 7:
    case 8:
      return true;
    default:
      return false;
  }
}

#if (__cplusplus < 201703) && (!defined(_MSC_VER) || (_MSC_VER >= 1900 && _MSC_VER < 1912))
constexpr DetectedObject_TrafficEvent DetectedObject::NORMAL;
constexpr DetectedObject_TrafficEvent DetectedObject::OVERSPEED;
constexpr DetectedObject_TrafficEvent DetectedObject::SLOWSPEED;
constexpr DetectedObject_TrafficEvent DetectedObject::CONTRAFLOW;
constexpr DetectedObject_TrafficEvent DetectedObject::ILLEGAL_PARKING;
constexpr DetectedObject_TrafficEvent DetectedObject::VRU_WARNING;
constexpr DetectedObject_TrafficEvent DetectedObject::Throwing_object;
constexpr DetectedObject_TrafficEvent DetectedObject::Traffic_event;
constexpr DetectedObject_TrafficEvent DetectedObject::Construction_area;
constexpr DetectedObject_TrafficEvent DetectedObject::TrafficEvent_MIN;
constexpr DetectedObject_TrafficEvent DetectedObject::TrafficEvent_MAX;
constexpr int DetectedObject::TrafficEvent_ARRAYSIZE;
#endif  // (__cplusplus < 201703) && (!defined(_MSC_VER) || (_MSC_VER >= 1900 && _MSC_VER < 1912))

// ===================================================================

class LicenePlate::_Internal {
 public:
};

LicenePlate::LicenePlate(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor(arena, is_message_owned);
  // @@protoc_insertion_point(arena_constructor:esurfing.proto.perc.LicenePlate)
}
LicenePlate::LicenePlate(const LicenePlate& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  LicenePlate* const _this = this; (void)_this;
  new (&_impl_) Impl_{
      decltype(_impl_.number_){}
    , decltype(_impl_.color_){}
    , decltype(_impl_.color_confidence_){}
    , decltype(_impl_.number_confidence_){}
    , /*decltype(_impl_._cached_size_)*/{}};

  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  _impl_.number_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.number_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_number().empty()) {
    _this->_impl_.number_.Set(from._internal_number(), 
      _this->GetArenaForAllocation());
  }
  ::memcpy(&_impl_.color_, &from._impl_.color_,
    static_cast<size_t>(reinterpret_cast<char*>(&_impl_.number_confidence_) -
    reinterpret_cast<char*>(&_impl_.color_)) + sizeof(_impl_.number_confidence_));
  // @@protoc_insertion_point(copy_constructor:esurfing.proto.perc.LicenePlate)
}

inline void LicenePlate::SharedCtor(
    ::_pb::Arena* arena, bool is_message_owned) {
  (void)arena;
  (void)is_message_owned;
  new (&_impl_) Impl_{
      decltype(_impl_.number_){}
    , decltype(_impl_.color_){0}
    , decltype(_impl_.color_confidence_){0}
    , decltype(_impl_.number_confidence_){0}
    , /*decltype(_impl_._cached_size_)*/{}
  };
  _impl_.number_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.number_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

LicenePlate::~LicenePlate() {
  // @@protoc_insertion_point(destructor:esurfing.proto.perc.LicenePlate)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void LicenePlate::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  _impl_.number_.Destroy();
}

void LicenePlate::SetCachedSize(int size) const {
  _impl_._cached_size_.Set(size);
}

void LicenePlate::Clear() {
// @@protoc_insertion_point(message_clear_start:esurfing.proto.perc.LicenePlate)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _impl_.number_.ClearToEmpty();
  ::memset(&_impl_.color_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&_impl_.number_confidence_) -
      reinterpret_cast<char*>(&_impl_.color_)) + sizeof(_impl_.number_confidence_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* LicenePlate::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // int32 color = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          _impl_.color_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // float color_confidence = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 21)) {
          _impl_.color_confidence_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // string number = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 26)) {
          auto str = _internal_mutable_number();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
          CHK_(::_pbi::VerifyUTF8(str, "esurfing.proto.perc.LicenePlate.number"));
        } else
          goto handle_unusual;
        continue;
      // float number_confidence = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 37)) {
          _impl_.number_confidence_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* LicenePlate::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:esurfing.proto.perc.LicenePlate)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // int32 color = 1;
  if (this->_internal_color() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(1, this->_internal_color(), target);
  }

  // float color_confidence = 2;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_color_confidence = this->_internal_color_confidence();
  uint32_t raw_color_confidence;
  memcpy(&raw_color_confidence, &tmp_color_confidence, sizeof(tmp_color_confidence));
  if (raw_color_confidence != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteFloatToArray(2, this->_internal_color_confidence(), target);
  }

  // string number = 3;
  if (!this->_internal_number().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_number().data(), static_cast<int>(this->_internal_number().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "esurfing.proto.perc.LicenePlate.number");
    target = stream->WriteStringMaybeAliased(
        3, this->_internal_number(), target);
  }

  // float number_confidence = 4;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_number_confidence = this->_internal_number_confidence();
  uint32_t raw_number_confidence;
  memcpy(&raw_number_confidence, &tmp_number_confidence, sizeof(tmp_number_confidence));
  if (raw_number_confidence != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteFloatToArray(4, this->_internal_number_confidence(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:esurfing.proto.perc.LicenePlate)
  return target;
}

size_t LicenePlate::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:esurfing.proto.perc.LicenePlate)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string number = 3;
  if (!this->_internal_number().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_number());
  }

  // int32 color = 1;
  if (this->_internal_color() != 0) {
    total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(this->_internal_color());
  }

  // float color_confidence = 2;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_color_confidence = this->_internal_color_confidence();
  uint32_t raw_color_confidence;
  memcpy(&raw_color_confidence, &tmp_color_confidence, sizeof(tmp_color_confidence));
  if (raw_color_confidence != 0) {
    total_size += 1 + 4;
  }

  // float number_confidence = 4;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_number_confidence = this->_internal_number_confidence();
  uint32_t raw_number_confidence;
  memcpy(&raw_number_confidence, &tmp_number_confidence, sizeof(tmp_number_confidence));
  if (raw_number_confidence != 0) {
    total_size += 1 + 4;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_impl_._cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData LicenePlate::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSourceCheck,
    LicenePlate::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*LicenePlate::GetClassData() const { return &_class_data_; }


void LicenePlate::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg) {
  auto* const _this = static_cast<LicenePlate*>(&to_msg);
  auto& from = static_cast<const LicenePlate&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:esurfing.proto.perc.LicenePlate)
  GOOGLE_DCHECK_NE(&from, _this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_number().empty()) {
    _this->_internal_set_number(from._internal_number());
  }
  if (from._internal_color() != 0) {
    _this->_internal_set_color(from._internal_color());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_color_confidence = from._internal_color_confidence();
  uint32_t raw_color_confidence;
  memcpy(&raw_color_confidence, &tmp_color_confidence, sizeof(tmp_color_confidence));
  if (raw_color_confidence != 0) {
    _this->_internal_set_color_confidence(from._internal_color_confidence());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_number_confidence = from._internal_number_confidence();
  uint32_t raw_number_confidence;
  memcpy(&raw_number_confidence, &tmp_number_confidence, sizeof(tmp_number_confidence));
  if (raw_number_confidence != 0) {
    _this->_internal_set_number_confidence(from._internal_number_confidence());
  }
  _this->_internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void LicenePlate::CopyFrom(const LicenePlate& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:esurfing.proto.perc.LicenePlate)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool LicenePlate::IsInitialized() const {
  return true;
}

void LicenePlate::InternalSwap(LicenePlate* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &_impl_.number_, lhs_arena,
      &other->_impl_.number_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(LicenePlate, _impl_.number_confidence_)
      + sizeof(LicenePlate::_impl_.number_confidence_)
      - PROTOBUF_FIELD_OFFSET(LicenePlate, _impl_.color_)>(
          reinterpret_cast<char*>(&_impl_.color_),
          reinterpret_cast<char*>(&other->_impl_.color_));
}

::PROTOBUF_NAMESPACE_ID::Metadata LicenePlate::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_esurfing_5fgroup_5fdetected_2eproto_getter, &descriptor_table_esurfing_5fgroup_5fdetected_2eproto_once,
      file_level_metadata_esurfing_5fgroup_5fdetected_2eproto[0]);
}

// ===================================================================

class DetectedObjects::_Internal {
 public:
};

DetectedObjects::DetectedObjects(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor(arena, is_message_owned);
  // @@protoc_insertion_point(arena_constructor:esurfing.proto.perc.DetectedObjects)
}
DetectedObjects::DetectedObjects(const DetectedObjects& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  DetectedObjects* const _this = this; (void)_this;
  new (&_impl_) Impl_{
      decltype(_impl_.objects_){from._impl_.objects_}
    , decltype(_impl_.lidar_poses_){from._impl_.lidar_poses_}
    , decltype(_impl_.group_name_){}
    , decltype(_impl_.group_info_){}
    , decltype(_impl_.time_meas_){}
    , decltype(_impl_.time_pub_){}
    , /*decltype(_impl_._cached_size_)*/{}};

  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  _impl_.group_name_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.group_name_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_group_name().empty()) {
    _this->_impl_.group_name_.Set(from._internal_group_name(), 
      _this->GetArenaForAllocation());
  }
  _impl_.group_info_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.group_info_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_group_info().empty()) {
    _this->_impl_.group_info_.Set(from._internal_group_info(), 
      _this->GetArenaForAllocation());
  }
  ::memcpy(&_impl_.time_meas_, &from._impl_.time_meas_,
    static_cast<size_t>(reinterpret_cast<char*>(&_impl_.time_pub_) -
    reinterpret_cast<char*>(&_impl_.time_meas_)) + sizeof(_impl_.time_pub_));
  // @@protoc_insertion_point(copy_constructor:esurfing.proto.perc.DetectedObjects)
}

inline void DetectedObjects::SharedCtor(
    ::_pb::Arena* arena, bool is_message_owned) {
  (void)arena;
  (void)is_message_owned;
  new (&_impl_) Impl_{
      decltype(_impl_.objects_){arena}
    , decltype(_impl_.lidar_poses_){arena}
    , decltype(_impl_.group_name_){}
    , decltype(_impl_.group_info_){}
    , decltype(_impl_.time_meas_){int64_t{0}}
    , decltype(_impl_.time_pub_){int64_t{0}}
    , /*decltype(_impl_._cached_size_)*/{}
  };
  _impl_.group_name_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.group_name_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  _impl_.group_info_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.group_info_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

DetectedObjects::~DetectedObjects() {
  // @@protoc_insertion_point(destructor:esurfing.proto.perc.DetectedObjects)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void DetectedObjects::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  _impl_.objects_.~RepeatedPtrField();
  _impl_.lidar_poses_.~RepeatedPtrField();
  _impl_.group_name_.Destroy();
  _impl_.group_info_.Destroy();
}

void DetectedObjects::SetCachedSize(int size) const {
  _impl_._cached_size_.Set(size);
}

void DetectedObjects::Clear() {
// @@protoc_insertion_point(message_clear_start:esurfing.proto.perc.DetectedObjects)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _impl_.objects_.Clear();
  _impl_.lidar_poses_.Clear();
  _impl_.group_name_.ClearToEmpty();
  _impl_.group_info_.ClearToEmpty();
  ::memset(&_impl_.time_meas_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&_impl_.time_pub_) -
      reinterpret_cast<char*>(&_impl_.time_meas_)) + sizeof(_impl_.time_pub_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* DetectedObjects::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // sfixed64 time_meas = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 9)) {
          _impl_.time_meas_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<int64_t>(ptr);
          ptr += sizeof(int64_t);
        } else
          goto handle_unusual;
        continue;
      // sfixed64 time_pub = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 17)) {
          _impl_.time_pub_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<int64_t>(ptr);
          ptr += sizeof(int64_t);
        } else
          goto handle_unusual;
        continue;
      // bytes group_name = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 26)) {
          auto str = _internal_mutable_group_name();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bytes group_info = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 34)) {
          auto str = _internal_mutable_group_info();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // repeated .esurfing.proto.perc.DetectedObject objects = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 42)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_objects(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<42>(ptr));
        } else
          goto handle_unusual;
        continue;
      // repeated .esurfing.proto.perc.LidarPose lidar_poses = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 50)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_lidar_poses(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<50>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* DetectedObjects::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:esurfing.proto.perc.DetectedObjects)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // sfixed64 time_meas = 1;
  if (this->_internal_time_meas() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteSFixed64ToArray(1, this->_internal_time_meas(), target);
  }

  // sfixed64 time_pub = 2;
  if (this->_internal_time_pub() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteSFixed64ToArray(2, this->_internal_time_pub(), target);
  }

  // bytes group_name = 3;
  if (!this->_internal_group_name().empty()) {
    target = stream->WriteBytesMaybeAliased(
        3, this->_internal_group_name(), target);
  }

  // bytes group_info = 4;
  if (!this->_internal_group_info().empty()) {
    target = stream->WriteBytesMaybeAliased(
        4, this->_internal_group_info(), target);
  }

  // repeated .esurfing.proto.perc.DetectedObject objects = 5;
  for (unsigned i = 0,
      n = static_cast<unsigned>(this->_internal_objects_size()); i < n; i++) {
    const auto& repfield = this->_internal_objects(i);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
        InternalWriteMessage(5, repfield, repfield.GetCachedSize(), target, stream);
  }

  // repeated .esurfing.proto.perc.LidarPose lidar_poses = 6;
  for (unsigned i = 0,
      n = static_cast<unsigned>(this->_internal_lidar_poses_size()); i < n; i++) {
    const auto& repfield = this->_internal_lidar_poses(i);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
        InternalWriteMessage(6, repfield, repfield.GetCachedSize(), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:esurfing.proto.perc.DetectedObjects)
  return target;
}

size_t DetectedObjects::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:esurfing.proto.perc.DetectedObjects)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .esurfing.proto.perc.DetectedObject objects = 5;
  total_size += 1UL * this->_internal_objects_size();
  for (const auto& msg : this->_impl_.objects_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // repeated .esurfing.proto.perc.LidarPose lidar_poses = 6;
  total_size += 1UL * this->_internal_lidar_poses_size();
  for (const auto& msg : this->_impl_.lidar_poses_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // bytes group_name = 3;
  if (!this->_internal_group_name().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::BytesSize(
        this->_internal_group_name());
  }

  // bytes group_info = 4;
  if (!this->_internal_group_info().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::BytesSize(
        this->_internal_group_info());
  }

  // sfixed64 time_meas = 1;
  if (this->_internal_time_meas() != 0) {
    total_size += 1 + 8;
  }

  // sfixed64 time_pub = 2;
  if (this->_internal_time_pub() != 0) {
    total_size += 1 + 8;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_impl_._cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData DetectedObjects::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSourceCheck,
    DetectedObjects::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*DetectedObjects::GetClassData() const { return &_class_data_; }


void DetectedObjects::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg) {
  auto* const _this = static_cast<DetectedObjects*>(&to_msg);
  auto& from = static_cast<const DetectedObjects&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:esurfing.proto.perc.DetectedObjects)
  GOOGLE_DCHECK_NE(&from, _this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  _this->_impl_.objects_.MergeFrom(from._impl_.objects_);
  _this->_impl_.lidar_poses_.MergeFrom(from._impl_.lidar_poses_);
  if (!from._internal_group_name().empty()) {
    _this->_internal_set_group_name(from._internal_group_name());
  }
  if (!from._internal_group_info().empty()) {
    _this->_internal_set_group_info(from._internal_group_info());
  }
  if (from._internal_time_meas() != 0) {
    _this->_internal_set_time_meas(from._internal_time_meas());
  }
  if (from._internal_time_pub() != 0) {
    _this->_internal_set_time_pub(from._internal_time_pub());
  }
  _this->_internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void DetectedObjects::CopyFrom(const DetectedObjects& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:esurfing.proto.perc.DetectedObjects)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool DetectedObjects::IsInitialized() const {
  return true;
}

void DetectedObjects::InternalSwap(DetectedObjects* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  _impl_.objects_.InternalSwap(&other->_impl_.objects_);
  _impl_.lidar_poses_.InternalSwap(&other->_impl_.lidar_poses_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &_impl_.group_name_, lhs_arena,
      &other->_impl_.group_name_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &_impl_.group_info_, lhs_arena,
      &other->_impl_.group_info_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(DetectedObjects, _impl_.time_pub_)
      + sizeof(DetectedObjects::_impl_.time_pub_)
      - PROTOBUF_FIELD_OFFSET(DetectedObjects, _impl_.time_meas_)>(
          reinterpret_cast<char*>(&_impl_.time_meas_),
          reinterpret_cast<char*>(&other->_impl_.time_meas_));
}

::PROTOBUF_NAMESPACE_ID::Metadata DetectedObjects::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_esurfing_5fgroup_5fdetected_2eproto_getter, &descriptor_table_esurfing_5fgroup_5fdetected_2eproto_once,
      file_level_metadata_esurfing_5fgroup_5fdetected_2eproto[1]);
}

// ===================================================================

class DetectedObject::_Internal {
 public:
  static const ::esurfing::proto::perc::Vector3f& position(const DetectedObject* msg);
  static const ::esurfing::proto::perc::Vector3f& shape(const DetectedObject* msg);
  static const ::esurfing::proto::perc::Polygon& hull(const DetectedObject* msg);
  static const ::esurfing::proto::perc::Velocity& velocity(const DetectedObject* msg);
  static const ::esurfing::proto::perc::Color& color(const DetectedObject* msg);
  static const ::esurfing::proto::perc::LicenePlate& plate(const DetectedObject* msg);
};

const ::esurfing::proto::perc::Vector3f&
DetectedObject::_Internal::position(const DetectedObject* msg) {
  return *msg->_impl_.position_;
}
const ::esurfing::proto::perc::Vector3f&
DetectedObject::_Internal::shape(const DetectedObject* msg) {
  return *msg->_impl_.shape_;
}
const ::esurfing::proto::perc::Polygon&
DetectedObject::_Internal::hull(const DetectedObject* msg) {
  return *msg->_impl_.hull_;
}
const ::esurfing::proto::perc::Velocity&
DetectedObject::_Internal::velocity(const DetectedObject* msg) {
  return *msg->_impl_.velocity_;
}
const ::esurfing::proto::perc::Color&
DetectedObject::_Internal::color(const DetectedObject* msg) {
  return *msg->_impl_.color_;
}
const ::esurfing::proto::perc::LicenePlate&
DetectedObject::_Internal::plate(const DetectedObject* msg) {
  return *msg->_impl_.plate_;
}
DetectedObject::DetectedObject(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor(arena, is_message_owned);
  // @@protoc_insertion_point(arena_constructor:esurfing.proto.perc.DetectedObject)
}
DetectedObject::DetectedObject(const DetectedObject& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  DetectedObject* const _this = this; (void)_this;
  new (&_impl_) Impl_{
      decltype(_impl_.feature_){from._impl_.feature_}
    , decltype(_impl_.trajectories_){from._impl_.trajectories_}
    , decltype(_impl_.str_array_){from._impl_.str_array_}
    , decltype(_impl_.int_array_){from._impl_.int_array_}
    , /*decltype(_impl_._int_array_cached_byte_size_)*/{0}
    , decltype(_impl_.overlap_name_){}
    , decltype(_impl_.position_){nullptr}
    , decltype(_impl_.shape_){nullptr}
    , decltype(_impl_.hull_){nullptr}
    , decltype(_impl_.velocity_){nullptr}
    , decltype(_impl_.color_){nullptr}
    , decltype(_impl_.plate_){nullptr}
    , decltype(_impl_.uuid_){}
    , decltype(_impl_.type_){}
    , decltype(_impl_.confidence_){}
    , decltype(_impl_.orientation_){}
    , decltype(_impl_.is_static_){}
    , decltype(_impl_.parking_time_){}
    , decltype(_impl_.obj_color_){}
    , decltype(_impl_.event_){}
    , /*decltype(_impl_._cached_size_)*/{}};

  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  _impl_.overlap_name_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.overlap_name_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_overlap_name().empty()) {
    _this->_impl_.overlap_name_.Set(from._internal_overlap_name(), 
      _this->GetArenaForAllocation());
  }
  if (from._internal_has_position()) {
    _this->_impl_.position_ = new ::esurfing::proto::perc::Vector3f(*from._impl_.position_);
  }
  if (from._internal_has_shape()) {
    _this->_impl_.shape_ = new ::esurfing::proto::perc::Vector3f(*from._impl_.shape_);
  }
  if (from._internal_has_hull()) {
    _this->_impl_.hull_ = new ::esurfing::proto::perc::Polygon(*from._impl_.hull_);
  }
  if (from._internal_has_velocity()) {
    _this->_impl_.velocity_ = new ::esurfing::proto::perc::Velocity(*from._impl_.velocity_);
  }
  if (from._internal_has_color()) {
    _this->_impl_.color_ = new ::esurfing::proto::perc::Color(*from._impl_.color_);
  }
  if (from._internal_has_plate()) {
    _this->_impl_.plate_ = new ::esurfing::proto::perc::LicenePlate(*from._impl_.plate_);
  }
  ::memcpy(&_impl_.uuid_, &from._impl_.uuid_,
    static_cast<size_t>(reinterpret_cast<char*>(&_impl_.event_) -
    reinterpret_cast<char*>(&_impl_.uuid_)) + sizeof(_impl_.event_));
  // @@protoc_insertion_point(copy_constructor:esurfing.proto.perc.DetectedObject)
}

inline void DetectedObject::SharedCtor(
    ::_pb::Arena* arena, bool is_message_owned) {
  (void)arena;
  (void)is_message_owned;
  new (&_impl_) Impl_{
      decltype(_impl_.feature_){arena}
    , decltype(_impl_.trajectories_){arena}
    , decltype(_impl_.str_array_){arena}
    , decltype(_impl_.int_array_){arena}
    , /*decltype(_impl_._int_array_cached_byte_size_)*/{0}
    , decltype(_impl_.overlap_name_){}
    , decltype(_impl_.position_){nullptr}
    , decltype(_impl_.shape_){nullptr}
    , decltype(_impl_.hull_){nullptr}
    , decltype(_impl_.velocity_){nullptr}
    , decltype(_impl_.color_){nullptr}
    , decltype(_impl_.plate_){nullptr}
    , decltype(_impl_.uuid_){uint64_t{0u}}
    , decltype(_impl_.type_){0}
    , decltype(_impl_.confidence_){0}
    , decltype(_impl_.orientation_){0}
    , decltype(_impl_.is_static_){false}
    , decltype(_impl_.parking_time_){int64_t{0}}
    , decltype(_impl_.obj_color_){0}
    , decltype(_impl_.event_){0}
    , /*decltype(_impl_._cached_size_)*/{}
  };
  _impl_.overlap_name_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.overlap_name_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

DetectedObject::~DetectedObject() {
  // @@protoc_insertion_point(destructor:esurfing.proto.perc.DetectedObject)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void DetectedObject::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  _impl_.feature_.~RepeatedField();
  _impl_.trajectories_.~RepeatedPtrField();
  _impl_.str_array_.~RepeatedPtrField();
  _impl_.int_array_.~RepeatedField();
  _impl_.overlap_name_.Destroy();
  if (this != internal_default_instance()) delete _impl_.position_;
  if (this != internal_default_instance()) delete _impl_.shape_;
  if (this != internal_default_instance()) delete _impl_.hull_;
  if (this != internal_default_instance()) delete _impl_.velocity_;
  if (this != internal_default_instance()) delete _impl_.color_;
  if (this != internal_default_instance()) delete _impl_.plate_;
}

void DetectedObject::SetCachedSize(int size) const {
  _impl_._cached_size_.Set(size);
}

void DetectedObject::Clear() {
// @@protoc_insertion_point(message_clear_start:esurfing.proto.perc.DetectedObject)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _impl_.feature_.Clear();
  _impl_.trajectories_.Clear();
  _impl_.str_array_.Clear();
  _impl_.int_array_.Clear();
  _impl_.overlap_name_.ClearToEmpty();
  if (GetArenaForAllocation() == nullptr && _impl_.position_ != nullptr) {
    delete _impl_.position_;
  }
  _impl_.position_ = nullptr;
  if (GetArenaForAllocation() == nullptr && _impl_.shape_ != nullptr) {
    delete _impl_.shape_;
  }
  _impl_.shape_ = nullptr;
  if (GetArenaForAllocation() == nullptr && _impl_.hull_ != nullptr) {
    delete _impl_.hull_;
  }
  _impl_.hull_ = nullptr;
  if (GetArenaForAllocation() == nullptr && _impl_.velocity_ != nullptr) {
    delete _impl_.velocity_;
  }
  _impl_.velocity_ = nullptr;
  if (GetArenaForAllocation() == nullptr && _impl_.color_ != nullptr) {
    delete _impl_.color_;
  }
  _impl_.color_ = nullptr;
  if (GetArenaForAllocation() == nullptr && _impl_.plate_ != nullptr) {
    delete _impl_.plate_;
  }
  _impl_.plate_ = nullptr;
  ::memset(&_impl_.uuid_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&_impl_.event_) -
      reinterpret_cast<char*>(&_impl_.uuid_)) + sizeof(_impl_.event_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* DetectedObject::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // bytes overlap_name = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_overlap_name();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // fixed64 uuid = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 17)) {
          _impl_.uuid_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<uint64_t>(ptr);
          ptr += sizeof(uint64_t);
        } else
          goto handle_unusual;
        continue;
      // sint32 type = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 24)) {
          _impl_.type_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarintZigZag32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // float confidence = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 37)) {
          _impl_.confidence_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // .esurfing.proto.perc.Vector3f position = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 42)) {
          ptr = ctx->ParseMessage(_internal_mutable_position(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .esurfing.proto.perc.Vector3f shape = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 50)) {
          ptr = ctx->ParseMessage(_internal_mutable_shape(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .esurfing.proto.perc.Polygon hull = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 58)) {
          ptr = ctx->ParseMessage(_internal_mutable_hull(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // float orientation = 8;
      case 8:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 69)) {
          _impl_.orientation_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // .esurfing.proto.perc.Velocity velocity = 9;
      case 9:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 74)) {
          ptr = ctx->ParseMessage(_internal_mutable_velocity(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool is_static = 10;
      case 10:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 80)) {
          _impl_.is_static_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .esurfing.proto.perc.Color color = 11;
      case 11:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 90)) {
          ptr = ctx->ParseMessage(_internal_mutable_color(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // repeated float feature = 12;
      case 12:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 98)) {
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::PackedFloatParser(_internal_mutable_feature(), ptr, ctx);
          CHK_(ptr);
        } else if (static_cast<uint8_t>(tag) == 101) {
          _internal_add_feature(::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr));
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // repeated .esurfing.proto.perc.WayPoints trajectories = 13;
      case 13:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 106)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_trajectories(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<106>(ptr));
        } else
          goto handle_unusual;
        continue;
      // repeated bytes str_array = 14;
      case 14:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 114)) {
          ptr -= 1;
          do {
            ptr += 1;
            auto str = _internal_add_str_array();
            ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<114>(ptr));
        } else
          goto handle_unusual;
        continue;
      // repeated sint32 int_array = 15;
      case 15:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 122)) {
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::PackedSInt32Parser(_internal_mutable_int_array(), ptr, ctx);
          CHK_(ptr);
        } else if (static_cast<uint8_t>(tag) == 120) {
          _internal_add_int_array(::PROTOBUF_NAMESPACE_ID::internal::ReadVarintZigZag32(&ptr));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int64 parking_time = 16;
      case 16:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 128)) {
          _impl_.parking_time_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .esurfing.proto.perc.LicenePlate plate = 17;
      case 17:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 138)) {
          ptr = ctx->ParseMessage(_internal_mutable_plate(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 obj_color = 18;
      case 18:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 144)) {
          _impl_.obj_color_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .esurfing.proto.perc.DetectedObject.TrafficEvent event = 19;
      case 19:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 152)) {
          uint64_t val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_event(static_cast<::esurfing::proto::perc::DetectedObject_TrafficEvent>(val));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* DetectedObject::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:esurfing.proto.perc.DetectedObject)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // bytes overlap_name = 1;
  if (!this->_internal_overlap_name().empty()) {
    target = stream->WriteBytesMaybeAliased(
        1, this->_internal_overlap_name(), target);
  }

  // fixed64 uuid = 2;
  if (this->_internal_uuid() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteFixed64ToArray(2, this->_internal_uuid(), target);
  }

  // sint32 type = 3;
  if (this->_internal_type() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteSInt32ToArray(3, this->_internal_type(), target);
  }

  // float confidence = 4;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_confidence = this->_internal_confidence();
  uint32_t raw_confidence;
  memcpy(&raw_confidence, &tmp_confidence, sizeof(tmp_confidence));
  if (raw_confidence != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteFloatToArray(4, this->_internal_confidence(), target);
  }

  // .esurfing.proto.perc.Vector3f position = 5;
  if (this->_internal_has_position()) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(5, _Internal::position(this),
        _Internal::position(this).GetCachedSize(), target, stream);
  }

  // .esurfing.proto.perc.Vector3f shape = 6;
  if (this->_internal_has_shape()) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(6, _Internal::shape(this),
        _Internal::shape(this).GetCachedSize(), target, stream);
  }

  // .esurfing.proto.perc.Polygon hull = 7;
  if (this->_internal_has_hull()) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(7, _Internal::hull(this),
        _Internal::hull(this).GetCachedSize(), target, stream);
  }

  // float orientation = 8;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_orientation = this->_internal_orientation();
  uint32_t raw_orientation;
  memcpy(&raw_orientation, &tmp_orientation, sizeof(tmp_orientation));
  if (raw_orientation != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteFloatToArray(8, this->_internal_orientation(), target);
  }

  // .esurfing.proto.perc.Velocity velocity = 9;
  if (this->_internal_has_velocity()) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(9, _Internal::velocity(this),
        _Internal::velocity(this).GetCachedSize(), target, stream);
  }

  // bool is_static = 10;
  if (this->_internal_is_static() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteBoolToArray(10, this->_internal_is_static(), target);
  }

  // .esurfing.proto.perc.Color color = 11;
  if (this->_internal_has_color()) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(11, _Internal::color(this),
        _Internal::color(this).GetCachedSize(), target, stream);
  }

  // repeated float feature = 12;
  if (this->_internal_feature_size() > 0) {
    target = stream->WriteFixedPacked(12, _internal_feature(), target);
  }

  // repeated .esurfing.proto.perc.WayPoints trajectories = 13;
  for (unsigned i = 0,
      n = static_cast<unsigned>(this->_internal_trajectories_size()); i < n; i++) {
    const auto& repfield = this->_internal_trajectories(i);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
        InternalWriteMessage(13, repfield, repfield.GetCachedSize(), target, stream);
  }

  // repeated bytes str_array = 14;
  for (int i = 0, n = this->_internal_str_array_size(); i < n; i++) {
    const auto& s = this->_internal_str_array(i);
    target = stream->WriteBytes(14, s, target);
  }

  // repeated sint32 int_array = 15;
  {
    int byte_size = _impl_._int_array_cached_byte_size_.load(std::memory_order_relaxed);
    if (byte_size > 0) {
      target = stream->WriteSInt32Packed(
          15, _internal_int_array(), byte_size, target);
    }
  }

  // int64 parking_time = 16;
  if (this->_internal_parking_time() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt64ToArray(16, this->_internal_parking_time(), target);
  }

  // .esurfing.proto.perc.LicenePlate plate = 17;
  if (this->_internal_has_plate()) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(17, _Internal::plate(this),
        _Internal::plate(this).GetCachedSize(), target, stream);
  }

  // int32 obj_color = 18;
  if (this->_internal_obj_color() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(18, this->_internal_obj_color(), target);
  }

  // .esurfing.proto.perc.DetectedObject.TrafficEvent event = 19;
  if (this->_internal_event() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteEnumToArray(
      19, this->_internal_event(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:esurfing.proto.perc.DetectedObject)
  return target;
}

size_t DetectedObject::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:esurfing.proto.perc.DetectedObject)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated float feature = 12;
  {
    unsigned int count = static_cast<unsigned int>(this->_internal_feature_size());
    size_t data_size = 4UL * count;
    if (data_size > 0) {
      total_size += 1 +
        ::_pbi::WireFormatLite::Int32Size(static_cast<int32_t>(data_size));
    }
    total_size += data_size;
  }

  // repeated .esurfing.proto.perc.WayPoints trajectories = 13;
  total_size += 1UL * this->_internal_trajectories_size();
  for (const auto& msg : this->_impl_.trajectories_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // repeated bytes str_array = 14;
  total_size += 1 *
      ::PROTOBUF_NAMESPACE_ID::internal::FromIntSize(_impl_.str_array_.size());
  for (int i = 0, n = _impl_.str_array_.size(); i < n; i++) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::BytesSize(
      _impl_.str_array_.Get(i));
  }

  // repeated sint32 int_array = 15;
  {
    size_t data_size = ::_pbi::WireFormatLite::
      SInt32Size(this->_impl_.int_array_);
    if (data_size > 0) {
      total_size += 1 +
        ::_pbi::WireFormatLite::Int32Size(static_cast<int32_t>(data_size));
    }
    int cached_size = ::_pbi::ToCachedSize(data_size);
    _impl_._int_array_cached_byte_size_.store(cached_size,
                                    std::memory_order_relaxed);
    total_size += data_size;
  }

  // bytes overlap_name = 1;
  if (!this->_internal_overlap_name().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::BytesSize(
        this->_internal_overlap_name());
  }

  // .esurfing.proto.perc.Vector3f position = 5;
  if (this->_internal_has_position()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *_impl_.position_);
  }

  // .esurfing.proto.perc.Vector3f shape = 6;
  if (this->_internal_has_shape()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *_impl_.shape_);
  }

  // .esurfing.proto.perc.Polygon hull = 7;
  if (this->_internal_has_hull()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *_impl_.hull_);
  }

  // .esurfing.proto.perc.Velocity velocity = 9;
  if (this->_internal_has_velocity()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *_impl_.velocity_);
  }

  // .esurfing.proto.perc.Color color = 11;
  if (this->_internal_has_color()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *_impl_.color_);
  }

  // .esurfing.proto.perc.LicenePlate plate = 17;
  if (this->_internal_has_plate()) {
    total_size += 2 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *_impl_.plate_);
  }

  // fixed64 uuid = 2;
  if (this->_internal_uuid() != 0) {
    total_size += 1 + 8;
  }

  // sint32 type = 3;
  if (this->_internal_type() != 0) {
    total_size += ::_pbi::WireFormatLite::SInt32SizePlusOne(this->_internal_type());
  }

  // float confidence = 4;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_confidence = this->_internal_confidence();
  uint32_t raw_confidence;
  memcpy(&raw_confidence, &tmp_confidence, sizeof(tmp_confidence));
  if (raw_confidence != 0) {
    total_size += 1 + 4;
  }

  // float orientation = 8;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_orientation = this->_internal_orientation();
  uint32_t raw_orientation;
  memcpy(&raw_orientation, &tmp_orientation, sizeof(tmp_orientation));
  if (raw_orientation != 0) {
    total_size += 1 + 4;
  }

  // bool is_static = 10;
  if (this->_internal_is_static() != 0) {
    total_size += 1 + 1;
  }

  // int64 parking_time = 16;
  if (this->_internal_parking_time() != 0) {
    total_size += 2 +
      ::_pbi::WireFormatLite::Int64Size(
        this->_internal_parking_time());
  }

  // int32 obj_color = 18;
  if (this->_internal_obj_color() != 0) {
    total_size += 2 +
      ::_pbi::WireFormatLite::Int32Size(
        this->_internal_obj_color());
  }

  // .esurfing.proto.perc.DetectedObject.TrafficEvent event = 19;
  if (this->_internal_event() != 0) {
    total_size += 2 +
      ::_pbi::WireFormatLite::EnumSize(this->_internal_event());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_impl_._cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData DetectedObject::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSourceCheck,
    DetectedObject::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*DetectedObject::GetClassData() const { return &_class_data_; }


void DetectedObject::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg) {
  auto* const _this = static_cast<DetectedObject*>(&to_msg);
  auto& from = static_cast<const DetectedObject&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:esurfing.proto.perc.DetectedObject)
  GOOGLE_DCHECK_NE(&from, _this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  _this->_impl_.feature_.MergeFrom(from._impl_.feature_);
  _this->_impl_.trajectories_.MergeFrom(from._impl_.trajectories_);
  _this->_impl_.str_array_.MergeFrom(from._impl_.str_array_);
  _this->_impl_.int_array_.MergeFrom(from._impl_.int_array_);
  if (!from._internal_overlap_name().empty()) {
    _this->_internal_set_overlap_name(from._internal_overlap_name());
  }
  if (from._internal_has_position()) {
    _this->_internal_mutable_position()->::esurfing::proto::perc::Vector3f::MergeFrom(
        from._internal_position());
  }
  if (from._internal_has_shape()) {
    _this->_internal_mutable_shape()->::esurfing::proto::perc::Vector3f::MergeFrom(
        from._internal_shape());
  }
  if (from._internal_has_hull()) {
    _this->_internal_mutable_hull()->::esurfing::proto::perc::Polygon::MergeFrom(
        from._internal_hull());
  }
  if (from._internal_has_velocity()) {
    _this->_internal_mutable_velocity()->::esurfing::proto::perc::Velocity::MergeFrom(
        from._internal_velocity());
  }
  if (from._internal_has_color()) {
    _this->_internal_mutable_color()->::esurfing::proto::perc::Color::MergeFrom(
        from._internal_color());
  }
  if (from._internal_has_plate()) {
    _this->_internal_mutable_plate()->::esurfing::proto::perc::LicenePlate::MergeFrom(
        from._internal_plate());
  }
  if (from._internal_uuid() != 0) {
    _this->_internal_set_uuid(from._internal_uuid());
  }
  if (from._internal_type() != 0) {
    _this->_internal_set_type(from._internal_type());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_confidence = from._internal_confidence();
  uint32_t raw_confidence;
  memcpy(&raw_confidence, &tmp_confidence, sizeof(tmp_confidence));
  if (raw_confidence != 0) {
    _this->_internal_set_confidence(from._internal_confidence());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_orientation = from._internal_orientation();
  uint32_t raw_orientation;
  memcpy(&raw_orientation, &tmp_orientation, sizeof(tmp_orientation));
  if (raw_orientation != 0) {
    _this->_internal_set_orientation(from._internal_orientation());
  }
  if (from._internal_is_static() != 0) {
    _this->_internal_set_is_static(from._internal_is_static());
  }
  if (from._internal_parking_time() != 0) {
    _this->_internal_set_parking_time(from._internal_parking_time());
  }
  if (from._internal_obj_color() != 0) {
    _this->_internal_set_obj_color(from._internal_obj_color());
  }
  if (from._internal_event() != 0) {
    _this->_internal_set_event(from._internal_event());
  }
  _this->_internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void DetectedObject::CopyFrom(const DetectedObject& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:esurfing.proto.perc.DetectedObject)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool DetectedObject::IsInitialized() const {
  return true;
}

void DetectedObject::InternalSwap(DetectedObject* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  _impl_.feature_.InternalSwap(&other->_impl_.feature_);
  _impl_.trajectories_.InternalSwap(&other->_impl_.trajectories_);
  _impl_.str_array_.InternalSwap(&other->_impl_.str_array_);
  _impl_.int_array_.InternalSwap(&other->_impl_.int_array_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &_impl_.overlap_name_, lhs_arena,
      &other->_impl_.overlap_name_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(DetectedObject, _impl_.event_)
      + sizeof(DetectedObject::_impl_.event_)
      - PROTOBUF_FIELD_OFFSET(DetectedObject, _impl_.position_)>(
          reinterpret_cast<char*>(&_impl_.position_),
          reinterpret_cast<char*>(&other->_impl_.position_));
}

::PROTOBUF_NAMESPACE_ID::Metadata DetectedObject::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_esurfing_5fgroup_5fdetected_2eproto_getter, &descriptor_table_esurfing_5fgroup_5fdetected_2eproto_once,
      file_level_metadata_esurfing_5fgroup_5fdetected_2eproto[2]);
}

// ===================================================================

class Vector3f::_Internal {
 public:
};

Vector3f::Vector3f(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor(arena, is_message_owned);
  // @@protoc_insertion_point(arena_constructor:esurfing.proto.perc.Vector3f)
}
Vector3f::Vector3f(const Vector3f& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  Vector3f* const _this = this; (void)_this;
  new (&_impl_) Impl_{
      decltype(_impl_.x_){}
    , decltype(_impl_.y_){}
    , decltype(_impl_.z_){}
    , /*decltype(_impl_._cached_size_)*/{}};

  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&_impl_.x_, &from._impl_.x_,
    static_cast<size_t>(reinterpret_cast<char*>(&_impl_.z_) -
    reinterpret_cast<char*>(&_impl_.x_)) + sizeof(_impl_.z_));
  // @@protoc_insertion_point(copy_constructor:esurfing.proto.perc.Vector3f)
}

inline void Vector3f::SharedCtor(
    ::_pb::Arena* arena, bool is_message_owned) {
  (void)arena;
  (void)is_message_owned;
  new (&_impl_) Impl_{
      decltype(_impl_.x_){0}
    , decltype(_impl_.y_){0}
    , decltype(_impl_.z_){0}
    , /*decltype(_impl_._cached_size_)*/{}
  };
}

Vector3f::~Vector3f() {
  // @@protoc_insertion_point(destructor:esurfing.proto.perc.Vector3f)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void Vector3f::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void Vector3f::SetCachedSize(int size) const {
  _impl_._cached_size_.Set(size);
}

void Vector3f::Clear() {
// @@protoc_insertion_point(message_clear_start:esurfing.proto.perc.Vector3f)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&_impl_.x_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&_impl_.z_) -
      reinterpret_cast<char*>(&_impl_.x_)) + sizeof(_impl_.z_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* Vector3f::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // float x = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 13)) {
          _impl_.x_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // float y = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 21)) {
          _impl_.y_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // float z = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 29)) {
          _impl_.z_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* Vector3f::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:esurfing.proto.perc.Vector3f)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // float x = 1;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_x = this->_internal_x();
  uint32_t raw_x;
  memcpy(&raw_x, &tmp_x, sizeof(tmp_x));
  if (raw_x != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteFloatToArray(1, this->_internal_x(), target);
  }

  // float y = 2;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_y = this->_internal_y();
  uint32_t raw_y;
  memcpy(&raw_y, &tmp_y, sizeof(tmp_y));
  if (raw_y != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteFloatToArray(2, this->_internal_y(), target);
  }

  // float z = 3;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_z = this->_internal_z();
  uint32_t raw_z;
  memcpy(&raw_z, &tmp_z, sizeof(tmp_z));
  if (raw_z != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteFloatToArray(3, this->_internal_z(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:esurfing.proto.perc.Vector3f)
  return target;
}

size_t Vector3f::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:esurfing.proto.perc.Vector3f)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // float x = 1;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_x = this->_internal_x();
  uint32_t raw_x;
  memcpy(&raw_x, &tmp_x, sizeof(tmp_x));
  if (raw_x != 0) {
    total_size += 1 + 4;
  }

  // float y = 2;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_y = this->_internal_y();
  uint32_t raw_y;
  memcpy(&raw_y, &tmp_y, sizeof(tmp_y));
  if (raw_y != 0) {
    total_size += 1 + 4;
  }

  // float z = 3;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_z = this->_internal_z();
  uint32_t raw_z;
  memcpy(&raw_z, &tmp_z, sizeof(tmp_z));
  if (raw_z != 0) {
    total_size += 1 + 4;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_impl_._cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData Vector3f::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSourceCheck,
    Vector3f::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*Vector3f::GetClassData() const { return &_class_data_; }


void Vector3f::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg) {
  auto* const _this = static_cast<Vector3f*>(&to_msg);
  auto& from = static_cast<const Vector3f&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:esurfing.proto.perc.Vector3f)
  GOOGLE_DCHECK_NE(&from, _this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_x = from._internal_x();
  uint32_t raw_x;
  memcpy(&raw_x, &tmp_x, sizeof(tmp_x));
  if (raw_x != 0) {
    _this->_internal_set_x(from._internal_x());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_y = from._internal_y();
  uint32_t raw_y;
  memcpy(&raw_y, &tmp_y, sizeof(tmp_y));
  if (raw_y != 0) {
    _this->_internal_set_y(from._internal_y());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_z = from._internal_z();
  uint32_t raw_z;
  memcpy(&raw_z, &tmp_z, sizeof(tmp_z));
  if (raw_z != 0) {
    _this->_internal_set_z(from._internal_z());
  }
  _this->_internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void Vector3f::CopyFrom(const Vector3f& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:esurfing.proto.perc.Vector3f)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Vector3f::IsInitialized() const {
  return true;
}

void Vector3f::InternalSwap(Vector3f* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(Vector3f, _impl_.z_)
      + sizeof(Vector3f::_impl_.z_)
      - PROTOBUF_FIELD_OFFSET(Vector3f, _impl_.x_)>(
          reinterpret_cast<char*>(&_impl_.x_),
          reinterpret_cast<char*>(&other->_impl_.x_));
}

::PROTOBUF_NAMESPACE_ID::Metadata Vector3f::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_esurfing_5fgroup_5fdetected_2eproto_getter, &descriptor_table_esurfing_5fgroup_5fdetected_2eproto_once,
      file_level_metadata_esurfing_5fgroup_5fdetected_2eproto[3]);
}

// ===================================================================

class Vector2f::_Internal {
 public:
};

Vector2f::Vector2f(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor(arena, is_message_owned);
  // @@protoc_insertion_point(arena_constructor:esurfing.proto.perc.Vector2f)
}
Vector2f::Vector2f(const Vector2f& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  Vector2f* const _this = this; (void)_this;
  new (&_impl_) Impl_{
      decltype(_impl_.x_){}
    , decltype(_impl_.y_){}
    , /*decltype(_impl_._cached_size_)*/{}};

  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&_impl_.x_, &from._impl_.x_,
    static_cast<size_t>(reinterpret_cast<char*>(&_impl_.y_) -
    reinterpret_cast<char*>(&_impl_.x_)) + sizeof(_impl_.y_));
  // @@protoc_insertion_point(copy_constructor:esurfing.proto.perc.Vector2f)
}

inline void Vector2f::SharedCtor(
    ::_pb::Arena* arena, bool is_message_owned) {
  (void)arena;
  (void)is_message_owned;
  new (&_impl_) Impl_{
      decltype(_impl_.x_){0}
    , decltype(_impl_.y_){0}
    , /*decltype(_impl_._cached_size_)*/{}
  };
}

Vector2f::~Vector2f() {
  // @@protoc_insertion_point(destructor:esurfing.proto.perc.Vector2f)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void Vector2f::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void Vector2f::SetCachedSize(int size) const {
  _impl_._cached_size_.Set(size);
}

void Vector2f::Clear() {
// @@protoc_insertion_point(message_clear_start:esurfing.proto.perc.Vector2f)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&_impl_.x_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&_impl_.y_) -
      reinterpret_cast<char*>(&_impl_.x_)) + sizeof(_impl_.y_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* Vector2f::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // float x = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 13)) {
          _impl_.x_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // float y = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 21)) {
          _impl_.y_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* Vector2f::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:esurfing.proto.perc.Vector2f)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // float x = 1;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_x = this->_internal_x();
  uint32_t raw_x;
  memcpy(&raw_x, &tmp_x, sizeof(tmp_x));
  if (raw_x != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteFloatToArray(1, this->_internal_x(), target);
  }

  // float y = 2;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_y = this->_internal_y();
  uint32_t raw_y;
  memcpy(&raw_y, &tmp_y, sizeof(tmp_y));
  if (raw_y != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteFloatToArray(2, this->_internal_y(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:esurfing.proto.perc.Vector2f)
  return target;
}

size_t Vector2f::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:esurfing.proto.perc.Vector2f)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // float x = 1;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_x = this->_internal_x();
  uint32_t raw_x;
  memcpy(&raw_x, &tmp_x, sizeof(tmp_x));
  if (raw_x != 0) {
    total_size += 1 + 4;
  }

  // float y = 2;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_y = this->_internal_y();
  uint32_t raw_y;
  memcpy(&raw_y, &tmp_y, sizeof(tmp_y));
  if (raw_y != 0) {
    total_size += 1 + 4;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_impl_._cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData Vector2f::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSourceCheck,
    Vector2f::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*Vector2f::GetClassData() const { return &_class_data_; }


void Vector2f::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg) {
  auto* const _this = static_cast<Vector2f*>(&to_msg);
  auto& from = static_cast<const Vector2f&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:esurfing.proto.perc.Vector2f)
  GOOGLE_DCHECK_NE(&from, _this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_x = from._internal_x();
  uint32_t raw_x;
  memcpy(&raw_x, &tmp_x, sizeof(tmp_x));
  if (raw_x != 0) {
    _this->_internal_set_x(from._internal_x());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_y = from._internal_y();
  uint32_t raw_y;
  memcpy(&raw_y, &tmp_y, sizeof(tmp_y));
  if (raw_y != 0) {
    _this->_internal_set_y(from._internal_y());
  }
  _this->_internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void Vector2f::CopyFrom(const Vector2f& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:esurfing.proto.perc.Vector2f)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Vector2f::IsInitialized() const {
  return true;
}

void Vector2f::InternalSwap(Vector2f* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(Vector2f, _impl_.y_)
      + sizeof(Vector2f::_impl_.y_)
      - PROTOBUF_FIELD_OFFSET(Vector2f, _impl_.x_)>(
          reinterpret_cast<char*>(&_impl_.x_),
          reinterpret_cast<char*>(&other->_impl_.x_));
}

::PROTOBUF_NAMESPACE_ID::Metadata Vector2f::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_esurfing_5fgroup_5fdetected_2eproto_getter, &descriptor_table_esurfing_5fgroup_5fdetected_2eproto_once,
      file_level_metadata_esurfing_5fgroup_5fdetected_2eproto[4]);
}

// ===================================================================

class Polygon::_Internal {
 public:
};

Polygon::Polygon(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor(arena, is_message_owned);
  // @@protoc_insertion_point(arena_constructor:esurfing.proto.perc.Polygon)
}
Polygon::Polygon(const Polygon& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  Polygon* const _this = this; (void)_this;
  new (&_impl_) Impl_{
      decltype(_impl_.points_){from._impl_.points_}
    , /*decltype(_impl_._cached_size_)*/{}};

  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:esurfing.proto.perc.Polygon)
}

inline void Polygon::SharedCtor(
    ::_pb::Arena* arena, bool is_message_owned) {
  (void)arena;
  (void)is_message_owned;
  new (&_impl_) Impl_{
      decltype(_impl_.points_){arena}
    , /*decltype(_impl_._cached_size_)*/{}
  };
}

Polygon::~Polygon() {
  // @@protoc_insertion_point(destructor:esurfing.proto.perc.Polygon)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void Polygon::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  _impl_.points_.~RepeatedPtrField();
}

void Polygon::SetCachedSize(int size) const {
  _impl_._cached_size_.Set(size);
}

void Polygon::Clear() {
// @@protoc_insertion_point(message_clear_start:esurfing.proto.perc.Polygon)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _impl_.points_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* Polygon::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // repeated .esurfing.proto.perc.Vector2f points = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_points(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<10>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* Polygon::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:esurfing.proto.perc.Polygon)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .esurfing.proto.perc.Vector2f points = 1;
  for (unsigned i = 0,
      n = static_cast<unsigned>(this->_internal_points_size()); i < n; i++) {
    const auto& repfield = this->_internal_points(i);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
        InternalWriteMessage(1, repfield, repfield.GetCachedSize(), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:esurfing.proto.perc.Polygon)
  return target;
}

size_t Polygon::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:esurfing.proto.perc.Polygon)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .esurfing.proto.perc.Vector2f points = 1;
  total_size += 1UL * this->_internal_points_size();
  for (const auto& msg : this->_impl_.points_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_impl_._cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData Polygon::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSourceCheck,
    Polygon::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*Polygon::GetClassData() const { return &_class_data_; }


void Polygon::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg) {
  auto* const _this = static_cast<Polygon*>(&to_msg);
  auto& from = static_cast<const Polygon&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:esurfing.proto.perc.Polygon)
  GOOGLE_DCHECK_NE(&from, _this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  _this->_impl_.points_.MergeFrom(from._impl_.points_);
  _this->_internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void Polygon::CopyFrom(const Polygon& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:esurfing.proto.perc.Polygon)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Polygon::IsInitialized() const {
  return true;
}

void Polygon::InternalSwap(Polygon* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  _impl_.points_.InternalSwap(&other->_impl_.points_);
}

::PROTOBUF_NAMESPACE_ID::Metadata Polygon::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_esurfing_5fgroup_5fdetected_2eproto_getter, &descriptor_table_esurfing_5fgroup_5fdetected_2eproto_once,
      file_level_metadata_esurfing_5fgroup_5fdetected_2eproto[5]);
}

// ===================================================================

class Velocity::_Internal {
 public:
};

Velocity::Velocity(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor(arena, is_message_owned);
  // @@protoc_insertion_point(arena_constructor:esurfing.proto.perc.Velocity)
}
Velocity::Velocity(const Velocity& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  Velocity* const _this = this; (void)_this;
  new (&_impl_) Impl_{
      decltype(_impl_.heading_){}
    , decltype(_impl_.speed_){}
    , decltype(_impl_.acceleration_){}
    , /*decltype(_impl_._cached_size_)*/{}};

  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&_impl_.heading_, &from._impl_.heading_,
    static_cast<size_t>(reinterpret_cast<char*>(&_impl_.acceleration_) -
    reinterpret_cast<char*>(&_impl_.heading_)) + sizeof(_impl_.acceleration_));
  // @@protoc_insertion_point(copy_constructor:esurfing.proto.perc.Velocity)
}

inline void Velocity::SharedCtor(
    ::_pb::Arena* arena, bool is_message_owned) {
  (void)arena;
  (void)is_message_owned;
  new (&_impl_) Impl_{
      decltype(_impl_.heading_){0}
    , decltype(_impl_.speed_){0}
    , decltype(_impl_.acceleration_){0}
    , /*decltype(_impl_._cached_size_)*/{}
  };
}

Velocity::~Velocity() {
  // @@protoc_insertion_point(destructor:esurfing.proto.perc.Velocity)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void Velocity::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void Velocity::SetCachedSize(int size) const {
  _impl_._cached_size_.Set(size);
}

void Velocity::Clear() {
// @@protoc_insertion_point(message_clear_start:esurfing.proto.perc.Velocity)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&_impl_.heading_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&_impl_.acceleration_) -
      reinterpret_cast<char*>(&_impl_.heading_)) + sizeof(_impl_.acceleration_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* Velocity::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // float heading = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 13)) {
          _impl_.heading_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // float speed = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 21)) {
          _impl_.speed_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // float acceleration = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 29)) {
          _impl_.acceleration_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* Velocity::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:esurfing.proto.perc.Velocity)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // float heading = 1;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_heading = this->_internal_heading();
  uint32_t raw_heading;
  memcpy(&raw_heading, &tmp_heading, sizeof(tmp_heading));
  if (raw_heading != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteFloatToArray(1, this->_internal_heading(), target);
  }

  // float speed = 2;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_speed = this->_internal_speed();
  uint32_t raw_speed;
  memcpy(&raw_speed, &tmp_speed, sizeof(tmp_speed));
  if (raw_speed != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteFloatToArray(2, this->_internal_speed(), target);
  }

  // float acceleration = 3;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_acceleration = this->_internal_acceleration();
  uint32_t raw_acceleration;
  memcpy(&raw_acceleration, &tmp_acceleration, sizeof(tmp_acceleration));
  if (raw_acceleration != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteFloatToArray(3, this->_internal_acceleration(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:esurfing.proto.perc.Velocity)
  return target;
}

size_t Velocity::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:esurfing.proto.perc.Velocity)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // float heading = 1;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_heading = this->_internal_heading();
  uint32_t raw_heading;
  memcpy(&raw_heading, &tmp_heading, sizeof(tmp_heading));
  if (raw_heading != 0) {
    total_size += 1 + 4;
  }

  // float speed = 2;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_speed = this->_internal_speed();
  uint32_t raw_speed;
  memcpy(&raw_speed, &tmp_speed, sizeof(tmp_speed));
  if (raw_speed != 0) {
    total_size += 1 + 4;
  }

  // float acceleration = 3;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_acceleration = this->_internal_acceleration();
  uint32_t raw_acceleration;
  memcpy(&raw_acceleration, &tmp_acceleration, sizeof(tmp_acceleration));
  if (raw_acceleration != 0) {
    total_size += 1 + 4;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_impl_._cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData Velocity::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSourceCheck,
    Velocity::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*Velocity::GetClassData() const { return &_class_data_; }


void Velocity::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg) {
  auto* const _this = static_cast<Velocity*>(&to_msg);
  auto& from = static_cast<const Velocity&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:esurfing.proto.perc.Velocity)
  GOOGLE_DCHECK_NE(&from, _this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_heading = from._internal_heading();
  uint32_t raw_heading;
  memcpy(&raw_heading, &tmp_heading, sizeof(tmp_heading));
  if (raw_heading != 0) {
    _this->_internal_set_heading(from._internal_heading());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_speed = from._internal_speed();
  uint32_t raw_speed;
  memcpy(&raw_speed, &tmp_speed, sizeof(tmp_speed));
  if (raw_speed != 0) {
    _this->_internal_set_speed(from._internal_speed());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_acceleration = from._internal_acceleration();
  uint32_t raw_acceleration;
  memcpy(&raw_acceleration, &tmp_acceleration, sizeof(tmp_acceleration));
  if (raw_acceleration != 0) {
    _this->_internal_set_acceleration(from._internal_acceleration());
  }
  _this->_internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void Velocity::CopyFrom(const Velocity& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:esurfing.proto.perc.Velocity)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Velocity::IsInitialized() const {
  return true;
}

void Velocity::InternalSwap(Velocity* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(Velocity, _impl_.acceleration_)
      + sizeof(Velocity::_impl_.acceleration_)
      - PROTOBUF_FIELD_OFFSET(Velocity, _impl_.heading_)>(
          reinterpret_cast<char*>(&_impl_.heading_),
          reinterpret_cast<char*>(&other->_impl_.heading_));
}

::PROTOBUF_NAMESPACE_ID::Metadata Velocity::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_esurfing_5fgroup_5fdetected_2eproto_getter, &descriptor_table_esurfing_5fgroup_5fdetected_2eproto_once,
      file_level_metadata_esurfing_5fgroup_5fdetected_2eproto[6]);
}

// ===================================================================

class Color::_Internal {
 public:
};

Color::Color(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor(arena, is_message_owned);
  // @@protoc_insertion_point(arena_constructor:esurfing.proto.perc.Color)
}
Color::Color(const Color& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  Color* const _this = this; (void)_this;
  new (&_impl_) Impl_{
      decltype(_impl_.r_){}
    , decltype(_impl_.g_){}
    , decltype(_impl_.b_){}
    , decltype(_impl_.a_){}
    , /*decltype(_impl_._cached_size_)*/{}};

  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&_impl_.r_, &from._impl_.r_,
    static_cast<size_t>(reinterpret_cast<char*>(&_impl_.a_) -
    reinterpret_cast<char*>(&_impl_.r_)) + sizeof(_impl_.a_));
  // @@protoc_insertion_point(copy_constructor:esurfing.proto.perc.Color)
}

inline void Color::SharedCtor(
    ::_pb::Arena* arena, bool is_message_owned) {
  (void)arena;
  (void)is_message_owned;
  new (&_impl_) Impl_{
      decltype(_impl_.r_){0}
    , decltype(_impl_.g_){0}
    , decltype(_impl_.b_){0}
    , decltype(_impl_.a_){0}
    , /*decltype(_impl_._cached_size_)*/{}
  };
}

Color::~Color() {
  // @@protoc_insertion_point(destructor:esurfing.proto.perc.Color)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void Color::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void Color::SetCachedSize(int size) const {
  _impl_._cached_size_.Set(size);
}

void Color::Clear() {
// @@protoc_insertion_point(message_clear_start:esurfing.proto.perc.Color)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&_impl_.r_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&_impl_.a_) -
      reinterpret_cast<char*>(&_impl_.r_)) + sizeof(_impl_.a_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* Color::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // int32 r = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          _impl_.r_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 g = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          _impl_.g_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 b = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 24)) {
          _impl_.b_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // float a = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 37)) {
          _impl_.a_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* Color::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:esurfing.proto.perc.Color)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // int32 r = 1;
  if (this->_internal_r() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(1, this->_internal_r(), target);
  }

  // int32 g = 2;
  if (this->_internal_g() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(2, this->_internal_g(), target);
  }

  // int32 b = 3;
  if (this->_internal_b() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(3, this->_internal_b(), target);
  }

  // float a = 4;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_a = this->_internal_a();
  uint32_t raw_a;
  memcpy(&raw_a, &tmp_a, sizeof(tmp_a));
  if (raw_a != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteFloatToArray(4, this->_internal_a(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:esurfing.proto.perc.Color)
  return target;
}

size_t Color::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:esurfing.proto.perc.Color)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // int32 r = 1;
  if (this->_internal_r() != 0) {
    total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(this->_internal_r());
  }

  // int32 g = 2;
  if (this->_internal_g() != 0) {
    total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(this->_internal_g());
  }

  // int32 b = 3;
  if (this->_internal_b() != 0) {
    total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(this->_internal_b());
  }

  // float a = 4;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_a = this->_internal_a();
  uint32_t raw_a;
  memcpy(&raw_a, &tmp_a, sizeof(tmp_a));
  if (raw_a != 0) {
    total_size += 1 + 4;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_impl_._cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData Color::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSourceCheck,
    Color::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*Color::GetClassData() const { return &_class_data_; }


void Color::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg) {
  auto* const _this = static_cast<Color*>(&to_msg);
  auto& from = static_cast<const Color&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:esurfing.proto.perc.Color)
  GOOGLE_DCHECK_NE(&from, _this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_r() != 0) {
    _this->_internal_set_r(from._internal_r());
  }
  if (from._internal_g() != 0) {
    _this->_internal_set_g(from._internal_g());
  }
  if (from._internal_b() != 0) {
    _this->_internal_set_b(from._internal_b());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_a = from._internal_a();
  uint32_t raw_a;
  memcpy(&raw_a, &tmp_a, sizeof(tmp_a));
  if (raw_a != 0) {
    _this->_internal_set_a(from._internal_a());
  }
  _this->_internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void Color::CopyFrom(const Color& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:esurfing.proto.perc.Color)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Color::IsInitialized() const {
  return true;
}

void Color::InternalSwap(Color* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(Color, _impl_.a_)
      + sizeof(Color::_impl_.a_)
      - PROTOBUF_FIELD_OFFSET(Color, _impl_.r_)>(
          reinterpret_cast<char*>(&_impl_.r_),
          reinterpret_cast<char*>(&other->_impl_.r_));
}

::PROTOBUF_NAMESPACE_ID::Metadata Color::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_esurfing_5fgroup_5fdetected_2eproto_getter, &descriptor_table_esurfing_5fgroup_5fdetected_2eproto_once,
      file_level_metadata_esurfing_5fgroup_5fdetected_2eproto[7]);
}

// ===================================================================

class WayPoints::_Internal {
 public:
};

WayPoints::WayPoints(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor(arena, is_message_owned);
  // @@protoc_insertion_point(arena_constructor:esurfing.proto.perc.WayPoints)
}
WayPoints::WayPoints(const WayPoints& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  WayPoints* const _this = this; (void)_this;
  new (&_impl_) Impl_{
      decltype(_impl_.waypoints_){from._impl_.waypoints_}
    , decltype(_impl_.probability_){}
    , /*decltype(_impl_._cached_size_)*/{}};

  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  _this->_impl_.probability_ = from._impl_.probability_;
  // @@protoc_insertion_point(copy_constructor:esurfing.proto.perc.WayPoints)
}

inline void WayPoints::SharedCtor(
    ::_pb::Arena* arena, bool is_message_owned) {
  (void)arena;
  (void)is_message_owned;
  new (&_impl_) Impl_{
      decltype(_impl_.waypoints_){arena}
    , decltype(_impl_.probability_){0}
    , /*decltype(_impl_._cached_size_)*/{}
  };
}

WayPoints::~WayPoints() {
  // @@protoc_insertion_point(destructor:esurfing.proto.perc.WayPoints)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void WayPoints::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  _impl_.waypoints_.~RepeatedPtrField();
}

void WayPoints::SetCachedSize(int size) const {
  _impl_._cached_size_.Set(size);
}

void WayPoints::Clear() {
// @@protoc_insertion_point(message_clear_start:esurfing.proto.perc.WayPoints)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _impl_.waypoints_.Clear();
  _impl_.probability_ = 0;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* WayPoints::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // repeated .esurfing.proto.perc.WayPoint waypoints = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_waypoints(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<10>(ptr));
        } else
          goto handle_unusual;
        continue;
      // float probability = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 21)) {
          _impl_.probability_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* WayPoints::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:esurfing.proto.perc.WayPoints)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .esurfing.proto.perc.WayPoint waypoints = 1;
  for (unsigned i = 0,
      n = static_cast<unsigned>(this->_internal_waypoints_size()); i < n; i++) {
    const auto& repfield = this->_internal_waypoints(i);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
        InternalWriteMessage(1, repfield, repfield.GetCachedSize(), target, stream);
  }

  // float probability = 2;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_probability = this->_internal_probability();
  uint32_t raw_probability;
  memcpy(&raw_probability, &tmp_probability, sizeof(tmp_probability));
  if (raw_probability != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteFloatToArray(2, this->_internal_probability(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:esurfing.proto.perc.WayPoints)
  return target;
}

size_t WayPoints::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:esurfing.proto.perc.WayPoints)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .esurfing.proto.perc.WayPoint waypoints = 1;
  total_size += 1UL * this->_internal_waypoints_size();
  for (const auto& msg : this->_impl_.waypoints_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // float probability = 2;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_probability = this->_internal_probability();
  uint32_t raw_probability;
  memcpy(&raw_probability, &tmp_probability, sizeof(tmp_probability));
  if (raw_probability != 0) {
    total_size += 1 + 4;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_impl_._cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData WayPoints::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSourceCheck,
    WayPoints::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*WayPoints::GetClassData() const { return &_class_data_; }


void WayPoints::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg) {
  auto* const _this = static_cast<WayPoints*>(&to_msg);
  auto& from = static_cast<const WayPoints&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:esurfing.proto.perc.WayPoints)
  GOOGLE_DCHECK_NE(&from, _this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  _this->_impl_.waypoints_.MergeFrom(from._impl_.waypoints_);
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_probability = from._internal_probability();
  uint32_t raw_probability;
  memcpy(&raw_probability, &tmp_probability, sizeof(tmp_probability));
  if (raw_probability != 0) {
    _this->_internal_set_probability(from._internal_probability());
  }
  _this->_internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void WayPoints::CopyFrom(const WayPoints& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:esurfing.proto.perc.WayPoints)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool WayPoints::IsInitialized() const {
  return true;
}

void WayPoints::InternalSwap(WayPoints* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  _impl_.waypoints_.InternalSwap(&other->_impl_.waypoints_);
  swap(_impl_.probability_, other->_impl_.probability_);
}

::PROTOBUF_NAMESPACE_ID::Metadata WayPoints::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_esurfing_5fgroup_5fdetected_2eproto_getter, &descriptor_table_esurfing_5fgroup_5fdetected_2eproto_once,
      file_level_metadata_esurfing_5fgroup_5fdetected_2eproto[8]);
}

// ===================================================================

class WayPoint::_Internal {
 public:
  static const ::esurfing::proto::perc::Vector3f& position(const WayPoint* msg);
  static const ::esurfing::proto::perc::Velocity& velocity(const WayPoint* msg);
};

const ::esurfing::proto::perc::Vector3f&
WayPoint::_Internal::position(const WayPoint* msg) {
  return *msg->_impl_.position_;
}
const ::esurfing::proto::perc::Velocity&
WayPoint::_Internal::velocity(const WayPoint* msg) {
  return *msg->_impl_.velocity_;
}
WayPoint::WayPoint(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor(arena, is_message_owned);
  // @@protoc_insertion_point(arena_constructor:esurfing.proto.perc.WayPoint)
}
WayPoint::WayPoint(const WayPoint& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  WayPoint* const _this = this; (void)_this;
  new (&_impl_) Impl_{
      decltype(_impl_.position_){nullptr}
    , decltype(_impl_.velocity_){nullptr}
    , decltype(_impl_.time_meas_){}
    , /*decltype(_impl_._cached_size_)*/{}};

  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_position()) {
    _this->_impl_.position_ = new ::esurfing::proto::perc::Vector3f(*from._impl_.position_);
  }
  if (from._internal_has_velocity()) {
    _this->_impl_.velocity_ = new ::esurfing::proto::perc::Velocity(*from._impl_.velocity_);
  }
  _this->_impl_.time_meas_ = from._impl_.time_meas_;
  // @@protoc_insertion_point(copy_constructor:esurfing.proto.perc.WayPoint)
}

inline void WayPoint::SharedCtor(
    ::_pb::Arena* arena, bool is_message_owned) {
  (void)arena;
  (void)is_message_owned;
  new (&_impl_) Impl_{
      decltype(_impl_.position_){nullptr}
    , decltype(_impl_.velocity_){nullptr}
    , decltype(_impl_.time_meas_){int64_t{0}}
    , /*decltype(_impl_._cached_size_)*/{}
  };
}

WayPoint::~WayPoint() {
  // @@protoc_insertion_point(destructor:esurfing.proto.perc.WayPoint)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void WayPoint::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete _impl_.position_;
  if (this != internal_default_instance()) delete _impl_.velocity_;
}

void WayPoint::SetCachedSize(int size) const {
  _impl_._cached_size_.Set(size);
}

void WayPoint::Clear() {
// @@protoc_insertion_point(message_clear_start:esurfing.proto.perc.WayPoint)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaForAllocation() == nullptr && _impl_.position_ != nullptr) {
    delete _impl_.position_;
  }
  _impl_.position_ = nullptr;
  if (GetArenaForAllocation() == nullptr && _impl_.velocity_ != nullptr) {
    delete _impl_.velocity_;
  }
  _impl_.velocity_ = nullptr;
  _impl_.time_meas_ = int64_t{0};
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* WayPoint::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // sfixed64 time_meas = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 9)) {
          _impl_.time_meas_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<int64_t>(ptr);
          ptr += sizeof(int64_t);
        } else
          goto handle_unusual;
        continue;
      // .esurfing.proto.perc.Vector3f position = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr = ctx->ParseMessage(_internal_mutable_position(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .esurfing.proto.perc.Velocity velocity = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 26)) {
          ptr = ctx->ParseMessage(_internal_mutable_velocity(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* WayPoint::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:esurfing.proto.perc.WayPoint)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // sfixed64 time_meas = 1;
  if (this->_internal_time_meas() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteSFixed64ToArray(1, this->_internal_time_meas(), target);
  }

  // .esurfing.proto.perc.Vector3f position = 2;
  if (this->_internal_has_position()) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(2, _Internal::position(this),
        _Internal::position(this).GetCachedSize(), target, stream);
  }

  // .esurfing.proto.perc.Velocity velocity = 3;
  if (this->_internal_has_velocity()) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(3, _Internal::velocity(this),
        _Internal::velocity(this).GetCachedSize(), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:esurfing.proto.perc.WayPoint)
  return target;
}

size_t WayPoint::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:esurfing.proto.perc.WayPoint)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .esurfing.proto.perc.Vector3f position = 2;
  if (this->_internal_has_position()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *_impl_.position_);
  }

  // .esurfing.proto.perc.Velocity velocity = 3;
  if (this->_internal_has_velocity()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *_impl_.velocity_);
  }

  // sfixed64 time_meas = 1;
  if (this->_internal_time_meas() != 0) {
    total_size += 1 + 8;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_impl_._cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData WayPoint::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSourceCheck,
    WayPoint::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*WayPoint::GetClassData() const { return &_class_data_; }


void WayPoint::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg) {
  auto* const _this = static_cast<WayPoint*>(&to_msg);
  auto& from = static_cast<const WayPoint&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:esurfing.proto.perc.WayPoint)
  GOOGLE_DCHECK_NE(&from, _this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_has_position()) {
    _this->_internal_mutable_position()->::esurfing::proto::perc::Vector3f::MergeFrom(
        from._internal_position());
  }
  if (from._internal_has_velocity()) {
    _this->_internal_mutable_velocity()->::esurfing::proto::perc::Velocity::MergeFrom(
        from._internal_velocity());
  }
  if (from._internal_time_meas() != 0) {
    _this->_internal_set_time_meas(from._internal_time_meas());
  }
  _this->_internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void WayPoint::CopyFrom(const WayPoint& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:esurfing.proto.perc.WayPoint)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool WayPoint::IsInitialized() const {
  return true;
}

void WayPoint::InternalSwap(WayPoint* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(WayPoint, _impl_.time_meas_)
      + sizeof(WayPoint::_impl_.time_meas_)
      - PROTOBUF_FIELD_OFFSET(WayPoint, _impl_.position_)>(
          reinterpret_cast<char*>(&_impl_.position_),
          reinterpret_cast<char*>(&other->_impl_.position_));
}

::PROTOBUF_NAMESPACE_ID::Metadata WayPoint::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_esurfing_5fgroup_5fdetected_2eproto_getter, &descriptor_table_esurfing_5fgroup_5fdetected_2eproto_once,
      file_level_metadata_esurfing_5fgroup_5fdetected_2eproto[9]);
}

// ===================================================================

class Quaternionf::_Internal {
 public:
};

Quaternionf::Quaternionf(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor(arena, is_message_owned);
  // @@protoc_insertion_point(arena_constructor:esurfing.proto.perc.Quaternionf)
}
Quaternionf::Quaternionf(const Quaternionf& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  Quaternionf* const _this = this; (void)_this;
  new (&_impl_) Impl_{
      decltype(_impl_.x_){}
    , decltype(_impl_.y_){}
    , decltype(_impl_.z_){}
    , decltype(_impl_.w_){}
    , /*decltype(_impl_._cached_size_)*/{}};

  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&_impl_.x_, &from._impl_.x_,
    static_cast<size_t>(reinterpret_cast<char*>(&_impl_.w_) -
    reinterpret_cast<char*>(&_impl_.x_)) + sizeof(_impl_.w_));
  // @@protoc_insertion_point(copy_constructor:esurfing.proto.perc.Quaternionf)
}

inline void Quaternionf::SharedCtor(
    ::_pb::Arena* arena, bool is_message_owned) {
  (void)arena;
  (void)is_message_owned;
  new (&_impl_) Impl_{
      decltype(_impl_.x_){0}
    , decltype(_impl_.y_){0}
    , decltype(_impl_.z_){0}
    , decltype(_impl_.w_){0}
    , /*decltype(_impl_._cached_size_)*/{}
  };
}

Quaternionf::~Quaternionf() {
  // @@protoc_insertion_point(destructor:esurfing.proto.perc.Quaternionf)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void Quaternionf::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void Quaternionf::SetCachedSize(int size) const {
  _impl_._cached_size_.Set(size);
}

void Quaternionf::Clear() {
// @@protoc_insertion_point(message_clear_start:esurfing.proto.perc.Quaternionf)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&_impl_.x_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&_impl_.w_) -
      reinterpret_cast<char*>(&_impl_.x_)) + sizeof(_impl_.w_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* Quaternionf::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // float x = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 13)) {
          _impl_.x_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // float y = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 21)) {
          _impl_.y_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // float z = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 29)) {
          _impl_.z_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // float w = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 37)) {
          _impl_.w_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* Quaternionf::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:esurfing.proto.perc.Quaternionf)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // float x = 1;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_x = this->_internal_x();
  uint32_t raw_x;
  memcpy(&raw_x, &tmp_x, sizeof(tmp_x));
  if (raw_x != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteFloatToArray(1, this->_internal_x(), target);
  }

  // float y = 2;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_y = this->_internal_y();
  uint32_t raw_y;
  memcpy(&raw_y, &tmp_y, sizeof(tmp_y));
  if (raw_y != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteFloatToArray(2, this->_internal_y(), target);
  }

  // float z = 3;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_z = this->_internal_z();
  uint32_t raw_z;
  memcpy(&raw_z, &tmp_z, sizeof(tmp_z));
  if (raw_z != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteFloatToArray(3, this->_internal_z(), target);
  }

  // float w = 4;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_w = this->_internal_w();
  uint32_t raw_w;
  memcpy(&raw_w, &tmp_w, sizeof(tmp_w));
  if (raw_w != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteFloatToArray(4, this->_internal_w(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:esurfing.proto.perc.Quaternionf)
  return target;
}

size_t Quaternionf::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:esurfing.proto.perc.Quaternionf)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // float x = 1;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_x = this->_internal_x();
  uint32_t raw_x;
  memcpy(&raw_x, &tmp_x, sizeof(tmp_x));
  if (raw_x != 0) {
    total_size += 1 + 4;
  }

  // float y = 2;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_y = this->_internal_y();
  uint32_t raw_y;
  memcpy(&raw_y, &tmp_y, sizeof(tmp_y));
  if (raw_y != 0) {
    total_size += 1 + 4;
  }

  // float z = 3;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_z = this->_internal_z();
  uint32_t raw_z;
  memcpy(&raw_z, &tmp_z, sizeof(tmp_z));
  if (raw_z != 0) {
    total_size += 1 + 4;
  }

  // float w = 4;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_w = this->_internal_w();
  uint32_t raw_w;
  memcpy(&raw_w, &tmp_w, sizeof(tmp_w));
  if (raw_w != 0) {
    total_size += 1 + 4;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_impl_._cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData Quaternionf::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSourceCheck,
    Quaternionf::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*Quaternionf::GetClassData() const { return &_class_data_; }


void Quaternionf::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg) {
  auto* const _this = static_cast<Quaternionf*>(&to_msg);
  auto& from = static_cast<const Quaternionf&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:esurfing.proto.perc.Quaternionf)
  GOOGLE_DCHECK_NE(&from, _this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_x = from._internal_x();
  uint32_t raw_x;
  memcpy(&raw_x, &tmp_x, sizeof(tmp_x));
  if (raw_x != 0) {
    _this->_internal_set_x(from._internal_x());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_y = from._internal_y();
  uint32_t raw_y;
  memcpy(&raw_y, &tmp_y, sizeof(tmp_y));
  if (raw_y != 0) {
    _this->_internal_set_y(from._internal_y());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_z = from._internal_z();
  uint32_t raw_z;
  memcpy(&raw_z, &tmp_z, sizeof(tmp_z));
  if (raw_z != 0) {
    _this->_internal_set_z(from._internal_z());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_w = from._internal_w();
  uint32_t raw_w;
  memcpy(&raw_w, &tmp_w, sizeof(tmp_w));
  if (raw_w != 0) {
    _this->_internal_set_w(from._internal_w());
  }
  _this->_internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void Quaternionf::CopyFrom(const Quaternionf& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:esurfing.proto.perc.Quaternionf)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Quaternionf::IsInitialized() const {
  return true;
}

void Quaternionf::InternalSwap(Quaternionf* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(Quaternionf, _impl_.w_)
      + sizeof(Quaternionf::_impl_.w_)
      - PROTOBUF_FIELD_OFFSET(Quaternionf, _impl_.x_)>(
          reinterpret_cast<char*>(&_impl_.x_),
          reinterpret_cast<char*>(&other->_impl_.x_));
}

::PROTOBUF_NAMESPACE_ID::Metadata Quaternionf::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_esurfing_5fgroup_5fdetected_2eproto_getter, &descriptor_table_esurfing_5fgroup_5fdetected_2eproto_once,
      file_level_metadata_esurfing_5fgroup_5fdetected_2eproto[10]);
}

// ===================================================================

class LidarPose::_Internal {
 public:
  static const ::esurfing::proto::perc::Vector3f& position(const LidarPose* msg);
  static const ::esurfing::proto::perc::Quaternionf& orientation(const LidarPose* msg);
};

const ::esurfing::proto::perc::Vector3f&
LidarPose::_Internal::position(const LidarPose* msg) {
  return *msg->_impl_.position_;
}
const ::esurfing::proto::perc::Quaternionf&
LidarPose::_Internal::orientation(const LidarPose* msg) {
  return *msg->_impl_.orientation_;
}
LidarPose::LidarPose(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor(arena, is_message_owned);
  // @@protoc_insertion_point(arena_constructor:esurfing.proto.perc.LidarPose)
}
LidarPose::LidarPose(const LidarPose& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  LidarPose* const _this = this; (void)_this;
  new (&_impl_) Impl_{
      decltype(_impl_.name_){}
    , decltype(_impl_.position_){nullptr}
    , decltype(_impl_.orientation_){nullptr}
    , /*decltype(_impl_._cached_size_)*/{}};

  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  _impl_.name_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.name_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_name().empty()) {
    _this->_impl_.name_.Set(from._internal_name(), 
      _this->GetArenaForAllocation());
  }
  if (from._internal_has_position()) {
    _this->_impl_.position_ = new ::esurfing::proto::perc::Vector3f(*from._impl_.position_);
  }
  if (from._internal_has_orientation()) {
    _this->_impl_.orientation_ = new ::esurfing::proto::perc::Quaternionf(*from._impl_.orientation_);
  }
  // @@protoc_insertion_point(copy_constructor:esurfing.proto.perc.LidarPose)
}

inline void LidarPose::SharedCtor(
    ::_pb::Arena* arena, bool is_message_owned) {
  (void)arena;
  (void)is_message_owned;
  new (&_impl_) Impl_{
      decltype(_impl_.name_){}
    , decltype(_impl_.position_){nullptr}
    , decltype(_impl_.orientation_){nullptr}
    , /*decltype(_impl_._cached_size_)*/{}
  };
  _impl_.name_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.name_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

LidarPose::~LidarPose() {
  // @@protoc_insertion_point(destructor:esurfing.proto.perc.LidarPose)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void LidarPose::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  _impl_.name_.Destroy();
  if (this != internal_default_instance()) delete _impl_.position_;
  if (this != internal_default_instance()) delete _impl_.orientation_;
}

void LidarPose::SetCachedSize(int size) const {
  _impl_._cached_size_.Set(size);
}

void LidarPose::Clear() {
// @@protoc_insertion_point(message_clear_start:esurfing.proto.perc.LidarPose)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _impl_.name_.ClearToEmpty();
  if (GetArenaForAllocation() == nullptr && _impl_.position_ != nullptr) {
    delete _impl_.position_;
  }
  _impl_.position_ = nullptr;
  if (GetArenaForAllocation() == nullptr && _impl_.orientation_ != nullptr) {
    delete _impl_.orientation_;
  }
  _impl_.orientation_ = nullptr;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* LidarPose::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string name = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_name();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
          CHK_(::_pbi::VerifyUTF8(str, "esurfing.proto.perc.LidarPose.name"));
        } else
          goto handle_unusual;
        continue;
      // .esurfing.proto.perc.Vector3f position = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr = ctx->ParseMessage(_internal_mutable_position(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .esurfing.proto.perc.Quaternionf orientation = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 26)) {
          ptr = ctx->ParseMessage(_internal_mutable_orientation(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* LidarPose::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:esurfing.proto.perc.LidarPose)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string name = 1;
  if (!this->_internal_name().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_name().data(), static_cast<int>(this->_internal_name().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "esurfing.proto.perc.LidarPose.name");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_name(), target);
  }

  // .esurfing.proto.perc.Vector3f position = 2;
  if (this->_internal_has_position()) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(2, _Internal::position(this),
        _Internal::position(this).GetCachedSize(), target, stream);
  }

  // .esurfing.proto.perc.Quaternionf orientation = 3;
  if (this->_internal_has_orientation()) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(3, _Internal::orientation(this),
        _Internal::orientation(this).GetCachedSize(), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:esurfing.proto.perc.LidarPose)
  return target;
}

size_t LidarPose::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:esurfing.proto.perc.LidarPose)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string name = 1;
  if (!this->_internal_name().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_name());
  }

  // .esurfing.proto.perc.Vector3f position = 2;
  if (this->_internal_has_position()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *_impl_.position_);
  }

  // .esurfing.proto.perc.Quaternionf orientation = 3;
  if (this->_internal_has_orientation()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *_impl_.orientation_);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_impl_._cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData LidarPose::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSourceCheck,
    LidarPose::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*LidarPose::GetClassData() const { return &_class_data_; }


void LidarPose::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg) {
  auto* const _this = static_cast<LidarPose*>(&to_msg);
  auto& from = static_cast<const LidarPose&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:esurfing.proto.perc.LidarPose)
  GOOGLE_DCHECK_NE(&from, _this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_name().empty()) {
    _this->_internal_set_name(from._internal_name());
  }
  if (from._internal_has_position()) {
    _this->_internal_mutable_position()->::esurfing::proto::perc::Vector3f::MergeFrom(
        from._internal_position());
  }
  if (from._internal_has_orientation()) {
    _this->_internal_mutable_orientation()->::esurfing::proto::perc::Quaternionf::MergeFrom(
        from._internal_orientation());
  }
  _this->_internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void LidarPose::CopyFrom(const LidarPose& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:esurfing.proto.perc.LidarPose)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool LidarPose::IsInitialized() const {
  return true;
}

void LidarPose::InternalSwap(LidarPose* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &_impl_.name_, lhs_arena,
      &other->_impl_.name_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(LidarPose, _impl_.orientation_)
      + sizeof(LidarPose::_impl_.orientation_)
      - PROTOBUF_FIELD_OFFSET(LidarPose, _impl_.position_)>(
          reinterpret_cast<char*>(&_impl_.position_),
          reinterpret_cast<char*>(&other->_impl_.position_));
}

::PROTOBUF_NAMESPACE_ID::Metadata LidarPose::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_esurfing_5fgroup_5fdetected_2eproto_getter, &descriptor_table_esurfing_5fgroup_5fdetected_2eproto_once,
      file_level_metadata_esurfing_5fgroup_5fdetected_2eproto[11]);
}

// @@protoc_insertion_point(namespace_scope)
}  // namespace perc
}  // namespace proto
}  // namespace esurfing
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::esurfing::proto::perc::LicenePlate*
Arena::CreateMaybeMessage< ::esurfing::proto::perc::LicenePlate >(Arena* arena) {
  return Arena::CreateMessageInternal< ::esurfing::proto::perc::LicenePlate >(arena);
}
template<> PROTOBUF_NOINLINE ::esurfing::proto::perc::DetectedObjects*
Arena::CreateMaybeMessage< ::esurfing::proto::perc::DetectedObjects >(Arena* arena) {
  return Arena::CreateMessageInternal< ::esurfing::proto::perc::DetectedObjects >(arena);
}
template<> PROTOBUF_NOINLINE ::esurfing::proto::perc::DetectedObject*
Arena::CreateMaybeMessage< ::esurfing::proto::perc::DetectedObject >(Arena* arena) {
  return Arena::CreateMessageInternal< ::esurfing::proto::perc::DetectedObject >(arena);
}
template<> PROTOBUF_NOINLINE ::esurfing::proto::perc::Vector3f*
Arena::CreateMaybeMessage< ::esurfing::proto::perc::Vector3f >(Arena* arena) {
  return Arena::CreateMessageInternal< ::esurfing::proto::perc::Vector3f >(arena);
}
template<> PROTOBUF_NOINLINE ::esurfing::proto::perc::Vector2f*
Arena::CreateMaybeMessage< ::esurfing::proto::perc::Vector2f >(Arena* arena) {
  return Arena::CreateMessageInternal< ::esurfing::proto::perc::Vector2f >(arena);
}
template<> PROTOBUF_NOINLINE ::esurfing::proto::perc::Polygon*
Arena::CreateMaybeMessage< ::esurfing::proto::perc::Polygon >(Arena* arena) {
  return Arena::CreateMessageInternal< ::esurfing::proto::perc::Polygon >(arena);
}
template<> PROTOBUF_NOINLINE ::esurfing::proto::perc::Velocity*
Arena::CreateMaybeMessage< ::esurfing::proto::perc::Velocity >(Arena* arena) {
  return Arena::CreateMessageInternal< ::esurfing::proto::perc::Velocity >(arena);
}
template<> PROTOBUF_NOINLINE ::esurfing::proto::perc::Color*
Arena::CreateMaybeMessage< ::esurfing::proto::perc::Color >(Arena* arena) {
  return Arena::CreateMessageInternal< ::esurfing::proto::perc::Color >(arena);
}
template<> PROTOBUF_NOINLINE ::esurfing::proto::perc::WayPoints*
Arena::CreateMaybeMessage< ::esurfing::proto::perc::WayPoints >(Arena* arena) {
  return Arena::CreateMessageInternal< ::esurfing::proto::perc::WayPoints >(arena);
}
template<> PROTOBUF_NOINLINE ::esurfing::proto::perc::WayPoint*
Arena::CreateMaybeMessage< ::esurfing::proto::perc::WayPoint >(Arena* arena) {
  return Arena::CreateMessageInternal< ::esurfing::proto::perc::WayPoint >(arena);
}
template<> PROTOBUF_NOINLINE ::esurfing::proto::perc::Quaternionf*
Arena::CreateMaybeMessage< ::esurfing::proto::perc::Quaternionf >(Arena* arena) {
  return Arena::CreateMessageInternal< ::esurfing::proto::perc::Quaternionf >(arena);
}
template<> PROTOBUF_NOINLINE ::esurfing::proto::perc::LidarPose*
Arena::CreateMaybeMessage< ::esurfing::proto::perc::LidarPose >(Arena* arena) {
  return Arena::CreateMessageInternal< ::esurfing::proto::perc::LidarPose >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>
