// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ros_passthrough.proto

#include "ros_passthrough.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>

PROTOBUF_PRAGMA_INIT_SEG

namespace _pb = ::PROTOBUF_NAMESPACE_ID;
namespace _pbi = _pb::internal;

namespace allride {
namespace proto {
namespace infra {
namespace rsuconnect {
PROTOBUF_CONSTEXPR RosPassThrough::RosPassThrough(
    ::_pbi::ConstantInitialized): _impl_{
    /*decltype(_impl_.data_)*/{&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{}}
  , /*decltype(_impl_.source_id_)*/{&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{}}
  , /*decltype(_impl_.ros_topic_name_)*/{&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{}}
  , /*decltype(_impl_.source_type_)*/{&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{}}
  , /*decltype(_impl_.seq_)*/int64_t{0}
  , /*decltype(_impl_.time_)*/int64_t{0}
  , /*decltype(_impl_._cached_size_)*/{}} {}
struct RosPassThroughDefaultTypeInternal {
  PROTOBUF_CONSTEXPR RosPassThroughDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~RosPassThroughDefaultTypeInternal() {}
  union {
    RosPassThrough _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 RosPassThroughDefaultTypeInternal _RosPassThrough_default_instance_;
}  // namespace rsuconnect
}  // namespace infra
}  // namespace proto
}  // namespace allride
static ::_pb::Metadata file_level_metadata_ros_5fpassthrough_2eproto[1];
static constexpr ::_pb::EnumDescriptor const** file_level_enum_descriptors_ros_5fpassthrough_2eproto = nullptr;
static constexpr ::_pb::ServiceDescriptor const** file_level_service_descriptors_ros_5fpassthrough_2eproto = nullptr;

const uint32_t TableStruct_ros_5fpassthrough_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::allride::proto::infra::rsuconnect::RosPassThrough, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::allride::proto::infra::rsuconnect::RosPassThrough, _impl_.seq_),
  PROTOBUF_FIELD_OFFSET(::allride::proto::infra::rsuconnect::RosPassThrough, _impl_.time_),
  PROTOBUF_FIELD_OFFSET(::allride::proto::infra::rsuconnect::RosPassThrough, _impl_.data_),
  PROTOBUF_FIELD_OFFSET(::allride::proto::infra::rsuconnect::RosPassThrough, _impl_.source_id_),
  PROTOBUF_FIELD_OFFSET(::allride::proto::infra::rsuconnect::RosPassThrough, _impl_.ros_topic_name_),
  PROTOBUF_FIELD_OFFSET(::allride::proto::infra::rsuconnect::RosPassThrough, _impl_.source_type_),
};
static const ::_pbi::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, -1, sizeof(::allride::proto::infra::rsuconnect::RosPassThrough)},
};

static const ::_pb::Message* const file_default_instances[] = {
  &::allride::proto::infra::rsuconnect::_RosPassThrough_default_instance_._instance,
};

const char descriptor_table_protodef_ros_5fpassthrough_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n\025ros_passthrough.proto\022\036allride.proto.i"
  "nfra.rsuconnect\"y\n\016RosPassThrough\022\013\n\003seq"
  "\030\001 \001(\003\022\014\n\004time\030\002 \001(\003\022\014\n\004data\030\003 \001(\014\022\021\n\tso"
  "urce_id\030\004 \001(\t\022\026\n\016ros_topic_name\030\005 \001(\t\022\023\n"
  "\013source_type\030\006 \001(\tb\006proto3"
  ;
static ::_pbi::once_flag descriptor_table_ros_5fpassthrough_2eproto_once;
const ::_pbi::DescriptorTable descriptor_table_ros_5fpassthrough_2eproto = {
    false, false, 186, descriptor_table_protodef_ros_5fpassthrough_2eproto,
    "ros_passthrough.proto",
    &descriptor_table_ros_5fpassthrough_2eproto_once, nullptr, 0, 1,
    schemas, file_default_instances, TableStruct_ros_5fpassthrough_2eproto::offsets,
    file_level_metadata_ros_5fpassthrough_2eproto, file_level_enum_descriptors_ros_5fpassthrough_2eproto,
    file_level_service_descriptors_ros_5fpassthrough_2eproto,
};
PROTOBUF_ATTRIBUTE_WEAK const ::_pbi::DescriptorTable* descriptor_table_ros_5fpassthrough_2eproto_getter() {
  return &descriptor_table_ros_5fpassthrough_2eproto;
}

// Force running AddDescriptors() at dynamic initialization time.
PROTOBUF_ATTRIBUTE_INIT_PRIORITY2 static ::_pbi::AddDescriptorsRunner dynamic_init_dummy_ros_5fpassthrough_2eproto(&descriptor_table_ros_5fpassthrough_2eproto);
namespace allride {
namespace proto {
namespace infra {
namespace rsuconnect {

// ===================================================================

class RosPassThrough::_Internal {
 public:
};

RosPassThrough::RosPassThrough(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor(arena, is_message_owned);
  // @@protoc_insertion_point(arena_constructor:allride.proto.infra.rsuconnect.RosPassThrough)
}
RosPassThrough::RosPassThrough(const RosPassThrough& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  RosPassThrough* const _this = this; (void)_this;
  new (&_impl_) Impl_{
      decltype(_impl_.data_){}
    , decltype(_impl_.source_id_){}
    , decltype(_impl_.ros_topic_name_){}
    , decltype(_impl_.source_type_){}
    , decltype(_impl_.seq_){}
    , decltype(_impl_.time_){}
    , /*decltype(_impl_._cached_size_)*/{}};

  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  _impl_.data_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.data_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_data().empty()) {
    _this->_impl_.data_.Set(from._internal_data(), 
      _this->GetArenaForAllocation());
  }
  _impl_.source_id_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.source_id_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_source_id().empty()) {
    _this->_impl_.source_id_.Set(from._internal_source_id(), 
      _this->GetArenaForAllocation());
  }
  _impl_.ros_topic_name_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.ros_topic_name_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_ros_topic_name().empty()) {
    _this->_impl_.ros_topic_name_.Set(from._internal_ros_topic_name(), 
      _this->GetArenaForAllocation());
  }
  _impl_.source_type_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.source_type_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_source_type().empty()) {
    _this->_impl_.source_type_.Set(from._internal_source_type(), 
      _this->GetArenaForAllocation());
  }
  ::memcpy(&_impl_.seq_, &from._impl_.seq_,
    static_cast<size_t>(reinterpret_cast<char*>(&_impl_.time_) -
    reinterpret_cast<char*>(&_impl_.seq_)) + sizeof(_impl_.time_));
  // @@protoc_insertion_point(copy_constructor:allride.proto.infra.rsuconnect.RosPassThrough)
}

inline void RosPassThrough::SharedCtor(
    ::_pb::Arena* arena, bool is_message_owned) {
  (void)arena;
  (void)is_message_owned;
  new (&_impl_) Impl_{
      decltype(_impl_.data_){}
    , decltype(_impl_.source_id_){}
    , decltype(_impl_.ros_topic_name_){}
    , decltype(_impl_.source_type_){}
    , decltype(_impl_.seq_){int64_t{0}}
    , decltype(_impl_.time_){int64_t{0}}
    , /*decltype(_impl_._cached_size_)*/{}
  };
  _impl_.data_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.data_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  _impl_.source_id_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.source_id_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  _impl_.ros_topic_name_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.ros_topic_name_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  _impl_.source_type_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.source_type_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

RosPassThrough::~RosPassThrough() {
  // @@protoc_insertion_point(destructor:allride.proto.infra.rsuconnect.RosPassThrough)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void RosPassThrough::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  _impl_.data_.Destroy();
  _impl_.source_id_.Destroy();
  _impl_.ros_topic_name_.Destroy();
  _impl_.source_type_.Destroy();
}

void RosPassThrough::SetCachedSize(int size) const {
  _impl_._cached_size_.Set(size);
}

void RosPassThrough::Clear() {
// @@protoc_insertion_point(message_clear_start:allride.proto.infra.rsuconnect.RosPassThrough)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _impl_.data_.ClearToEmpty();
  _impl_.source_id_.ClearToEmpty();
  _impl_.ros_topic_name_.ClearToEmpty();
  _impl_.source_type_.ClearToEmpty();
  ::memset(&_impl_.seq_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&_impl_.time_) -
      reinterpret_cast<char*>(&_impl_.seq_)) + sizeof(_impl_.time_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* RosPassThrough::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // int64 seq = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          _impl_.seq_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int64 time = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          _impl_.time_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bytes data = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 26)) {
          auto str = _internal_mutable_data();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string source_id = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 34)) {
          auto str = _internal_mutable_source_id();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
          CHK_(::_pbi::VerifyUTF8(str, "allride.proto.infra.rsuconnect.RosPassThrough.source_id"));
        } else
          goto handle_unusual;
        continue;
      // string ros_topic_name = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 42)) {
          auto str = _internal_mutable_ros_topic_name();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
          CHK_(::_pbi::VerifyUTF8(str, "allride.proto.infra.rsuconnect.RosPassThrough.ros_topic_name"));
        } else
          goto handle_unusual;
        continue;
      // string source_type = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 50)) {
          auto str = _internal_mutable_source_type();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
          CHK_(::_pbi::VerifyUTF8(str, "allride.proto.infra.rsuconnect.RosPassThrough.source_type"));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* RosPassThrough::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:allride.proto.infra.rsuconnect.RosPassThrough)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // int64 seq = 1;
  if (this->_internal_seq() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt64ToArray(1, this->_internal_seq(), target);
  }

  // int64 time = 2;
  if (this->_internal_time() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt64ToArray(2, this->_internal_time(), target);
  }

  // bytes data = 3;
  if (!this->_internal_data().empty()) {
    target = stream->WriteBytesMaybeAliased(
        3, this->_internal_data(), target);
  }

  // string source_id = 4;
  if (!this->_internal_source_id().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_source_id().data(), static_cast<int>(this->_internal_source_id().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "allride.proto.infra.rsuconnect.RosPassThrough.source_id");
    target = stream->WriteStringMaybeAliased(
        4, this->_internal_source_id(), target);
  }

  // string ros_topic_name = 5;
  if (!this->_internal_ros_topic_name().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_ros_topic_name().data(), static_cast<int>(this->_internal_ros_topic_name().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "allride.proto.infra.rsuconnect.RosPassThrough.ros_topic_name");
    target = stream->WriteStringMaybeAliased(
        5, this->_internal_ros_topic_name(), target);
  }

  // string source_type = 6;
  if (!this->_internal_source_type().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_source_type().data(), static_cast<int>(this->_internal_source_type().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "allride.proto.infra.rsuconnect.RosPassThrough.source_type");
    target = stream->WriteStringMaybeAliased(
        6, this->_internal_source_type(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:allride.proto.infra.rsuconnect.RosPassThrough)
  return target;
}

size_t RosPassThrough::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:allride.proto.infra.rsuconnect.RosPassThrough)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // bytes data = 3;
  if (!this->_internal_data().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::BytesSize(
        this->_internal_data());
  }

  // string source_id = 4;
  if (!this->_internal_source_id().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_source_id());
  }

  // string ros_topic_name = 5;
  if (!this->_internal_ros_topic_name().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_ros_topic_name());
  }

  // string source_type = 6;
  if (!this->_internal_source_type().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_source_type());
  }

  // int64 seq = 1;
  if (this->_internal_seq() != 0) {
    total_size += ::_pbi::WireFormatLite::Int64SizePlusOne(this->_internal_seq());
  }

  // int64 time = 2;
  if (this->_internal_time() != 0) {
    total_size += ::_pbi::WireFormatLite::Int64SizePlusOne(this->_internal_time());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_impl_._cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData RosPassThrough::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSourceCheck,
    RosPassThrough::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*RosPassThrough::GetClassData() const { return &_class_data_; }


void RosPassThrough::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg) {
  auto* const _this = static_cast<RosPassThrough*>(&to_msg);
  auto& from = static_cast<const RosPassThrough&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:allride.proto.infra.rsuconnect.RosPassThrough)
  GOOGLE_DCHECK_NE(&from, _this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_data().empty()) {
    _this->_internal_set_data(from._internal_data());
  }
  if (!from._internal_source_id().empty()) {
    _this->_internal_set_source_id(from._internal_source_id());
  }
  if (!from._internal_ros_topic_name().empty()) {
    _this->_internal_set_ros_topic_name(from._internal_ros_topic_name());
  }
  if (!from._internal_source_type().empty()) {
    _this->_internal_set_source_type(from._internal_source_type());
  }
  if (from._internal_seq() != 0) {
    _this->_internal_set_seq(from._internal_seq());
  }
  if (from._internal_time() != 0) {
    _this->_internal_set_time(from._internal_time());
  }
  _this->_internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void RosPassThrough::CopyFrom(const RosPassThrough& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:allride.proto.infra.rsuconnect.RosPassThrough)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool RosPassThrough::IsInitialized() const {
  return true;
}

void RosPassThrough::InternalSwap(RosPassThrough* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &_impl_.data_, lhs_arena,
      &other->_impl_.data_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &_impl_.source_id_, lhs_arena,
      &other->_impl_.source_id_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &_impl_.ros_topic_name_, lhs_arena,
      &other->_impl_.ros_topic_name_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &_impl_.source_type_, lhs_arena,
      &other->_impl_.source_type_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(RosPassThrough, _impl_.time_)
      + sizeof(RosPassThrough::_impl_.time_)
      - PROTOBUF_FIELD_OFFSET(RosPassThrough, _impl_.seq_)>(
          reinterpret_cast<char*>(&_impl_.seq_),
          reinterpret_cast<char*>(&other->_impl_.seq_));
}

::PROTOBUF_NAMESPACE_ID::Metadata RosPassThrough::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_ros_5fpassthrough_2eproto_getter, &descriptor_table_ros_5fpassthrough_2eproto_once,
      file_level_metadata_ros_5fpassthrough_2eproto[0]);
}

// @@protoc_insertion_point(namespace_scope)
}  // namespace rsuconnect
}  // namespace infra
}  // namespace proto
}  // namespace allride
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::allride::proto::infra::rsuconnect::RosPassThrough*
Arena::CreateMaybeMessage< ::allride::proto::infra::rsuconnect::RosPassThrough >(Arena* arena) {
  return Arena::CreateMessageInternal< ::allride::proto::infra::rsuconnect::RosPassThrough >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>
