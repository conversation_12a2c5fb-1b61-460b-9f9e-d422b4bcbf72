namespace MECData;

// @brief 单车协调规划信息对应的用例类型
table DE_CoordinationInfo {
    info:byte;      // 存在下列情况时，字段对应二进制位置1
                    // cooperativelaneChanging=0，合作式变道
                    // cooperativeVehMerging=1，合作式车辆汇入
                    // laneChangingAtIntersection=2，交叉路口变道
                    // nosignalIntersectionPassing=3，通过无信号交叉口
                    // dynamicLaneManagement=4，动态车道管理
                    // laneReservation=5，车道预订
                    // laneRestriction=6，车道禁行
                    // signalPriority=7，信号优先
}
