include "NodeReferenceID.fbs";
include "SignalWaitingLane.fbs";
include "ConnectingLaneEx.fbs";
include "Maneuver.fbs";

namespace MECData;

// @brief 当前车道与下游路段中车道连接关系的扩展信息
table DF_ConnectionEx {
    remoteIntersection: DF_NodeReferenceID;     // 下游交叉口
    swl: DF_SingleWaitingLane;                  // 待行区
    connectingLane: [DF_ConnectingLaneEx];      // 下游车道间连接信息
    phaseID: ubyte = 0;                         // 信号灯组ID
    turnDirection: DE_Maneuver;                 // 转向方向
    ext_id: string;                             // 扩展ID
}
