namespace MECData;

// @brief 四轮分别的刹车状态。当车辆为单排轮胎（摩托车等）时，以左前和左后表示其前后轮，后侧轮胎对应数值置为0。当车辆某一组轮胎由多个组成时，其状态将等效到一个数值来表示。

table DE_BrakeAppliedStatus{
    wheelBrakes:byte;           // 存在下列情况时，字段对应二进制位置1
                                // 0 = 刹车状态不可用
                                // 1 = 左前轮刹车
                                // 2 = 左后轮刹车
                                // 3 = 右前轮刹车
                                // 4 = 右后轮刹车
}
