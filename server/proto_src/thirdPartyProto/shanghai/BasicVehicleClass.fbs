namespace MECData;

// @brief 车辆类型

enum DE_BasicVehicleClass: uint8 {
    unknownVehicleClass = 0,                // 未配备、未知或不可用
    specialVehicleClass = 1,                // 特殊车辆
    passenger_Vehicle_TypeUnknown = 10,     // 乘用车辆默认类型
    passenger_Vehicle_TypeOther = 11,       // 乘用车类型其他
    lightTruck_Vehicle_TypeUnknown = 20,    // 小型卡车、皮卡、面包车
    lightTruck_Vehicle_TypeOther = 21,      // 轻型卡车车辆类型其他
    truck_Vehicle_TypeUnknown = 25,         // 大卡车、货车默认类型
    truck_Vehicle_TypeOther = 26,           // 卡车，其他类型
    truck_axleCnt2 = 27,                    // 两轴，六个轮胎单元
    truck_axleCnt3 = 28,                    // 三轴，一个轮胎单元
    truck_axleCnt4 = 29,                    // 轴数4或以上，一个轮胎单元
    truck_axleCnt4Trailer = 30,             // 轴数4或以下，单拖车
    truck_axleCnt5Trailer = 31,             // 轴数5或以下，单拖车
    truck_axleCnt6Trailer = 32,             // 轴数6或以上，单拖车
    truck_axleCnt5MultiTrailer = 33,        // 轴数5或以下，多拖车
    truck_axleCnt6MultiTrailer = 34,        // 轴数6，多拖车
    truck_axleCnt7MultiTrailer = 35,        // 轴数7，多拖车
    motorcycle_TypeUnknown = 40,            // 摩托车默认类型
    motorcycle_TypeOther = 41,              // 摩托车类型其他
    motorcycle_Cruiser_Standard = 42,       // 巡航摩托车
    motorcycle_SportUnclad = 43,            // 运动摩托车
    motorcycle_SportTouring = 44,           // 运动摩托车
    motorcycle_SuperSport = 45,             // 运动摩托车
    motorcycle_Touring = 46,                // 巡游摩托车
    motorcycle_Trike = 47,                  // 三轮摩托车
    motorcycle_wPassengers = 48,            // 载客摩托车
    transit_TypeUnknown = 50,               // 公交默认类型
    transit_TypeOther = 51,                 // 公交其他类型
    transit_BRT = 52,                       // 快速公交系统BRT
    transit_ExpressBus = 53,                // 快线
    transit_LocalBus = 54,                  // 本地线路
    transit_SchoolBus = 55,                 // 校车
    transit_FixedGuideway = 56,             // 固定导轨公交
    transit_Paratransit = 57,               // 辅助客运系统
    transit_Paratransit_Ambulance = 58,     // 辅助客运系统
    emergency_TypeUnknown = 60,             // 消防、救护、警车等特种车辆
    emergency_TypeOther = 61,               // 特种车辆其他
    emergency_Fire_Light_Vehicle = 62,      // 轻型消防车
    emergency_Fire_Heavy_Vehicle = 63,      // 重型消防车
    emergency_Fire_Paramedic_Vehicle = 64,  // 救护车
    emergency_Fire_Ambulance_Vehicle = 65,  // 救护车
    emergency_Police_Light_Vehicle = 66,    // 轻型警车
    emergency_Police_Heavy_Vehicle = 67,    // 重型警车
    emergency_Other_Responder = 68,         // 其他应急车辆
    emergency_Other_Ambulance = 69,         // 其他救护车
    otherTraveler_TypeUnknown = 80,         // 其他交通参与者默认类型
    otherTraveler_TypeOther = 81,           // 其他交通参与者其他类型
    otherTraveler_Pedestrian = 82,          // 行人
    otherTraveler_Visually_Disabled = 83,   // 视障人士
    otherTraveler_Physically_Disabled = 84, // 残疾人
    otherTraveler_Bicycle = 85,             // 自行车
    otherTraveler_Vulnerable_Roadworker = 86,   // 施工工人
    infrastructure_TypeUnknown = 90,        // 基础设施默认
    infrastructure_Fixed = 91,              // 基础设施固定
    infrastructure_Movable = 92,            // 基础设施可移动
    equipped_CargoTrailer = 93              // 拖挂车
}
