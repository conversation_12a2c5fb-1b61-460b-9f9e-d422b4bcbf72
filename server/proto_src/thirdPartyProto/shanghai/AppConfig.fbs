include "DeploySpecificInfo.fbs";
include "DeviceInfo.fbs";
include "ModuleMiscellaneous.fbs";
include "LogConfig.fbs";
include "ZStub.fbs";
include "OperationTags.fbs";

namespace MECData;

// For OTA:
// Only fill in fields that user hopes to configure
// fill in all info that user has to provide if referred by DF_AddInstance

// @brief 应用模块实例配置

table DF_AppInstanceConfig {
    id: uint16;                                 // 实例ID
    name: string;                               // 实例名称
    description: string;                        // 实例描述
    version: string;                            // 对应应用模块版本号，推荐使用语义化版本2.0.0
    io_requirements: DF_ModuleIOConfig;         // 实例标准消息输入输出需求
    deploy_specific: DF_DeploySpecificInfo;     // 针对本实例的部署信息
    zstub: DF_ZStubConfig;                      // ZStub配置
    crash: DF_CrashRestartConfig;               // 崩溃重启策略配置
    log: DF_LogConfig;                          // 日志策略配置
    io_record: DF_IORecorder;                   // IO记录策略配置
    ui: DF_ModuleWebUI;                         // Web可视化页面配置
    ssl_names: [string];                        // 本实例可引用的SSL证书集名称，所引用的证书集必须在证书管理器中已经配置好
    free_config: [uint8];                       // 其他自由配置内容，二进制形式，具体格式由应用模块自定
    tags: DE_OperationTags;                     // 运维标记
    device_info: DF_DeviceInfo;                 // 应用实例下挂外接设备信息，仅用于外设协议接口类型
}

// For OTA:
// Only fill in fields that user hopes to configure
// fill in all info that user has to provide if referred by DF_AddOrUpgradeAppPackage

// @brief 应用模块配置

table DF_AppPackageConfig {
    description: string;                        // 应用模块配置
    deployment_type: DE_DeploymentType;         // 应用模块部署类型
    free_config: [uint8];                       // 其他自由配置内容，二进制形式，具体格式由应用模块自定
    tags: DE_OperationTags;                     // 运维标记
    deploy_specific: DF_DeploySpecificInfo;     // 应用模块部署信息
}
