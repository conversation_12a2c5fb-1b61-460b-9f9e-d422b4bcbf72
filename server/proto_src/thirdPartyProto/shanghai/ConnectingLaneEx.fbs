include "RoadPoint.fbs";

namespace MECData;

// @brief 定位上游车道转向连接的下游车道的扩展信息

table DF_ConnectingLaneEx {
    target_section: uint8;                  // 下游车道经过的路段ID
    target_lane: int8;                      // 下游车道的车道ID
    connectingLaneWidth: ushort;            // 真实或虚拟连接车道宽度，单位：cm，取值范围0~32767
    connectingLanePoints: [DF_RoadPoint];   // 真实或虚拟连接车道的中心点序列
    isolatedConnectingLane: bool = false;   // 是否为单独渠化的连接车道（如右转渠化岛）
    ext_id: string;                         // 连接车道的扩展ID
    target_section_ext_id: string;          // 下游车道经过的路段扩展ID
    target_lane_ext_id: string;             // 下游车道的车道扩展ID
}
