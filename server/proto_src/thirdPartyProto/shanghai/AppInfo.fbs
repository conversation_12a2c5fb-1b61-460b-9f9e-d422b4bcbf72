include "DeviceInfo.fbs";
include "ModulePerformance.fbs";
include "ModuleIOConfig.fbs";
include "OperationTags.fbs";
include "DeploySpecificInfo.fbs";
include "DeviceSpecificInfo.fbs";
include "ZStub.fbs";
include "ModuleMiscellaneous.fbs";
include "AppConfig.fbs";
include "Image.fbs";

namespace MECData;

// @brief 实例运行时间统计

table DF_InstanceUptime {
    last_start_time: int64;                 // 上次启动时间，毫秒级UNIX时间戳
    continuous_uptime: int64;               // 截至当前连续运行时间，单位：ms
}

// @brief 消耗计算资源统计

table DF_ResourceUsage {
    cpu: uint16 = 65535;                    // CPU占有率，单位：0.01%, 65535为无效值
    memory: uint16 = 65535;                 // 内存占有率，单位：0.01%, 65535为无效值
    gpu: uint16 = 65535;                    // GPU占有率，单位：0.01%, 65535为无效值
    storage: uint16 = 65535;                // 储存占有率，按应用可使用的储存空间配额计算，单位：0.01%，65535为无效值
}

// @brief 实例运行时异常状态

table DF_InstanceRuntimeAbnormal {
    code: int = 0;                          // 实例运行异常代码，一般取0为正常，其他数值含义由模块自行确定
    desc: string;                           // 异常描述信息
}

// @brief 应用模块实例即时信息

table DF_AppInstanceInfo {
    id: uint16;                                     // 实例id
    name: string;                                   // 实例名称
    status: DE_ModuleStatus = UNINITIALIZED;        // 实例状态
    uptime: DF_InstanceUptime;                      // 实例运行时间统计
    resources: DF_ResourceUsage;                    // 实例消耗计算资源统计，可能取无效值
    performance_indexes: [DF_ModulePerformance];    // 实例自主上报的统计指标
    device_info: DF_DeviceInfo;                     // 实例对接外接设备信息
                                                    // 仅用于外设协议接口类型模块
    config: DF_AppInstanceConfig;                   // 实例配置
                                                    // 周期性上报时不填
    runtime_abnormal: DF_InstanceRuntimeAbnormal;   // 模块运行时异常状态
}

// @brief 应用模块即时信息

table MSG_AppInfo {
    package_id: string (required);          // 应用模块唯一ID，UUID格式
    name: string (required);                // 应用模块名称
    description: string;                    // 应用模块描述
    version: string;                        // 版本号，推荐使用语义化版本2.0.0
    build_date: string;                     // 构建日期，格式：yyyy-mm-dd
    gpu_driver_dependency: string;          // 依赖GPU工具链信息
    mecdata_version: string;                // 依赖MECData标准消息版本
    category: DE_ModuleCategory = APPLICATION_ALGORITHM;        // 应用模块类型
    instances: [DF_AppInstanceInfo];        // 应用模块实例信息
    config: DF_AppPackageConfig;            // 应用模块配置
                                            // 周期性上报时不填
    msg_id: int64;                          // 消息雪花ID
    icon: DF_Image;                         // 应用图标
    resources: DF_ResourceUsage;            // 应用所有实例消耗计算资源统计，可能取无效值
}

root_type MSG_AppInfo;
