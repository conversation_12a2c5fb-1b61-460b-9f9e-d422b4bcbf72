include "BasicVehicleClass.fbs";
include "Position3D.fbs";
include "NodeReferenceID.fbs";
include "PositionOffsetLLV.fbs";

namespace MECData;

// there are loads of collision criticality measurements available
// pick the one you like and list metric values that trigger this warning
// see a comprehensive list of metrics at https://criticality-metrics.readthedocs.io/en/latest/index.html
// more metric definitions could be supported in the future

// @brief 冲突风险指标：车头时距

table DE_CriticalityMetricTimeHeadway {
    h: int32;                               // 车头时距，单位：1/100秒
}

// @brief 冲突风险指标：Time To Collision

table DE_CriticalityMetricTimeToCollision {
    ttc: int32;                             // Time To Collision，单位：1/100秒
}

// @brief 冲突风险指标：Post Encroachment Time

table DE_CriticalityMetricPostEncroachmentTime {
    pet: int32;                             // Post Encroachment Time，单位：1/100秒
}

// @brief 冲突风险指标

union DE_CriticalityMetric {
    DE_CriticalityMetricTimeHeadway,        // 车头时距
    DE_CriticalityMetricTimeToCollision,    // Time To Collision
    DE_CriticalityMetricPostEncroachmentTime    // Post Encroachment Time
}

// @brief 潜在冲突风险交通参与者信息

table DF_CollisionWarningParticipant {
    refer_pos: DF_PositionOffsetLLV;        // 相对被预警交通参与者位置的经纬度偏移
    speed: uint16;                          // 交通参与者速度，单位：0.02 m/s，取值范围0~8191
    ptc_id: uint16 = 0;                     // 交通参与者ID，与MSG_RoadsideSafetyMessage或MSG_SafetyMessage的交通参与者对应
    ptc_type: uint8 = 0;                    // 交通参与者类型
                                            // 0 = 未知类型
                                            // 1 = 机动车
                                            // 2 = 非机动车
                                            // 3 = 行人
                                            // 4 = RSU接收BSM获得的V2X交通参与者
    veh_class: DE_BasicVehicleClass = unknownVehicleClass;  // 车型
    obu_id: [uint8];                        // OBU ID
    criticality: [DE_CriticalityMetric];    // 与被预警交通参与者间冲突风险指标值合集
}

// @brief 交通参与者碰撞预警。每条消息仅用于向单个交通参与者发送

table MSG_CollisionWarning {
    mec_id: string;                             // MEC ID
    time: uint64;                               // 消息发出时刻，毫秒级UNIX时间戳
    node: DF_NodeReferenceID;                   // 预警消息所属交叉口
    pos: DF_Position3D(required);               // 被预警交通参与者所在绝对位置
    speed: uint16;                              // 被预警交通参与者速度，单位：0.02 m/s，取值范围0~8191
    ptc_id: uint16 = 0;                         // 被预警交通参与者的交通参与者ID，与MSG_RoadsideSafetyMessage或MSG_SafetyMessage的交通参与者对应
    ptc_type: uint8 = 0;                        // 交通参与者类型
                                                // 0 = 未知类型
                                                // 1 = 机动车
                                                // 2 = 非机动车
                                                // 3 = 行人
                                                // 4 = RSU接收BSM获得的V2X交通参与者
    veh_class: DE_BasicVehicleClass = unknownVehicleClass;      // 被预警交通参与者车型
    obu_id: [uint8];                            // 被预警交通参与者的OBU ID
    parts: [DF_CollisionWarningParticipant];    // 与被预警交通参与者有潜在碰撞风险的其他交通参与者信息
    msg_id: int64;                              // 消息雪花ID
}

root_type MSG_CollisionWarning;
