include "ModuleMiscellaneous.fbs";
include "DeploySpecificInfo.fbs";
include "DeviceSpecificInfo.fbs";
include "ModuleIOConfig.fbs";
include "OperationTags.fbs";
include "AppConfig.fbs";
include "ZStub.fbs";

namespace MECData;

// @brief 增加实例指令

table DF_AddInstance {
    id: uint16;                                 // 应用模块实例ID
    package_id: string (required);              // 对应应用模块UUID
    config: DF_AppInstanceConfig;               // 应用模块配置
                                                // 如未填写，ZBRS将尝试从OTA资源包中获取默认配置并使其生效
}

// @brief 实例级别OTA指令

table DF_InstanceOTA {
    add: [DF_AddInstance];                      // 增加实例指令，被增加的实例将被尝试自动拉起
    conf: [DF_AppInstanceConfig];               // 修改实例配置指令，数组元素为修改后的对象实例配置
    start: [uint16];                            // 启动实例指令，数组元素为对象实例ID
    stop: [uint16];                             // 停止实例运行指令，数组元素为对象实例ID
    restart: [uint16];                          // 重启实例指令，数组元素为对象实例ID
    del: [uint16];                              // 删除实例指令，数组元素为对象实例ID
}

// @brief 应用模块级别OTA指令

table DF_AppPackageOTA {
    package_id: string (required);              // 应用模块ID，UUID格式
    name: string (required);                    // 应用模块名称
    category: DE_ModuleCategory = APPLICATION_ALGORITHM;    // 应用模块类型
    version: string;                            // 应用模块目标版本号
    build_date: string;                         // 应用模块构建日期，单位：yyyy-mm-dd
    config: DF_AppPackageConfig;                // 应用模块配置
    remove: bool = false;                       // 是否删除此应用模块
    sha256sum: string;                          // OTA资源包的SHA256摘要，用于资源包下载后的文件校验
}

// @brief 应用模块OTA指令

table MSG_AppOTA {
    packages: [DF_AppPackageOTA];               // 应用模块级别OTA指令，先执行
    instances: [DF_InstanceOTA];                // 实例级别OTA指令，后执行
}

root_type MSG_AppOTA;
