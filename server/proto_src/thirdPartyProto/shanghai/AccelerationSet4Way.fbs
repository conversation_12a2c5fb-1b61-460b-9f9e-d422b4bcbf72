
namespace MECData;

// @brief 车辆四轴加速度

table DF_AccelerationSet4Way {
    lon:short;                                       // 纵向加速度，向前加速为正，反向为负，分辨率为0.01m/s2，数值2001为无效数值
    lat:short;                                       // 横向加速度，向右加速为正，反向为负，分辨率为0.01m/s2，数值2001为无效数值
    vert:byte;                                       // 垂直加速度，沿重力方向向下为正，反向为负，分辨率为0.01m/s2，数值2001为无效数值
    yaw:short;                                       // 横摆角速度，顺时针旋转为正，反向为负，数据分辨率为0.01°/s
}
