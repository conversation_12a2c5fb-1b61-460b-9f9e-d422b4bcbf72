syntax = "proto3";

package datacapture;

message Message{
    enum MessageType{
        PROTO_COMMON_CMD = 0;
        PROTO_UPDATE_CONFIG = 1;
        PROTO_UPDATE_STATUS = 2;
        PROTO_DATA_QUERY_REQ = 3;
        PROTO_DATA_QUERY_RES = 4;
        PROTO_DATA_COMPRESS_REQ = 5;
        PROTO_DATA_COMPRESS_RES = 6;
        PROTO_DATA_MONITOR = 7;
        PROTO_CONFIG_REQ = 8;
        PROTO_CONFIG_RES = 9;
        PROTO_ROADINFO_UPDATE = 10;
        PROTO_UPDATE_STSTEM_STATUS = 11;
        PROTO_DATA_QUERY_BATCHES_REQ = 12;
        PROTO_DATA_QUERY_BATCHES_RES = 13;
        PROTO_SYSTEM_INFO_REQ = 14;
        PROTO_SYSTEM_INFO_RES = 15;
        PROTO_CLIENT_INFO_REQ = 16;
        PROTO_CLIENT_INFO_RES = 17;
        PROTO_SYSTEM_CMD_REQ = 18;
        PROTO_SYSTEM_STORE_REQ = 19;
        PROTO_SYSTEM_NET_REQ =20;
        PROTO_SYSTEM_CMD_RES = 21;
        PROTO_SYSTEM_STORE_RES = 22;
        PROTO_SYSTEM_NET_RES =23;
        PROTO_DATABASE_CLOSED =24;
        PROTO_COMMON_LOG =25;   
        PROTO_TESTCASE_GET_ALL_INFO =26;
        PROTO_TESTCASE_UPDATE_INFO = 27;
        PROTO_TESTCASE_GET_UPLOAD_FILE_LIST_REQ = 28;
        PROTO_TESTCASE_GET_UPLOAD_FILE_LIST_RES = 29;
        PROTO_TESTCASE_ADD_TESTCASE_REQ = 30;
        PROTO_TESTCASE_ADD_TESTCASE_RES = 31;
        PROTO_TESTCASE_CONTROL_DATA_PUSH_REQ = 32;
        PROTO_TESTCASE_CONTROL_DATA_PUSH_RES = 33;
    }

    MessageType type = 1;
    oneof data{
        CommonCMD commonCmd = 2;
        ArgsList argsList = 3;
        CapStatus capStatus = 4;
        DataQuery dataQueryReq = 5;
        DataQueryRes dataQueryRes = 6;
        CompressReqList compressReq = 7;
        CompressRes compressRes = 8;
        Monitor monitor = 9;
        RoadInfoList roadInfoList = 10;
        SystemStatus systemStatus = 11;
        DataQueryBatchesReq dataQueryBatchesReq = 12;
        DataQueryBatchesRes dataQueryBatchesRes = 13;
        SystemInfo  systemInfo = 14;
        ClientInfo clientInfo = 15;
        SystemCmd systemcmd = 16;
        storeInfo storeinfo = 17;
        NetConfig netconfig = 18;
        SystemLog systemLog = 19;
        TestCaseInfoList testCaseInfoList = 20;
        FileInfoList fileInfoList = 21;
        CasePushRes casePushRes = 22;
        TestCasePushStatus testCasePushStatus = 23;
        AddTestCasePackage addTestCasePackage = 24;
        TestCasePushConf testCasePushConf = 25;
    }
}

//数据更新
message RawData{
    string strCrossroadId = 1;  //路口id
    uint64 llRecvTime = 2;  //数据接收时间,采用13位UTC时间戳
    uint64 llDataTime = 3;  //数据时间,采用13位UTC时间戳
    uint32 dwDatalength = 4;    //数据长度
    string strData = 5;     //数据
}

//采集状态
message CapStatus{
    string strCrossroadId = 1;  //路口id
    string strDescribe = 2;     //路口描述
    uint64 llTimestamp = 3;     //时间,采用13位UTC时间戳
    bool bIsCaptruing = 4;      //是否在采集 
    float fFreq = 5;    //时间频率
    uint32 dwRecvDataCnt = 6;   //接收数据量
}

//状态监控
message Monitor{
    uint32 dwStatusCnt = 1;     
    uint64 llUpdateTime = 2;    //更新时间 采用13位UTC时间戳
    uint64 llStartTime = 3;     //采集开始时间 采用13位UTC时间戳
    uint64 llDurationTime = 4;  //持续时间 采用13位UTC时间戳
    repeated CapStatus statusList = 5;
}

//数据查询请求
message DataQuery{
    enum dataType{
        DATA_TYPE_RAW = 0;      // 原始数据
        DATA_TYPE_STABILITY = 1;    //稳定性
        DATA_TYPE_TSARI  = 2;   // 清研院标准数据格式
    }
    string strCrossroadId = 1;  //路口id
    dataType type = 2;  //数据类型
    uint64 llStartTime = 3;     //起始时间
    uint64 llEndTime = 4;       //结束时间
}

//数据查询响应
message DataQueryRes{
    uint32 dwCnt = 1;       //数据量
    bool bIsSucceed = 2;    //是否成功
    string strErr = 3;      //错误信息
    repeated RawData dataList = 4;  //数据列表
}

//数据分块查询请求
message DataQueryBatchesReq{
    string strCrossID = 1;  //路口ID
    uint64 llStartTime =2;  // 开始时间
    uint64 llEndTime = 3;   // 结束时间
    uint32 dwNowPage = 4;   // 当前页数
    uint32 dwPageSize = 5;  // 每页数据量
}

//数据分块查询响应
message DataQueryBatchesRes{
    bool bIsSucceed = 1;    // 是否成功
    uint32 dwNowPage = 2;   // 当前页数
    uint32 dwPageSize = 3;  // 每页数据量
    uint32 dwTotalPages = 4;    // 总页数
    uint32 dwTotalDatas = 5;    // 总数据量
    string strErr = 6;          // 错误信息
    uint32 dwCnt = 7;           // 数据个数
    repeated RawData dataList = 8;  // 数据
}


//文件批量压缩请求
message CompressReqList{

    uint32 compressType = 1; //压缩类型 zip 0x01|tar 0x02
    uint32 dwCnt =2;    //文件数量
    string strHttpUrl = 3; //http url
    string strPackageName = 4;  //压缩包名
    string strSerialNum = 5;
    repeated DataQuery queryList = 6;   //列表
}

//文件批量压缩请求响应
message CompressRes{
    uint32 compressType = 1;
    bool bIsSucceed = 2;    //是否成功
    string filepath = 3;    //文件路径
    string strErr = 4;      //错误信息
    string strSerialNum = 5;
    string strPackageName = 6;

}

//参数配置
message ConnectArgs{
enum ConnectType{
        PROTO_MQTT = 0;
        PROTO_HTTP = 1;
        PROTO_KAFKA = 2;
        PROTO_TCP_SERVER = 3;
    }
    uint32 dwNo = 1 ;
    bool isEnable = 2;
    ConnectType type = 3;
    string strCrossroadId = 4;
    string strTopice = 5 ;
    string strPassword = 6;
    string strClientId = 7;
    string strAddr = 8 ;
    string strUsername = 9 ;
    string strDescribe = 10 ;
    uint32 dwFactory = 11;
}

//参数更新列表
message ArgsList{
    uint32 dwCnt = 1;
    repeated ConnectArgs argsList = 2;
}

//通用配置
message CommonCMD{
    enum cmdType{
        PROTO_SYSTEM_INIT = 0;
        PROTO_SYSTEM_START = 1;
        PROTO_SYSTEM_STOP  = 2;
        PROTO_SYSTEM_PAUSE = 3;
    }
    cmdType type = 1;
}

//路口信息
message RoadInfo{
    string strID = 1;
    string strDescribe = 2;
}

//路口信息列表
message RoadInfoList{
    uint32 dwCnt = 1;
    repeated RoadInfo list = 2;
}

//系统状态
message SystemStatus{
    enum statusType{
        START = 0;
        STOP = 1;
        PAUSE = 2;
    }
    uint64 llTimestamp = 1;
    statusType type = 2;
}

//系统硬件信息
message SystemInfo{
    float cpuUsed = 1;
    float cpuTemp = 2;
    float cpuFreq = 3;
    float cpuCoreNum = 4;
    float memoryUsed = 5;
    float memoryTotal = 6;
    float diskUsed = 7;
}

//客户端信息
message ClientInfo{
    string ip  =1;
    uint32 port = 2;
}

//网络设置
message NetConfig{
    string localIp = 1;
    string mask = 2;
    string gateway = 3;
    string targetIp = 4;
    string route = 5;
}

//存储设置
message storeInfo{
    string storePath = 1;
}

//系统控制
message SystemCmd{
    uint32 port = 1;
    enum statusType{
        RECONNECTSERVER = 0;
        RESTARTSERVER = 1;
        REBOOT = 2;
        NOCMD = 3;
    }
    statusType type = 2;
}

//系统日历
message SystemLog{
    bool status =1;
    string log =2;
}

// 测试用例信息
message TestCaseInfo{
    string strDataUID = 1;
    uint32 dwCaseID = 2;
    string strCaseName = 3;
    enum dataType{
        RSM = 0;
        BSM = 1;
        MAP = 2;
        RSI = 3;
        SPAT = 4;
    }
    dataType type = 4;
    uint64 llImportTime = 5;
}

// 测试用例信息列表
message TestCaseInfoList{
    repeated TestCaseInfo list = 1;
}

// 测试用例推送方式及参数
message TestCasePushConf{
    enum pushWay{
        MQTT = 0;
        UDP = 1;
    }
    pushWay ePushWay = 1;
    string strAddress = 2;
    string strTopic = 3;
    string strClientID = 4;
    string strUsername = 5;
    string strPWD = 6;
    string strDataUid = 7;
}

// 测试用例推送结果
message CasePushRes{
    enum PushStatus{
        waiting = 0;
        pushing = 1;
        error = 2;
    }
    PushStatus status = 1;
    string strDataUid = 2;
    string strErrMsg = 3;
}

// 测试用例压缩包文件信息
message FileInfo{
    string strFileName = 1;
    string fileUID = 2;
}

// 文件列表
message FileInfoList{
    repeated FileInfo list = 1;
}

// 添加测试用例
message AddTestCasePackage{
    string strCaseName = 1;
    string strData = 2;
}

// 推送数据状态
message TestCasePushStatus{
    uint64 llTimstamp = 1;
    enum enumStatus{
        START = 0;
        STOP = 1;
        PAUSE = 2;
    }
    enumStatus emStatus = 2;
}