[common]
totalNum = 10
[connect0]
addr = 
clientId = 
describe = 
enable = 1
id = 1
password = 
roadId = 测试6
topic = 
type = http
username = 
[connect1]
addr = ws://127.0.0.1:8083/mqtt
clientId = test
describe = test
enable = 1
id = 2
password = *******
roadId = 测试2
topic = test
type = mqtt
username = tsari
[connect2]
addr = 
clientId = 
describe = 测试路口a
enable = 1
id = 3
password = 
roadId = 测试1
topic = 
type = http
username = 
[connect3]
addr = 
clientId = 
describe = 
enable = 1
id = 4
password = 
roadId = 测试3
topic = 
type = http
username = 
[connect4]
addr = 
clientId = 
describe = 
enable = 1
id = 5
password = 
roadId = 测试5
topic = 
type = http
username = 
[connect5]
addr = 
clientId = 
describe = 
enable = 1
id = 6
password = 
roadId = 测试4
topic = 
type = http
username = 
[connect6]
addr = 
clientId = 
describe = 
enable = 0
id = 7
password = 
roadId = 
topic = 
type = http
username = 
[connect7]
addr = 
clientId = 
describe = 
enable = 0
id = 8
password = 
roadId = 
topic = 
type = http
username = 
[connect8]
addr = 
clientId = 
describe = 
enable = 0
id = 9
password = 
roadId = 
topic = 
type = http
username = 
[connect9]
addr = 
clientId = 
describe = 
enable = 0
id = 10
password = 
roadId = 
topic = 
type = http
username = 
