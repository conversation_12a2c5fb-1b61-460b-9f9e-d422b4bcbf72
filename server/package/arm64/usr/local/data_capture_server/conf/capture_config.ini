[common]
totalNum = 10
[connect0]
addr = tcp://127.0.0.1:1883
clientId = tsari-0
describe = 测试
enable = 1
id = 1
password = 
roadId = test-0
topic = test-0
type = mqtt
username = SZARITU
[connect1]
addr = tcp://127.0.0.1:1883
clientId = tsari-0
describe = 测试
enable = 0
id = 2
password = 
roadId = test-1
topic = test-0
type = mqtt
username = 
[connect2]
addr = 127.0.0.1:8083/mqtt
clientId = tsari-2
describe = 测试
enable = 0
id = 3
password = 
roadId = test-2
topic = test-2
type = mqtt
username = 
[connect3]
addr = 127.0.0.1:8083/mqtt
clientId = tsari-3
describe = 测试
enable = 0
id = 4
password = 
roadId = test-3
topic = test-3
type = mqtt
username = 
[connect4]
addr = 127.0.0.1:8083/mqtt
clientId = tsari-4
describe = 测试
enable = 0
id = 5
password = 
roadId = test-4
topic = test-4
type = mqtt
username = 
[connect5]
addr = 127.0.0.1:8083/mqtt
clientId = tsari-5
describe = 测试
enable = 0
id = 6
password = 
roadId = test-5
topic = test-5
type = mqtt
username = 
[connect6]
addr = 127.0.0.1:8083/mqtt
clientId = tsari-6
describe = 测试
enable = 0
id = 7
password = 
roadId = test-6
topic = test-6
type = mqtt
username = 
[connect7]
addr = 127.0.0.1:8083/mqtt
clientId = tsari-7
describe = 测试
enable = 0
id = 8
password = 
roadId = test-7
topic = test-7
type = mqtt
username = 
[connect8]
addr = 127.0.0.1:8083/mqtt
clientId = tsari-8
describe = 测试
enable = 0
id = 9
password = 
roadId = test-8
topic = test-8
type = mqtt
username = 
[connect9]
addr = 127.0.0.1:8083/mqtt
clientId = tsari-9
describe = 测试
enable = 0
id = 10
password = 
roadId = test-9
topic = test-9
type = mqtt
username = 
