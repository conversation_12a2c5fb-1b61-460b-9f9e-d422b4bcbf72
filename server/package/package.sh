#!/bin/sh

version=1.1.0

platform=x86 #arm64 x86

system=ubuntu20.04

current_date=$(date '+%Y%m%d')
echo "Operating System: $system Platform: $platform Version: $version"
if [ "$platform" = "arm64" ]; then
	sed -i "s/Version: .*/Version: $version/" ./arm64/DEBIAN/control
    dpkg -b arm64/ dataCaptureServer_"$version"_"$system"_"$platform"_"$current_date".deb
else
    sed -i "s/Version: .*/Version: $version/" ./x86/DEBIAN/control
    dpkg -b x86/ dataCaptureServer_"$version"_"$system"_"$platform"_"$current_date".deb
fi
