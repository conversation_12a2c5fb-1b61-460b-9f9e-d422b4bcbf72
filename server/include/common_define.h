#ifndef _COMMON_DEFINE_H_
#define _COMMON_DEFINE_H_

namespace commonDefine
{
/********capture mode define***********/
#define CAPTURE_GUI 0
#define CAPTURE_TERMIAL 1

/**********factory define**************/
#define FACTORY_NONE 0 // 无需解析
#define FACTORY_ZXK 1  // 中智行 解析数据
#define FACTORY_ZJJT 2 // 浙江交投 根据deviceId 区分路口
#define FACTORY_WANJI 3 // 万集 
#define FACTORY_SHANGHAI 4 //上海奉贤
#define FACTORY_SHANGHAI_TRAFFIC 5 //上海奉贤交通流
#define FACTORY_SHANGHAI_TCP 6 //上海tcp

#define FACTORY_TSARI   7 //清研院标准数据协议
#define FACTORY_GROUP_STANDARDS 8   //团标

/**********data format define*********/
#define DATA_FORMAT_RAW 0           // 原始数据
#define DATA_FORMAT_STABILITY 1     // 稳定性数据
#define DATA_FORMAT_TSARI 2         // 清研院标准数据
};

#endif