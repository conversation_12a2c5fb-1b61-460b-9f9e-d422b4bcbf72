#ifndef LOGGER_H
#define LOGGER_H

// 在spdlog.h之前定义才有效
#define SPDLOG_ACTIVE_LEVEL SPDLOG_LEVEL_DEBUG

#include "common/Singleton.hpp"
#include "spdlog/spdlog.h"
#include "spdlog/async.h"
#include "spdlog/cfg/argv.h"
#include "spdlog/sinks/rotating_file_sink.h"

class Logger : public ISingle<Logger>
{
public:
    Logger(){};
    ~Logger(){};

    std::shared_ptr<spdlog::logger> GetLogger() { return m_logger; }

    void InitLogger(const std::string &loggername,
                    const std::string &filename,
                    size_t max_file_size,
                    size_t max_files)
    {
        spdlog::init_thread_pool(10000, 1);
        m_logger = spdlog::rotating_logger_mt<spdlog::async_factory>(loggername, filename, max_file_size, max_files);
        m_logger->set_pattern("[%Y-%m-%d %H:%M:%S.%e][%l][%s:%#] %v");
        m_logger->flush_on(spdlog::level::warn);
        spdlog::flush_every(std::chrono::seconds(3));
    }

    void CleanLogger(){spdlog::drop_all();};

public:
    // 启动时代入日志等级控制
    static void SetLogLevel(int argc, char **argv)
    {
        spdlog::cfg::load_argv_levels(argc, argv);
    }

private:
    std::shared_ptr<spdlog::logger> m_logger;
};

#define DEBUG(...) SPDLOG_LOGGER_DEBUG(Logger::Instance().GetLogger(), __VA_ARGS__)
#define INFO(...)  SPDLOG_LOGGER_INFO(Logger::Instance().GetLogger(), __VA_ARGS__)
#define ERROR(...) SPDLOG_LOGGER_ERROR(Logger::Instance().GetLogger(), __VA_ARGS__)
#define WARN(...)  SPDLOG_LOGGER_WARN(Logger::Instance().GetLogger(), __VA_ARGS__)

#endif