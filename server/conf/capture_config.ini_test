[common]
totalNum = 30
[connect0]
addr = ws://127.0.0.1:8083/mqtt
clientId = tsari-0
describe = road0
enable = 1
id = 1
password = 
roadId = road0
topic = test-0
type = mqtt
username = 
[connect1]
addr = ws://127.0.0.1:8083/mqtt
clientId = tsari-1
describe = road1
enable = 1
id = 2
password = *******
roadId = road1
topic = test-1
type = mqtt
username = tsari
[connect10]
addr = ws://127.0.0.1:8083/mqtt
clientId = tsari-10
describe = 
enable = 1
id = 11
password = 
roadId = road10
topic = test-10
type = mqtt
username = 
[connect11]
addr = ws://127.0.0.1:8083/mqtt
clientId = tsari-11
describe = test
enable = 1
id = 12
password = *******
roadId = road11
topic = test-11
type = mqtt
username = tsari
[connect12]
addr = ws://127.0.0.1:8083/mqtt
clientId = tsari-12
describe = 测试路口a
enable = 1
id = 13
password = 
roadId = road12
topic = test-12
type = mqtt
username = 
[connect13]
addr = ws://127.0.0.1:8083/mqtt
clientId = tsari-13
describe = 
enable = 1
id = 14
password = 
roadId = road13
topic = test-13
type = mqtt
username = 
[connect14]
addr = ws://127.0.0.1:8083/mqtt
clientId = tsari-14
describe = 
enable = 1
id = 15
password = 
roadId = road14
topic = test-14
type = mqtt
username = 
[connect15]
addr = ws://127.0.0.1:8083/mqtt
clientId = tsari-15
describe = 
enable = 1
id = 16
password = 
roadId = road15
topic = test-15
type = mqtt
username = 
[connect16]
addr = ws://127.0.0.1:8083/mqtt
clientId = tsari-16
describe = 
enable = 1
id = 17
password = 
roadId = road16
topic = test-16
type = mqtt
username = 
[connect17]
addr = ws://127.0.0.1:8083/mqtt
clientId = tsari-17
describe = 
enable = 1
id = 18
password = 
roadId = road17
topic = test-17
type = mqtt
username = 
[connect18]
addr = ws://127.0.0.1:8083/mqtt
clientId = tsari-18
describe = 
enable = 1
id = 19
password = 
roadId = road18
topic = test-18
type = mqtt
username = 
[connect19]
addr = ws://127.0.0.1:8083/mqtt
clientId = tsari-19
describe = 
enable = 1
id = 20
password = 
roadId = road19
topic = test-19
type = mqtt
username = 
[connect2]
addr = ws://127.0.0.1:8083/mqtt
clientId = tsari-2
describe = 测试路口a
enable = 1
id = 3
password = 
roadId = road2
topic = test-2
type = mqtt
username = 
[connect20]
addr = ws://127.0.0.1:8083/mqtt
clientId = tsari-20
describe = 
enable = 1
id = 21
password = 
roadId = road20
topic = test-20
type = mqtt
username = 
[connect21]
addr = ws://127.0.0.1:8083/mqtt
clientId = tsari-21
describe = test
enable = 1
id = 22
password = *******
roadId = road21
topic = test-21
type = mqtt
username = tsari
[connect22]
addr = ws://127.0.0.1:8083/mqtt
clientId = tsari-22
describe = 测试路口a
enable = 1
id = 23
password = 
roadId = road22
topic = test-22
type = mqtt
username = 
[connect23]
addr = ws://127.0.0.1:8083/mqtt
clientId = tsari-23
describe = 
enable = 1
id = 24
password = 
roadId = road23
topic = test-23
type = mqtt
username = 
[connect24]
addr = ws://127.0.0.1:8083/mqtt
clientId = tsari-24
describe = 
enable = 1
id = 25
password = 
roadId = road24
topic = test-24
type = mqtt
username = 
[connect25]
addr = ws://127.0.0.1:8083/mqtt
clientId = tsari-25
describe = 
enable = 1
id = 26
password = 
roadId = road25
topic = test-25
type = mqtt
username = 
[connect26]
addr = ws://127.0.0.1:8083/mqtt
clientId = tsari-26
describe = 
enable = 1
id = 27
password = 
roadId = road26
topic = test-26
type = mqtt
username = 
[connect27]
addr = ws://127.0.0.1:8083/mqtt
clientId = tsari-27
describe = 
enable = 1
id = 28
password = 
roadId = road27
topic = test-27
type = mqtt
username = 
[connect28]
addr = ws://127.0.0.1:8083/mqtt
clientId = tsari-28
describe = 
enable = 1
id = 29
password = 
roadId = road28
topic = test-28
type = mqtt
username = 
[connect29]
addr = ws://127.0.0.1:8083/mqtt
clientId = tsari-29
describe = 
enable = 1
id = 10
password = 
roadId = road29
topic = test-29
type = mqtt
username = 
[connect3]
addr = ws://127.0.0.1:8083/mqtt
clientId = tsari-3
describe = 
enable = 1
id = 4
password = 
roadId = road3
topic = test-3
type = mqtt
username = 
[connect4]
addr = ws://127.0.0.1:8083/mqtt
clientId = tsari-4
describe = 
enable = 1
id = 5
password = 
roadId = road4
topic = test-4
type = mqtt
username = 
[connect5]
addr = ws://127.0.0.1:8083/mqtt
clientId = tsari-5
describe = 
enable = 1
id = 6
password = 
roadId = road5
topic = test-5
type = mqtt
username = 
[connect6]
addr = ws://127.0.0.1:8083/mqtt
clientId = tsari-6
describe = 
enable = 1
id = 7
password = 
roadId = road6
topic = test-6
type = mqtt
username = 
[connect7]
addr = ws://127.0.0.1:8083/mqtt
clientId = tsari-7
describe = 
enable = 1
id = 8
password = 
roadId = road7
topic = test-7
type = mqtt
username = 
[connect8]
addr = ws://127.0.0.1:8083/mqtt
clientId = tsari-8
describe = 
enable = 1
id = 9
password = 
roadId = road8
topic = test-8
type = mqtt
username = 
[connect9]
addr = ws://127.0.0.1:8083/mqtt
clientId = tsari-9
describe = 
enable = 1
id = 10
password = 
roadId = road9
topic = test-9
type = mqtt
username = 
